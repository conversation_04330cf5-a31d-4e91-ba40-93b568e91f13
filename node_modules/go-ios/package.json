{"name": "go-ios", "version": "v1.0.182", "description": "Provide a stable and production ready opensource solution to automate iOS device on Linux, Windows and Mac OS X.", "main": "index.js", "scripts": {"postinstall": "node postinstall.js install", "preuninstall": "node postinstall.js uninstall"}, "repository": {"type": "git", "url": "git+https://github.com/danielpaulus/go-ios.git"}, "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/danielpaulus/go-ios/issues"}, "homepage": "https://github.com/danielpaulus/go-ios#readme", "goBinary": {"name": "ios", "path": "./bin"}, "files": ["dist", "postinstall.js"], "dependencies": {"mkdirp": "^1.0.4"}}