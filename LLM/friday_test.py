import requests
import json

def check_popup(image_url):
    url = "https://aigc.sankuai.com/v1/openai/native/chat/completions"
    
    headers = {
        "Authorization": "Bearer 1680849412324622382",
        "Content-Type": "application/json"
    }
    
    payload = {
        "stream": False,  # 改为False以获取完整响应
        "model": "LongCat-VL-Medium",
        "messages": [{
            "role": "user",
            "content": [{
                "type": "text",
                "text": """请分析图片中是否存在弹窗。请严格按照以下JSON格式返回：
                        {
                            "has_popup": boolean,
                            "close_action": string
                        }
                        其中has_popup为true表示有弹窗，false表示无弹窗。
                        close_action在有弹窗时描述关闭方式，无弹窗时为null。
                        示例：
                        有弹窗：{"has_popup": true, "close_action": "点击右上角X按钮"}
                        无弹窗：{"has_popup": false, "close_action": null}"""
            }, {
                "type": "image_url",
                "image_url": {"url": image_url}
            }]
        }],
        "max_new_tokens": 4096,
        "temperature": 0.3,
        "repetition_penalty": 1.1,
        "top_p": 0.7,
        "top_k": 4
    }

    try:
        response = requests.post(url, headers=headers, json=payload)
        response.raise_for_status()
        result = response.json()
        
        # 从API响应中提取实际的结果
        if 'choices' in result and len(result['choices']) > 0:
            content = result['choices'][0]['message']['content']
            # 尝试解析返回的JSON字符串
            try:
                return json.loads(content)
            except json.JSONDecodeError:
                print(f"无法解析返回的JSON: {content}")
                return None
        return None
    except requests.exceptions.RequestException as e:
        print(f"请求错误: {e}")
        return None

if __name__ == "__main__":
    test_image_url = "http://p0.meituan.net/ptautotest/aa0dda41bbf3a8869ee10870e676e8dd1297399.png"
    result = check_popup(test_image_url)
    if result:
        print(json.dumps(result, ensure_ascii=False, indent=2))
    else:
        print("获取结果失败") 