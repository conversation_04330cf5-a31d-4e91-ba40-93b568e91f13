curl --location 'https://aigc.sankuai.com/v1/openai/native/chat/completions' \
--header 'Authorization: Bearer 1680849412324622382' \
--header 'Content-Type: application/json' \
--data '{
    "stream": true,
    "model": "LongCat-VL-Medium",
    "messages": [
        {
            "role": "user",
            "content": [
                {
                    "type": "text",
                    "text": "识别图片中是否有弹窗或pop-up消息，并告诉我怎么关闭，仅返回json，格式为 ｛'has_popup':true/false, 'how_to_close':'xxx'｝"
                },
                {
                    "type": "image_url",
                    "image_url": {
                        "url": "http://p0.meituan.net/ptautotest/9211f0867cbcb2aa8a6370af26515db2415855.png"
                    }
                }
            ]
        }
    ],
    "max_new_tokens": 4096,
    "temperature": 0.95,
    "repetition_penalty": 1.1,
    "top_p": 0.7,
    "top_k": 4
}'
