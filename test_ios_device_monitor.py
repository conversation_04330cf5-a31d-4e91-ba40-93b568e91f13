#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
iOS设备内存监控测试脚本
用于检测iPhone设备的内存、CPU等系统资源使用情况
"""

import os
import sys
import time
import json
import subprocess
import requests
from datetime import datetime

class IOSDeviceMonitor:
    def __init__(self, device_udid=None, wda_port=8100):
        """
        初始化iOS设备监控
        
        Args:
            device_udid: 设备UDID，如果为None则使用第一个连接的设备
            wda_port: WebDriverAgent端口
        """
        self.device_udid = device_udid
        self.wda_port = wda_port
        
        # 如果没有指定UDID，尝试获取第一个连接的设备
        if not self.device_udid:
            self.device_udid = self.get_first_connected_device()
            
        if not self.device_udid:
            raise Exception("没有找到连接的iOS设备")
            
        print(f"监控设备: {self.device_udid}")
        
    def get_first_connected_device(self):
        """获取第一个连接的iOS设备UDID"""
        try:
            result = subprocess.run(['idevice_id', '-l'], 
                                 capture_output=True, text=True, timeout=10)
            if result.returncode == 0 and result.stdout.strip():
                devices = result.stdout.strip().split('\n')
                return devices[0] if devices else None
        except Exception as e:
            print(f"获取设备列表失败: {e}")
        return None
    
    def get_device_info_via_ideviceinfo(self):
        """使用ideviceinfo获取设备基本信息"""
        try:
            cmd = ['ideviceinfo', '-u', self.device_udid]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=15)
            
            if result.returncode != 0:
                return {"error": f"ideviceinfo失败: {result.stderr}"}
                
            # 解析设备信息
            device_info = {}
            for line in result.stdout.split('\n'):
                if ':' in line:
                    key, value = line.split(':', 1)
                    device_info[key.strip()] = value.strip()
                    
            return {
                "device_name": device_info.get("DeviceName", "Unknown"),
                "product_type": device_info.get("ProductType", "Unknown"),
                "product_version": device_info.get("ProductVersion", "Unknown"),
                "build_version": device_info.get("BuildVersion", "Unknown"),
                "total_disk_capacity": device_info.get("TotalDiskCapacity", "Unknown"),
                "total_system_available": device_info.get("TotalSystemAvailable", "Unknown"),
                "wifi_address": device_info.get("WiFiAddress", "Unknown"),
                "bluetooth_address": device_info.get("BluetoothAddress", "Unknown"),
                "battery_is_charging": device_info.get("BatteryIsCharging", "Unknown"),
                "battery_current_capacity": device_info.get("BatteryCurrentCapacity", "Unknown"),
            }
        except Exception as e:
            return {"error": f"获取设备信息失败: {e}"}
    
    def get_device_info_via_wda(self):
        """通过WebDriverAgent获取设备信息"""
        try:
            # 启动iproxy进行端口转发
            iproxy_process = subprocess.Popen(
                ['iproxy', '-u', self.device_udid, f'{self.wda_port}:{self.wda_port}'],
                stdout=subprocess.PIPE, stderr=subprocess.PIPE
            )
            
            # 等待iproxy启动
            time.sleep(2)
            
            try:
                # 获取设备信息
                response = requests.get(f'http://localhost:{self.wda_port}/wda/device/info', timeout=10)
                if response.status_code == 200:
                    device_info = response.json()['value']
                    return {
                        "name": device_info.get("name", "Unknown"),
                        "model": device_info.get("model", "Unknown"),
                        "os_version": device_info.get("os", {}).get("version", "Unknown"),
                        "screen_size": device_info.get("screen", {}).get("size", {}),
                        "locale": device_info.get("locale", "Unknown"),
                        "timezone": device_info.get("timeZone", "Unknown"),
                    }
                else:
                    return {"error": f"WDA请求失败: {response.status_code}"}
            except Exception as e:
                return {"error": f"WDA连接失败: {e}"}
            finally:
                # 终止iproxy进程
                try:
                    iproxy_process.terminate()
                    iproxy_process.wait(timeout=5)
                except:
                    iproxy_process.kill()
                    
        except Exception as e:
            return {"error": f"启动iproxy失败: {e}"}
    
    def get_running_processes(self):
        """获取设备运行的进程信息（需要越狱设备或开发者证书）"""
        try:
            # 尝试通过WDA获取当前运行的应用
            iproxy_process = subprocess.Popen(
                ['iproxy', '-u', self.device_udid, f'{self.wda_port}:{self.wda_port}'],
                stdout=subprocess.PIPE, stderr=subprocess.PIPE
            )
            
            time.sleep(2)
            
            try:
                # 获取当前会话信息
                response = requests.get(f'http://localhost:{self.wda_port}/status', timeout=10)
                if response.status_code == 200:
                    status_info = response.json()['value']
                    return {
                        "session_id": status_info.get("sessionId", "None"),
                        "ready": status_info.get("ready", False),
                        "message": status_info.get("message", ""),
                        "build": status_info.get("build", {}),
                        "ios_version": status_info.get("ios", {}).get("version", "Unknown"),
                        "device_udid": status_info.get("device", {}).get("udid", "Unknown"),
                    }
                else:
                    return {"error": f"获取状态失败: {response.status_code}"}
            except Exception as e:
                return {"error": f"获取进程信息失败: {e}"}
            finally:
                try:
                    iproxy_process.terminate()
                    iproxy_process.wait(timeout=5)
                except:
                    iproxy_process.kill()
                    
        except Exception as e:
            return {"error": f"获取进程信息失败: {e}"}
    
    def check_device_performance(self):
        """检查设备性能指标"""
        try:
            # 使用instruments命令获取设备性能数据（需要Xcode）
            cmd = [
                'instruments', '-s', 'devices'
            ]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                # 查找我们的设备
                devices_output = result.stdout
                device_found = False
                for line in devices_output.split('\n'):
                    if self.device_udid in line:
                        device_found = True
                        return {
                            "device_status": "connected",
                            "instruments_available": True,
                            "device_line": line.strip()
                        }
                
                if not device_found:
                    return {"error": "设备未在instruments中找到"}
            else:
                return {"error": f"instruments命令失败: {result.stderr}"}
                
        except Exception as e:
            return {"error": f"性能检查失败: {e}"}
    
    def get_disk_usage(self):
        """获取设备存储使用情况"""
        try:
            # 使用ideviceinfo获取磁盘信息
            cmd = ['ideviceinfo', '-u', self.device_udid, '-q', 'com.apple.disk_usage']
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                disk_info = {}
                for line in result.stdout.split('\n'):
                    if ':' in line:
                        key, value = line.split(':', 1)
                        disk_info[key.strip()] = value.strip()
                        
                # 获取总容量和可用容量
                total_capacity = disk_info.get('TotalDiskCapacity', '0')
                available_capacity = disk_info.get('TotalSystemAvailable', '0')
                
                try:
                    total_gb = int(total_capacity) / (1024**3) if total_capacity.isdigit() else 0
                    available_gb = int(available_capacity) / (1024**3) if available_capacity.isdigit() else 0
                    used_gb = total_gb - available_gb
                    usage_percent = (used_gb / total_gb * 100) if total_gb > 0 else 0
                    
                    return {
                        "total_capacity_gb": round(total_gb, 2),
                        "available_capacity_gb": round(available_gb, 2),
                        "used_capacity_gb": round(used_gb, 2),
                        "usage_percent": round(usage_percent, 2),
                        "raw_info": disk_info
                    }
                except:
                    return {"error": "磁盘信息解析失败", "raw_info": disk_info}
            else:
                # 尝试使用基本的ideviceinfo
                return self.get_basic_disk_info()
                
        except Exception as e:
            return {"error": f"获取磁盘使用情况失败: {e}"}
    
    def get_basic_disk_info(self):
        """获取基本磁盘信息"""
        try:
            device_info = self.get_device_info_via_ideviceinfo()
            if "error" not in device_info:
                total_capacity = device_info.get('total_disk_capacity', '0')
                available_capacity = device_info.get('total_system_available', '0')
                
                return {
                    "total_capacity_raw": total_capacity,
                    "available_capacity_raw": available_capacity,
                    "note": "原始数据，可能需要进一步解析"
                }
            else:
                return device_info
        except Exception as e:
            return {"error": f"获取基本磁盘信息失败: {e}"}
    
    def monitor_device_full_report(self):
        """生成完整的设备监控报告"""
        print("=" * 60)
        print(f"iOS设备监控报告 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 60)
        print(f"设备UDID: {self.device_udid}")
        print(f"WDA端口: {self.wda_port}")
        print()
        
        # 1. 基本设备信息
        print("📱 基本设备信息:")
        print("-" * 40)
        device_info = self.get_device_info_via_ideviceinfo()
        if "error" not in device_info:
            for key, value in device_info.items():
                print(f"  {key}: {value}")
        else:
            print(f"  ❌ {device_info['error']}")
        print()
        
        # 2. WDA连接信息
        print("🔗 WebDriverAgent连接信息:")
        print("-" * 40)
        wda_info = self.get_device_info_via_wda()
        if "error" not in wda_info:
            for key, value in wda_info.items():
                print(f"  {key}: {value}")
        else:
            print(f"  ❌ {wda_info['error']}")
        print()
        
        # 3. 进程状态
        print("⚙️ 设备状态信息:")
        print("-" * 40)
        process_info = self.get_running_processes()
        if "error" not in process_info:
            for key, value in process_info.items():
                print(f"  {key}: {value}")
        else:
            print(f"  ❌ {process_info['error']}")
        print()
        
        # 4. 磁盘使用情况
        print("💾 存储使用情况:")
        print("-" * 40)
        disk_info = self.get_disk_usage()
        if "error" not in disk_info:
            for key, value in disk_info.items():
                if key != "raw_info":
                    print(f"  {key}: {value}")
        else:
            print(f"  ❌ {disk_info['error']}")
        print()
        
        # 5. 性能检查
        print("🔧 性能检查:")
        print("-" * 40)
        perf_info = self.check_device_performance()
        if "error" not in perf_info:
            for key, value in perf_info.items():
                print(f"  {key}: {value}")
        else:
            print(f"  ❌ {perf_info['error']}")
        print()
        
        # 6. 建议
        print("💡 监控建议:")
        print("-" * 40)
        
        # 根据磁盘使用情况给出建议
        if "error" not in disk_info and "usage_percent" in disk_info:
            usage = disk_info["usage_percent"]
            if usage > 90:
                print(f"  ⚠️  存储空间严重不足 ({usage}%)，建议清理设备")
            elif usage > 80:
                print(f"  ⚠️  存储空间不足 ({usage}%)，需要关注")
            elif usage > 70:
                print(f"  ℹ️  存储空间使用较高 ({usage}%)，建议监控")
            else:
                print(f"  ✅ 存储空间正常 ({usage}%)")
        
        # 根据WDA连接状态给出建议
        if "error" in wda_info:
            print("  ⚠️  WebDriverAgent连接异常，可能影响自动化测试")
        else:
            print("  ✅ WebDriverAgent连接正常")
        
        return {
            "device_info": device_info,
            "wda_info": wda_info,
            "process_info": process_info,
            "disk_info": disk_info,
            "performance_info": perf_info
        }

def main():
    """主函数"""
    print("🔍 iOS设备监控工具")
    print("=" * 60)
    
    try:
        # 如果有命令行参数，使用第一个参数作为UDID
        device_udid = sys.argv[1] if len(sys.argv) > 1 else None
        wda_port = int(sys.argv[2]) if len(sys.argv) > 2 else 8100
        
        # 创建监控实例
        monitor = IOSDeviceMonitor(device_udid, wda_port)
        
        # 生成完整报告
        report = monitor.monitor_device_full_report()
        
        # 可以选择将报告保存到文件
        save_report = input("\n是否保存报告到文件？(y/n): ").lower().strip()
        if save_report == 'y':
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"ios_device_monitor_report_{timestamp}.json"
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            
            print(f"✅ 报告已保存到: {filename}")
        
    except KeyboardInterrupt:
        print("\n\n⏹️  监控已停止")
    except Exception as e:
        print(f"\n❌ 监控出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()