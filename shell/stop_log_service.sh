#!/bin/bash

# 获取脚本所在目录的绝对路径
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
# 获取项目根目录
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
# PID文件路径
PID_FILE="$PROJECT_ROOT/log/log_service_pid.txt"
# 日志文件路径
LOG_FILE="$PROJECT_ROOT/log/python_logs/log_service.log"

# 检查PID文件是否存在
if [ ! -f "$PID_FILE" ]; then
    echo "日志服务未运行（PID文件不存在）"
    exit 0
fi

# 读取PID
PID=$(cat "$PID_FILE" | tr -d '\r\n')

# 检查PID是否为数字
if ! [[ "$PID" =~ ^[0-9]+$ ]]; then
    echo "PID文件内容无效: '$PID'"
    rm -f "$PID_FILE"
    exit 1
fi

# 检查进程是否存在
if ! ps -p $PID > /dev/null; then
    echo "日志服务未运行（PID: ${PID} 不存在）"
    rm -f "$PID_FILE"
    exit 0
fi

echo "正在停止日志服务（PID: ${PID}）..."

# 发送终止信号
kill $PID

# 等待进程结束
for i in {1..10}; do
    if ! ps -p $PID > /dev/null; then
        echo "日志服务已停止"
        # 删除PID文件（如果还存在）
        rm -f "$PID_FILE"
        exit 0
    fi
    sleep 1
done

# 如果进程仍然存在，强制终止
echo "日志服务未响应，正在强制终止..."
kill -9 $PID

# 再次检查进程
if ! ps -p $PID > /dev/null; then
    echo "日志服务已强制停止"
    # 删除PID文件（如果还存在）
    rm -f "$PID_FILE"
    exit 0
else
    echo "无法停止日志服务，请手动检查进程（PID: ${PID}）"
    exit 1
fi 