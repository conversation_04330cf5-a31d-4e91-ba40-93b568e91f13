#!/bin/bash
getJsonVal() {
    local json=$1
    local key=$2
    local value
    # 使用 sed 提取键对应的值，兼容字符串和数值
    value=$(echo "$json" | sed -E -n "s/.*\"$key\":(\"[^\"]*\"|[^,}]*).*/\1/p")
    # 去除值两端的引号（如果有）
    value=$(echo "$value" | sed -E 's/^"(.*)"$/\1/')
    echo "$value"
}
get_apk_url() {
    local version=$1
    # 构造基础的 curl 命令
    local data
    data=$(curl -s "$COMPASS_URL/compass/api/getApk/getLatestRegressionTestApkUrlFromSigma" -G \
    --data-urlencode "os=iOS" \
    --data-urlencode "version=$version")
    
    # 检查 data 是否为空
    if [[ -z "$data" ]]; then
        echo "empty empty"  # 特殊标记，用于主流程判断
        echo "版本 $version 的回归测试包还未生成" >&2  # 错误信息输出到标准错误
        return 1
    fi
    
    # 获取数据
    apk_url=$data
    versionName=$version
    echo "$apk_url $versionName"
}

request_fuzzing_task() {
    local template_id=$1
    local notifier=$2
    local versionName=$3
    local apk_url=$4
    local template_name=$5

    attempts=0
    max_attempts=3
    success=false

    while [ $attempts -lt $max_attempts ]; do
        response=$(curl -s -X POST "https://fargo.sankuai.com/gameHelper/api/fuzzing/createFuzzingTask" \
            -H "Content-Type: application/json" \
            -d "{
              \"taskName\": \"美团平台-iOS-$version-$template_name-模板ID$template_id\",
              \"templateId\": $template_id,
              \"os\": 1,
              \"taskMode\": 1,
              \"nodeDepth\": 1,
              \"notifier\": \"$notifier\",
              \"testPackageUrl\": \"$apk_url\",
              \"appType\": 1
            }")

        code=$(getJsonVal "$response" "code")
        if [ "$code" = "200" ]; then
            # 提取taskId
            taskId=$(echo "$response" | sed -E -n 's/.*"taskId":"([^"]*)".*/\1/p')
            echo "健壮性测试请求成功，taskId: $taskId (Template ID: $template_id, Notifier: $notifier)"
            success=true
            break
        else
            attempts=$((attempts+1))
            echo "健壮性测试请求失败 (Template ID: $template_id, Notifier: $notifier)，尝试次数：$attempts"
            if [ $attempts -lt $max_attempts ]; then
                echo "等待60秒后重试..."
                sleep 60
            else
                echo "健壮性测试接口多次尝试失败 (Template ID: $template_id, Notifier: $notifier)"
            fi
        fi
    done

    if $success; then
        # 将版本号和 apk_url 写入 tested_list.txt，表示已经测试过
        echo "请求健壮性测试接口成功"
        echo "$template_id|$version|$apk_url" >> "$TESTED_LIST"
    else
        echo "请求健壮性测试接口失败，退出脚本。"
        exit 1
    fi
}

COMPASS_URL="http://qaassist.sankuai.com"
TESTED_LIST="tested_list.txt"
# 在这儿添加模板和对应通知人
templateIds=("66")
misIds=("cuijie12")
templateNames=("首页回归")

# 如果 tested_list 文件不存在，先创建空文件
[ ! -f "$TESTED_LIST" ] && touch "$TESTED_LIST"

# 获取当前时间，如果需要手动测试，可手动指定
#current_time="2024-12-10 19:00"
current_time=$(date '+%Y-%m-%d %H:%M')
# 将 YYYY-MM-DD HH:MM 转成可比较大小的数值（如 202409181030）
to_timestamp() {
    local datetime="$1"
    echo "$datetime" | sed 's/[^0-9]//g'
}

current_ts=$(to_timestamp "$current_time")

# 添加环境变量，默认为 false
FORCE_TEST=${FORCE_TEST:-false}

# 请求 compass 后端接口来获取最近的一次回归状态，这样就不怕他错过回归测试时间段的窗口期了
eventResponse=$(curl -s "http://qaassist.sankuai.com/compass/api/calendar/nearestRegressionTest?id=2")
#echo "手动测试"
#eventResponse="2025-02-05 19:00|12.28.400|回归测试"
echo "eventResponse: $eventResponse"
eventResponse="2024-12-25 10:00|12.29.200|回归测试"
开始对返回结果进行拆分
version=$(echo "$eventResponse" | cut -d'|' -f2)
stage=$(echo "$eventResponse" | cut -d'|' -f3)
echo "当前所处版本: $version  当前所处阶段: $stage"

# 检查阶段名称中是否包含"回归测试"
if echo "$stage" | grep -q "回归测试"; then
    # 提取版本
    echo "当前处于回归测试阶段, 版本号: $version"
    
    # 在请求健壮性接口前，先检查本地 tested_list 是否已测试过该版本
    if [ "$FORCE_TEST" = false ] && grep -Fq "$version" "$TESTED_LIST"; then
        echo "版本 $version 已经进行过健壮性测试，跳过。"
        exit 0
    fi

    # 获取测试包链接
    result=$(get_apk_url "$version" "true")  # 第一次请求时包含 misId
    
    # 检查返回结果
    if [[ "$result" == "empty empty" ]]; then
        echo "当前版本回归测试包未生成，退出脚本"
        exit 0
    fi
    
    apk_url=$(echo "$result" | awk '{print $1}')
    versionName=$(echo "$result" | awk '{print $2}')

    echo "应用包链接：$apk_url"
    echo "版本号：$versionName"

    # 遍历 templateIds 和 misIds 数组，分别请求健壮性接口
    for i in "${!templateIds[@]}"; do
      template_id=${templateIds[$i]}
      template_name=${templateNames[$i]}
      notifier=${misIds[$i]}
      request_fuzzing_task "$template_id" "$notifier" "$version" "$apk_url" "$template_name"
      sleep 600
    done
else
    echo "不是回归测试阶段"
    exit 0
fi
