#!/bin/bash
getJsonVal() {
    local json=$1
    local key=$2
    local value
    # 使用 sed 提取键对应的值，兼容字符串和数值
    value=$(echo "$json" | sed -E -n "s/.*\"$key\":(\"[^\"]*\"|[^,}]*).*/\1/p")
    # 去除值两端的引号（如果有）
    value=$(echo "$value" | sed -E 's/^"(.*)"$/\1/')
    echo "$value"
}
# 设定一个域名，省的每次一个一个换
COMPASS_URL="http://qaassist.sankuai.com"
# 通知 URL，用于接收测试结果
NOTIFY_URL="$COMPASS_URL/compass/api/jump/test/result"
# 设备黑名单（设备标识符），测试时排除这些设备
blackNumbers="928QAEVL2248H,bca074fc8faa85b0edf54144200c7ebc54e31e3e,8703ef8ac2380fb78b44c9aa6a0df8bd015baae0,41349c9f05d87fffa4803d4e29f40011c51aa690,60aeb8e0176ec26a7a564c4b50ca7cec9334516b,26b04711656247b40b6f2076348fadf3f8151c3c,00008030-001C359C2E50802E"
# 测试设备列表，指定可用于测试的设备型号
devices="iPhone 7,iPhone 8,iPhone 11,iPhone 12,iPhone X,iPhone XR,iPhone XS,iPhone SE,iPhone 11 Pro"
# 初始化 apk 列表，用于存储待测试的应用包链接
apkList=()
# 初始化 mockId 列表，用于存储待测试的应用包链接
mockIdList=()
# 检查 APK_URL 是否为空，如果为空则执行定时触发的冒烟测试
if [ -z "$APK_URL" ]; then
    echo "定时触发冒烟"
    eventResponse=$(curl -s "http://qaassist.sankuai.com/compass/api/calendar/nearestRegressionTest?id=2")
	#echo "手动测试"
	#eventResponse="2025-02-05 19:00|12.28.400|回归测试"
	echo "eventResponse: $eventResponse"
	version=$(echo "$eventResponse" | cut -d'|' -f2)
	apk_url=$(curl -s "$COMPASS_URL/compass/api/getApk/getLatestRegressionTestApkUrlFromSigma" -G \
    	--data-urlencode "os=iOS" \
    	--data-urlencode "version=$version")
        
    # 如果最新版本的包为空，尝试获取上一个版本
    if [ -z "$apk_url" ]; then
        echo "最新版本 $version 的包未找到，尝试获取上一个版本..."
        eventResponse=$(curl -s "http://qaassist.sankuai.com/compass/api/calendar/previousRegressionTest?id=2")
        echo "上一个版本 eventResponse: $eventResponse"
        version=$(echo "$eventResponse" | cut -d'|' -f2)
        apk_url=$(curl -s "$COMPASS_URL/compass/api/getApk/getLatestRegressionTestApkUrlFromSigma" -G \
            --data-urlencode "os=iOS" \
            --data-urlencode "version=$version")
            
        # 如果上一个版本的包也为空，则退出脚本
        if [ -z "$apk_url" ]; then
            echo "错误：无法获取测试包。最新版本和上一个版本都未找到对应的包。"
            exit 1
        fi
    fi
    
    echo "应用包链接：$apk_url"
    apkList=("$apk_url")
    echo "版本号：$version"
else
    # 如果 APK_URL 不为空，则使用指定的应用包链接
    echo "HPX 打包触发冒烟"
    apkList=("$APK_URL")
fi

# 如果 apkList 为空，结束脚本执行
if [ ${#apkList[@]} -eq 0 ]; then
    echo "============本次没有需要测试的分支，结束============"
    exit 0
fi

# 如果 apkList 为空，结束脚本执行
if [ ${#apkList[@]} -eq 0 ]; then
    echo "============本次没有需要测试的分支，结束============"
    exit 0
fi

# 获取北上广深的 display 接口的 mockId
mockResponse=$(curl -s "$COMPASS_URL/compass/api/getCategoryList?platform=0&version=$version&cities=1、10、20、30")
mockIds=$(echo "$mockResponse" | sed 's/^\[\(.*\)\]$/\1/' | sed 's/,/ /g')
mockIdList=($mockIds)

echo "============本次构建中含有的城市数量为: ${#mockIdList[@]} ============"
echo "城市对应 mockid：${mockIdList[@]}"

echo "============本次构建中含有 apk 数量为: ${#apkList[@]} ============"
echo "测试包列表：${apkList[@]}"

# 常用的接口
BUILD_PATH="$COMPASS_URL/compass/api/autotest/build"
JOB_PATH="$COMPASS_URL/compass/api/autotest/job"
JUMP_BUILD_PATH="$COMPASS_URL/compass/api/jump/test"
# 设置构建和任务的 API 路径
echo "BUILD_NUMBER: $BUILD_NUMBER"
# Jenkins 构建的 URL
jenkinsUrl="https://jenkins.sankuai.com/job/平台业务组/job/imeituan_jump_test_for_conan/$BUILD_NUMBER"
echo "Jenkins URL: $jenkinsUrl"
# 测试报告的基础 URL
reportURL='https://conan.sankuai.com/v2/auto-function/report/'

# 工作目录
project_name="platform_appium_autotest"
repo_url="ssh://*******************/ptqa/platform_appium_autotest.git"
branch_name="cuijie12"
# jumpTest1.xml 路径
input_file="./$project_name/config-testngxml/group_platform/iOSMain/jumpTest1.xml"
# 云测最终测试 testng.xml 路径
output_file="./$project_name/testng.xml"

# 定义上传测试用例的函数，在循环外部定义以提高效率
upload_case() {
    local time=0
    while [[ $time -lt $((30 * 20)) ]]; do
        # 上传测试用例 ZIP 文件到测试平台
        res=$(curl -s -F "file=@./platform_appium_autotest.zip" "https://conan.sankuai.com/ci/upload?type=case&misId=cuijie12&conanKey=9dced111-0b0a-42c7-a919-09cceb03841f")
        # 解析上传结果，获取状态和用例名称
        status=$(echo "$res" | sed -n 's/.*"status":\([^,}]*\).*/\1/p')
        caseName=$(echo "$res" | sed -n 's/.*"fileName":"\([^"]*\)".*/\1/p')
        if [ "$status" -eq 1 ]; then
            # 上传成功，输出用例名称并退出循环
            echo "$caseName"
            break
        else
            # 上传失败，等待一段时间后重试
            sleep 20
            time=$(($time + 20))
        fi
    done
}
# 定义提交测试任务的函数
submit_job() {
    local time=0
    while [[ $time -lt $((31 * 10)) ]]; do
        # 提交测试任务到测试平台
		jobResponse=$(curl -s "https://conan.sankuai.com/ci/auto-function/appium" \
			-d "run_time=14400" \
			-d "ipa=$1" \
			-d "case=$2" \
			-d "blackNumbers=$blackNumbers" \
			-d "devices=$devices" \
			-d "businessLineId=89" \
			-d "deviceCount=1" \
			-d "appiumVersion=1.21.0" \
			-d "platform=iOS" \
			-d "alias=美团" \
			-d "devicesVersion=16,15,14,13" \
			-d "misId=cuijie12" \
			-d "conanKey=9dced111-0b0a-42c7-a919-09cceb03841f")
        # 解析提交结果，获取状态和任务 ID
		echo "jobResponse: $jobResponse"
        responseStatus=$(getJsonVal "$jobResponse" "status")
        jobId=$(getJsonVal "$jobResponse" "jobId")
		echo "responseStatus: $responseStatus"
        if [ "$responseStatus" -eq 1 ]; then
            # 提交成功，输出任务响应并退出循环
            echo "提交成功"
            break
        else
            echo "提交失败，等待 10s 重试"
            sleep 10
            time=$(($time + 10))
        fi
    done
}

notify_job() {
    local jobId="$1"
    local doDataAnalyse="$2"
    local doNotification="$3"
    local processAnyWay="$4"
    curl -s -X POST \
        -H "Content-Type: application/json" \
        -d "{
            \"timedata\":\"{\\\"totalTime\\\":\\\"49.13\\\",\\\"startTime\\\":\\\"2024-09-06 10:00:21\\\",\\\"endTime\\\":\\\"2024-09-06 10:49:29\\\"}\",
            \"test\":\"{\\\"doDataAnalyse\\\":\\\"$doDataAnalyse\\\",\\\"doNotification\\\":\\\"$doNotification\\\",\\\"processAnyWay\\\":\\\"$processAnyWay\\\"}\",
            \"jobdata\":\"{\\\"jobMsgCode\\\":100,\\\"jobResult\\\":1,\\\"report\\\":\\\"https:\\/\\/conan.sankuai.com\\/v2\\/auto-function\\/report\\/$jobId\\\",\\\"id\\\":$jobId,\\\"taskResult\\\":0,\\\"jobType\\\":\\\"auto-function\\\",\\\"jobMsg\\\":\\\"success\\\",\\\"test\\\":0}\",
            \"extra\":null,
            \"devicelist\":\"[{\\\"deviceResolution\\\":\\\"1133x2453\\\",\\\"deviceModel\\\":\\\"PPA-AL20\\\",\\\"deviceVersion\\\":\\\"10\\\",\\\"udid\\\":\\\"CTV0221414009020\\\"}]\"
        }" \
        "$NOTIFY_URL"
}

if [[ -d "$project_name" ]]; then
    echo "目录 $project_name 已存在，正在删除..."
    rm -rf "$project_name"
    if [[ $? -ne 0 ]]; then
        echo "删除 $project_name 失败，请检查权限。"
        exit 1
    fi
    echo "目录 $project_name 已成功删除。"
fi

# 拉取测试代码
echo "开始拉取代码..."
git clone -b "$branch_name" "$repo_url"

if [[ -d "$project_name" ]]; then
    echo "正在检查 $project_name 目录下的一级 .xml 文件..."
    # 查找并删除一级目录下的 .xml 文件 (排除 pom.xml)
    xml_files=$(find "$project_name" -maxdepth 1 -type f -name "*.xml" ! -name "pom.xml")
    if [[ -n "$xml_files" ]]; then
        echo "找到以下 .xml 文件："
        echo "$xml_files"
        echo "正在删除 .xml 文件..."
        find "$project_name" -maxdepth 1 -type f -name "*.xml" ! -name "pom.xml" -exec rm -f {} \;
        # 检查删除是否成功
        if [[ $? -eq 0 ]]; then
            echo "一级目录下的 .xml 文件已全部删除。"
        else
            echo "删除 .xml 文件时出错，请检查权限或文件状态。"
            exit 1
        fi
    else
        echo "一级目录下未找到 .xml 文件，无需删除。"
    fi
else
    echo "目录 $project_name 不存在，请检查。"
    exit 1
fi

# 将指定的配置文件复制为 app.properties
echo "复制 iOS.properties 为 app.properties"
cp ./$project_name/config-properties/meituan/iOS.properties ./$project_name/app.properties

# 遍历 apkList 中的每个应用包链接
for apk_url in "${apkList[@]}"; do

    # 新增一次构建，获取构建 ID
    buildId=$(curl -s -X POST -d "jenkinsId=$BUILD_NUMBER&platform=iOS&buildUrl=$jenkinsUrl&buildType=jump&alias=美团" "$BUILD_PATH/add")
    # 新增一次跳转测试，获取跳转构建 ID
    jump_buildId=$(curl -s -X POST -d "jenkinsId=$BUILD_NUMBER&platform=iOS&type=homePageCheck&buildUrl=$jenkinsUrl&testApp=$apk_url" "$JUMP_BUILD_PATH/new")
    # 更新构建状态为进行中
    curl -s -X POST -d "id=$buildId&status=0" "$BUILD_PATH/update/status"
    echo "Jump Build ID: $jump_buildId"
    # 开始针对不同城市进行测试，记录开始时间
	curl -s -X POST -d "id=$buildId" "$BUILD_PATH/update/startUploadCase"
    jobId=0
    jobIdList=()
    for mockId in "${mockIdList[@]}"; do
		echo "-----开始修改 jumpTest1.xml 中对应 mockId 信息，当前 mockId 为: $mockId-----"
		sed "s/\(<parameter name=\"mockId\" *value=\"\)[^\"]*\"/\1${mockId}\"/" "$input_file" > "$output_file"
        
		echo '-----上传测试用例-----'
		cd .
        
		# 压缩测试自动化代码，排除 .git 目录
		rm -f platform_appium_autotest.zip
		zip -rq platform_appium_autotest.zip platform_appium_autotest/ -x "platform_appium_autotest/.git/*"
		# 上传测试用例到测试平台
		TestCaseName=$(upload_case)
		echo "测试用例名称：$TestCaseName"
		# 提交测试任务（待测应用包）
		curl -s -X POST -d "id=$buildId" "$BUILD_PATH/update/startSubmit"
		echo "提交测试任务，APK URL：$apk_url"
		submit_job "$apk_url" "$TestCaseName"
		echo "测试任务 ID：$jobId"
        jobIdList+=("$jobId")
		# 更新测试报告 ID 和状态
		curl -s -X POST -d "testReportId=$jobId&id=$jump_buildId&testStatus=0&testMockid=$mockId" "$JUMP_BUILD_PATH/update"
		# 设置一个等待时间，避免一次性请求太多次云测任务
        sleep 60
    done
    
    sleep 3000
    # 挨个落库
    for id in "${jobIdList[@]}"; do
    	echo "现在对:$id 进行落库"
    	notify_job "$id" 1 0 0
        sleep  300
	done
    
    # 等待所有测试用例都执行完再触发通知
    notify_job $jobId 0 1 0 
    
	# 记录结束时间
	curl -s -X POST -d "id=$buildId" "$BUILD_PATH/update/endUploadCase"

    # 完成任务提交，更新构建状态
    curl -s -X POST -d "id=$buildId" "$BUILD_PATH/update/endSubmit"
    curl -s -X POST -d "buildId=$buildId&platform=iOS&alias=美团&jobId=$jobId&jobType=jump" "$JOB_PATH/add"

    # 更新构建状态为完成
    curl -s -X POST -d "buildUrl=$jenkinsUrl" "$BUILD_PATH/update/end"
    curl -s -X POST -d "buildUrl=$jenkinsUrl&status=1" "$BUILD_PATH/update/status"
    curl -s -X POST -d "id=$jump_buildId&testStatus=1" "$JUMP_BUILD_PATH/update"
    echo "测试报告 URL：${reportURL}${jobId}"

    # 返回根目录，准备处理下一个应用包（如果有）
    cd .
done

echo "脚本执行完成。"