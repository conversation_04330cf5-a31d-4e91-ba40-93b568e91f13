#!/bin/bash

# 获取脚本所在目录的绝对路径
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
# 获取项目根目录
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
# Python脚本路径
LOG_SERVICE_SCRIPT="$PROJECT_ROOT/python/log_service.py"
# PID文件路径
PID_FILE="$PROJECT_ROOT/log/log_service_pid.txt"
# 日志文件路径
LOG_FILE="$PROJECT_ROOT/log/python_logs/log_service.log"

# 支持手动指定 Python 路径
if [ -n "$CUSTOM_PYTHON_PATH" ] && [ -x "$CUSTOM_PYTHON_PATH" ]; then
    PYTHON_PATH="$CUSTOM_PYTHON_PATH"
    echo "使用自定义指定的 Python 解释器: $PYTHON_PATH"
else
    # 优先用 which python，确认主版本为3
    PYTHON_PATH=""
    if PYTHON_PATH_TMP=$(which python 2>/dev/null); then
        PYTHON_VERSION=$($PYTHON_PATH_TMP --version 2>&1 | awk '{print $2}' | cut -d. -f1)
        if [ "$PYTHON_VERSION" = "3" ]; then
            PYTHON_PATH="$PYTHON_PATH_TMP"
        fi
    fi

    # 如果 which python 不符合要求，再用 which python3
    if [ -z "$PYTHON_PATH" ]; then
        if PYTHON_PATH_TMP=$(which python3 2>/dev/null); then
            PYTHON_VERSION=$($PYTHON_PATH_TMP --version 2>&1 | awk '{print $2}' | cut -d. -f1)
            if [ "$PYTHON_VERSION" = "3" ]; then
                PYTHON_PATH="$PYTHON_PATH_TMP"
            fi
        fi
    fi

    if [ -z "$PYTHON_PATH" ] || [ ! -x "$PYTHON_PATH" ]; then
        echo "错误: 未找到可用的 Python 3 解释器。请安装 Python 3 后再试。"
        exit 1
    fi
    echo "最终选用的 Python 解释器: $PYTHON_PATH"
    $PYTHON_PATH --version
fi

# 确保日志目录存在
mkdir -p "$PROJECT_ROOT/log/python_logs"
mkdir -p "$PROJECT_ROOT/log/processed_logs"

# 检查服务是否已经在运行
if [ -f "$PID_FILE" ]; then
    PID=$(cat "$PID_FILE")
    if ps -p $PID > /dev/null; then
        echo "日志服务已经在运行，PID: $PID"
        exit 0
    else
        echo "发现过期的PID文件，将删除"
        rm -f "$PID_FILE"
    fi
fi

echo "正在启动日志服务..."

# 启动服务，处理现有日志并持续监控
# 移除 --delete-after-process 参数，保留原始日志文件
nohup "$PYTHON_PATH" "$LOG_SERVICE_SCRIPT" --process-existing > "$LOG_FILE" 2>&1 &

# 等待PID文件生成
for i in {1..30}; do
    if [ -f "$PID_FILE" ]; then
        PID=$(cat "$PID_FILE")
        echo "日志服务已启动，PID: $PID"
        exit 0
    fi
    sleep 1
done

echo "启动日志服务失败，请检查日志: $LOG_FILE"
exit 1 