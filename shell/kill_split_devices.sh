#!/bin/bash

# 快速停止 split_devices.py 服务的脚本
# 使用方法: ./kill_split_devices.sh

echo "🛑 Quick Kill - split_devices.py 服务停止器"
echo "================================================"

# 检查Python脚本是否存在
if [ ! -f "../python/stop_split_devices.py" ]; then
    echo "❌ 错误: stop_split_devices.py 不存在"
    echo "💡 请确保在正确的目录下运行此脚本"
    exit 1
fi

# 检查是否安装了psutil
python3 -c "import psutil" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "⚠️  警告: psutil 模块未安装，尝试安装..."
    pip3 install psutil
    if [ $? -ne 0 ]; then
        echo "❌ 错误: 无法安装 psutil 模块"
        echo "💡 请手动运行: pip3 install psutil"
        exit 1
    fi
fi

echo ""
echo "🔄 正在启动 Python 停止脚本..."
cd ..
python3 python/stop_split_devices.py

echo ""
echo "✅ 停止脚本执行完成"
echo "📋 详细日志请查看 stop_split_devices_*.log 文件" 