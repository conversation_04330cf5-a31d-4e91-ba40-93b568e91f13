#!/bin/bash
getJsonVal() {
    ~/venv/bin/python -c "import json,sys;sys.stdout.write(json.dumps(json.load(sys.stdin)$1))";
}

# 添加环境变量，默认为 false
FORCE_TEST=${FORCE_TEST:-false}

TESTED_LIST="tested_list.txt"

# 如果 tested_list 文件不存在，先创建空文件
[ ! -f "$TESTED_LIST" ] && touch "$TESTED_LIST"

COMPASS_URL="http://qaassist.sankuai.com"
eventResponse=$(curl -s "http://qaassist.sankuai.com/compass/api/calendar/nearestRegressionTest?id=1")
#echo "手动测试"
#eventResponse="2025-02-05 19:00|12.29.200|回归测试"
echo "eventResponse: $eventResponse"
version=$(echo "$eventResponse" | cut -d'|' -f2)
stage=$(echo "$eventResponse" | cut -d'|' -f3)
apk_url=$(curl -s "$COMPASS_URL/compass/api/getApk/getLatestRegressionTestApkUrlFromSigma" -G \
	--data-urlencode "os=Android" \
	--data-urlencode "version=$version")

# 如果最新版本的包为空，尝试获取上一个版本
if [ -z "$apk_url" ]; then
  echo "最新版本 $version 的包未找到，尝试获取上一个版本..."
  eventResponse=$(curl -s "http://qaassist.sankuai.com/compass/api/calendar/previousRegressionTest?id=1")
  echo "上一个版本 eventResponse: $eventResponse"
  version=$(echo "$eventResponse" | cut -d'|' -f2)
  apk_url=$(curl -s "$COMPASS_URL/compass/api/getApk/getLatestRegressionTestApkUrlFromSigma" -G \
      --data-urlencode "os=Android" \
      --data-urlencode "version=$version")
  
  # 如果上一个版本的包也为空，则退出脚本
  if [ -z "$apk_url" ]; then
      echo "错误：无法获取测试包。最新版本和上一个版本都未找到对应的包。"
      exit 1
      fi
fi

echo "应用包链接：$apk_url"
apkList=("$apk_url")
echo "版本号：$version"
echo "当前所处版本: $version  当前所处阶段: $stage"

# 在进行测试前，先检查本地 tested_list 是否已测试过该版本
if [ "$FORCE_TEST" = false ] && grep -Fq "$version" "$TESTED_LIST"; then
  echo "版本 $version 已经进行过埋点测试，跳过。"
  exit 0
fi

env='prod'

if [[ "prod" == "$env" ]];then
    BUILD_PATH='http://qaassist.sankuai.com/compass/api/oreoJob'
	DETAIL_PATH='http://qaassist.sankuai.com/compass/api/oreoDetail'
else
    BUILD_PATH='http://10.101.255.57:8080/compass/api/oreoJob'
    DETAIL_PATH='http://10.101.255.57:8080/compass/api/oreoDetail'
fi
echo $env


echo 'BUILD_NUMBER:' $BUILD_NUMBER
jenkinsUrl='https://jenkins.sankuai.com/job/%E5%B9%B3%E5%8F%B0%E4%B8%9A%E5%8A%A1%E7%BB%84/job/trackJob_ios/'$BUILD_NUMBER
echo 'jenkinsUrl:'$jenkinsUrl
reportURL='https://conan.sankuai.com/v2/auto-function/report/'

##新增一次构建
echo '=============Case更新========'
date "+%Y-%m-%d %H:%M:%S"
rm -rf platform_appium_autotest
git clone -b cuijie12 ssh://*******************/ptqa/platform_appium_autotest.git
date "+%Y-%m-%d %H:%M:%S"
echo '=============Case更新完毕========'
app_test_url=$apkurl


blackNumbers=$(curl -d "" http://qaassist.sankuai.com/compass/api/autotest/getblacklist)

upload_case(){
	while true
	do
		res=$(curl -F "file=@platform_appium_autotest.zip" https://conan.sankuai.com/ci/upload\?type=case\&misId=cuijie12\&conanKey=9dced111-0b0a-42c7-a919-09cceb03841f)
        status=$(echo "$res" | sed -n 's/.*"status":\([^,}]*\).*/\1/p')
		caseName=$(echo "$res" | sed -n 's/.*"fileName":"\([^"]*\)".*/\1/p')
       # status=${status:-0}
		if [ $status -eq 1 ]
		then
			echo $caseName
			break
		else
			sleep 20
		fi
	done
}

submit_job(){
	while true
	do
		JobResponse=$(curl -d "apk=$1&case=$2&blackNumbers=$blackNumbers&platform=Android&businessLineId=89&appiumVersion=1.21.0&alias=美团&after=notifyResultCommon&notify=http://qaassist.sankuai.com/compass/api/trackJob/result&misId=cuijie12&conanKey=9dced111-0b0a-42c7-a919-09cceb03841f&devicesVerson=15,14,13,12,11,10&deviceCount=1&devices=BRQ-AN00,RTE-AL00,RNA-AN00" https://conan.sankuai.com/ci/auto-function/appium)
		ResponseStatus=$(echo $JobResponse | grep -o '"status":[0-9]*' | cut -d: -f2)
		JobId=$(echo $JobResponse | grep -o '"jobId":[0-9]*' | cut -d: -f2)
		if [ -z "$apkurl" ]
		then
			echo "jobId: $JobId"
			break
		else
			sleep 10	
		fi
	done
}

jobIds=()
echo '=============分块提交自动化job========'
date "+%Y-%m-%d %H:%M:%S"

echo '-----uploading TestCase-----'
rm -f platform_appium_autotest.zip
rm -f ./testng.xml
cd platform_appium_autotest
echo ${i}
cp ./config-properties/meituan/android.properties ./app.properties
cp "./config-testngxml/group_platform/trackHomepage.xml" ./testng.xml
cd ..
zip -qr platform_appium_autotest.zip platform_appium_autotest/  -x "platform_appium_autotest/.git/*"
ls -l
echo 'uploading'

TestCaseName=`upload_case platform_appium_autotest.zip`
echo "################TestCaseName################"
echo $TestCaseName

##
##开始提交job
##

echo '-----submit job.....-----'
nowData=`date "+%Y-%m-%d %H:%M:%S"`
JobId=`submit_job  ${apk_url} $TestCaseName`
echo "jobId: $JobId"
submit_success_time=$(date "+%Y-%m-%d %H:%M:%S")
echo submit_success_time:$submit_success_time

echo '=============job提交完毕========'
##job提交完成
# 将版本号和 apk_url 写入 tested_list.txt，表示已经测试过
echo "$version|$apk_url" >> "$TESTED_LIST"

