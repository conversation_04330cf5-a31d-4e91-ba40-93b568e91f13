{"name": "platform_autotest_frame_python", "lockfileVersion": 3, "requires": true, "packages": {"": {"dependencies": {"go-ios": "^1.0.182"}}, "node_modules/go-ios": {"version": "1.0.182", "resolved": "https://mirrors.cloud.tencent.com/npm/go-ios/-/go-ios-1.0.182.tgz", "integrity": "sha512-nH/dcvaNA+kd45aRcD6eSsR6XcSfMwl7RBwksz+yA8Jbz5Tk3k/dBLJC5jIKeXONHHSvm341dOscaNREQLxujQ==", "hasInstallScript": true, "license": "MIT", "dependencies": {"mkdirp": "^1.0.4"}}, "node_modules/mkdirp": {"version": "1.0.4", "resolved": "https://mirrors.cloud.tencent.com/npm/mkdirp/-/mkdirp-1.0.4.tgz", "integrity": "sha512-vVqVZQyf3WLx2Shd0qJ9xuvqgAyKPLAiqITEtqW0oIUjzo3PePDd6fW9iFz30ef7Ysp/oiWqbhszeGWW2T6Gzw==", "license": "MIT", "bin": {"mkdirp": "bin/cmd.js"}, "engines": {"node": ">=10"}}}}