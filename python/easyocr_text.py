import cv2
import easyocr
import numpy as np

# 灵活导入pop_up_detector模块
try:
    from python.pop_up_detector import quick_detect_popup
except ImportError:
    try:
        from pop_up_detector import quick_detect_popup
    except ImportError:
        print("警告：无法导入pop_up_detector模块")
        quick_detect_popup = None

def recognize_text_in_image(image_path):
    """
    使用EasyOCR识别图片中的文本，并区分弹窗内外的文本
    
    Args:
        image_path: 图片路径
    
    Returns:
        dict: 包含识别结果的字典，区分弹窗内外的文本
    """
    try:
        # 初始化EasyOCR读取器，支持中文和英文
        reader = easyocr.Reader(['ch_sim', 'en'])
        
        # 读取图像
        image = cv2.imread(image_path)
        if image is None:
            print(f"错误: 无法读取图像 {image_path}")
            return None
        
        # 使用easyocr识别文本
        results = reader.readtext(image)
        
        # 检测是否有弹窗
        if quick_detect_popup is None:
            print("警告：弹窗检测功能不可用，假设无弹窗")
            has_popup = False
        else:
            has_popup = quick_detect_popup(image_path)
        
        # 如果有弹窗，获取弹窗坐标
        popup_coords = None
        if has_popup:
            # 读取图像用于计算特征
            image = cv2.imread(image_path)
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # 计算亮度信息
            mean_brightness = np.mean(gray)
            std_brightness = np.std(gray)
            threshold = mean_brightness + std_brightness
            bright_mask = gray > threshold
            
            height, width = gray.shape
            top_region_height = int(height * 0.1) # 顶部10%的区域高度
            
            # 分析顶部区域
            top_region_mask = bright_mask[:top_region_height, :]
            top_region_detected = False
            
            for y in range(top_region_height):
                row = top_region_mask[y]
                if np.sum(row) > width * 0.8:
                    top_region_detected = True
                    break
            
            # 获取弹窗坐标
            if top_region_detected:
                # 顶部横幅
                y_coords, x_coords = np.where(top_region_mask)
                if len(x_coords) > 0:
                    min_x, max_x = np.min(x_coords), np.max(x_coords)
                    min_y, max_y = np.min(y_coords), np.max(y_coords)
                    popup_coords = {
                        'left_top': (min_x, min_y),
                        'right_bottom': (max_x, max_y)
                    }
            else:
                # 常规弹窗
                analysis_mask = bright_mask.copy()
                analysis_mask[:top_region_height, :] = False
                y_coords, x_coords = np.where(analysis_mask)
                
                if len(x_coords) > 0 and len(y_coords) > 0:
                    min_x, max_x = np.min(x_coords), np.max(x_coords)
                    min_y, max_y = np.min(y_coords), np.max(y_coords)
                    
                    # 计算区域特征
                    width_region = max_x - min_x
                    height_region = max_y - min_y
                    area = width_region * height_region
                    image_area = height * width
                    area_ratio = area / image_area if image_area > 0 else 0
                    relative_x_center = (min_x + max_x) / (2 * width) if width > 0 else 0
                    relative_y_center = (min_y + max_y) / (2 * height) if height > 0 else 0
                    
                    # 使用常量
                    CENTER_RANGE_MIN = 0.2
                    CENTER_RANGE_MAX = 0.8
                    MIN_AREA_RATIO = 0.05
                    MAX_AREA_RATIO = 0.8
                    MIN_ASPECT_RATIO = 0.3
                    MAX_ASPECT_RATIO = 2.5
                    
                    # 判断条件
                    is_center = (CENTER_RANGE_MIN <= relative_x_center <= CENTER_RANGE_MAX and
                                CENTER_RANGE_MIN <= relative_y_center <= CENTER_RANGE_MAX)
                    is_reasonable_size = (MIN_AREA_RATIO <= area_ratio <= MAX_AREA_RATIO)
                    aspect_ratio = width_region / height_region if height_region != 0 else 0
                    is_reasonable_shape = (MIN_ASPECT_RATIO <= aspect_ratio <= MAX_ASPECT_RATIO)
                    
                    if is_center and is_reasonable_size and is_reasonable_shape:
                        popup_coords = {
                            'left_top': (min_x, min_y),
                            'right_bottom': (max_x, max_y)
                        }
        
        # 分类文本到弹窗内外
        popup_texts = []
        other_texts = []
        
        for result in results:
            bbox, text, prob = result
            
            # 计算文本框中心点
            points = np.array(bbox)
            center_x = np.mean(points[:, 0])
            center_y = np.mean(points[:, 1])
            
            # 检查文本是否在弹窗区域内
            is_in_popup = False
            if popup_coords:
                left_top = popup_coords['left_top']
                right_bottom = popup_coords['right_bottom']
                
                if (left_top[0] <= center_x <= right_bottom[0] and
                    left_top[1] <= center_y <= right_bottom[1]):
                    is_in_popup = True
            
            text_info = {
                'text': text,
                'center': (int(center_x), int(center_y)),
                'confidence': float(prob)
            }
            
            if is_in_popup:
                popup_texts.append(text_info)
            else:
                other_texts.append(text_info)
        
        return {
            'has_popup': has_popup,
            'popup_texts': popup_texts,
            'other_texts': other_texts,
            'all_results': results
        }
    
    except Exception as e:
        print(f"OCR识别过程中发生错误: {e}")
        return None

def format_ocr_results(results, debug=False):
    """
    格式化OCR识别结果，生成按照要求的输出文本
    
    Args:
        results: recognize_text_in_image返回的结果
        debug: 是否输出详细信息，默认为False
    
    Returns:
        str: 格式化后的文本
    """
    if not results:
        return "OCR识别失败，未返回结果。"
    
    has_popup = results['has_popup']
    popup_texts = results['popup_texts']
    other_texts = results['other_texts']
    
    output = []
    
    if debug:
        output.append("OCR 识别已完成，结果已返回。可以进行后续操作。")
    
    output.append("最终文本:")
    
    if has_popup and popup_texts:
        # 只显示弹窗数量1，不要显示实际数量
        output.append(f"当前页面有1个弹窗，其中的元素有：")
        for i, text_info in enumerate(popup_texts):
            text = text_info['text']
            center_x, center_y = text_info['center']
            output.append(f"{i+1}.{text} ({center_x},{center_y})")
    else:
        output.append("当前页面没有检测到弹窗。")
    
    if other_texts:
        if has_popup and popup_texts:
            output.append("其他不在弹窗内的文本有：")
        else:
            output.append("页面文本：")
            
        for i, text_info in enumerate(other_texts):
            text = text_info['text']
            center_x, center_y = text_info['center']
            output.append(f"{i+1}.{text} ({center_x},{center_y})")
    
    return "\n".join(output)

def main(image_path, debug=False):
    """
    主函数，处理图片并输出结果
    
    Args:
        image_path: 图片路径
        debug: 是否输出详细信息，默认为False
    """
    results = recognize_text_in_image(image_path)
    formatted_output = format_ocr_results(results, debug)
    
    if debug:
        print(formatted_output)
        
    return formatted_output

if __name__ == "__main__":
    import sys
    
    # 检查命令行参数
    if len(sys.argv) < 2:
        print("使用方法: python easyocr_text.py <图片路径> [debug]")
        sys.exit(1)
    
    image_path = sys.argv[1]
    debug = len(sys.argv) > 2 and sys.argv[2].lower() == 'debug'
    
    main(image_path, debug)
    
