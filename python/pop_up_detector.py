import cv2
import numpy as np
import matplotlib.pyplot as plt
import matplotlib
from python.call_horus_agent import call_horus_api
from python.upload_image import get_image_url
import easyocr
from matplotlib.font_manager import fontManager
from python.get_message_from_horus import get_ui_page_parse
import time

# ========== 新增统一日志工具函数 ==========
def log_with_device(device_logger, udid, msg, level='info', prefix='[弹窗处理]'):
    # 去掉设备ID标记，只保留[弹窗处理]
    full_msg = f"{prefix} {msg}"
    if device_logger:
        getattr(device_logger, level)(full_msg)
    else:
        print(full_msg)
# ========== 统一日志工具函数结束 ==========

# 设置matplotlib支持中文显示
# 优先使用的中文字体列表（按优先级排序）
cn_font_list = ['Arial Unicode MS', 'STHeiti', 'PingFang SC', 'Heiti SC', 'Microsoft YaHei']

# 基本字体列表（作为备选）
basic_font_list = ['Arial', '.SF NS', 'Helvetica', 'Tahoma']

# 检查系统中可用的字体
available_cn_fonts = []
available_basic_fonts = []

# 先检查中文字体
for font in fontManager.ttflist:
    font_name = font.name.strip()
    if any(f.lower() in font_name.lower() for f in cn_font_list):
        if font_name not in available_cn_fonts:
            available_cn_fonts.append(font_name)
    elif any(f.lower() in font_name.lower() for f in basic_font_list):
        if font_name not in available_basic_fonts:
            available_basic_fonts.append(font_name)

# 首先使用可用的中文字体，如果没有则使用基本字体
if available_cn_fonts:
    font_family = ['sans-serif']
    # 将中文字体放在首位，确保被优先使用
    font_sans = available_cn_fonts + available_basic_fonts
else:
    font_family = ['sans-serif']
    font_sans = available_basic_fonts

# 配置matplotlib字体
matplotlib.rcParams['font.family'] = font_family
matplotlib.rcParams['font.sans-serif'] = font_sans
matplotlib.rcParams['axes.unicode_minus'] = False

# ===== 可调参数区域 =====
BOTTOM_CROP_PERCENTAGE = 0.02  # 从底部裁切的百分比 (0.0 to 1.0)
CENTER_RANGE_MIN = 0.2
CENTER_RANGE_MAX = 0.8
MIN_AREA_RATIO = 0.05
MAX_AREA_RATIO = 0.8
MIN_ASPECT_RATIO = 0.3
MAX_ASPECT_RATIO = 2.5
# ========================

device_ids = ["10AD5F1KJ4002B6", "bc15452a"]
close_button_position = [(930, 940), (900, 900)]

def detect_popup_by_brightness(image_path):
    # 读取图像
    image = cv2.imread(image_path)
    if image is None:
        print(f"错误: 无法读取图像 {image_path}")
        return False, None

    # --- 新增：裁切图像底部 ---
    original_height, original_width = image.shape[:2]
    crop_height = int(original_height * BOTTOM_CROP_PERCENTAGE)
    # 确保裁切高度有效
    if 0 < crop_height < original_height:
        cropped_image = image[0:original_height - crop_height, :]
        print(f"已从底部裁切 {crop_height} 像素 (原始高度: {original_height}, 裁切后高度: {cropped_image.shape[0]})")
    else:
        cropped_image = image # 不进行裁切或裁切比例无效
        print("未进行底部裁切。")
    # --- 结束新增 ---

    # 转换为灰度图 (使用裁切后的图像)
    gray = cv2.cvtColor(cropped_image, cv2.COLOR_BGR2GRAY)

    # 获取裁切后图像的尺寸用于后续计算
    height, width = gray.shape

    # 计算亮度信息
    mean_brightness = np.mean(gray)
    std_brightness = np.std(gray)
    print(f"平均亮度: {mean_brightness:.2f}")
    print(f"亮度标准差: {std_brightness:.2f}")
    
    # 创建一个新的图像布局
    plt.figure(figsize=(15, 10))
    
    # 1. 显示原始灰度图
    plt.subplot(221)
    plt.imshow(gray, cmap='gray')
    plt.colorbar(label='亮度值')
    plt.title('原始灰度图像')
    
    # 2. 显示亮度直方图
    plt.subplot(222)
    plt.hist(gray.ravel(), 256, [0, 256], color='blue', alpha=0.7)
    plt.axvline(x=mean_brightness, color='r', linestyle='--', 
                label=f'平均亮度: {mean_brightness:.1f}')
    plt.axvline(x=mean_brightness + std_brightness, color='g', linestyle='--',
                label=f'平均亮度+标准差: {mean_brightness + std_brightness:.1f}')
    plt.title('亮度分布直方图')
    plt.xlabel('像素亮度值 (0-255)')
    plt.ylabel('像素数量')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 3. 找出高亮区域
    threshold = mean_brightness + std_brightness
    bright_mask = gray > threshold
    
    # 4. 在原图上标记高亮区域
    plt.subplot(223)
    plt.imshow(gray, cmap='gray')
    
    # 初始化弹窗检测结果
    popup_detected = False
    popup_coords = None
    
    # 获取 *裁切后* 图片尺寸 (已在前面获取: height, width)
    # height, width = gray.shape # 不再需要重复获取
    top_region_height = int(height * 0.1)  # 顶部10%的区域高度 (基于裁切后高度)
    
    # 分析顶部区域
    top_region_mask = bright_mask[:top_region_height, :]
    top_region_detected = False
    
    if np.any(top_region_mask):  # 如果顶部区域有高亮点
        # 检查每一行的连续性
        for y in range(top_region_height):
            row = top_region_mask[y]
            if np.sum(row) > width * 0.8:  # 如果该行80%以上是高亮的
                top_region_detected = True
                break
    
    print("\n=== 顶部区域分析 ===")
    print(f"是否检测到顶部横幅: {top_region_detected}")
    
    # 根据顶部分析结果处理图像
    if top_region_detected:
        # 如果检测到顶部横幅，将其作为一个弹窗区域
        y_coords, x_coords = np.where(top_region_mask)
        if len(x_coords) > 0:
            min_x, max_x = np.min(x_coords), np.max(x_coords)
            min_y, max_y = np.min(y_coords), np.max(y_coords)
            popup_detected = True
            popup_coords = {
                'left_top': (min_x, min_y),
                'right_bottom': (max_x, max_y),
                'type': 'banner'  # 标记为横幅类型
            }
            print("检测到顶部横幅弹窗")
    else:
        # 如果没有检测到顶部横幅，忽略顶部区域进行常规检测
        # 创建新的掩码，排除顶部区域
        analysis_mask = bright_mask.copy()
        analysis_mask[:top_region_height, :] = False
        
        # 在剩余区域寻找高亮区域
        y_coords, x_coords = np.where(analysis_mask)
        if len(x_coords) > 0 and len(y_coords) > 0:
            min_x, max_x = np.min(x_coords), np.max(x_coords)
            min_y, max_y = np.min(y_coords), np.max(y_coords)
            
            # 计算区域特征 (使用裁切后的图像尺寸: height, width)
            width_region = max_x - min_x
            height_region = max_y - min_y
            area = width_region * height_region
            image_area = height * width # 使用裁切后的面积
            area_ratio = area / image_area if image_area > 0 else 0
            relative_x_center = (min_x + max_x) / (2 * width) if width > 0 else 0
            relative_y_center = (min_y + max_y) / (2 * height) if height > 0 else 0
            
            # ===== 使用已定义的常量 =====
            # CENTER_RANGE_MIN = 0.2
            # CENTER_RANGE_MAX = 0.8
            # MIN_AREA_RATIO = 0.05
            # MAX_AREA_RATIO = 0.8 # 调整为 0.8 保持一致性
            # MIN_ASPECT_RATIO = 0.3
            # MAX_ASPECT_RATIO = 2.5
            # ==========================
            
            # 判断条件
            is_center = (CENTER_RANGE_MIN <= relative_x_center <= CENTER_RANGE_MAX and
                        CENTER_RANGE_MIN <= relative_y_center <= CENTER_RANGE_MAX)
            is_reasonable_size = (MIN_AREA_RATIO <= area_ratio <= MAX_AREA_RATIO) # 调整为常量 MAX_AREA_RATIO
            aspect_ratio = width_region / height_region if height_region != 0 else 0
            is_reasonable_shape = (MIN_ASPECT_RATIO <= aspect_ratio <= MAX_ASPECT_RATIO)
            
            # 打印调试信息
            print("\n=== 常规弹窗检测调试信息 ===")
            print(f"位置检查:")
            print(f"  中心点相对位置: X={relative_x_center:.2f}, Y={relative_y_center:.2f}")
            print(f"  是否在中央区域: {is_center}")
            
            print(f"\n大小检查:")
            print(f"  区域面积: {area} 像素")
            print(f"  占总面积比例: {area_ratio:.2%}")
            print(f"  是否符合大小要求: {is_reasonable_size}")
            
            print(f"\n形状检查:")
            print(f"  宽高比: {aspect_ratio:.2f}")
            print(f"  是否符合形状要求: {is_reasonable_shape}")
            
            # 综合判断是否为弹窗
            popup_detected = is_center and is_reasonable_size and is_reasonable_shape
            if popup_detected:
                popup_coords = {
                    'left_top': (min_x, min_y),
                    'right_bottom': (max_x, max_y),
                    'type': 'normal'  # 标记为普通弹窗类型
                }
    
    # 在图像上标记检测结果 (在裁切后的灰度图上标记)
    plt.subplot(223)
    plt.imshow(gray, cmap='gray') # 显示裁切后的灰度图
    if popup_detected and popup_coords:
        min_x, min_y = popup_coords['left_top']
        max_x, max_y = popup_coords['right_bottom']
        popup_type = popup_coords['type']
        color = 'r' if popup_type == 'normal' else 'g'  # 普通弹窗用红色，横幅用绿色
        plt.plot([min_x, max_x, max_x, min_x, min_x],
                [min_y, min_y, max_y, max_y, min_y], 
                color=color, linewidth=2)
        
        title_text = "检测到顶部横幅！" if popup_type == 'banner' else "检测到弹窗！"
        plt.title(f'高亮区域标记\n{title_text}')
    else:
        plt.title('高亮区域标记\n未检测到弹窗')
    
    # 5. 显示二值化结果 (基于裁切后的图像)
    plt.subplot(224)
    plt.imshow(bright_mask, cmap='gray')
    plt.title(f'二值化结果\n(阈值={threshold:.1f})')
    
    plt.tight_layout()
    plt.show()
    
    return popup_detected, popup_coords

def quick_detect_popup(image_path):
    """
    快速检测图片中是否存在弹窗，不生成可视化结果
    
    Args:
        image_path: 图片路径
    
    Returns:
        bool: 是否检测到弹窗
    """
    # 读取图像
    image = cv2.imread(image_path)
    if image is None:
        # 可以选择记录日志或返回False
        return False

    # --- 新增：裁切图像底部 ---
    original_height, original_width = image.shape[:2]
    crop_height = int(original_height * BOTTOM_CROP_PERCENTAGE)
    if 0 < crop_height < original_height:
        cropped_image = image[0:original_height - crop_height, :]
    else:
        cropped_image = image
    # --- 结束新增 ---

    # 转换为灰度图 (使用裁切后的图像)
    gray = cv2.cvtColor(cropped_image, cv2.COLOR_BGR2GRAY)
    # 获取裁切后图像的尺寸
    height, width = gray.shape

    # 计算亮度信息
    mean_brightness = np.mean(gray)
    std_brightness = np.std(gray)
    threshold = mean_brightness + std_brightness
    bright_mask = gray > threshold

    # 获取 *裁切后* 图片尺寸 (已在前面获取: height, width)
    # height, width = gray.shape # 不再需要重复获取
    top_region_height = int(height * 0.1) # 基于裁切后高度

    # 检查顶部区域
    top_region_mask = bright_mask[:top_region_height, :]
    
    # 检查是否存在顶部横幅
    for y in range(top_region_height):
        row = top_region_mask[y]
        if np.sum(row) > width * 0.8:  # 如果该行80%以上是高亮的
            return True
    
    # 如果没有检测到顶部横幅，检查常规弹窗
    analysis_mask = bright_mask.copy()
    analysis_mask[:top_region_height, :] = False
    y_coords, x_coords = np.where(analysis_mask)
    
    if len(x_coords) > 0 and len(y_coords) > 0:
        min_x, max_x = np.min(x_coords), np.max(x_coords)
        min_y, max_y = np.min(y_coords), np.max(y_coords)
        
        # 计算区域特征 (使用裁切后的图像尺寸: height, width)
        width_region = max_x - min_x
        height_region = max_y - min_y
        area = width_region * height_region
        image_area = height * width # 使用裁切后的面积
        area_ratio = area / image_area if image_area > 0 else 0
        relative_x_center = (min_x + max_x) / (2 * width) if width > 0 else 0
        relative_y_center = (min_y + max_y) / (2 * height) if height > 0 else 0
        
        # 使用已定义的常量进行判断条件
        is_center = (CENTER_RANGE_MIN <= relative_x_center <= CENTER_RANGE_MAX and
                    CENTER_RANGE_MIN <= relative_y_center <= CENTER_RANGE_MAX)
        is_reasonable_size = (MIN_AREA_RATIO <= area_ratio <= MAX_AREA_RATIO) # 调整为常量 MAX_AREA_RATIO
        aspect_ratio = width_region / height_region if height_region != 0 else 0
        is_reasonable_shape = (MIN_ASPECT_RATIO <= aspect_ratio <= MAX_ASPECT_RATIO)
        
        return is_center and is_reasonable_size and is_reasonable_shape
    
    return False

def detect_new_version_popup(image_path, device_logger=None):
    """
    使用easyocr检测截图中是否包含"新版本抢先体验"信息
    
    Args:
        image_path: 截图路径
        device_logger: 日志对象
    
    Returns:
        tuple: (是否是新版本弹窗, OCR识别文本列表)
    """
    try:
        if device_logger:
            device_logger.info("使用easyocr检测新版本弹窗...")
            
        # 初始化easyocr读取器，使用中文和英文
        reader = easyocr.Reader(['ch_sim', 'en'])
        
        # 读取图像
        image = cv2.imread(image_path)
        
        # 使用easyocr识别文本
        results = reader.readtext(image)
        
        # 提取识别出的文本
        texts = [text for _, text, _ in results]
        
        if device_logger:
            device_logger.info(f"OCR识别结果: {texts}")
        
        # 检查是否包含关键词
        keywords = ["新版本", "抢先体验", "版本升级", "立即更新", "稍后再说"]
        for text in texts:
            for keyword in keywords:
                if keyword in text:
                    if device_logger:
                        device_logger.info(f"检测到新版本弹窗，关键词: {keyword}")
                    return True, texts
        
        return False, texts
    
    except Exception as e:
        if device_logger:
            device_logger.error(f"使用easyocr检测新版本弹窗时出错: {e}")
        return False, []

def handle_popup(screenshot_path, screenshot_url=None, device_logger=None, scale_ratio=1.0, device_id=None):
    """
    处理弹窗，尝试获取关闭按钮并返回点击坐标
    
    Args:
        screenshot_path: 截图路径
        screenshot_url: 已上传的截图URL，如果为None则会上传screenshot_path
        device_logger: 日志对象
        scale_ratio: 缩放比例，用于将识别坐标转换为实际屏幕坐标
        device_id: 设备ID或设备名，用于日志标识
    
    Returns:
        tuple: (是否成功处理, 实际屏幕坐标点[x, y]或None)
    """
    def log_info(msg):
        log_with_device(device_logger, device_id, msg, level='info', prefix='[弹窗处理]')
    def log_error(msg):
        log_with_device(device_logger, device_id, msg, level='error', prefix='[弹窗处理][异常]')
    try:
        log_info(f"开始处理弹窗，截图路径: {screenshot_path}")
        # 如果没有提供截图URL，则上传截图
        if not screenshot_url:
            log_info("未提供截图URL，开始上传截图...")
            screenshot_url = get_image_url(screenshot_path)
            log_info(f"截图上传成功，URL: {screenshot_url}")
        else:
            log_info(f"已提供截图URL: {screenshot_url}")
        # 先用get_ui_page_parse查找icon-关闭
        try:
            log_info("尝试通过get_ui_page_parse查找icon-关闭...")
            ui_elements = get_ui_page_parse(screenshot_url, device_logger=device_logger)
            for elem in ui_elements:
                if elem.get('type') == 'icon' and elem.get('detail') == '关闭' and elem.get('center'):
                    center = elem['center']
                    scaled_x = center[0] / scale_ratio
                    scaled_y = center[1] / scale_ratio
                    log_info(f"通过get_ui_page_parse检测到icon-关闭，坐标: [x={scaled_x:.2f}, y={scaled_y:.2f}]")
                    log_info("[结果] 成功通过get_ui_page_parse获取关闭按钮坐标")
                    time.sleep(5)  # 点击操作后等待5秒
                    return True, [scaled_x, scaled_y]
            log_info("get_ui_page_parse未找到icon-关闭")
        except Exception as e:
            log_error(f"调用get_ui_page_parse检测icon-关闭时出错: {e}")
        # 如果UI页面解析未找到关闭按钮，调用Friday通用模型API获取坐标
        log_info("通过UI页面解析未找到关闭按钮，尝试调用Friday通用模型API...")
        try:
            # 灵活导入call_friday_agent模块
            try:
                from python.call_friday_agent import analyze_image
            except ImportError:
                from call_friday_agent import analyze_image
            coordinates = analyze_image(screenshot_path)
            if coordinates and isinstance(coordinates, list) and len(coordinates) == 2 and coordinates[0] != -1 and coordinates[1] != -1:
                scaled_x = coordinates[0] / scale_ratio
                scaled_y = coordinates[1] / scale_ratio
                log_info(f"通过Friday通用模型API获取到坐标: [x={coordinates[0]}, y={coordinates[1]}]")
                log_info(f"转换后的实际屏幕坐标: [x={scaled_x:.2f}, y={scaled_y:.2f}]")
                log_info("[结果] 成功通过Friday通用模型API获取关闭按钮坐标")
                time.sleep(5)  # 点击操作后等待5秒
                return True, [scaled_x, scaled_y]
            else:
                log_info(f"Friday通用模型API未返回有效坐标: {coordinates}")
        except Exception as e:
            log_error(f"调用Friday通用模型API时出错: {e}")
        # 如果上述方法都失败，使用Horus API作为备选方案
        log_info("未能通过其他方法获取坐标，尝试调用Horus API...")
        try:
            action, center_point = call_horus_api(
                image_url=screenshot_url,
                prompt="关闭弹窗",
                simple_mode=True
            )
            log_info(f"Horus API返回原始数据: action={action}, center_point={center_point}")
            if action:
                log_info(f"Horus返回动作: {action}")
                if center_point:
                    log_info(f"Horus返回坐标: [x={center_point[0]:.2f}, y={center_point[1]:.2f}]")
                else:
                    log_info("Horus未返回有效坐标")
            else:
                log_info("Horus未返回有效动作")
        except Exception as e:
            log_error(f"调用Horus API时出错: {e}")
            action = None
            center_point = None
        # 检查是否成功获取坐标
        if center_point and isinstance(center_point, (list, tuple)) and len(center_point) == 2:
            scaled_x = center_point[0] / scale_ratio
            scaled_y = center_point[1] / scale_ratio
            log_info(f"转换后的实际屏幕坐标: [x={scaled_x:.2f}, y={scaled_y:.2f}]")
            log_info("[结果] 成功通过Horus API获取关闭按钮坐标")
            time.sleep(5)  # 点击操作后等待5秒
            return True, [scaled_x, scaled_y]
        elif action == 'tap' and not center_point:
            log_info("未获取到具体坐标，但识别到tap动作，将点击屏幕中间位置关闭弹窗")
            image = cv2.imread(screenshot_path)
            height, width = image.shape[:2]
            center_x = width / 2 / scale_ratio
            center_y = height / 2 / scale_ratio
            log_info(f"屏幕中间位置坐标: [x={center_x:.2f}, y={center_y:.2f}]")
            log_info("[结果] 通过Horus API tap动作，使用屏幕中心坐标关闭弹窗")
            time.sleep(5)  # 点击操作后等待5秒
            return True, [center_x, center_y]
        log_info("[结果] 未能获取关闭弹窗的有效坐标")
    except Exception as e:
        log_error(f"处理弹窗时出错: {e}")
    return False, None

def debug_popup_detection(image_path, prompt="关闭弹窗", scale_ratio=3.0, device_id=None):
    """
    用于调试弹窗检测和处理的函数，分析给定的截图并打印详细信息
    
    Args:
        image_path: 截图路径
        prompt: 提示词，默认为"关闭弹窗"
        scale_ratio: 缩放比例，默认为3.0
        device_id: 设备ID，用于判断是否使用预定义的关闭按钮位置
    """
    print("=" * 50)
    print("弹窗检测与处理调试功能")
    print("=" * 50)
    
    # 1. 检测是否有弹窗
    print("\n1. 快速检测是否有弹窗")
    has_popup = quick_detect_popup(image_path)
    print(f"检测结果: {'有弹窗' if has_popup else '无弹窗'}")
    
    if has_popup:
        # 2. 上传图片
        print("\n2. 上传图片")
        try:
            screenshot_url = get_image_url(image_path)
            print(f"图片上传成功, URL: {screenshot_url}")
        except Exception as e:
            print(f"图片上传失败: {e}")
            return
        
        # 3. 尝试调用Friday通用模型API
        print("\n3. 尝试调用Friday通用模型API")
        try:
            # 灵活导入call_friday_agent模块
            try:
                from python.call_friday_agent import analyze_image
            except ImportError:
                from call_friday_agent import analyze_image
            friday_coordinates = analyze_image(image_path, debug=True)
            if friday_coordinates and isinstance(friday_coordinates, list) and len(friday_coordinates) == 2:
                print(f"Friday API返回坐标: [x={friday_coordinates[0]}, y={friday_coordinates[1]}]")
            else:
                print(f"Friday API未返回有效坐标: {friday_coordinates}")
        except Exception as e:
            print(f"调用Friday API时出错: {e}")
        
        # 4. 调用Horus API
        print("\n4. 调用Horus API")
        try:
            action, center_point = call_horus_api(
                image_url=screenshot_url,
                prompt=prompt,
                simple_mode=True
            )
            
            print(f"API返回动作: {action}")
            if center_point:
                print(f"API返回坐标: [x={center_point[0]:.2f}, y={center_point[1]:.2f}]")
            else:
                print("API未返回有效坐标")
                
            # 5. 模拟处理弹窗
            print("\n5. 模拟处理弹窗")
            # 直接调用handle_popup，不再单独处理坐标
            success, handle_coords = handle_popup(
                screenshot_path=image_path,
                screenshot_url=screenshot_url,
                scale_ratio=scale_ratio,
                device_id=device_id
            )
            
            if success:
                print(f"弹窗处理结果: 成功, 坐标: [x={handle_coords[0]:.2f}, y={handle_coords[1]:.2f}]")
                # 判断使用了哪种方法获取坐标
                if friday_coordinates and handle_coords[0] == friday_coordinates[0]/scale_ratio and handle_coords[1] == friday_coordinates[1]/scale_ratio:
                    print("使用了Friday API返回的坐标")
                elif center_point and isinstance(center_point, (list, tuple)) and len(center_point) == 2 and handle_coords[0] == center_point[0]/scale_ratio and handle_coords[1] == center_point[1]/scale_ratio:
                    print("使用了Horus API返回的坐标")
                else:
                    print("使用了屏幕中心坐标或其他方法")
            else:
                print("弹窗处理结果: 失败(无法获取有效坐标)")
                
        except Exception as e:
            print(f"调用API或处理弹窗时出错: {e}")
    
    print("\n" + "=" * 50)

def detect_popup_with_connected_components(image_path, debug=False, device_logger=None, udid=None):
    """
    使用连通组件分析检测弹窗，避免多个分离高亮区域被错误合并
    
    Args:
        image_path: 图片路径
        debug: 是否显示调试信息和可视化结果
        device_logger: 日志对象
        device_id: 设备ID，用于日志标识
    
    Returns:
        tuple: (是否检测到弹窗, 弹窗坐标信息dict或None)
    """
    
    # ===== 连通组件分析参数配置 =====
    # 亮度阈值相关参数
    BRIGHTNESS_THRESHOLD_MULTIPLIER = 0.7  # 亮度阈值倍数：threshold = mean + multiplier * std
                                           # 调节建议：0.5-2.0，值越大越严格（检测更亮的区域）
                                           # 当前设置：0.8（降低阈值，检测更多区域）
    
    # 连通区域过滤参数
    MIN_COMPONENT_AREA = 500              # 最小连通区域面积（像素数）
                                         # 调节建议：100-2000，用于过滤噪声点和小区域
    
    MIN_COMPONENT_AREA_RATIO = 0.001     # 最小连通区域面积占比（相对于整个图像）
                                         # 调节建议：0.0005-0.01，过滤太小的区域
    
    # 弹窗候选区域判断参数
    POPUP_CENTER_X_RANGE_MIN = 0.3      # 弹窗中心点X方向相对位置最小值
    POPUP_CENTER_X_RANGE_MAX = 0.7      # 弹窗中心点X方向相对位置最大值
    POPUP_CENTER_Y_RANGE_MIN = 0.2      # 弹窗中心点Y方向相对位置最小值  
    POPUP_CENTER_Y_RANGE_MAX = 0.8      # 弹窗中心点Y方向相对位置最大值
                                         # 调节建议：0.1-0.9，控制弹窗必须在屏幕中央区域
    
    POPUP_MIN_AREA_RATIO = 0.05          # 弹窗最小面积占比
    POPUP_MAX_AREA_RATIO = 0.6           # 弹窗最大面积占比
                                         # 调节建议：min 0.01-0.05, max 0.4-0.8
                                         # 太小可能是按钮，太大可能是背景
    
    POPUP_MIN_ASPECT_RATIO = 0.2         # 弹窗最小宽高比（宽/高）
    POPUP_MAX_ASPECT_RATIO = 5.0         # 弹窗最大宽高比（宽/高）
                                         # 调节建议：min 0.1-0.5, max 3.0-10.0
                                         # 控制弹窗的形状，避免过窄或过宽的区域
    
    POPUP_MIN_WIDTH = 50                 # 弹窗最小宽度（像素）
    POPUP_MIN_HEIGHT = 50                # 弹窗最小高度（像素）
                                         # 调节建议：30-100，确保弹窗有足够大小
    
    # 顶部横幅检测参数
    TOP_REGION_RATIO = 0.05              # 顶部区域占比（用于检测横幅类弹窗）
    TOP_BANNER_WIDTH_RATIO = 0.8         # 顶部横幅最小宽度占比
                                         # 调节建议：0.5-0.9，横幅应该占据大部分屏幕宽度
    
    # 评分权重参数（用于选择最佳弹窗候选）
    SCORE_AREA_WEIGHT = 0.3              # 面积得分权重
    SCORE_CENTER_WEIGHT = 0.4            # 中心位置得分权重  
    SCORE_SHAPE_WEIGHT = 0.3             # 形状得分权重
                                         # 调节建议：三个权重和应为1.0，根据重要性调整
    
    # 误报排除参数
    MIN_EDGE_DISTANCE_RATIO = 0.01       # 弹窗边缘距离屏幕边界的最小比例
                                         # 调节建议：0.02-0.1，越大越严格
    VERTICAL_RATIO_THRESHOLD = 0.25      # 纵向占比阈值（配合边缘距离检查使用）
                                         # 调节建议：0.2-0.5，用于区分横幅和全屏弹窗
    MIN_INTERNAL_VARIATION = 5           # 弹窗内部亮度变化的最小标准差
                                         # 调节建议：3-10，用于排除纯色背景
    MAX_EDGE_TOUCH_RATIO = 0.8           # 弹窗边缘接触屏幕边界的最大比例
                                         # 调节建议：0.6-0.9，超过此比例认为是背景
    
    # 亮度筛选参数
    MIN_BRIGHTNESS_MULTIPLIER = 1.05     # 弹窗区域亮度必须是检测阈值的最小倍数
                                         # 调节建议：1.1-2.0，值越大越严格
                                         # 用于排除亮度不够突出的区域
    # ================================
    
    def log_info(msg):
        log_with_device(device_logger, udid, msg, level='info', prefix='[连通组件弹窗检测]')
    
    def log_debug(msg):
        if debug:
            log_with_device(device_logger, udid, msg, level='info', prefix='[连通组件弹窗检测][调试]')
    
    def check_false_positive(gray, bbox, labels, component_id):
        """
        检查连通组件是否为误报
        
        Args:
            gray: 灰度图像
            bbox: 边界框 (x, y, w, h)
            labels: 连通组件标签图像
            component_id: 连通组件ID
            
        Returns:
            tuple: (是否为误报, 排除原因列表)
        """
        x, y, w, h = bbox
        height, width = gray.shape
        reasons = []
        
        # 1. 边缘距离检查（恢复，但增加纵向占比条件）
        min_edge_distance = min(width, height) * MIN_EDGE_DISTANCE_RATIO
        left_distance = x
        right_distance = width - (x + w)
        top_distance = y
        bottom_distance = height - (y + h)
        
        # 计算纵向占比
        vertical_ratio = h / height if height > 0 else 0
        
        # 只有当左右边缘都太近屏幕边界，且纵向占比超过阈值时才排除
        # 这样可以避免误排除横幅类弹窗（横幅通常纵向占比较小）
        if (left_distance < min_edge_distance and right_distance < min_edge_distance and vertical_ratio > VERTICAL_RATIO_THRESHOLD):
            reasons.append(f"左右边缘都太近屏幕边界且纵向占比过高 (左:{left_distance:.0f}, 右:{right_distance:.0f}, 最小要求:{min_edge_distance:.0f}, 纵向占比:{vertical_ratio:.1%}, 阈值:{VERTICAL_RATIO_THRESHOLD:.1%})")
        
        # 2. 边缘接触比例检查
        edge_touch_pixels = 0
        total_perimeter = 2 * (w + h)
        
        # 检查四条边是否接触屏幕边界
        if x == 0:  # 左边缘
            edge_touch_pixels += h
        if y == 0:  # 上边缘
            edge_touch_pixels += w
        if x + w == width:  # 右边缘
            edge_touch_pixels += h
        if y + h == height:  # 下边缘
            edge_touch_pixels += w
            
        edge_touch_ratio = edge_touch_pixels / total_perimeter if total_perimeter > 0 else 0
        if edge_touch_ratio > MAX_EDGE_TOUCH_RATIO:
            reasons.append(f"边缘接触屏幕比例过高 ({edge_touch_ratio:.2f} > {MAX_EDGE_TOUCH_RATIO})")
        
        # 3. 内部亮度变化检查（排除纯色背景）
        component_mask = (labels == component_id)
        component_pixels = gray[component_mask]
        if len(component_pixels) > 0:
            internal_std = np.std(component_pixels)
            if internal_std < MIN_INTERNAL_VARIATION:
                reasons.append(f"内部亮度变化太小 (标准差:{internal_std:.1f} < {MIN_INTERNAL_VARIATION})")
        
        # 4. 宽高比异常检查（极端细长的区域）
        aspect_ratio = w / h if h > 0 else 0
        if aspect_ratio > 10 or aspect_ratio < 0.1:
            reasons.append(f"宽高比异常 ({aspect_ratio:.2f})")
        
        # 5. 区域密度检查（连通区域在边界框中的占比）
        bbox_area = w * h
        component_area = np.sum(component_mask)
        density = component_area / bbox_area if bbox_area > 0 else 0
        if density < 0.3:  # 连通区域在边界框中占比过低
            reasons.append(f"区域密度过低 ({density:.2f} < 0.3)")
        
        # 6. 位置分布检查（是否过于偏向某个角落）
        center_x, center_y = x + w/2, y + h/2
        rel_center_x, rel_center_y = center_x / width, center_y / height
        
        # 检查是否在四个角落的极端位置
        corner_threshold = 0.15
        in_corner = ((rel_center_x < corner_threshold or rel_center_x > 1-corner_threshold) and 
                     (rel_center_y < corner_threshold or rel_center_y > 1-corner_threshold))
        if in_corner:
            reasons.append(f"位置过于偏向角落 (相对中心: {rel_center_x:.2f}, {rel_center_y:.2f})")
        
        is_false_positive = len(reasons) > 0
        return is_false_positive, reasons
    
    try:
        # 读取图像
        image = cv2.imread(image_path)
        if image is None:
            log_info(f"错误: 无法读取图像 {image_path}")
            return False, None
        
        log_info(f"开始使用连通组件分析检测弹窗: {image_path}")
        
        # 裁切图像底部
        original_height, original_width = image.shape[:2]
        crop_height = int(original_height * BOTTOM_CROP_PERCENTAGE)
        if 0 < crop_height < original_height:
            cropped_image = image[0:original_height - crop_height, :]
            log_debug(f"已从底部裁切 {crop_height} 像素")
        else:
            cropped_image = image
            log_debug("未进行底部裁切")
        
        # 转换为灰度图
        gray = cv2.cvtColor(cropped_image, cv2.COLOR_BGR2GRAY)
        height, width = gray.shape
        total_pixels = height * width
        
        # 计算亮度阈值
        mean_brightness = np.mean(gray)
        std_brightness = np.std(gray)
        threshold = mean_brightness + BRIGHTNESS_THRESHOLD_MULTIPLIER * std_brightness
        
        log_debug(f"图像尺寸: {width}x{height}")
        log_debug(f"平均亮度: {mean_brightness:.2f}, 标准差: {std_brightness:.2f}")
        log_debug(f"亮度阈值: {threshold:.2f}")
        
        # 创建二值化掩码
        bright_mask = (gray > threshold).astype(np.uint8)

        # ===== 新增：形态学操作来连接断开的区域 =====
        # 形态学操作参数配置
        MORPH_KERNEL_SIZE = 5         # 形态学核大小，调节建议：3-7（奇数）
        MORPH_CLOSE_ITERATIONS = 1    # 闭运算迭代次数，调节建议：1-3  
        MORPH_OPEN_ITERATIONS = 1     # 开运算迭代次数，调节建议：1-2

        # 创建形态学操作核
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (MORPH_KERNEL_SIZE, MORPH_KERNEL_SIZE))

        # 先进行闭运算（膨胀+腐蚀）连接断开的区域
        bright_mask_closed = cv2.morphologyEx(bright_mask, cv2.MORPH_CLOSE, kernel, iterations=MORPH_CLOSE_ITERATIONS)

        # 再进行开运算（腐蚀+膨胀）去除噪声
        bright_mask_processed = cv2.morphologyEx(bright_mask_closed, cv2.MORPH_OPEN, kernel, iterations=MORPH_OPEN_ITERATIONS)

        log_debug(f"形态学操作参数: 核大小={MORPH_KERNEL_SIZE}, 闭运算{MORPH_CLOSE_ITERATIONS}次, 开运算{MORPH_OPEN_ITERATIONS}次")
        # ===== 形态学操作结束 =====

        # 进行连通组件分析（使用处理后的掩码）
        num_labels, labels, stats, centroids = cv2.connectedComponentsWithStats(
            bright_mask_processed, connectivity=8, ltype=cv2.CV_32S
        )
        
        log_debug(f"检测到 {num_labels-1} 个连通组件（不包括背景）")
        log_debug(f"筛选参数: 最小亮度倍数={MIN_BRIGHTNESS_MULTIPLIER}x, 内部变化阈值={MIN_INTERNAL_VARIATION}, 边缘接触比例上限={MAX_EDGE_TOUCH_RATIO}")
        log_debug(f"边缘距离检查: 最小距离比例={MIN_EDGE_DISTANCE_RATIO}, 纵向占比阈值={VERTICAL_RATIO_THRESHOLD:.1%} (只有同时满足左右边缘太近且纵向占比过高才排除)")
        
        # 分析每个连通组件
        popup_candidates = []
        false_positives = []  # 记录被排除的误报
        filtered_small_count = 0  # 记录因面积太小被过滤的数量
        area_passed_components = []  # 记录通过面积筛选的所有组件（用于可视化）
        
        for i in range(1, num_labels):  # 跳过标签0（背景）
            # 获取连通组件的统计信息
            x, y, w, h, area = stats[i]
            center_x, center_y = centroids[i]
            
            # 过滤太小的连通组件（直接跳过，不输出debug信息）
            area_ratio = area / total_pixels
            if area < MIN_COMPONENT_AREA or area_ratio < MIN_COMPONENT_AREA_RATIO:
                filtered_small_count += 1
                continue
            
            # 计算相对中心位置
            relative_center_x = center_x / width
            relative_center_y = center_y / height
            
            # 计算宽高比
            aspect_ratio = w / h if h > 0 else 0
            
            # 计算连通组件的亮度信息
            component_mask = (labels == i)
            component_pixels = gray[component_mask]
            if len(component_pixels) > 0:
                component_brightness = np.mean(component_pixels)
                brightness_multiplier = component_brightness / threshold if threshold > 0 else 0
                min_brightness = np.min(component_pixels)
                max_brightness = np.max(component_pixels)
            else:
                component_brightness = 0
                brightness_multiplier = 0
                min_brightness = 0
                max_brightness = 0
            
            log_debug(f"连通组件 {i}: 位置({x},{y}), 大小({w}x{h}), 面积({area}), 中心({center_x:.1f},{center_y:.1f})")
            log_debug(f"  相对中心: ({relative_center_x:.3f},{relative_center_y:.3f}), 宽高比: {aspect_ratio:.2f}")
            log_debug(f"  亮度信息: 平均={component_brightness:.1f}, 范围=[{min_brightness:.0f}-{max_brightness:.0f}], 阈值={threshold:.1f}, 倍数={brightness_multiplier:.2f}x")
            
            # 记录通过面积筛选的组件（用于可视化）
            area_passed_components.append({
                'component_id': i,
                'bbox': (x, y, w, h),
                'area': area,
                'area_ratio': area_ratio,
                'center': (center_x, center_y),
                'relative_center': (relative_center_x, relative_center_y),
                'aspect_ratio': aspect_ratio,
                'brightness': component_brightness,
                'brightness_multiplier': brightness_multiplier
            })
            
            # 检查是否在顶部区域（横幅检测）
            top_region_height = int(height * TOP_REGION_RATIO)
            is_top_banner = (y < top_region_height and w >= width * TOP_BANNER_WIDTH_RATIO)
            
            if is_top_banner:
                log_with_device(device_logger, udid, 
                    f"检测到顶部横幅 位置({x},{y}), 大小({w}x{h}), 宽度占比({w/width:.1%}), 跳过弹窗识别", 
                    level='warning', prefix='[顶部横幅检测]')
                continue  # 跳过，不作为弹窗候选
            
            # 常规弹窗判断条件
            is_center_positioned = (POPUP_CENTER_X_RANGE_MIN <= relative_center_x <= POPUP_CENTER_X_RANGE_MAX and
                                  POPUP_CENTER_Y_RANGE_MIN <= relative_center_y <= POPUP_CENTER_Y_RANGE_MAX)
            
            is_reasonable_size = (POPUP_MIN_AREA_RATIO <= area_ratio <= POPUP_MAX_AREA_RATIO and
                                w >= POPUP_MIN_WIDTH and h >= POPUP_MIN_HEIGHT)
            
            is_reasonable_shape = (POPUP_MIN_ASPECT_RATIO <= aspect_ratio <= POPUP_MAX_ASPECT_RATIO)
            
            # 新增：亮度倍数检查
            is_bright_enough = (brightness_multiplier >= MIN_BRIGHTNESS_MULTIPLIER)
            
            log_debug(f"  中心位置合适: {is_center_positioned}")
            log_debug(f"  大小合适: {is_reasonable_size}")
            log_debug(f"  形状合适: {is_reasonable_shape}")
            log_debug(f"  亮度足够: {is_bright_enough} (需要>={MIN_BRIGHTNESS_MULTIPLIER:.1f}x, 实际{brightness_multiplier:.2f}x)")
            
            if is_center_positioned and is_reasonable_size and is_reasonable_shape and is_bright_enough:
                # 误报检查
                is_false_positive, false_reasons = check_false_positive(gray, (x, y, w, h), labels, i)
                
                if is_false_positive:
                    log_debug(f"  连通组件 {i}: 被识别为误报，原因: {'; '.join(false_reasons)}")
                    false_positives.append({
                        'component_id': i,
                        'bbox': (x, y, w, h),
                        'reasons': false_reasons,
                        'area': area,
                        'aspect_ratio': aspect_ratio
                    })
                    continue  # 跳过这个候选
                
                # 计算候选弹窗的综合得分
                # 面积得分：中等面积得分最高
                area_score = 1.0 - abs(area_ratio - 0.2) / 0.2  # 面积比例接近0.2时得分最高
                area_score = max(0, min(1, area_score))
                
                # 中心位置得分：越靠近屏幕中心得分越高
                center_distance = ((relative_center_x - 0.5) ** 2 + (relative_center_y - 0.5) ** 2) ** 0.5
                center_score = 1.0 - min(1.0, center_distance / 0.5)
                
                # 形状得分：宽高比接近1.0-1.5时得分最高
                shape_score = 1.0 - abs(aspect_ratio - 1.2) / 1.2
                shape_score = max(0, min(1, shape_score))
                
                # 综合得分
                total_score = (SCORE_AREA_WEIGHT * area_score + 
                             SCORE_CENTER_WEIGHT * center_score + 
                             SCORE_SHAPE_WEIGHT * shape_score)
                
                log_debug(f"  弹窗候选得分: 面积{area_score:.2f}, 中心{center_score:.2f}, 形状{shape_score:.2f}, 总分{total_score:.2f}")
                log_debug(f"  通过误报检查，添加为候选")
                
                popup_candidates.append({
                    'component_id': i,
                    'bbox': (x, y, w, h),
                    'area': area,
                    'area_ratio': area_ratio,
                    'center': (center_x, center_y),
                    'relative_center': (relative_center_x, relative_center_y),
                    'aspect_ratio': aspect_ratio,
                    'type': 'normal',
                    'score': total_score,
                    'false_positive_check': 'passed'
                })
        
        # 输出过滤统计信息
        log_debug(f"面积过滤统计: {filtered_small_count} 个连通组件因面积太小被过滤")
        
        # 选择最佳弹窗候选
        if popup_candidates:
            # 按得分排序，选择得分最高的
            best_popup = max(popup_candidates, key=lambda x: x['score'])
            
            log_info(f"检测到弹窗！类型: {best_popup['type']}, 得分: {best_popup['score']:.2f}")
            log_info(f"位置: {best_popup['bbox']}, 面积比例: {best_popup['area_ratio']:.2%}")
            log_info(f"误报排除统计: 排除了 {len(false_positives)} 个误报候选")
            
            # 在debug模式下输出详细的筛选阈值信息
            if debug:
                x, y, w, h = best_popup['bbox']
                center_x, center_y = best_popup['center']
                rel_center_x, rel_center_y = best_popup['relative_center']
                aspect_ratio = best_popup['aspect_ratio']
                
                # 计算最终选中弹窗的亮度信息
                final_component_mask = (labels == best_popup['component_id'])
                final_component_pixels = gray[final_component_mask]
                if len(final_component_pixels) > 0:
                    final_brightness = np.mean(final_component_pixels)
                    final_brightness_multiplier = final_brightness / threshold if threshold > 0 else 0
                    final_min_brightness = np.min(final_component_pixels)
                    final_max_brightness = np.max(final_component_pixels)
                else:
                    final_brightness = 0
                    final_brightness_multiplier = 0
                    final_min_brightness = 0
                    final_max_brightness = 0
                
                log_debug("========== 最终选中弹窗详细信息 ==========")
                log_debug(f"尺寸信息: 宽度={w}px, 高度={h}px, 面积={best_popup['area']}px²")
                log_debug(f"绝对坐标: 左上角({x},{y}), 中心点({center_x:.1f},{center_y:.1f})")
                log_debug(f"相对坐标: 中心点在屏幕({rel_center_x:.3f},{rel_center_y:.3f}) [X范围要求:({POPUP_CENTER_X_RANGE_MIN}-{POPUP_CENTER_X_RANGE_MAX}), Y范围要求:({POPUP_CENTER_Y_RANGE_MIN}-{POPUP_CENTER_Y_RANGE_MAX})]")
                log_debug(f"形状信息: 宽高比={aspect_ratio:.3f} [范围要求:({POPUP_MIN_ASPECT_RATIO}-{POPUP_MAX_ASPECT_RATIO})]")
                log_debug(f"面积信息: 占屏比例={best_popup['area_ratio']:.3f} [范围要求:({POPUP_MIN_AREA_RATIO}-{POPUP_MAX_AREA_RATIO})]")
                log_debug(f"亮度信息: 平均亮度={final_brightness:.1f}, 亮度范围=[{final_min_brightness:.0f}-{final_max_brightness:.0f}]")
                log_debug(f"亮度详细: 图像平均亮度={mean_brightness:.1f}, 标准差={std_brightness:.1f}, 检测阈值={threshold:.1f}")
                log_debug(f"亮度倍数: 弹窗亮度是阈值的{final_brightness_multiplier:.2f}倍，是图像平均亮度的{final_brightness/mean_brightness:.2f}倍")
                log_debug(f"筛选状态: 中心位置={'✓通过' if POPUP_CENTER_X_RANGE_MIN <= rel_center_x <= POPUP_CENTER_X_RANGE_MAX and POPUP_CENTER_Y_RANGE_MIN <= rel_center_y <= POPUP_CENTER_Y_RANGE_MAX else '✗不符'}")
                log_debug(f"筛选状态: 面积大小={'✓通过' if POPUP_MIN_AREA_RATIO <= best_popup['area_ratio'] <= POPUP_MAX_AREA_RATIO else '✗不符'}")
                log_debug(f"筛选状态: 形状比例={'✓通过' if POPUP_MIN_ASPECT_RATIO <= aspect_ratio <= POPUP_MAX_ASPECT_RATIO else '✗不符'}")
                log_debug(f"筛选状态: 最小尺寸={'✓通过' if w >= POPUP_MIN_WIDTH and h >= POPUP_MIN_HEIGHT else '✗不符'}")
                log_debug(f"筛选状态: 亮度倍数={'✓通过' if final_brightness_multiplier >= MIN_BRIGHTNESS_MULTIPLIER else '✗不符'} (需要>={MIN_BRIGHTNESS_MULTIPLIER:.1f}x)")
                log_debug(f"边缘距离检查: 左距离={x:.0f}px, 右距离={width-(x+w):.0f}px, 最小要求={width*MIN_EDGE_DISTANCE_RATIO:.0f}px, 纵向占比={h/height:.1%}")
                log_debug(f"注意: 只有左右边缘都太近且纵向占比>{VERTICAL_RATIO_THRESHOLD:.0%}时才排除（避免误排除横幅）")
                log_debug("========================================")
            
            x, y, w, h = best_popup['bbox']
            popup_coords = {
                'left_top': (x, y),
                'right_bottom': (x + w, y + h),
                'center': best_popup['center'],
                'type': best_popup['type'],
                'score': best_popup['score'],
                'area_ratio': best_popup['area_ratio']
            }
            
            # 如果开启调试模式，显示可视化结果
            if debug:
                plt.figure(figsize=(20, 12))
                
                # 显示原始灰度图
                plt.subplot(231)
                plt.imshow(gray, cmap='gray')
                plt.title('原始灰度图像')
                
                # 显示二值化结果
                plt.subplot(232)
                plt.imshow(bright_mask, cmap='gray')
                plt.title(f'二值化结果 (阈值={threshold:.1f})')
                
                # 显示形态学处理结果
                plt.subplot(233)
                plt.imshow(bright_mask_processed, cmap='gray')
                plt.title('形态学处理后')
                
                # 显示连通组件标记
                plt.subplot(234)
                plt.imshow(labels, cmap='tab20')
                plt.title(f'连通组件分析 ({num_labels-1}个组件)')
                
                # 显示对比：处理前后的差异
                plt.subplot(235)
                diff_mask = bright_mask_processed.astype(np.int16) - bright_mask.astype(np.int16)
                plt.imshow(diff_mask, cmap='RdBu')
                plt.title('形态学操作差异\n(蓝=填补 红=去除)')
                plt.colorbar()
                
                # 显示检测结果
                plt.subplot(236)
                plt.imshow(gray, cmap='gray')
                
                # 首先标记所有通过面积筛选的组件（浅蓝色）
                for comp in area_passed_components:
                    x, y, w, h = comp['bbox']
                    comp_id = comp['component_id']
                    
                    # 检查这个组件是否是最终弹窗候选或被排除的误报
                    is_final_popup = any(candidate['component_id'] == comp_id for candidate in popup_candidates if candidate == best_popup)
                    is_popup_candidate = any(candidate['component_id'] == comp_id for candidate in popup_candidates)
                    is_false_positive = any(fp['component_id'] == comp_id for fp in false_positives)
                    
                    # 根据状态选择颜色和线型
                    if is_final_popup:
                        continue  # 最终弹窗单独处理
                    elif is_popup_candidate:
                        color = 'yellow'
                        linewidth = 2
                        linestyle = '-'
                        label_text = f'候选#{comp_id}'
                    elif is_false_positive:
                        color = 'orange'
                        linewidth = 1
                        linestyle = '--'
                        label_text = f'误报#{comp_id}'
                    else:
                        # 通过面积筛选但不满足其他条件的组件
                        color = 'cyan'
                        linewidth = 1
                        linestyle = ':'
                        label_text = f'#{comp_id}'
                    
                    # 绘制边框
                    plt.plot([x, x+w, x+w, x, x], [y, y, y+h, y+h, y], 
                            color=color, linewidth=linewidth, linestyle=linestyle)
                    
                    # 标记组件编号和亮度倍数
                    plt.text(x, y-5, label_text, color=color, fontweight='bold', fontsize=8)
                    plt.text(x, y+h+15, f'{comp["brightness_multiplier"]:.1f}x', 
                            color=color, fontsize=7)
                
                # 最后标记最终选中的弹窗（红色，最显眼）
                x, y, w, h = best_popup['bbox']
                plt.plot([x, x+w, x+w, x, x], [y, y, y+h, y+h, y], 
                        color='red', linewidth=3)
                plt.text(x, y-5, f'最终#{best_popup["component_id"]}', 
                        color='red', fontweight='bold', fontsize=10)
                plt.text(x, y-20, f'得分:{best_popup["score"]:.2f}', 
                        color='red', fontweight='bold', fontsize=8)
                
                # 添加图例说明
                legend_text = f'筛选统计: 面积通过{len(area_passed_components)}个, 弹窗候选{len(popup_candidates)}个, 误报排除{len(false_positives)}个'
                plt.title(f'连通组件弹窗检测结果\n{legend_text}')
                
                # 在图片角落添加颜色说明
                plt.text(0.02, 0.98, '[红]最终弹窗 [黄]弹窗候选 [橙]误报排除 [青]面积通过', 
                        transform=plt.gca().transAxes, fontsize=9, 
                        verticalalignment='top', bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
                
                plt.tight_layout()
                plt.show()
            
            return True, popup_coords
        else:
            log_info("未检测到弹窗候选区域")
            if debug:
                plt.figure(figsize=(15, 8))
                plt.subplot(131)
                plt.imshow(gray, cmap='gray')
                plt.title('原始灰度图像')
                
                plt.subplot(132)
                plt.imshow(labels, cmap='tab20')
                plt.title(f'连通组件分析 ({num_labels-1}个组件)')
                
                # 显示通过面积筛选的组件
                plt.subplot(133)
                plt.imshow(gray, cmap='gray')
                
                # 标记所有通过面积筛选的组件
                for comp in area_passed_components:
                    x, y, w, h = comp['bbox']
                    comp_id = comp['component_id']
                    
                    # 检查这个组件是否被排除为误报
                    is_false_positive = any(fp['component_id'] == comp_id for fp in false_positives)
                    
                    if is_false_positive:
                        color = 'orange'
                        linestyle = '--'
                        label_text = f'误报#{comp_id}'
                    else:
                        color = 'cyan'
                        linestyle = ':'
                        label_text = f'#{comp_id}'
                    
                    plt.plot([x, x+w, x+w, x, x], [y, y, y+h, y+h, y], 
                            color=color, linewidth=1, linestyle=linestyle)
                    plt.text(x, y-5, label_text, color=color, fontweight='bold', fontsize=8)
                    plt.text(x, y+h+15, f'{comp["brightness_multiplier"]:.1f}x', 
                            color=color, fontsize=7)
                
                plt.title(f'面积筛选结果 ({len(area_passed_components)}个通过, {len(false_positives)}个误报)')
                
                # 添加颜色说明
                plt.text(0.02, 0.98, '[橙]误报排除 [青]面积通过', 
                        transform=plt.gca().transAxes, fontsize=9, 
                        verticalalignment='top', bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
                
                plt.tight_layout()
                plt.show()
            
            return False, None
            
    except Exception as e:
        log_info(f"连通组件弹窗检测出错: {e}")
        return False, None

def quick_detect_popup_with_connected_components(image_path):
    """
    快速版本的连通组件弹窗检测，不显示调试信息
    
    Args:
        image_path: 图片路径
    
    Returns:
        bool: 是否检测到弹窗
    """
    result, _ = detect_popup_with_connected_components(image_path, debug=False)
    return result

def detect_popup_with_contour(image_path, debug=False, device_logger=None, device_id=None):
    """
    使用边缘轮廓分析检测弹窗，适合暗色或有明显边框的弹窗
    Args:
        image_path: 图片路径
        debug: 是否显示调试信息和可视化结果
        device_logger: 日志对象
        device_id: 设备ID，用于日志标识
    Returns:
        tuple: (是否检测到弹窗, 弹窗坐标信息dict或None)
    """
    def log_info(msg):
        log_with_device(device_logger, device_id, msg, level='info', prefix='[轮廓弹窗检测]')
    def log_debug(msg):
        if debug:
            log_with_device(device_logger, device_id, msg, level='info', prefix='[轮廓弹窗检测][调试]')
    try:
        image = cv2.imread(image_path)
        if image is None:
            log_info(f"错误: 无法读取图像 {image_path}")
            return False, None
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        blurred = cv2.GaussianBlur(gray, (5, 5), 0)
        edges = cv2.Canny(blurred, 50, 150)
        contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        height, width = gray.shape
        total_area = height * width
        min_area = total_area * 0.10  # 弹窗最小面积10%
        max_area = total_area * 0.6   # 弹窗最大面积
        min_aspect = 0.3
        max_aspect = 3.5
        center_range_min = 0.15
        center_range_max = 0.85
        candidates = []
        # 新增：收集所有轮廓参数和排除原因
        contour_infos = []
        for cnt in contours:
            x, y, w, h = cv2.boundingRect(cnt)
            area = w * h
            aspect = w / h if h > 0 else 0
            cx = x + w / 2
            cy = y + h / 2
            rel_cx = cx / width
            rel_cy = cy / height
            reason = None
            if area < min_area or area > max_area:
                reason = f"面积不符 area={area:.0f}"
            elif not (min_aspect <= aspect <= max_aspect):
                reason = f"宽高比不符 aspect={aspect:.2f}"
            elif not (center_range_min <= rel_cx <= center_range_max and center_range_min <= rel_cy <= center_range_max):
                reason = f"中心不在中央 rel_cx={rel_cx:.2f}, rel_cy={rel_cy:.2f}"
            else:
                reason = "通过所有筛选"
                candidates.append({
                    'bbox': (x, y, w, h),
                    'area': area,
                    'aspect': aspect,
                    'center': (cx, cy),
                    'rel_center': (rel_cx, rel_cy),
                    'contour': cnt
                })
            contour_infos.append({
                'bbox': (x, y, w, h),
                'area': area,
                'aspect': aspect,
                'center': (cx, cy),
                'rel_center': (rel_cx, rel_cy),
                'reason': reason
            })
        # 新增：debug模式下输出面积最大10个轮廓的详细参数和排除原因
        if debug and len(contour_infos) > 0:
            print("\n[轮廓分析] 面积最大10个轮廓参数及排除原因：")
            top10 = sorted(contour_infos, key=lambda c: -c['area'])[:10]
            for idx, info in enumerate(top10, 1):
                print(f"  {idx}. bbox={info['bbox']}, area={info['area']:.0f}, aspect={info['aspect']:.2f}, center=({info['center'][0]:.1f},{info['center'][1]:.1f}), rel_center=({info['rel_center'][0]:.2f},{info['rel_center'][1]:.2f}), 排除原因: {info['reason']}")
        # 包含关系法：优先选能包含最多其他轮廓的候选
        best = None
        if candidates:
            contain_counts = []
            for i, c1 in enumerate(candidates):
                x1, y1, w1, h1 = c1['bbox']
                count = 0
                for j, c2 in enumerate(candidates):
                    if i == j:
                        continue
                    x2, y2, w2, h2 = c2['bbox']
                    if x2 >= x1 and y2 >= y1 and x2 + w2 <= x1 + w1 and y2 + h2 <= y1 + h1:
                        count += 1
                contain_counts.append(count)
            max_count = max(contain_counts)
            idxs = [i for i, c in enumerate(contain_counts) if c == max_count]
            # 多个都包含最多，优先面积最大
            if idxs:
                best = max([candidates[i] for i in idxs], key=lambda c: c['area'])
            else:
                best = max(candidates, key=lambda c: c['area'])
            log_info(f"检测到弹窗轮廓，位置: {best['bbox']}, 面积: {best['area']}, 宽高比: {best['aspect']:.2f}, 包含{max_count}个其他轮廓")
        else:
            log_info("未检测到符合条件的弹窗轮廓")
        if debug:
            plt.figure(figsize=(15, 10))
            plt.subplot(221)
            plt.imshow(gray, cmap='gray')
            plt.title('灰度图')
            plt.subplot(222)
            plt.imshow(edges, cmap='gray')
            plt.title('Canny边缘')
            plt.subplot(223)
            plt.imshow(gray, cmap='gray')
            for c in contours:
                x, y, w, h = cv2.boundingRect(c)
                plt.plot([x, x+w, x+w, x, x], [y, y, y+h, y+h, y], color='yellow', linewidth=1)
            plt.title('所有轮廓')
            plt.subplot(224)
            plt.imshow(gray, cmap='gray')
            if best:
                x, y, w, h = best['bbox']
                plt.plot([x, x+w, x+w, x, x], [y, y, y+h, y+h, y], color='red', linewidth=2)
                plt.title('检测到的弹窗轮廓')
            else:
                plt.title('未检测到弹窗')
            plt.tight_layout()
            plt.show()
        if best:
            return True, {
                'left_top': (best['bbox'][0], best['bbox'][1]),
                'right_bottom': (best['bbox'][0]+best['bbox'][2], best['bbox'][1]+best['bbox'][3]),
                'center': best['center'],
                'area': best['area'],
                'aspect': best['aspect']
            }
        else:
            return False, None
    except Exception as e:
        log_info(f"轮廓弹窗检测出错: {e}")
        return False, None

def detect_popup_with_all_methods(image_path, debug=False, device_logger=None, device_id=None):
    """
    综合弹窗检测：连通组件亮度分析 + 边缘轮廓分析
    debug模式下输出详细判断过程和可视化
    Args:
        image_path: 图片路径
        debug: 是否显示调试信息和可视化结果
        device_logger: 日志对象
        device_id: 设备ID，用于日志标识
    Returns:
        tuple: (是否检测到弹窗, 详细信息dict)
    """
    steps = []
    result_bright, info_bright = detect_popup_with_connected_components(
        image_path, debug=debug, device_logger=device_logger, device_id=device_id)
    steps.append({
        'method': '连通组件亮度分析',
        'result': result_bright,
        'info': info_bright
    })
    result_contour, info_contour = detect_popup_with_contour(
        image_path, debug=debug, device_logger=device_logger, device_id=device_id)
    steps.append({
        'method': '边缘轮廓分析',
        'result': result_contour,
        'info': info_contour
    })
    # 综合判断
    final_result = result_bright or result_contour
    final_info = {
        '亮度分析': info_bright,
        '轮廓分析': info_contour,
        '详细步骤': steps
    }
    if debug:
        print("\n========== 综合弹窗检测流程 ==========")
        for idx, step in enumerate(steps, 1):
            print(f"步骤{idx}: {step['method']}")
            print(f"  检测结果: {'有弹窗' if step['result'] else '无弹窗'}")
            if step['info']:
                print(f"  详细信息: {step['info']}")
            else:
                print("  无详细信息")
        print(f"\n最终判断: {'有弹窗' if final_result else '无弹窗'}")
        print("====================================\n")
    return final_result, final_info

def debug_connectivity_issues(image_path, brightness_multiplier=0.7, kernel_size=3, close_iter=1, open_iter=1):
    """
    专门用于调试连通域问题的函数，可以调整各种参数并观察效果
    
    Args:
        image_path: 图片路径
        brightness_multiplier: 亮度阈值倍数
        kernel_size: 形态学核大小（奇数）
        close_iter: 闭运算迭代次数
        open_iter: 开运算迭代次数
    """
    print(f"=== 连通域调试 ===")
    print(f"图片: {image_path}")
    print(f"亮度倍数: {brightness_multiplier}")
    print(f"形态学参数: 核大小={kernel_size}, 闭运算{close_iter}次, 开运算{open_iter}次")
    
    # 读取和预处理
    image = cv2.imread(image_path)
    if image is None:
        print(f"错误: 无法读取图像")
        return
    
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    height, width = gray.shape
    
    # 计算阈值
    mean_brightness = np.mean(gray)
    std_brightness = np.std(gray)
    threshold = mean_brightness + brightness_multiplier * std_brightness
    print(f"平均亮度: {mean_brightness:.2f}, 标准差: {std_brightness:.2f}, 阈值: {threshold:.2f}")
    
    # 二值化
    bright_mask = (gray > threshold).astype(np.uint8)
    
    # 形态学操作
    kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (kernel_size, kernel_size))
    bright_mask_closed = cv2.morphologyEx(bright_mask, cv2.MORPH_CLOSE, kernel, iterations=close_iter)
    bright_mask_processed = cv2.morphologyEx(bright_mask_closed, cv2.MORPH_OPEN, kernel, iterations=open_iter)
    
    # 连通组件分析：处理前
    num_labels_before, labels_before, stats_before, _ = cv2.connectedComponentsWithStats(
        bright_mask, connectivity=8, ltype=cv2.CV_32S)
    
    # 连通组件分析：处理后
    num_labels_after, labels_after, stats_after, _ = cv2.connectedComponentsWithStats(
        bright_mask_processed, connectivity=8, ltype=cv2.CV_32S)
    
    print(f"\n连通组件数量:")
    print(f"  处理前: {num_labels_before-1} 个")
    print(f"  处理后: {num_labels_after-1} 个")
    
    # 分析大的连通组件
    print(f"\n处理前的大连通组件 (面积>1000):")
    for i in range(1, num_labels_before):
        x, y, w, h, area = stats_before[i]
        if area > 1000:
            print(f"  组件{i}: 位置({x},{y}), 大小({w}x{h}), 面积({area})")
    
    print(f"\n处理后的大连通组件 (面积>1000):")
    for i in range(1, num_labels_after):
        x, y, w, h, area = stats_after[i]
        if area > 1000:
            print(f"  组件{i}: 位置({x},{y}), 大小({w}x{h}), 面积({area})")
    
    # 可视化对比
    plt.figure(figsize=(20, 15))
    
    # 原图
    plt.subplot(331)
    plt.imshow(gray, cmap='gray')
    plt.title('原始灰度图')
    
    # 二值化结果
    plt.subplot(332)
    plt.imshow(bright_mask, cmap='gray')
    plt.title(f'二值化 (阈值={threshold:.1f})')
    
    # 闭运算结果
    plt.subplot(333)
    plt.imshow(bright_mask_closed, cmap='gray')
    plt.title(f'闭运算 ({close_iter}次)')
    
    # 最终处理结果
    plt.subplot(334)
    plt.imshow(bright_mask_processed, cmap='gray')
    plt.title(f'开运算 ({open_iter}次)')
    
    # 处理前连通组件
    plt.subplot(335)
    plt.imshow(labels_before, cmap='tab20')
    plt.title(f'处理前连通组件 ({num_labels_before-1}个)')
    
    # 处理后连通组件
    plt.subplot(336)
    plt.imshow(labels_after, cmap='tab20')
    plt.title(f'处理后连通组件 ({num_labels_after-1}个)')
    
    # 差异对比
    plt.subplot(337)
    diff = bright_mask_processed.astype(np.int16) - bright_mask.astype(np.int16)
    plt.imshow(diff, cmap='RdBu')
    plt.title('形态学操作差异')
    plt.colorbar()
    
    # 大连通组件标记（处理前）
    plt.subplot(338)
    plt.imshow(gray, cmap='gray')
    for i in range(1, num_labels_before):
        x, y, w, h, area = stats_before[i]
        if area > 1000:
            plt.plot([x, x+w, x+w, x, x], [y, y, y+h, y+h, y], 'r-', linewidth=2)
            plt.text(x, y-5, f'{i}({area})', color='red', fontsize=8)
    plt.title('处理前大连通组件')
    
    # 大连通组件标记（处理后）
    plt.subplot(339)
    plt.imshow(gray, cmap='gray')
    for i in range(1, num_labels_after):
        x, y, w, h, area = stats_after[i]
        if area > 1000:
            plt.plot([x, x+w, x+w, x, x], [y, y, y+h, y+h, y], 'g-', linewidth=2)
            plt.text(x, y-5, f'{i}({area})', color='green', fontsize=8)
    plt.title('处理后大连通组件')
    
    plt.tight_layout()
    plt.show()
    
    print(f"\n=== 调试完成 ===")

if __name__ == "__main__":
    # 测试新的连通组件弹窗检测功能
    print("=== 连通组件弹窗检测测试 ===")
    
    # 测试图片路径
    test_image = "/Users/<USER>/Desktop/work/platform_autotest_frame_python/photo/免费水果误弹窗.webp"
    
    print(f"测试图片: {test_image}")
    print("\n1. 使用原始方法检测:")
    old_result = quick_detect_popup(test_image)
    print(f"原始方法结果: {'检测到弹窗' if old_result else '未检测到弹窗'}")
    
    print("\n2. 使用连通组件方法检测:")
    new_result, coords = detect_popup_with_connected_components(test_image, debug=True)
    print(f"连通组件方法结果: {'检测到弹窗' if new_result else '未检测到弹窗'}")
    if coords:
        print(f"弹窗详细信息: {coords}")
    
    print("\n3. 对比测试结果:")
    if old_result == new_result:
        print("✅ 两种方法检测结果一致")
    else:
        print("⚠️  两种方法检测结果不同，请查看可视化结果进行分析")
    
    # # 如果你想测试其他图片，取消下面的注释
    # print("\n=== 自定义图片测试 ===")
    # custom_image = input("请输入要测试的图片路径（直接回车跳过）: ")
    # if custom_image.strip():
    #     print(f"\n测试自定义图片: {custom_image}")
    #     result, coords = detect_popup_with_connected_components(custom_image, debug=True)
    #     print(f"检测结果: {'检测到弹窗' if result else '未检测到弹窗'}")
    #     if coords:
    #         print(f"弹窗信息: {coords}")
    
