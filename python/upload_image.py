# coding=utf-8
import base64
import hmac
import hashlib
import requests
import urllib.parse as urlparse 
from io import StringIO
import time
from PIL import Image
import os
import json
import logging

# 创建logger
logger = logging.getLogger(__name__)

def gmttime(duration):
    now = time.time()
    now += duration
    date = time.gmtime(now)
    return time.strftime('%a, %d %b %Y %H:%M:%S GMT', date)

class Venus(object):
		## 线上域名，需要区分
    base_url = 'http://pic-in.vip.sankuai.com/storage/'
    bucket = "ptautotest"
    client_id = "ptautotest"
    client_secret = "1292ee16e73a9d36aa52cbd45b3d0bcc"

    def __init__(self, bucket, client_id, client_secret):
        self.bucket = bucket
        self.client_id = client_id
        self.client_secret = client_secret
		## 上传
    def upload(self, **kwargs):
        assert kwargs.__contains__('filename') or  kwargs.__contains__('data')
        url = self.base_url + self.bucket
        date = gmttime(10)
        print(date)

        headers = {
            'Date': date,
            'Authorization': self._getAuthorization('POST', url, date, self.client_id, self.client_secret),
            'Host':'pic-in.vip.sankuai.com',
            'Accept-Language':'zh-CN,zh;q=0.9'
        }
        if kwargs.__contains__('filename'):
            filename = kwargs.get('filename')
            assert isinstance(filename, str)
            files = (filename, open(filename, 'rb'))
        elif kwargs.__contains__('data'):
            data = kwargs.get('data')
            assert isinstance(data, str)
            files = ("fake.png", StringIO.StringIO(data))
        return requests.post(url=url, headers=headers, files={'file': files}).content

		## 生成鉴权信息
    def _getAuthorization(self, method, uri, date, clientId, secret):
        string_to_sign = method + " " + urlparse.urlparse(uri).path + "\n" + date
        hash = hmac.new(secret.encode('utf-8'), string_to_sign.encode('utf-8'), hashlib.sha1).digest()
        signature = str(base64.b64encode(hash), 'utf-8').replace("\n", "")   #for python3
        return "MWS" + " " + clientId + ":" + signature


def get_image_url(file_path):
    bucket = "ptautotest"
    client_id = "ptautotest"
    client_secret = "1292ee16e73a9d36aa52cbd45b3d0bcc"
    globals = {'true':True,'false':False}
    
    ## 创建 venus 类
    venus = Venus(bucket, client_id, client_secret)

    ## 上传文件，最多尝试两次
    for attempt in range(2):
        result = venus.upload(filename=file_path)
        # 使用json.loads替代不安全的eval
        try:
            result = json.loads(result)
        except json.JSONDecodeError:
            logger.error(f"无法解析上传结果: {result}")
            if attempt == 1:  # 最后一次尝试
                return None
            continue
        
        ## 从响应json包中读取 fileKey
        fileKey = result.get("data", {}).get("originalLink")
        
        if fileKey:  # 如果获取到有效的fileKey，直接返回
            return fileKey
        
        if attempt == 0:  # 第一次失败后等待1秒再重试
            time.sleep(2)
    
    raise Exception(f"上传图片失败，文件路径: {file_path}")

def compress_and_upload_image(file_path, scale_factor):
    """
    压缩图片并上传
    :param file_path: 图片路径
    :param scale_factor: 压缩比例，例如2表示将图片长宽各除以2
    :return: 上传后的图片URL
    """
    # 打开原图
    with Image.open(file_path) as img:
        # 计算新的尺寸
        new_width = int(img.width / scale_factor)
        new_height = int(img.height / scale_factor)
        
        # 等比压缩图片
        resized_img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)
        
        # 创建临时文件保存压缩后的图片
        filename, ext = os.path.splitext(file_path)
        temp_path = f"{filename}_compressed{ext}"
        resized_img.save(temp_path, quality=95)
        
        try:
            # 上传压缩后的图片
            fileKey = get_image_url(temp_path)
            return fileKey
        finally:
            # 删除临时文件
            if os.path.exists(temp_path):
                os.remove(temp_path)


if __name__ == '__main__':
    # 测试原图上传
    fileKey = get_image_url('/Users/<USER>/Desktop/work/platform_autotest_frame_python/photo/小美果园 3.jpg')
    print("原图URL:", fileKey)
    
    # 测试压缩后上传（压缩比例为2）
    # compressed_fileKey = compress_and_upload_image('/Users/<USER>/Desktop/work/金刚区巡检历史问题记录/20250217晚跑的结果/screenshot/vivo_V2243A/error/点击_美食_vivo_V2243A_20250217_202156.png', 2)
    # print("压缩图URL:", compressed_fileKey)