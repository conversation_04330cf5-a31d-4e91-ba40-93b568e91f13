import os
from typing import List, Dict

class Config:
    """配置类，管理所有常量"""
    
    # 路径相关常量
    SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
    PROJECT_ROOT = os.path.dirname(SCRIPT_DIR)
    LOG_ROOT = os.path.join(PROJECT_ROOT, "log")
    
    # 美团App测试相关常量
    TARGET_ICONS = ["外卖", "团购", "美食", "酒店民宿", "酒店旅游", "休闲玩乐", "看病买药"]
    TARGET_ICONS_FLAG = ["外卖", "团购", "美食", "美团订酒店", "美团订酒店", "休闲玩乐", "看病买药"]
    # TARGET_ICONS = ["美食"]  # 测试用，只测试一个图标
    HOMEPAGE_TARGET_ICONS = ["购物车", "我的", "消息", "资质与规则", "视频", "搜索"]
    
    # App更新检查相关常量
    APP_UPDATE_CHECK_INTERVAL = 24 * 60 * 60  # 24小时，单位为秒
    APP_UPDATE_WAIT_TIME = 2 * 60  # 点击更新后等待时间（2分钟）
    MEITUAN_APP_ID = "423084029"  # 美团App在App Store的ID
    
    # 特殊处理的图标组（这些图标在计算总数时只算一个）
    # 用于处理同一个图标在不同场景下显示不同文字的情况
    # 例如："酒店旅游"和"酒店民宿"实际上是同一个功能入口，只是显示文字可能不同
    # 在图标识别和错误检查时，会将同组内的图标视为等价的
    SPECIAL_ICON_GROUPS = [
        ["酒店民宿", "酒店旅游"],  # 酒店相关功能的不同显示名称
        # 示例：可以添加更多图标组
        # ["团购", "优惠团购"],    # 团购功能的不同显示名称  
        # ["美食", "美食餐厅"],    # 美食功能的不同显示名称
    ]
    
    # 频道区图标文本检测字典（图标名称 -> 检测文本）
    # 支持两种格式：
    # 1. 单个文本：图标名称 -> "目标文本"
    # 2. 多个文本：图标名称 -> ["文本1", "文本2", "文本3"] （找到任何一个即为成功）
    CHANNEL_ICONS_TEXT_CHECK = {
        # 示例：
        # "免费水果": "免费水果",  # 单个文本
        # "小象超市": ["小象超市", "美团自营", "30分钟达"],  # 多个文本，任一匹配即可
        "外卖": "外卖",
        "团购": "团购",
        "美食": "美食",
        "酒店民宿": "美团订酒店",
        "酒店旅游": "美团订酒店",
        "休闲玩乐": "休闲玩乐",
        "看病买药": "看病买药",
        "超市便利": "超市便利",
        "闪购": "闪购",
        "小象超市": ["小象超市", "美团自营", "30分钟达", "水果鲜花"],
        "机票火车票": ["火车票", "机票"],
        "美团打车":["美团打车", "预约用车"],
        "打车":["美团打车", "预约用车"],
        "丽人美发":["丽人美发", "美发", "美甲"],
        "跑腿":"美团跑腿",
        "超市便利":"超市便利",
        "免费水果":["签到","领水滴"],
        "电影演出":"电影演出",
        "运动健身":["运动", "健身"],
        "KTV":"KTV",
        "拼好饭":"美团拼好饭",
        "按摩足疗":["按摩", "足疗"],
        "宠物":"宠物",
        "洗浴汗蒸":["洗浴", "汗蒸"],
        "特价团":["特价团", "官方补贴"],
        "亲子乐园":["亲子乐园", "儿童乐园"],
        "借钱":["借钱", "额度"],
        "免费小说":"美团小说",
        "小说赚钱":"美团小说",
        "养车用车":"养车用车",
        "医学美容":["医学美容", "嫩肤"],
        "医疗牙科":["医疗", "牙科"],
        "台球":"台球",
        "员工餐":"员工",
        "歪马送酒":["歪马送酒", "啤酒"],
        "生活服务":["生活服务", "保洁"],
        "景点游玩":"景点",
        "天天现金":["提现","打款","召唤","天天","现金"],
        "结婚摄影":["结婚", "摄影"],
        "蔬菜水果":"蔬菜水果",
        "商场购物":["商场", "购物"],
        "美甲美睫":["美甲", "美睫"],
        "学习培训":"培训",
        "家具装修":["家具", "装修"],
        "浪漫鲜花":"浪漫鲜花",
        "母婴服务":["母婴","孕期"],
        "拍照妆造":["拍照", "写真"],
        "民宿公寓":["美团民宿", "短租"],
        "手机充值":"手机充值",
        "男士理发":["理发", "男士"],
        "骑车":["单车","找车"],
    }
    
    @classmethod
    def get_actual_icon_count(cls):
        """
        计算实际的图标数量，特殊处理某些图标组
        例如："酒店民宿"和"酒店旅游"只算一个
        """
        count = len(cls.TARGET_ICONS)
        
        # 减去特殊图标组中的重复计数
        for group in cls.SPECIAL_ICON_GROUPS:
            # 计算这个组中在TARGET_ICONS中出现的图标数量
            icons_in_group = sum(1 for icon in group if icon in cls.TARGET_ICONS)
            # 如果有多个，则减去多余的计数（只保留一个）
            if icons_in_group > 1:
                count -= (icons_in_group - 1)
        
        return count
    
    @classmethod
    def get_icon_group(cls, icon_name):
        """
        获取指定图标所属的特殊图标组
        
        Args:
            icon_name: 图标名称
            
        Returns:
            list: 如果图标属于特殊组，返回该组的所有图标列表；否则返回包含单个图标的列表
        """
        for group in cls.SPECIAL_ICON_GROUPS:
            if icon_name in group:
                return group
        return [icon_name]
    
    @classmethod
    def is_icon_found_in_group(cls, target_icon, found_icons):
        """
        检查目标图标是否在已找到的图标中（考虑特殊图标组别名）
        
        Args:
            target_icon: 目标图标名称
            found_icons: 已找到的图标列表或字典的键
            
        Returns:
            tuple: (is_found, matched_icon) - 是否找到和匹配的图标名称
        """
        # 直接找到
        if target_icon in found_icons:
            return True, target_icon
        
        # 在特殊图标组中查找
        for group in cls.SPECIAL_ICON_GROUPS:
            if target_icon in group:
                # 检查该组中是否有任何图标被找到
                for group_icon in group:
                    if group_icon in found_icons:
                        return True, group_icon
                break
        
        return False, None
    
    # 锁文件路径
    WDA_LOCK_FILE = os.path.join(PROJECT_ROOT, "wda_install.lock")
    
    # WDA相关常量
    WDA_RESTART_INTERVAL = 6 * 60 * 60  # 6小时，单位为秒
    WDA_WAIT_TIME = 10  # WDA服务完全启动等待时间（秒），从90秒减少到10秒
    WDA_PROCESS_TIMEOUT = 150  # WDA启动进程超时时间（秒）
    WDA_STOP_WAIT_TIME = 30  # 停止WDA后等待时间（秒）
    WDA_PORT_STEP = 1  # WDA端口步长
    WDA_INITIAL_WAIT_TIME = 100  # WDA初始等待时间（秒）
    WDA_KILL_PROCESS_WAIT_TIME = 1  # 终止WDA进程后等待时间（秒）
    WDA_IPROXY_WAIT_TIME = 3  # 等待iproxy启动的时间（秒）
    WDA_IPROXY_TERMINATE_TIMEOUT = 5  # iproxy进程终止超时时间（秒）
    WDA_RESTART_RETRY_WAIT = 30  # WDA重启失败后重试等待时间（秒）
    WDA_MAX_RETRIES = 3  # WDA启动最大重试次数
    WDA_RESTART_MAX_RETRIES = 5  # WDA重启最大重试次数
    
    # Appium相关常量
    APPIUM_PORT_STEP = 100  # Appium端口步长
    APPIUM_WAIT_TIME = 10  # Appium服务启动等待时间（秒）
    APPIUM_START_WAIT_TIME = 5  # Appium服务启动后等待时间（秒）
    APPIUM_STATUS_TIMEOUT = 3  # 检查Appium状态的超时时间（秒）
    APPIUM_MAX_RETRIES = 3  # Appium启动最大重试次数
    
    # 服务启动相关常量
    SERVICE_RETRY_WAIT_TIME = 10  # 服务重试等待时间（秒）
    SERVICE_PROCESS_TIMEOUT = 10  # 服务进程通信超时时间（秒）
    
    # 设备控制相关常量
    DEVICE_TEST_INTERVAL = 30  # 设备测试间隔（秒）
    DEVICE_ERROR_WAIT_TIME = 60  # 设备错误后等待时间（秒）
    DEVICE_START_INTERVAL = 30  # Android设备启动间隔（秒）
    DEVICE_RETRY_WAIT_TIME = 30  # 设备重试等待时间（秒）
    
    # 端口相关常量
    IOS_BASE_APPIUM_PORT = 4724  # iOS设备Appium基础端口
    IOS_BASE_WDA_PORT = 8101  # iOS设备WDA基础端口
    IOS_APPIUM_PORT_INTERVAL = 2  # iOS设备Appium端口间隔
    IOS_WDA_PORT_INTERVAL = 100  # iOS设备WDA端口间隔
    
    # 监控相关常量
    STATUS_PRINT_INTERVAL = 60  # 状态打印间隔（秒）
    STATUS_CHECK_INTERVAL = 300  # 状态检查间隔（秒）
    MAX_STATUS_UNCHANGED_TIME = 6000  # 状态最长不变时间（100分钟）
    MONITOR_SLEEP_INTERVAL = 60  # 监控进程睡眠间隔（秒）
    
    # 设备控制器相关常量
    CONTROLLER_WAIT_TIME = 10  # 控制器等待时间（秒）
    MAX_WAIT_TIME_WDA_INSTALL = 300  # WDA安装最大等待时间（5分钟）
    MAX_WAIT_TIME_DEVICE_START = 600  # 设备启动最大等待时间（10分钟）
    CONTROLLER_STATUS_PRINT_INTERVAL = 10  # 控制器状态打印间隔（秒）
    CONTROLLER_DEVICE_CHECK_INTERVAL = 2  # 控制器设备检查间隔（秒）
    PROCESS_JOIN_TIMEOUT = 10  # 进程join超时时间（秒）
    PROCESS_TERMINATE_TIMEOUT = 5  # 进程终止超时时间（秒）
    
    # ADB服务相关常量
    ADB_RESTART_INTERVAL = 4 * 60 * 60  # 4小时，单位为秒
    ADB_RESTART_TIMEOUT = 30  # ADB重启超时时间（秒）
    ADB_DEVICE_WAIT_TIME = 10  # 重启后等待设备连接的时间（秒）
    ADB_COMMAND_TIMEOUT = 10  # ADB命令超时时间（秒）

    # 设备重连相关常量
    DEVICE_RECONNECT_INTERVAL = 3 * 60 * 60  # 3小时，单位为秒
    DEVICE_RECONNECT_TIMEOUT = 60  # 重连操作超时时间（秒）
    DEVICE_RECONNECT_RETRY_COUNT = 3  # 重连尝试次数
    DEVICE_RECONNECT_RETRY_INTERVAL = 10  # 重连尝试间隔时间（秒）

    # 分层重试设置
    DEVICE_RECONNECT_FIRST_LAYER_RETRY_COUNT = 3  # 首次尝试重连次数
    DEVICE_RECONNECT_FIRST_LAYER_INTERVAL = 30  # 首次重连间隔（秒）
    
    DEVICE_RECONNECT_SECOND_LAYER_RETRY_COUNT = 3  # 第二次尝试重连次数
    DEVICE_RECONNECT_SECOND_LAYER_INTERVAL = 15 * 60  # 第二次重连间隔（15分钟）
    
    DEVICE_RECONNECT_THIRD_LAYER_RETRY_COUNT = 3  # 第三次尝试重连次数
    DEVICE_RECONNECT_THIRD_LAYER_INTERVAL = 60 * 60  # 第三次重连间隔（1小时）

    # iOS设备重连相关常量
    IOS_DEVICE_RECONNECT_TIMEOUT = 180  # iOS设备重连超时时间（秒），考虑到WDA安装
    IOS_DEVICE_RECONNECT_WAIT_TIME = 120  # iOS设备重连后等待时间（秒）
    IOS_DEVICE_RECONNECT_RETRY_COUNT = 5  # iOS设备第一层重连尝试次数
    IOS_DEVICE_RECONNECT_RETRY_INTERVAL = 20  # iOS设备第一层重连尝试间隔时间（秒）

    # Android设备重连相关常量
    ANDROID_DEVICE_RECONNECT_TIMEOUT = 60  # Android设备重连超时时间（秒）
    ANDROID_DEVICE_RECONNECT_WAIT_TIME = 15  # Android设备重连后等待时间（秒）
    ANDROID_DEVICE_RECONNECT_RETRY_COUNT = 3  # Android设备第一层重连尝试次数
    ANDROID_DEVICE_RECONNECT_RETRY_INTERVAL = 10  # Android设备第一层重连尝试间隔时间（秒）

    TARGET_UDIDS_IOS = [
        "00008120-001279343ADB401E",
        "00008030-000D78DE02EA802E",
        "00008120-0002519E1400C01E",
        "00008101-000979841401001E",
        "00008130-0002710621E0001C",
        "00008101-0002612C3608001E",
        "00008140-000238321401801C",
        "00008110-001104592252801E",
        "00008030-001550602192802E",
        "00008120-000A746E0E80C01E",
        "00008101-00166C3811BA001E"
    ]

    TARGET_UDIDS_ANDROID = [
        "10AD5F1KJ4002B6",
        "6d6fe149",
        "d6f09d4a",
        "vceqq8s46dk7kbsk",
        "928QAEVM227HA",
        "cfba9977",
        "bc15452a",
        "10AF6A30BC003FJ",
        "7a57d2d8"
    ] 