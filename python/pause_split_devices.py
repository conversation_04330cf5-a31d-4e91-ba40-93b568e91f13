#!/usr/bin/env python3
"""
暂停 split_devices.py 测试流程

该脚本会：
1. 暂停所有正在运行的设备测试进程
2. 允许用户手动操作设备进行设置
3. 在指定时间后自动恢复测试流程
4. 提供手动恢复选项

使用方法：
python3 pause_split_devices.py --duration 300  # 暂停5分钟
python3 pause_split_devices.py --duration 1800 # 暂停30分钟
python3 pause_split_devices.py --resume         # 手动恢复测试流程
"""

import os
import sys
import time
import argparse
import logging
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Optional

# 添加当前目录的父目录到sys.path，确保能正确导入模块
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from python.device_status_manager import get_device_status, update_device_status, get_all_device_status
from python.log_manager import global_logger

# 设置日志格式
log_dir = 'log/pause_logs'
os.makedirs(log_dir, exist_ok=True)
log_file = os.path.join(log_dir, f'pause_split_devices_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler(log_file)
    ]
)
logger = logging.getLogger(__name__)

class TestPauseManager:
    """测试暂停管理器"""
    
    def __init__(self):
        self.paused_devices = []
        self.pause_start_time = None
        self.pause_duration = None
        self.resume_timer = None
        
    def get_active_devices(self) -> List[Dict]:
        """获取所有活跃的设备（正在测试或等待测试的设备）"""
        active_devices = []
        all_devices = get_all_device_status()
        
        if not all_devices:
            logger.warning("没有找到任何设备状态信息")
            return active_devices
        
        for device_id, status_info in all_devices.items():
            if not status_info or not isinstance(status_info, dict):
                continue
            
            status = status_info.get('status', 'unknown')
            device_name = status_info.get('device_name', 'Unknown')
            
            # 只处理正在运行、等待或者已经暂停的设备
            if status in ['running', 'waiting', 'installing_wda', 'wda_installed', 'paused', 'success', 'failed', 'error', 'completed']:
                active_devices.append({
                    'device_id': device_id,
                    'device_name': device_name,
                    'original_status': status,
                    'platform': status_info.get('platform', 'unknown')
                })
                logger.info(f"找到活跃设备: {device_name} (UDID: {device_id}), 状态: {status}")
        
        return active_devices
    
    def pause_all_devices(self, duration_seconds: int) -> bool:
        """暂停所有活跃设备的测试"""
        logger.info("🚫 开始暂停所有设备的测试流程...")
        
        # 获取所有活跃设备
        active_devices = self.get_active_devices()
        
        if not active_devices:
            logger.warning("没有找到需要暂停的活跃设备")
            return False
        
        logger.info(f"找到 {len(active_devices)} 台活跃设备，准备暂停")
        
        # 记录暂停信息
        self.pause_start_time = time.time()
        self.pause_duration = duration_seconds
        pause_end_time = self.pause_start_time + duration_seconds
        
        # 暂停每个设备
        paused_count = 0
        for device in active_devices:
            device_id = device['device_id']
            device_name = device['device_name']
            original_status = device['original_status']
            
            try:
                # 如果设备已经是暂停状态，跳过
                if original_status == 'paused':
                    logger.info(f"设备 {device_name} (UDID: {device_id}) 已经处于暂停状态")
                    continue
                
                # 更新设备状态为暂停
                update_device_status(device_id, {
                    'status': 'paused',
                    'pause_start_time': self.pause_start_time,
                    'pause_duration': duration_seconds,
                    'pause_end_time': pause_end_time,
                    'original_status_before_pause': original_status,
                    'last_update': time.time()
                })
                
                self.paused_devices.append(device)
                paused_count += 1
                logger.info(f"✅ 设备 {device_name} (UDID: {device_id}) 已暂停，原状态: {original_status}")
                
            except Exception as e:
                logger.error(f"❌ 暂停设备 {device_name} (UDID: {device_id}) 时发生错误: {str(e)}")
        
        if paused_count > 0:
            logger.info(f"🎉 成功暂停 {paused_count} 台设备，暂停时长: {duration_seconds} 秒 ({duration_seconds//60} 分钟)")
            logger.info(f"⏰ 暂停开始时间: {datetime.fromtimestamp(self.pause_start_time).strftime('%Y-%m-%d %H:%M:%S')}")
            logger.info(f"⏰ 预计恢复时间: {datetime.fromtimestamp(pause_end_time).strftime('%Y-%m-%d %H:%M:%S')}")
            
            # 启动自动恢复定时器
            self.start_auto_resume_timer(duration_seconds)
            return True
        else:
            logger.warning("没有成功暂停任何设备")
            return False
    
    def resume_all_devices(self, manual_resume: bool = False) -> bool:
        """恢复所有设备的测试"""
        resume_reason = "手动恢复" if manual_resume else "自动恢复"
        logger.info(f"🔄 开始{resume_reason}所有设备的测试流程...")
        
        # 获取所有暂停的设备
        paused_devices = []
        all_devices = get_all_device_status()
        
        if not all_devices:
            logger.warning("没有找到任何设备状态信息")
            return False
        
        for device_id, status_info in all_devices.items():
            if not status_info or not isinstance(status_info, dict):
                continue
            
            status = status_info.get('status', 'unknown')
            device_name = status_info.get('device_name', 'Unknown')
            
            # 只处理暂停状态的设备
            if status == 'paused':
                original_status = status_info.get('original_status_before_pause', 'running')
                paused_devices.append({
                    'device_id': device_id,
                    'device_name': device_name,
                    'original_status': original_status
                })
        
        if not paused_devices:
            logger.warning("没有找到暂停状态的设备")
            return False
        
        logger.info(f"找到 {len(paused_devices)} 台暂停的设备，准备恢复")
        
        # 恢复每个设备
        resumed_count = 0
        for device in paused_devices:
            device_id = device['device_id']
            device_name = device['device_name']
            original_status = device['original_status']
            
            try:
                # 恢复设备到原来的状态
                update_device_status(device_id, {
                    'status': original_status,
                    'last_update': time.time(),
                    'pause_start_time': None,
                    'pause_duration': None,
                    'pause_end_time': None,
                    'original_status_before_pause': None
                })
                
                resumed_count += 1
                logger.info(f"✅ 设备 {device_name} (UDID: {device_id}) 已恢复到状态: {original_status}")
                
            except Exception as e:
                logger.error(f"❌ 恢复设备 {device_name} (UDID: {device_id}) 时发生错误: {str(e)}")
        
        if resumed_count > 0:
            logger.info(f"🎉 成功{resume_reason} {resumed_count} 台设备的测试")
            
            # 取消自动恢复定时器（如果存在）
            if self.resume_timer:
                self.resume_timer.cancel()
                self.resume_timer = None
            
            # 清空暂停设备列表
            self.paused_devices = []
            self.pause_start_time = None
            self.pause_duration = None
            
            return True
        else:
            logger.warning("没有成功恢复任何设备")
            return False
    
    def start_auto_resume_timer(self, duration_seconds: int):
        """启动自动恢复定时器"""
        def auto_resume():
            logger.info("⏰ 暂停时间已到，开始自动恢复测试...")
            self.resume_all_devices(manual_resume=False)
        
        self.resume_timer = threading.Timer(duration_seconds, auto_resume)
        self.resume_timer.start()
        logger.info(f"🕐 自动恢复定时器已启动，将在 {duration_seconds} 秒后自动恢复测试")
    
    def get_pause_status(self) -> Dict:
        """获取当前暂停状态"""
        paused_devices = []
        all_devices = get_all_device_status()
        
        if all_devices:
            for device_id, status_info in all_devices.items():
                if not status_info or not isinstance(status_info, dict):
                    continue
                
                status = status_info.get('status', 'unknown')
                if status == 'paused':
                    device_name = status_info.get('device_name', 'Unknown')
                    pause_start_time = status_info.get('pause_start_time', 0)
                    pause_duration = status_info.get('pause_duration', 0)
                    pause_end_time = status_info.get('pause_end_time', 0)
                    
                    paused_devices.append({
                        'device_id': device_id,
                        'device_name': device_name,
                        'pause_start_time': pause_start_time,
                        'pause_duration': pause_duration,
                        'pause_end_time': pause_end_time,
                        'remaining_time': max(0, pause_end_time - time.time()) if pause_end_time > 0 else 0
                    })
        
        return {
            'has_paused_devices': len(paused_devices) > 0,
            'paused_devices': paused_devices,
            'total_paused': len(paused_devices)
        }
    
    def monitor_pause_status(self):
        """监控暂停状态并显示进度"""
        logger.info("📊 开始监控暂停状态...")
        
        while True:
            status = self.get_pause_status()
            
            if not status['has_paused_devices']:
                logger.info("📋 没有设备处于暂停状态，停止监控")
                break
            
            current_time = time.time()
            logger.info("="*60)
            logger.info("📋 当前暂停状态:")
            logger.info(f"   暂停设备数量: {status['total_paused']}")
            
            for device in status['paused_devices']:
                device_name = device['device_name']
                remaining_time = device['remaining_time']
                
                if remaining_time > 0:
                    minutes, seconds = divmod(int(remaining_time), 60)
                    hours, minutes = divmod(minutes, 60)
                    time_str = f"{hours:02d}:{minutes:02d}:{seconds:02d}"
                    logger.info(f"   📱 {device_name}: 剩余时间 {time_str}")
                else:
                    logger.info(f"   📱 {device_name}: 暂停时间已到，等待恢复...")
            
            logger.info("="*60)
            logger.info("💡 提示:")
            logger.info("   - 现在可以安全地手动操作你的设备进行设置")
            logger.info("   - 如需提前恢复测试，请运行: python3 pause_split_devices.py --resume")
            logger.info("   - 按 Ctrl+C 可以退出监控（不会影响暂停状态）")
            logger.info("="*60)
            
            # 等待30秒后再次检查
            try:
                time.sleep(30)
            except KeyboardInterrupt:
                logger.info("\n👋 监控已停止，设备仍保持暂停状态")
                break

def parse_duration(duration_str: str) -> int:
    """解析时间字符串，支持多种格式"""
    duration_str = duration_str.lower().strip()
    
    # 如果是纯数字，当作秒处理
    if duration_str.isdigit():
        return int(duration_str)
    
    # 解析带单位的时间
    import re
    
    # 匹配数字和单位
    match = re.match(r'^(\d+)(s|m|h)?$', duration_str)
    if match:
        number = int(match.group(1))
        unit = match.group(2) or 's'  # 默认单位为秒
        
        if unit == 's':
            return number
        elif unit == 'm':
            return number * 60
        elif unit == 'h':
            return number * 3600
    
    # 尝试解析 HH:MM:SS 格式
    time_parts = duration_str.split(':')
    if len(time_parts) == 2:  # MM:SS
        try:
            minutes, seconds = map(int, time_parts)
            return minutes * 60 + seconds
        except ValueError:
            pass
    elif len(time_parts) == 3:  # HH:MM:SS
        try:
            hours, minutes, seconds = map(int, time_parts)
            return hours * 3600 + minutes * 60 + seconds
        except ValueError:
            pass
    
    raise ValueError(f"无法解析时间格式: {duration_str}")

def format_duration(seconds: int) -> str:
    """格式化时间显示"""
    if seconds < 60:
        return f"{seconds} 秒"
    elif seconds < 3600:
        minutes = seconds // 60
        remaining_seconds = seconds % 60
        if remaining_seconds == 0:
            return f"{minutes} 分钟"
        else:
            return f"{minutes} 分钟 {remaining_seconds} 秒"
    else:
        hours = seconds // 3600
        remaining_minutes = (seconds % 3600) // 60
        remaining_seconds = seconds % 60
        
        result = f"{hours} 小时"
        if remaining_minutes > 0:
            result += f" {remaining_minutes} 分钟"
        if remaining_seconds > 0:
            result += f" {remaining_seconds} 秒"
        return result

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='暂停或恢复 split_devices.py 测试流程',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python3 pause_split_devices.py --duration 300     # 暂停5分钟
  python3 pause_split_devices.py --duration 30m     # 暂停30分钟  
  python3 pause_split_devices.py --duration 1h      # 暂停1小时
  python3 pause_split_devices.py --duration 10:30   # 暂停10分30秒
  python3 pause_split_devices.py --duration 1:30:00 # 暂停1小时30分钟
  python3 pause_split_devices.py --resume           # 手动恢复测试
  python3 pause_split_devices.py --status           # 查看暂停状态
        """
    )
    
    parser.add_argument('--duration', '-d', type=str, 
                        help='暂停时长，支持格式: 秒数(300)、带单位(30m, 1h)、时间格式(10:30, 1:30:00)')
    parser.add_argument('--resume', '-r', action='store_true',
                        help='手动恢复所有暂停的设备测试')
    parser.add_argument('--status', '-s', action='store_true',
                        help='查看当前暂停状态')
    parser.add_argument('--monitor', '-m', action='store_true',
                        help='持续监控暂停状态直到恢复')
    
    args = parser.parse_args()
    
    # 检查参数
    if not any([args.duration, args.resume, args.status, args.monitor]):
        parser.print_help()
        return
    
    print("🔧 split_devices.py 测试暂停管理工具")
    print("="*50)
    
    pause_manager = TestPauseManager()
    
    try:
        if args.status or args.monitor:
            # 查看暂停状态
            status = pause_manager.get_pause_status()
            
            if status['has_paused_devices']:
                logger.info(f"📋 当前有 {status['total_paused']} 台设备处于暂停状态:")
                
                for device in status['paused_devices']:
                    device_name = device['device_name']
                    remaining_time = device['remaining_time']
                    
                    if remaining_time > 0:
                        logger.info(f"   📱 {device_name}: 剩余 {format_duration(int(remaining_time))}")
                    else:
                        logger.info(f"   📱 {device_name}: 暂停时间已到，等待恢复")
                
                if args.monitor:
                    # 开始监控
                    pause_manager.monitor_pause_status()
            else:
                logger.info("✅ 当前没有设备处于暂停状态")
        
        elif args.resume:
            # 手动恢复
            logger.info("🔄 执行手动恢复操作...")
            
            success = pause_manager.resume_all_devices(manual_resume=True)
            
            if success:
                logger.info("🎉 所有设备已成功恢复测试流程！")
            else:
                logger.warning("⚠️  没有找到需要恢复的设备")
        
        elif args.duration:
            # 暂停操作
            try:
                duration_seconds = parse_duration(args.duration)
                logger.info(f"⏰ 准备暂停测试流程 {format_duration(duration_seconds)}")
                
                # 确认操作
                confirm = input(f"\n❓ 确定要暂停所有设备测试 {format_duration(duration_seconds)} 吗？(y/N): ").strip().lower()
                if confirm not in ['y', 'yes']:
                    logger.info("❌ 操作已取消")
                    return
                
                logger.info("🚫 开始暂停操作...")
                success = pause_manager.pause_all_devices(duration_seconds)
                
                if success:
                    logger.info("🎉 所有设备已成功暂停！")
                    logger.info("💡 提示:")
                    logger.info("   - 现在可以安全地手动操作你的设备进行设置")
                    logger.info("   - 测试将在指定时间后自动恢复")
                    logger.info("   - 如需提前恢复，请运行: python3 pause_split_devices.py --resume")
                    logger.info("   - 如需监控状态，请运行: python3 pause_split_devices.py --monitor")
                    
                    # 询问是否要持续监控
                    monitor = input("\n❓ 是否要持续监控暂停状态？(y/N): ").strip().lower()
                    if monitor in ['y', 'yes']:
                        pause_manager.monitor_pause_status()
                else:
                    logger.warning("⚠️  暂停操作未完全成功，请检查日志")
                    
            except ValueError as e:
                logger.error(f"❌ 时间格式错误: {str(e)}")
                logger.info("💡 支持的时间格式:")
                logger.info("   - 纯数字秒数: 300")
                logger.info("   - 带单位: 30s, 5m, 1h")
                logger.info("   - 时间格式: 10:30 (分:秒), 1:30:00 (时:分:秒)")
    
    except KeyboardInterrupt:
        logger.info("\n👋 操作被用户中断")
    except Exception as e:
        logger.error(f"❌ 发生错误: {str(e)}")
        logger.error(f"详细错误信息: {traceback.format_exc()}")
    
    logger.info(f"\n📋 详细日志已保存到: {log_file}")

if __name__ == "__main__":
    import traceback
    main() 