import os
import time
import logging
import subprocess
import requests

# 内部配置类，如果无法导入完整配置，将使用这些默认值
class Config:
    """配置类，管理所有常量"""
    
    # 路径相关常量
    SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
    PROJECT_ROOT = os.path.dirname(SCRIPT_DIR)
    LOG_ROOT = os.path.join(PROJECT_ROOT, "log")
    
    # Appium相关常量
    APPIUM_PORT_STEP = 100  # Appium端口步长
    APPIUM_WAIT_TIME = 10  # Appium服务启动等待时间（秒）
    APPIUM_START_WAIT_TIME = 5  # Appium服务启动后等待时间（秒）
    APPIUM_STATUS_TIMEOUT = 3  # 检查Appium状态的超时时间（秒）
    APPIUM_MAX_RETRIES = 3  # Appium启动最大重试次数

# 尝试导入实际的Config
try:
    from config import Config as FullConfig
    # 更新本地Config类
    Config.SCRIPT_DIR = FullConfig.SCRIPT_DIR
    Config.PROJECT_ROOT = FullConfig.PROJECT_ROOT
    Config.LOG_ROOT = FullConfig.LOG_ROOT
    Config.APPIUM_PORT_STEP = FullConfig.APPIUM_PORT_STEP
    Config.APPIUM_WAIT_TIME = FullConfig.APPIUM_WAIT_TIME
    Config.APPIUM_START_WAIT_TIME = FullConfig.APPIUM_START_WAIT_TIME
    Config.APPIUM_STATUS_TIMEOUT = FullConfig.APPIUM_STATUS_TIMEOUT
    Config.APPIUM_MAX_RETRIES = FullConfig.APPIUM_MAX_RETRIES
except ImportError:
    # 如果导入失败，使用默认值
    pass

# 尝试导入日志管理器
try:
    from log_manager import LogManager, global_logger
except ImportError:
    # 如果导入失败，创建一个简单的日志管理器
    class LogManager:
        def __init__(self, log_root=None):
            self.log_root = log_root or Config.LOG_ROOT
            self.appium_logs_dir = os.path.join(self.log_root, "appium_logs")
            # 确保日志目录存在
            os.makedirs(self.appium_logs_dir, exist_ok=True)
        
        def get_appium_log_file(self, port):
            """获取Appium日志文件路径
            
            Args:
                port: Appium服务端口
                
            Returns:
                str: 日志文件路径
            """
            return os.path.join(self.appium_logs_dir, f"appium_{port}.log")
    
    # 创建全局日志记录器
    global_logger = logging.getLogger("appium_manager")
    handler = logging.StreamHandler()
    formatter = logging.Formatter("%(asctime)s - %(levelname)s - [%(name)s] - %(message)s")
    handler.setFormatter(formatter)
    global_logger.addHandler(handler)
    global_logger.setLevel(logging.INFO)

class AppiumServiceManager:
    """Appium服务管理器，负责Appium服务的启动和状态检查"""
    
    def __init__(self, config, logger):
        self.config = config
        self.logger = logger
        self.log_manager = LogManager()
    
    def check_existing_appium_process(self, port, device_logger):
        """检查是否已有Appium进程在指定端口运行
        
        Args:
            port: 端口号
            device_logger: 日志记录器
            
        Returns:
            tuple: (exists, pid) - exists为布尔值，pid为进程ID或None
        """
        try:
            # 检查端口是否被占用
            result = subprocess.run(f"lsof -t -i:{port}", shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
            if result.returncode == 0 and result.stdout.strip():
                pid = result.stdout.strip()
                
                # 进一步检查进程是否为appium进程
                ps_result = subprocess.run(f"ps -p {pid} -o command=", shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
                if ps_result.returncode == 0 and 'appium' in ps_result.stdout.lower():
                    device_logger.info(f"发现现有Appium进程在端口 {port}，PID: {pid}")
                    return True, pid
                else:
                    device_logger.warning(f"端口 {port} 被非Appium进程占用，PID: {pid}")
                    return False, None
            
            return False, None
        except Exception as e:
            device_logger.warning(f"检查现有Appium进程时出错: {e}")
            return False, None

    def start_appium_server(self, port, attempt=0, max_retries=None, device_logger=None):
        """启动 Appium 服务（使用nohup持久化运行）
        
        Args:
            port: 端口号
            attempt: 当前尝试次数
            max_retries: 最大重试次数，默认使用Config.APPIUM_MAX_RETRIES
            device_logger: 日志记录器
            
        Returns:
            tuple: (success, result) - success为布尔值，result为进程PID或None
        """
        # 如果未指定max_retries，使用配置中的默认值
        if max_retries is None:
            max_retries = Config.APPIUM_MAX_RETRIES
        
        # 首先检查是否已有Appium进程在运行
        exists, existing_pid = self.check_existing_appium_process(port, device_logger)
        if exists:
            device_logger.info(f"Appium服务已在端口 {port} 运行，PID: {existing_pid}，跳过启动")
            return True, existing_pid
            
        log_file_appium = self.log_manager.get_appium_log_file(port)
        
        try:
            # 确保日志目录存在
            os.makedirs(os.path.dirname(log_file_appium), exist_ok=True)
            
            appium_path = subprocess.check_output(["which", "appium"]).decode().strip()
            if device_logger:
                device_logger.info(f"使用 Appium 路径: {appium_path}")
            
            # 使用nohup启动持久化Appium服务
            cmd = [
                "nohup",
                appium_path,
                "-p", str(port),
                "--base-path", "/wd/hub",
                "--log", log_file_appium,
                "--keep-alive-timeout", "600",  # 10分钟无活动超时
                "--session-override",  # 允许会话覆盖
                "--relaxed-security"  # 放宽安全限制，避免一些连接问题
            ]
            
            device_logger.info(f"启动Appium服务命令: {' '.join(cmd)}")
            
            # 使用nohup在后台启动，重定向输出到日志文件
            with open(log_file_appium, "a") as log_file:
                log_file.write(f"\n\n=== Appium服务启动时间: {time.strftime('%Y-%m-%d %H:%M:%S')} ===\n")
                log_file.write(f"命令: {' '.join(cmd)}\n")
                log_file.write(f"端口: {port}\n\n")
                
                process = subprocess.Popen(
                    cmd,
                    stdout=log_file,
                    stderr=subprocess.STDOUT,
                    preexec_fn=os.setpgrp  # 创建新的进程组，确保进程独立
                )
            
            # 等待服务启动
            device_logger.info(f"等待Appium服务启动，PID: {process.pid}")
            time.sleep(Config.APPIUM_START_WAIT_TIME)
            
            # 检查进程是否还在运行
            if process.poll() is None:
                device_logger.info(f"Appium服务启动成功，端口: {port}, PID: {process.pid}")
                return True, process.pid
            else:
                device_logger.error(f"Appium服务启动后立即退出，返回码: {process.returncode}")
                return False, None
                
        except Exception as e:
            if device_logger:
                device_logger.error(f"启动 Appium 失败: {str(e)}")
            
            return False, None

    def check_appium_status(self, port, device_logger, with_retry=True):
        """检查 Appium 服务状态
        
        Args:
            port: 端口号
            device_logger: 日志记录器
            with_retry: 是否启用重试机制
            
        Returns:
            bool: Appium 服务是否正常运行
        """
        max_attempts = 3 if with_retry else 1
        timeout_seconds = 10  # 增加超时时间到10秒
        
        for attempt in range(max_attempts):
            try:
                device_logger.debug(f"检查Appium服务状态，尝试 {attempt + 1}/{max_attempts}")
                response = requests.get(f"http://127.0.0.1:{port}/wd/hub/status", timeout=timeout_seconds)
                
                if response.status_code == 200:
                    response_data = response.json()
                    if "value" in response_data and response_data["value"].get("ready", False):
                        device_logger.info(f"Appium 服务在端口 {port} 正常运行中")
                        return True
                    else:
                        device_logger.debug(f"Appium服务响应正常但状态不是ready: {response_data}")
                else:
                    device_logger.debug(f"Appium服务HTTP状态码异常: {response.status_code}")
                    
            except requests.exceptions.Timeout:
                device_logger.debug(f"检查Appium状态超时 (尝试 {attempt + 1}/{max_attempts})")
                if attempt < max_attempts - 1:
                    time.sleep(2)  # 重试前等待2秒
                    continue
            except requests.exceptions.ConnectionError:
                device_logger.debug(f"Appium服务连接失败 (尝试 {attempt + 1}/{max_attempts})")
                if attempt < max_attempts - 1:
                    time.sleep(2)  # 重试前等待2秒
                    continue
            except Exception as e:
                device_logger.debug(f"检查Appium状态异常: {str(e)} (尝试 {attempt + 1}/{max_attempts})")
                if attempt < max_attempts - 1:
                    time.sleep(2)  # 重试前等待2秒
                    continue
        
        # 所有尝试都失败了
        device_logger.info(f"检查 Appium 状态失败，经过 {max_attempts} 次尝试")
        return False
    
    def keep_appium_alive(self, port, device_logger):
        """向Appium服务发送保活请求
        
        Args:
            port: 端口号
            device_logger: 日志记录器
            
        Returns:
            bool: 保活请求是否成功
        """
        try:
            # 发送简单的状态请求作为保活
            response = requests.get(f"http://127.0.0.1:{port}/wd/hub/status", timeout=5)
            if response.status_code == 200:
                device_logger.debug(f"Appium保活成功，端口 {port}")
                return True
            else:
                device_logger.debug(f"Appium保活失败，端口 {port}，状态码: {response.status_code}")
                return False
        except Exception as e:
            device_logger.debug(f"Appium保活请求失败，端口 {port}: {e}")
            return False

    def stop_appium_server(self, port, device_logger):
        """停止指定端口的Appium服务
        
        Args:
            port: 端口号
            device_logger: 日志记录器
            
        Returns:
            bool: 是否成功停止服务
        """
        try:
            # 先检查端口是否被占用
            cmd = f"lsof -t -i:{port}"
            result = subprocess.run(cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
            
            # 如果端口没有被占用（返回值非0），直接返回
            if result.returncode != 0:
                device_logger.info(f"端口 {port} 当前没有运行的Appium服务")
                return True
                
            # 如果端口被占用，则获取PID并终止进程
            pids = result.stdout.strip().split('\n')
            stopped_count = 0
            
            for pid in pids:
                if pid.strip():
                    try:
                        # 先尝试优雅关闭（SIGTERM）
                        subprocess.run(['kill', '-TERM', pid.strip()], check=False)
                        device_logger.info(f"向Appium进程 {pid} 发送SIGTERM信号")
                        
                        # 等待2秒看进程是否已经停止
                        time.sleep(2)
                        
                        # 检查进程是否还在运行
                        check_result = subprocess.run(['ps', '-p', pid.strip()], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
                        if check_result.returncode != 0:
                            device_logger.info(f"Appium进程 {pid} 已优雅停止")
                            stopped_count += 1
                        else:
                            # 如果还在运行，强制杀死
                            subprocess.run(['kill', '-9', pid.strip()], check=False)
                            device_logger.info(f"强制停止Appium进程 {pid}")
                            stopped_count += 1
                            
                    except Exception as e:
                        device_logger.warning(f"停止Appium进程 {pid} 时出错: {e}")
            
            if stopped_count > 0:
                device_logger.info(f"已停止端口 {port} 的 {stopped_count} 个Appium服务")
                return True
            else:
                device_logger.warning(f"未能成功停止端口 {port} 的Appium服务")
                return False
                
        except Exception as e:
            device_logger.error(f"停止Appium服务时发生错误: {e}")
        return False

# 创建Appium服务管理器全局实例
appium_service_manager = AppiumServiceManager(Config(), global_logger) 