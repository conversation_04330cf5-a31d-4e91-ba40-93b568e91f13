import time
import logging
import subprocess
import os
from PIL import Image
from python.device_status_manager import get_device_status, update_device_status
from python.heartbeat_monitor import log_device_issue, ISSUE_TYPE_APP, ISSUE_TYPE_TEST, initialize_issue_logger
from python.config import Config
import re
import traceback
from python.device_common import device_operate_external
from python.swipe_homepage import swipe_homepage_recommend
from python.tap_main_icons import tap_main_icons
from python.tap_all_icons import tap_all_icons

# 从Config类导入常量
TARGET_ICONS = Config.TARGET_ICONS
HOMEPAGE_TARGET_ICONS = Config.HOMEPAGE_TARGET_ICONS

# 问题类型常量
ISSUE_TYPE_APP = "app"
ISSUE_TYPE_TEST = "test"

# 美团App的包名常量
MEITUAN_PACKAGE_NAME = 'com.sankuai.meituan'

DEFAULT_LOGGER = logging.getLogger(__name__)

# 添加全局字典来跟踪设备测试问题
device_issues = {}

def check_android_device_online(udid, logger):
    """检查 Android 设备是否在线"""
    try:
        stdout, stderr = device_operate_external.execute_adb_command(udid, "devices")
        if not stdout:
            logger.warning("没有检测到任何 Android 设备")
            return False
        # 解析 adb devices 输出，跳过第一行的 "List of devices attached"
        connected_devices = [line.split()[0] for line in stdout.strip().split('\n')[1:] if line.strip() and 'device' in line]
        return udid in connected_devices
    except Exception as e:
        logger.error(f"检查 Android 设备状态时发生错误: {e}")
        return False

def check_adb_environment(logger):
    """检查ADB环境和版本
    
    Args:
        logger: 日志对象
    
    Returns:
        bool: ADB环境是否正常
    """
    try:
        # 检查ADB版本
        process = subprocess.Popen("adb version", shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        stdout, stderr = process.communicate()
        
        if process.returncode != 0 or stderr:
            logger.error(f"ADB命令执行失败: {stderr.decode('utf-8') if stderr else '未知错误'}")
            return False
        
        version_output = stdout.decode('utf-8').strip()
        logger.info(f"ADB版本信息: {version_output}")
        
        # 检查连接的设备
        process = subprocess.Popen("adb devices", shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        stdout, stderr = process.communicate()
        
        if process.returncode != 0 or stderr:
            logger.error(f"无法获取ADB设备列表: {stderr.decode('utf-8') if stderr else '未知错误'}")
            return False
        
        devices_output = stdout.decode('utf-8').strip()
        logger.info(f"ADB设备列表: {devices_output}")
        
        # 检测是否有设备连接
        if "List of devices attached" in devices_output and len(devices_output.split('\n')) > 1:
            return True
        else:
            logger.warning("没有检测到已连接的ADB设备")
            return False
    
    except Exception as e:
        logger.error(f"检查ADB环境时发生错误: {e}")
        return False

def test_meituan_android(device_id, logger=None):
    """
    Android版本的美团App测试函数 - 完全使用ADB命令实现，不依赖Appium
    """
    if logger is None:
        logger = DEFAULT_LOGGER

    # 记录测试开始时间
    start_time = time.time()
    udid = device_id # device_id需要在finally之外定义
    # 获取设备的名称避免后续出错
    device_name = get_device_status(device_id)['device_name']

    # 初始化本轮测试的问题标记，确保在 try 块之前，以便 finally 中可用
    # 如果 try 块完全失败，device_issues 可能未初始化
    if device_id not in device_issues:
        device_issues[device_id] = {
            'app_issue': False,
            'test_issue': False
        }
    
    # 用于finally块统计的变量
    is_special_round_executed = False
    final_completed_actions_for_round = 0
    final_total_actions_for_round = 0
    completed_icons_main_loop = 0


    try:
        # 检查ADB环境
        logger.info("开始检查ADB环境...")
        if not check_adb_environment(logger):
            logger.error("ADB环境检查失败，可能影响测试执行")
            # 继续执行，但记录警告
        
        # 首先确保问题日志记录器已初始化，在所有操作之前完成这一步
        script_dir = os.path.dirname(os.path.abspath(__file__))
        project_root = os.path.dirname(script_dir)
        initialize_issue_logger(project_root)
        
        # 从设备状态中获取当前轮次信息（现在由split_devices.py管理）
        status_info = get_device_status(device_id)
        session_round_num = status_info.get('session_round_num', 1)
        current_round = status_info.get('round_num', 1)
        
        # 记录轮次信息日志
        logger.info(f"开始第 {session_round_num} 轮测试 (历史轮次: {current_round})")
        
        # 问题日志记录器初始化完成日志
        logger.info(f"问题日志记录器初始化完成 - 进程ID: {os.getpid()}")
        
        # 生成当前测试的唯一执行ID
        execution_id = f"{device_id}_{current_round}_{int(start_time)}"
        logger.info(f"生成测试执行ID: {execution_id}")
        
        # 添加变量跟踪已完成的icon数量 (此变量用于常规流程)
        # completed_icons = 0 # 改名为 completed_icons_main_loop 并在try开始处初始化
        total_icons_main_loop = Config.get_actual_icon_count()  # 使用Config类的方法获取实际图标数量
        
        # 更新设备状态
        update_device_status(device_id, {
            'status': 'running',
            'last_update': time.time(),
            'session_round_num': session_round_num
        })

        logger.info(f"开始测试设备：{device_name} (UDID: {device_id}) - 第 {session_round_num} 轮（历史轮次: {current_round}）")

        # 设备在线检测现在由split_devices.py统一处理，这里不再需要检测
        logger.info(f"设备 {device_name} (UDID: {device_id}) 已通过预检查，开始测试流程")

        # 动态获取当前文件的上级目录，并创建设备专属的截图目录
        current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        device_screenshot_dir = os.path.join(current_dir, 'screenshot', device_name)
        device_error_dir = os.path.join(device_screenshot_dir, 'error')
        os.makedirs(device_screenshot_dir, exist_ok=True)
        os.makedirs(device_error_dir, exist_ok=True)
        update_device_status(udid, {
            'device_screenshot_dir': device_screenshot_dir,
            'device_error_dir': device_error_dir
        })

        try:
            # 重启应用
            logger.info("重启美团应用...")
            device_operate_external.restart_app(
                driver=None,
                udid=udid,
                logger=logger
            )
            time.sleep(10)

            # 获取屏幕尺寸
            screen_size_output, _ = device_operate_external.execute_adb_command(udid, "shell wm size")
            screen_size = {"width": 1080, "height": 2400}  # 默认值
            if "Physical size:" in screen_size_output:
                size_match = re.search(r'Physical size: (\d+)x(\d+)', screen_size_output)
                if size_match:
                    screen_size["width"] = int(size_match.group(1))
                    screen_size["height"] = int(size_match.group(2))
            logger.info(f"实际屏幕尺寸: {screen_size}")

            # 计算缩放比例（通过临时截图获取分辨率）
            timestamp = time.strftime('%Y%m%d_%H%M%S')
            temp_screenshot_path = os.path.join(device_screenshot_dir, f'临时_缩放比例计算_{device_name}_{timestamp}.png')
            screenshot_success = device_operate_external.take_screenshot(
                driver=None,
                udid=udid,
                screenshot_path=temp_screenshot_path,
                logger=logger
            )
            
            if screenshot_success == "success" and os.path.exists(temp_screenshot_path) and os.path.getsize(temp_screenshot_path) > 0:
                # 获取缩放比例 通过分析截图分辨率和屏幕分辨率的比例
                screenshot_image = Image.open(temp_screenshot_path)
                screenshot_size = screenshot_image.size
                scale_ratio = screenshot_size[0] / screen_size['width']
                logger.info(f"计算得到新的缩放比例: {scale_ratio}")
                update_device_status(udid, {
                    'scale_ratio': scale_ratio
                })
                
                # 删除临时截图
                os.remove(temp_screenshot_path)
            else:
                logger.warning("临时截图失败，使用默认缩放比例")
            
            logger.info("准备开始具体的测试流程...")

            # 特殊测试流程：第9轮执行推荐测试，第10轮执行全量图标测试
            if current_round % 10 == 9:
                is_special_round_executed = True
                logger.info(f"当前历史轮次 {current_round} 是每 10 轮的第 9 轮，开始执行【首页上下滑测试】：当前通过点击美团首页“推荐”按钮实现上下滑")
                
                # 执行推荐点击测试
                recommend_completed, recommend_total = swipe_homepage_recommend(
                    driver=None,
                    udid=udid,
                    logger=logger,
                    execution_id=execution_id,
                    device_issues=device_issues
                )
                
                # 记录推荐测试结果
                final_total_actions_for_round = recommend_total
                final_completed_actions_for_round = recommend_completed
                
            elif current_round % 10 == 0:
                is_special_round_executed = True
                logger.info(f"当前历史轮次 {current_round} 是每 10 轮的第 10 轮，开始执行【全量金刚区图标测试】：测试美团首页所有频道图标")
                
                # 执行全量金刚区图标测试
                completed, total, screen_count, all_screen_icons_list = tap_all_icons(
                    driver=None,
                    udid=udid,
                    logger=logger,
                    execution_id=execution_id,
                    device_issues=device_issues,
                    perform_ui_check=True
                )
                final_completed_actions_for_round = completed
                logger.info(f"全量金刚区图标测试完成，共测试 {total} 个图标，成功完成 {screen_count} 屏")

            if not is_special_round_executed:
                # 设置常规流程的总操作数
                logger.info(f"开始执行【常规测试】：测试美团首页主要频道图标")
                final_total_actions_for_round = total_icons_main_loop
                
                # 使用新的tap_main_icons函数进行固定图标测试
                completed_icons_main_loop, total_icons_main_loop = tap_main_icons(
                    driver=None,
                    udid=udid,
                    logger=logger,
                    execution_id=execution_id,
                    device_issues=device_issues
                )
                
                # 将常规流程的完成数赋值给最终变量
                final_completed_actions_for_round = completed_icons_main_loop

        except Exception as e:
            logger.error(f"测试过程中发生全局错误: {e}")
            # 标记本轮测试有测试流程问题
            device_issues[device_id]['test_issue'] = True
            
            # 获取详细的错误信息和堆栈跟踪
            error_traceback = traceback.format_exc()
            logger.error(f"全局错误详情: {error_traceback}")
            
            # 提取更有用的错误信息
            error_type = type(e).__name__
            error_message = str(e)
            
            # 根据错误类型提供更具体的错误描述
            error_description = f"{error_type}: {error_message}"
            if "timeout" in error_message.lower():
                error_description = f"操作超时: {error_message}"
            elif "no such element" in error_message.lower():
                error_description = f"未找到元素: {error_message}"
            elif "stale element reference" in error_message.lower():
                error_description = f"元素已过期: {error_message}"
            
            # 记录问题
            log_device_issue(
                device_id=device_id,
                device_name=device_name,
                round_num=current_round,
                issue_type=ISSUE_TYPE_TEST,
                issue_details=f"测试过程中发生全局错误: {error_description}",
                execution_id=execution_id
            )

    except Exception as e:
        logger.error(f"测试过程中发生全局错误: {e}")
        # 标记本轮测试有测试流程问题
        device_issues[device_id]['test_issue'] = True
        
        # 获取详细的错误信息和堆栈跟踪
        error_traceback = traceback.format_exc()
        logger.error(f"全局错误详情: {error_traceback}")
        
        # 提取更有用的错误信息
        error_type = type(e).__name__
        error_message = str(e)
        
        # 根据错误类型提供更具体的错误描述
        error_description = f"{error_type}: {error_message}"
        if "timeout" in error_message.lower():
            error_description = f"操作超时: {error_message}"
        elif "no such element" in error_message.lower():
            error_description = f"未找到元素: {error_message}"
        elif "stale element reference" in error_message.lower():
            error_description = f"元素已过期: {error_message}"
        
        # 记录问题
        log_device_issue(
            device_id=device_id,
            device_name=device_name,
            round_num=current_round,
            issue_type=ISSUE_TYPE_TEST,
            issue_details=f"测试过程中发生全局错误: {error_description}",
            execution_id=execution_id
        )

    finally:
        # 计算测试耗时
        test_duration = time.time() - start_time
        
        # 获取当前轮次的问题状态
        # device_issues 应该在 finally 之前已经被正确设置
        app_issue = device_issues[device_id].get('app_issue', False) # 使用get防止KeyError
        test_issue = device_issues[device_id].get('test_issue', False)
        
        status = ''
        # 根据测试问题状态设置测试状态
        if test_issue or app_issue or final_completed_actions_for_round < final_total_actions_for_round:
            status = 'partial_completed'
        else:
            status = 'completed'
        
        log_message_status_type = "额外首页测试轮次" if is_special_round_executed else "常规测试轮次"
        logger.info(f"更新设备状态({log_message_status_type})为 {status}: {device_name}, 第 {session_round_num} 轮（历史轮次: {current_round}）, 耗时: {test_duration:.2f}秒, 应用问题: {app_issue}, 测试问题: {test_issue}, 完成操作: {final_completed_actions_for_round}/{final_total_actions_for_round}")
        
        update_device_status(udid, {
            'status': status,
            'last_update': time.time(),
            'test_duration': test_duration,
            'app_issue': app_issue,
            'test_issue': test_issue
        })
        
        # 重置本轮测试的问题标记
        device_issues[device_id] = {
            'app_issue': False,
            'test_issue': False
        }
        
        # 简单检查设备是否仍在线（不进行重连，由split_devices.py统一处理）
        if not check_android_device_online(udid, logger):
            logger.warning(f"测试完成后发现设备 {device_name} (UDID: {udid}) 已离线")
            update_device_status(udid, {
                'status': 'offline_after_test',
                'last_update': time.time()
            })
        else:
            logger.info(f"测试完成后设备 {device_name} (UDID: {udid}) 仍在线")

        # 在所有测试完成后清理截图（不管是常规测试还是特殊测试）
        try:
            screenshot_count = 0
            for filename in os.listdir(device_screenshot_dir):
                file_path = os.path.join(device_screenshot_dir, filename)
                # 只删除文件（不删除目录），且不处理error目录
                if os.path.isfile(file_path) and 'error' not in file_path:
                    os.remove(file_path)
                    screenshot_count += 1
            logger.info(f"本轮测试清理完成：已删除设备 {device_name} 的 {screenshot_count} 张非error截图")
        except Exception as e:
            logger.error(f"清理截图时发生错误: {e}")
            
        # 创建并返回TestResult对象
        from python.split_devices import TestResult
        test_result = TestResult(
            device_id=device_id,
            device_name=device_name,
            platform='android',
            timestamp=time.strftime('%Y-%m-%d %H:%M:%S'),
            status=status,
            error_message=None if status == 'completed' else (f"{log_message_status_type}未完全完成或出现问题"),
            test_duration=test_duration,
            app_issue=app_issue,
            test_issue=test_issue,
            completed_icons=final_completed_actions_for_round, # 使用新的统计变量
            total_icons=final_total_actions_for_round # 使用新的统计变量
        )
        
        return test_result

def get_crash_logs(udid, package_name, logger):
    """
    尝试获取应用的崩溃日志
    
    :param udid: 设备ID
    :param package_name: 应用包名
    :param logger: 日志对象
    :return: str - 崩溃日志，如果没有找到则返回空字符串
    """
    try:
        # 获取最近的系统日志
        stdout, _ = device_operate_external.execute_adb_command(udid, "shell logcat -d -t 200")
        
        # 筛选与应用相关的错误日志
        crash_lines = []
        for line in stdout.split('\n'):
            if package_name in line and ('E/' in line or 'FATAL' in line or 'Exception' in line or 'Error' in line):
                crash_lines.append(line)
        
        # 如果找到崩溃日志，返回它们
        if crash_lines:
            logger.info(f"找到 {len(crash_lines)} 行崩溃日志")
            return '\n'.join(crash_lines)
        
        logger.info("未找到崩溃日志")
        return ""
        
    except Exception as e:
        logger.error(f"获取崩溃日志时发生错误: {e}")
        return ""
