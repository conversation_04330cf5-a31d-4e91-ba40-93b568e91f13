import time
import os
from PIL import Image
from python.device_common import (
    check_is_homepage, check_and_handle_popup_after_screenshot,
    after_tap_test, device_operate_external
)
from python.upload_image import get_image_url
from python.notify_user import send_individual_kingkong_message
from python.heartbeat_monitor import log_device_issue
import traceback
from get_category_icons import get_category_icons_by_row_count
from python.get_message_from_horus import find_back_button_location, find_back_button_location_especial
from python.device_status_manager import get_device_status, update_device_status, manager
from python.config import Config

def tap_all_icons(
    driver=None,
    udid=None,
    logger=None,
    execution_id=None,
    device_issues=None,
    perform_ui_check=True,
    debug_mode=False
):
    """
    首页频道区多屏批量点击测试，自动识别每一屏 icon，遇到"全部服务"停止。
    
    Args:
        driver: WebDriver实例（iOS必需）
        udid: 设备UDID（必需）
        logger: 日志对象
        execution_id: 执行ID
        device_issues: 设备问题状态字典
        perform_ui_check: 是否执行UI检查，默认True
        debug_mode: 调试模式，每屏只点击一个图标，默认True
        
    Returns:
        tuple: (completed_count, total_count, screen_count, all_screen_icons_list)
    """
    if udid is None:
        logger.error("udid 参数不能为空")
        return 0, 0, 0, []
    if device_issues is None:
        logger.error("device_issues 参数不能为空")
        return 0, 0, 0, []
    
    device_name = get_device_status(udid)["device_name"]
    scale_ratio = get_device_status(udid)["scale_ratio"]
    platform = get_device_status(udid)["platform"]
    current_round = get_device_status(udid).get('round_num')
    session_round_num = get_device_status(udid).get('session_round_num')
    device_screenshot_dir = get_device_status(udid).get('device_screenshot_dir')
    device_error_dir = get_device_status(udid).get('device_error_dir')
    
    completed_count = 0
    total_count = 0
    
    # 首先检测是否到了首页
    from python.device_common import go_back_homepage
    homepage_success = go_back_homepage(
        driver=driver,
        udid=udid,
        logger=logger,
        execution_id=execution_id,
        device_issues=device_issues,
        test_context_name="全量图标测试"
    )
    
    if not homepage_success:
        logger.error("全量图标测试：无法确保在美团首页，终止测试")
        return 0, 0, 0, []
    
    # 首页检查成功后，重新获取首页截图用于后续的图标识别
    logger.info("开始获取首页截图用于图标识别...")
    timestamp = time.strftime('%Y%m%d_%H%M%S')
    homepage_screenshot_path = os.path.join(device_screenshot_dir, f'全量图标测试首页_{device_name}_{timestamp}.png')
    
    try:
        # 获取首页截图，这里不需要OCR，因为后续会用get_category_icons_by_row_count
        screenshot_result = device_operate_external.take_screenshot(
            driver=driver,
            udid=udid,
            screenshot_path=homepage_screenshot_path,
            logger=logger
        )
        if screenshot_result != "success":
            logger.error(f"获取首页截图失败: {screenshot_result}")
            device_issues[udid]['test_issue'] = True
            return 0, 0, 0, []
        logger.info(f"首页截图已保存: {homepage_screenshot_path}")
    except Exception as e:
        logger.error(f"获取首页截图失败: {e}")
        device_issues[udid]['test_issue'] = True
        return 0, 0, 0, []
    
    current_screenshot_path = homepage_screenshot_path

    # 核心状态变量
    seen_icons = set()  # 已测试过的图标名称集合
    current_screen_index = 0  # 当前屏索引（从0开始）
    current_icon_index = 0  # 当前屏内图标索引（从0开始）
    screen_count = 0  # 总屏数计数

    # 缓存变量
    cached_y_ratio = None  # 第一排icon的纵坐标比例
    screen_height = None

    # 记录变量（用于调试和统计）
    all_screen_icons_list = []  # 记录每一屏的 icon 名集合
    all_tested_icons = []  # 记录已测试的图标顺序
    completed_count = 0  # 已完成的图标数量
    total_count = 0  # 总图标数量
    icon_click_count = 0  # 图标点击计数
    consecutive_loop_count = 0  # 连续循环检测计数
    max_consecutive_loops = 3  # 最大连续循环次数
    consecutive_missing_icons = 0  # 连续找不到的图标数量
    max_consecutive_missing = 5  # 最大连续找不到图标数量

    def get_current_screen_icons():
        """获取当前屏的图标信息，返回排序后的图标列表"""
        icons_dict = get_category_icons_by_row_count(current_screenshot_path, channel_keyword="外卖", verbose=True)
        if not icons_dict:
            return None, None

        # 排除"全部服务"和"更多服务"
        last_screen_keywords = ["全部服务", "更多服务"]
        is_last_screen = False
        for keyword in last_screen_keywords:
            if keyword in icons_dict:
                icons_dict.pop(keyword)
                is_last_screen = True

        # 按照点击顺序排序（按y坐标升序，再按x坐标升序）
        icons_list_sorted = sorted(icons_dict.items(), key=lambda item: (item[1]['y'], item[1]['x']))
        return icons_list_sorted, is_last_screen

    # 主循环：逐屏测试图标
    while True:
        logger.info(f"==================== 开始测试第 {current_screen_index + 1} 屏 ====================")

        # 1. 识别当前屏图标
        current_screen_icons, is_last_screen = get_current_screen_icons()

        if current_screen_icons is None:
            logger.error(f"全量图标测试：第 {current_screen_index + 1} 屏未识别到频道区icon，截图路径: {current_screenshot_path}")

            # 获取当前截图URL用于错误报告
            current_screenshot_url = get_image_url(current_screenshot_path)

            # 发送通知和记录问题
            failure_time = time.strftime('%Y-%m-%d %H:%M:%S')
            failure_message = (
                f"时间：{failure_time}\n"
                f"设备：{device_name}\n"
                f"轮次：第 {session_round_num} 轮（历史轮次: {current_round}）\n"
                f"问题：第 {current_screen_index + 1} 屏无法识别频道区图标\n"
                f"详情：在当前页面未识别到任何频道区图标，可能页面异常或图标识别失败\n"
                f"图片URL：{current_screenshot_url}"
            )
            send_individual_kingkong_message(failure_message, ['cuijie12'])
            device_issues[udid]['app_issue'] = True
            log_device_issue(
                device_id=udid,
                device_name=device_name,
                round_num=current_round,
                issue_type='app',
                issue_details=f"第 {current_screen_index + 1} 屏：未识别到任何频道区图标",
                page_name=f"频道区第{current_screen_index + 1}屏",
                screenshot_url=current_screenshot_url,
                execution_id=execution_id
            )
            break

        # 2. 第一次循环时计算并缓存纵坐标比例
        if cached_y_ratio is None and current_screen_icons:
            screen_info = device_operate_external.get_device_screen_info(driver=driver, udid=udid, logger=logger)
            screen_height = screen_info.get('height')
            if screen_height:
                first_row_y = current_screen_icons[0][1]['y']
                first_row = [item for item in current_screen_icons if abs(item[1]['y'] - first_row_y) < 10]
                if first_row:
                    avg_y = sum(item[1]['y'] for item in first_row) / len(first_row)
                    cached_y_ratio = avg_y / screen_height
                    logger.info(f"缓存第一排icon纵坐标比例: {cached_y_ratio:.3f}")
                else:
                    cached_y_ratio = current_screen_icons[0][1]['y'] / screen_height
                    logger.info(f"缓存第一个icon纵坐标比例: {cached_y_ratio:.3f}")
            else:
                logger.warning("无法获取屏幕高度，使用默认纵坐标比例")
                cached_y_ratio = 0.4

        # 3. 记录当前屏信息
        current_screen_icon_names = [icon_name for icon_name, _ in current_screen_icons]
        all_screen_icons_list.append(set(current_screen_icon_names))
        logger.info(f"第 {current_screen_index + 1} 屏识别到 {len(current_screen_icons)} 个图标: {current_screen_icon_names}")

        # 4. 检查是否有重复图标（表示已经循环回来了）
        if current_screen_index > 0:
            current_screen_set = set(current_screen_icon_names)
            for prev_screen_set in all_screen_icons_list[:-1]:  # 排除当前屏
                if current_screen_set & prev_screen_set:  # 有交集
                    consecutive_loop_count += 1
                    logger.info(f"第 {current_screen_index + 1} 屏出现了之前屏的图标，循环检测次数: {consecutive_loop_count}")

                    if consecutive_loop_count >= max_consecutive_loops:
                        logger.info(f"连续检测到 {consecutive_loop_count} 次循环，强制结束测试")
                        is_last_screen = True
                    break
            else:
                # 如果没有检测到循环，重置计数器
                consecutive_loop_count = 0

        # 5. 如果检测到循环，直接跳出主循环
        if is_last_screen:
            logger.info(f"检测到屏幕循环，测试结束")
            break

        # 6. 从当前图标索引开始测试（支持中断恢复）
        icons_to_test = current_screen_icons[current_icon_index:]
        if not icons_to_test:
            logger.info(f"第 {current_screen_index + 1} 屏所有图标已测试完成")
        else:
            # 过滤掉已测试过的图标
            icons_to_test = [(name, pos) for name, pos in icons_to_test if name not in seen_icons]
            total_count += len(icons_to_test)

        # 7. 累加本屏要测试的图标总数到设备状态
        if icons_to_test:
            add_total_count = len(icons_to_test)
            if debug_mode:
                add_total_count = 1  # debug模式下只测试1个
            success = manager.add_icon_count(udid, total_add=add_total_count, completed_add=0)
            if success:
                logger.info(f"第 {current_screen_index + 1} 屏累加图标总数: {add_total_count}")
            else:
                logger.warning(f"更新设备累计图标总数失败")

        # 7. debug模式处理
        if debug_mode and icons_to_test:
            icons_to_test = [icons_to_test[0]]
            logger.info(f"调试模式：第 {current_screen_index + 1} 屏只测试第一个图标")

        # 8. 逐个测试当前屏的图标
        for test_index, (icon_name, pos) in enumerate(icons_to_test):
            actual_icon_index = current_icon_index + test_index  # 在当前屏中的实际索引
            logger.info(f"==================== 第 {current_screen_index + 1} 屏第 {actual_icon_index + 1} 个图标「{icon_name}」 ====================")

            # 记录已测试图标
            seen_icons.add(icon_name)
            all_tested_icons.append(f"第{current_screen_index + 1}屏第{actual_icon_index + 1}个:{icon_name}")

            # 更新当前图标索引
            current_icon_index = actual_icon_index
            seen_icons.add(icon_name)
            x, y = pos['x'], pos['y']
            icon_click_count += 1  # 增加计数
            try:
                update_device_status(udid, {
                    'status': 'running',
                    'last_update': time.time()
                })

                # 重新识别当前屏图标位置（处理返回后页面重建的情况）
                logger.info(f"重新识别当前屏图标位置...")
                timestamp = time.strftime('%Y%m%d_%H%M%S')
                recheck_screenshot_path = os.path.join(
                    device_screenshot_dir,
                    f'重新识别第{current_screen_index + 1}屏_{icon_name}_{device_name}_{timestamp}.png'
                )

                # 截图
                screenshot_result = device_operate_external.take_screenshot(
                    driver=driver,
                    udid=udid,
                    screenshot_path=recheck_screenshot_path,
                    logger=logger
                )
                if screenshot_result != "success":
                    logger.error(f"重新识别截图失败: {screenshot_result}")
                    continue

                # 重新识别图标
                # 临时更新current_screenshot_path用于get_current_screen_icons函数
                temp_current_path = current_screenshot_path
                current_screenshot_path = recheck_screenshot_path
                updated_screen_icons, _ = get_current_screen_icons()
                current_screenshot_path = temp_current_path  # 恢复

                # 重新识别图标并更新坐标
                if updated_screen_icons is None:
                    logger.error(f"重新识别图标失败，跳过图标「{icon_name}」")
                    continue

                # 查找当前图标的最新位置
                updated_icon_pos = None
                for updated_name, updated_pos in updated_screen_icons:
                    if updated_name == icon_name:
                        updated_icon_pos = updated_pos
                        break

                if updated_icon_pos is None:
                    logger.error(f"重新识别后未找到图标「{icon_name}」，可能已不在当前屏")
                    logger.info(f"当前屏图标: {[name for name, _ in updated_screen_icons]}")

                    # 检查图标是否在其他屏，如果是则尝试滑动回去
                    target_screen_index = None
                    for screen_idx, screen_icons in enumerate(all_screen_icons_list):
                        if icon_name in [icon for icon in screen_icons]:
                            target_screen_index = screen_idx
                            break

                    if target_screen_index is not None and target_screen_index != current_screen_index:
                        logger.info(f"图标「{icon_name}」可能在第 {target_screen_index + 1} 屏，尝试滑动回去")
                        # 计算需要滑动的方向和次数
                        swipe_count = target_screen_index - current_screen_index
                        if swipe_count > 0:
                            # 向右滑动（到后面的屏）
                            for _ in range(swipe_count):
                                if cached_y_ratio is not None:
                                    device_operate_external.swipe(
                                        driver=driver,
                                        udid=udid,
                                        from_x_ratio=0.8,
                                        from_y_ratio=cached_y_ratio,
                                        to_x_ratio=0.2,
                                        to_y_ratio=cached_y_ratio,
                                        duration=0.5,
                                        logger=logger
                                    )
                                    time.sleep(1)
                        else:
                            # 向左滑动（到前面的屏）
                            for _ in range(abs(swipe_count)):
                                if cached_y_ratio is not None:
                                    device_operate_external.swipe(
                                        driver=driver,
                                        udid=udid,
                                        from_x_ratio=0.2,
                                        from_y_ratio=cached_y_ratio,
                                        to_x_ratio=0.8,
                                        to_y_ratio=cached_y_ratio,
                                        duration=0.5,
                                        logger=logger
                                    )
                                    time.sleep(1)

                        # 更新当前屏索引
                        current_screen_index = target_screen_index

                        # 重新截图并识别
                        timestamp = time.strftime('%Y%m%d_%H%M%S')
                        current_screenshot_path = os.path.join(
                            device_screenshot_dir,
                            f'滑动回第{current_screen_index + 1}屏_{device_name}_{timestamp}.png'
                        )
                        device_operate_external.take_screenshot(
                            driver=driver,
                            udid=udid,
                            screenshot_path=current_screenshot_path,
                            logger=logger
                        )

                        # 重新识别图标
                        temp_current_path = current_screenshot_path
                        current_screenshot_path = current_screenshot_path
                        updated_screen_icons, _ = get_current_screen_icons()

                        # 再次查找图标
                        for updated_name, updated_pos in updated_screen_icons:
                            if updated_name == icon_name:
                                updated_icon_pos = updated_pos
                                logger.info(f"滑动后重新找到图标「{icon_name}」")
                                break

                    if updated_icon_pos is None:
                        logger.error(f"尝试滑动后仍未找到图标「{icon_name}」，跳过该图标")
                        consecutive_missing_icons += 1

                        # 如果连续多个图标都找不到，可能需要滑动到下一屏
                        if consecutive_missing_icons >= max_consecutive_missing:
                            logger.warning(f"连续 {consecutive_missing_icons} 个图标找不到，可能需要滑动到下一屏")
                            consecutive_missing_icons = 0  # 重置计数器
                            break  # 跳出当前屏的图标测试循环
                        continue
                else:
                    # 找到图标，重置连续找不到的计数器
                    consecutive_missing_icons = 0

                # 更新图标坐标
                original_pos = pos.copy()
                pos.update(updated_icon_pos)
                x, y = pos['x'], pos['y']
                logger.info(f"更新图标「{icon_name}」坐标: {original_pos} -> {pos}")
                
                scaled_x = x / scale_ratio
                scaled_y = y / scale_ratio
                logger.info(f"点击频道图标「{icon_name}」坐标: ({int(scaled_x)}, {int(scaled_y)})")
                tap_success = device_operate_external.tap(
                    driver=driver,
                    udid=udid,
                    x=scaled_x,
                    y=scaled_y,
                    logger=logger
                )
                if not tap_success:
                    logger.error(f"点击频道图标「{icon_name}」失败")
                    continue
                time.sleep(10)
                timestamp = time.strftime('%Y%m%d_%H%M%S')
                screenshot_path = os.path.join(
                    device_screenshot_dir,
                    f'频道图标_{icon_name}_{device_name}_{timestamp}.png'
                )
                # 针对特定页面跳过UI bug检测
                _perform_ui_check = perform_ui_check
                skip_ui_check_icons = ["借钱", "跑腿", "天天现金"]
                if icon_name in skip_ui_check_icons:
                    _perform_ui_check = False
                    logger.info(f"图标「{icon_name}」跳过UI检测")
                
                # 执行截图操作
                logger.info(f"开始对频道图标「{icon_name}」页面进行截图...")
                screenshot_result = device_operate_external.take_screenshot(
                    driver=driver,
                    udid=udid,
                    screenshot_path=screenshot_path,
                    logger=logger
                )
                
                if screenshot_result != "success":
                    logger.error(f"频道图标「{icon_name}」页面截图失败: {screenshot_result}")
                    if screenshot_result == "device_offline":
                        device_issues[udid]['test_issue'] = True
                        break  # 设备掉线，直接跳出循环
                    continue  # 其他错误，跳过当前图标
                
                logger.info(f"频道图标「{icon_name}」页面截图已保存: {screenshot_path}")
                
                # 上传截图获取URL
                screenshot_url = get_image_url(screenshot_path)
                logger.info(f"频道图标「{icon_name}」页面截图已上传，URL: {screenshot_url} 轮次: {current_round}")
                
                
                # 使用 after_tap_test 进行完整检测
                test_result = after_tap_test(
                    driver=driver,
                    udid=udid,
                    screenshot_path=screenshot_path,
                    screenshot_url=screenshot_url,
                    logger=logger,
                    scale_ratio=scale_ratio,
                    current_round=current_round,
                    session_round_num=session_round_num,
                    execution_id=execution_id,
                    page_name=f"频道图标_{icon_name}",
                    device_error_dir=device_error_dir,
                    perform_ui_check=_perform_ui_check,
                    platform=platform,
                    device_issues=device_issues,
                )
                
                # 更新截图信息
                final_screenshot_path = test_result['screenshot_path']
                final_screenshot_url = test_result['screenshot_url']
                last_marked_image_url = test_result['marked_image_url']
                
                # 根据检测结果决定是否继续
                if not test_result['success']:
                    if test_result['app_crashed']:
                        logger.error(f"频道图标「{icon_name}」测试因 app 闪退中断")
                    elif test_result['jump_failed']:
                        logger.error(f"频道图标「{icon_name}」测试因跳转失败中断")
                    else:
                        logger.error(f"频道图标「{icon_name}」测试失败: {test_result['error_reason']}")
                    continue
                
                # 如果应用进入后台但已恢复，记录警告信息
                if test_result['app_background']:
                    logger.warning(f"频道图标「{icon_name}」页面app异常退到后台，已重新拉回前台，继续当前图标测试流程")
                
                # 添加页面文本检测逻辑
                if icon_name in Config.CHANNEL_ICONS_TEXT_CHECK:
                    logger.info(f"开始进行频道图标「{icon_name}」页面文本存在性检测...")
                    
                    # 等待页面稳定后进行文本检测
                    time.sleep(5)
                    
                    # 获取用于文本检测的截图
                    timestamp = time.strftime('%Y%m%d_%H%M%S')
                    text_check_screenshot_path = os.path.join(
                        device_screenshot_dir,
                        f'频道文本检测_{icon_name}_{device_name}_{timestamp}.png'
                    )
                    
                    # 截图
                    text_check_screenshot_result = device_operate_external.take_screenshot(
                        driver=driver,
                        udid=udid,
                        screenshot_path=text_check_screenshot_path,
                        logger=logger
                    )
                    
                    if text_check_screenshot_result == "success":
                        logger.info(f"频道图标「{icon_name}」页面文本检测截图已保存: {text_check_screenshot_path}")
                        
                        # 上传截图获取URL
                        try:
                            text_check_screenshot_url = get_image_url(text_check_screenshot_path)
                            logger.info(f"频道图标「{icon_name}」页面文本检测截图已上传，URL: {text_check_screenshot_url} 轮次: {current_round}")
                            
                            # 获取对应的检测文本
                            expected_text_config = Config.CHANNEL_ICONS_TEXT_CHECK[icon_name]
                            
                            # 判断是单个文本还是多个文本
                            if isinstance(expected_text_config, str):
                                # 单个文本检测
                                expected_texts = [expected_text_config]
                                logger.info(f"频道图标「{icon_name}」页面开始检测目标文本: '{expected_text_config}'")
                            elif isinstance(expected_text_config, list):
                                # 多个文本检测
                                expected_texts = expected_text_config
                                logger.info(f"频道图标「{icon_name}」页面开始检测目标文本（任一匹配即可）: {expected_texts}")
                            else:
                                logger.error(f"频道图标「{icon_name}」的文本检测配置格式错误: {expected_text_config}")
                                continue
                            
                            # 调用多文本检测函数（更高效，只调用一次OCR）
                            from python.device_common import check_multiple_elements_exist
                            
                            # 使用多文本检测，require_all=False表示找到任意一个即可
                            check_result = check_multiple_elements_exist(
                                screenshot_url=text_check_screenshot_url,
                                target_texts=expected_texts,
                                device_name=device_name,
                                logger=logger,
                                match_mode='contains',  # 使用包含匹配模式
                                require_all=False  # 找到任意一个文本即为成功
                            )
                            
                            text_exists = check_result['overall_result']
                            matched_text = None
                            
                            # 获取匹配到的文本
                            if text_exists and check_result['found_elements']:
                                matched_text = check_result['found_elements'][0]['target']  # 取第一个匹配的目标文本
                                logger.info(f"频道图标「{icon_name}」页面找到匹配文本: '{matched_text}'")
                                logger.info(f"检测结果: 找到 {check_result['found_count']}/{check_result['total_count']} 个目标文本")
                            else:
                                logger.info(f"频道图标「{icon_name}」页面未找到任何目标文本")
                                if check_result['missing_elements']:
                                    logger.info(f"未找到的文本: {check_result['missing_elements']}")
                            
                            if text_exists:
                                if len(expected_texts) == 1:
                                    logger.info(f"频道图标「{icon_name}」页面文本检测通过：成功找到目标文本 '{matched_text}'")
                                else:
                                    logger.info(f"频道图标「{icon_name}」页面文本检测通过：在多个候选文本中成功找到 '{matched_text}'")
                            else:
                                if len(expected_texts) == 1:
                                    logger.error(f"频道图标「{icon_name}」页面文本检测失败：未找到目标文本 '{expected_texts[0]}'，可能未正确跳转到目标页面")
                                else:
                                    logger.error(f"频道图标「{icon_name}」页面文本检测失败：未找到任何目标文本 {expected_texts}，可能未正确跳转到目标页面")
                                
                                # 移动检测失败的截图到error目录
                                error_path = device_operate_external.move_screenshot_to_error(text_check_screenshot_path, device_error_dir)
                                logger.info(f"频道图标文本检测失败截图已移动到: {error_path}")
                                
                                # 发送通知
                                failure_time = time.strftime('%Y-%m-%d %H:%M:%S')
                                if len(expected_texts) == 1:
                                    failure_detail = f"未找到目标文本 '{expected_texts[0]}'，可能未正确跳转到目标页面"
                                    issue_detail = f"频道图标「{icon_name}」页面文本检测失败，未找到目标文本 '{expected_texts[0]}'"
                                else:
                                    failure_detail = f"未找到任何目标文本 {expected_texts}，可能未正确跳转到目标页面"
                                    issue_detail = f"频道图标「{icon_name}」页面文本检测失败，未找到任何目标文本 {expected_texts}"
                                
                                failure_message = (
                                    f"时间：{failure_time}\n"
                                    f"设备：{device_name}\n"
                                    f"轮次：第 {session_round_num} 轮（历史轮次: {current_round}）\n"
                                    f"问题：频道图标「{icon_name}」页面文本检测失败\n"
                                    f"详情：{failure_detail}\n"
                                    f"图片URL：{text_check_screenshot_url}"
                                )
                                send_individual_kingkong_message(failure_message, ['cuijie12'])
                                
                                # 标记本轮测试有应用问题
                                device_issues[udid]['app_issue'] = True
                                
                                # 记录问题到日志系统
                                log_device_issue(
                                    device_id=udid,
                                    device_name=device_name,
                                    round_num=current_round,
                                    issue_type='app',
                                    issue_details=issue_detail,
                                    page_name=f"频道图标_{icon_name}",
                                    screenshot_url=text_check_screenshot_url,
                                    execution_id=execution_id
                                )
                                
                                logger.warning(f"频道图标「{icon_name}」页面文本检测失败，但继续执行返回操作...")
                                
                        except Exception as e:
                            logger.error(f"频道图标「{icon_name}」页面文本检测过程中发生错误: {e}")
                            logger.error(f"错误详情: {traceback.format_exc()}")
                            # 文本检测失败不影响整体流程，继续执行
                            
                    else:
                        logger.error(f"频道图标「{icon_name}」页面文本检测截图失败: {text_check_screenshot_result}")
                        # 截图失败不影响整体流程，继续执行
                    
                    logger.info(f"频道图标「{icon_name}」页面文本检测流程完成")
                else:
                    logger.info(f"频道图标「{icon_name}」不在文本检测字典中，跳过文本检测")
                
                # 针对"团团赚" icon 的特殊iOS滑动返回处理
                special_swipe_success = False
                if icon_name == "团团赚" and platform == 'ios':
                    logger.info(f"频道图标「{icon_name}」是iOS设备，使用特殊滑动返回方式")
                    try:
                        # 从屏幕左边缘滑动到屏幕中心
                        logger.info("执行iOS特殊滑动返回：从屏幕左边缘滑动到屏幕中心")
                        driver.execute_script('mobile: dragFromToForDuration', {
                            'fromX': 0,
                            'fromY': 100,
                            'toX': 500,
                            'toY': 100,
                            'duration': 0.15
                        })
                        time.sleep(3)  # 等待滑动完成
                        
                        # 滑动后检查是否回到首页
                        timestamp = time.strftime('%Y%m%d_%H%M%S')
                        after_special_swipe_screenshot_path = os.path.join(
                            device_screenshot_dir,
                            f'团团赚特殊滑动返回后_{device_name}_{timestamp}.png'
                        )
                        device_operate_external.take_screenshot(
                            driver=driver,
                            udid=udid,
                            screenshot_path=after_special_swipe_screenshot_path,
                            logger=logger
                        )
                        after_special_swipe_screenshot_url = get_image_url(after_special_swipe_screenshot_path)
                        logger.info(f"团团赚特殊滑动返回后的状态截图已上传，URL: {after_special_swipe_screenshot_url}")
                        
                        # 弹窗处理
                        check_and_handle_popup_after_screenshot(
                            driver=driver,
                            udid=udid,
                            logger=logger,
                            page_name=f"频道图标_{icon_name}_特殊滑动返回后",
                        )
                        
                        # 检查是否回到首页
                        is_home = check_is_homepage(
                            driver=driver,
                            screenshot_path=after_special_swipe_screenshot_path,
                            logger=logger,
                            udid=udid,
                            screenshot_url=after_special_swipe_screenshot_url,
                        )
                        
                        if is_home:
                            logger.info(f"频道图标「{icon_name}」特殊滑动返回后首页检查结果：已成功返回首页")
                            special_swipe_success = True
                        else:
                            logger.warning(f"频道图标「{icon_name}」特殊滑动返回后首页检查结果：未能返回首页，将使用常规返回方式")
                            error_path = device_operate_external.move_screenshot_to_error(after_special_swipe_screenshot_path, device_error_dir)
                            logger.info(f"特殊滑动返回失败截图已移动到: {error_path}")
                            
                    except Exception as e:
                        logger.error(f"频道图标「{icon_name}」特殊滑动返回过程中发生错误: {e}")
                        logger.info("将使用常规返回方式")
                
                # 如果特殊滑动成功，跳过常规返回逻辑
                if special_swipe_success:
                    logger.info(f"频道图标「{icon_name}」特殊滑动返回成功，跳过常规返回逻辑")
                else:
                    # 返回首页（原有逻辑）
                    retry_count = 0
                    max_retries = 3
                    while retry_count < max_retries:
                        try:
                            screenshot_image = Image.open(final_screenshot_path)
                            image_width, image_height = screenshot_image.size
                        except Exception:
                            image_width = image_height = None
                        back_button_location = None
                        # 新增：仅对特殊业务 icon 调用特殊返回键检测（像素坐标）
                        especial_pixel_coords = None
                        special_icons = ["小美果园", "免费水果", "天天现金"]
                        if icon_name in special_icons:
                            try:
                                especial_pixel_coords = find_back_button_location_especial(icon_name, final_screenshot_path)
                            except Exception as e:
                                logger.warning(f"查找特殊业务返回按钮像素坐标异常: {e}")
                        if especial_pixel_coords and image_width and image_height:
                            back_button_location = especial_pixel_coords
                            logger.info(f"使用特殊业务圆圈检测返回按钮像素坐标: {back_button_location}")
                        else:
                            try:
                                back_button_location = find_back_button_location(
                                    final_screenshot_url,
                                    device_name,
                                    image_height,
                                    device_logger=logger,
                                    image_width=image_width
                                )
                            except Exception as e:
                                logger.warning(f"查找返回按钮异常: {e}")
                        if back_button_location:
                            bx, by = back_button_location
                            scaled_bx = bx / scale_ratio
                            scaled_by = by / scale_ratio
                            logger.info(f"找到返回按钮，正在点击返回...")
                            device_operate_external.tap(
                                driver=driver,
                                udid=udid,
                                x=scaled_bx,
                                y=scaled_by,
                                logger=logger
                            )
                            time.sleep(5)
                        else:
                            if platform == 'ios':
                                logger.info("未找到返回按钮，尝试点击左上角兜底区域 (5.6%, 7.9%) ...")
                                if driver:
                                    screen_size = driver.get_window_size()
                                    fallback_x = screen_size['width'] * 0.05
                                    fallback_y = screen_size['height'] * 0.08
                                else:
                                    fallback_x = fallback_y = 10
                                device_operate_external.tap(
                                    driver=driver,
                                    udid=udid,
                                    x=fallback_x,
                                    y=fallback_y,
                                    logger=logger
                                )
                                time.sleep(5)
                            else:
                                logger.info("未找到返回按钮，尝试使用系统返回键...")
                                device_operate_external.execute_adb_command(udid, "shell input keyevent 4")
                                time.sleep(5)
                        # 截图检查是否回到首页
                        timestamp = time.strftime('%Y%m%d_%H%M%S')
                        after_return_screenshot_path = os.path.join(
                            device_screenshot_dir,
                            f'频道返回后_{icon_name}_第{retry_count + 1}次_{device_name}_{timestamp}.png'
                        )
                        device_operate_external.take_screenshot(
                            driver=driver,
                            udid=udid,
                            screenshot_path=after_return_screenshot_path,
                            logger=logger
                        )
                        after_return_screenshot_url = get_image_url(after_return_screenshot_path)
                        logger.info(f"频道图标「{icon_name}」第{retry_count + 1}次返回操作后的状态截图已上传，URL: {after_return_screenshot_url} 轮次: {current_round}")
                        # 弹窗处理
                        check_and_handle_popup_after_screenshot(
                            driver=driver,
                            udid=udid,
                            logger=logger,
                            page_name=f"频道图标_{icon_name}_返回后",
                        )
                        # 检查是否回到首页
                        is_home = check_is_homepage(
                            driver=driver,
                            screenshot_path=after_return_screenshot_path,
                            logger=logger,
                            udid=udid,
                            screenshot_url=after_return_screenshot_url,
                        ) 
                        if is_home:
                            logger.info(f"频道图标「{icon_name}」页面返回后首页检查结果：已成功返回首页")
                            break
                        else:
                            logger.warning(f"频道图标「{icon_name}」页面返回后首页检查结果：未能返回首页")
                            error_path = device_operate_external.move_screenshot_to_error(after_return_screenshot_path, device_error_dir)
                            logger.info(f"返回失败截图已移动到: {error_path}")
                        retry_count += 1
                        if retry_count < max_retries:
                            logger.info(f"从频道图标「{icon_name}」页面返回首页失败，正在进行第 {retry_count + 1} 次尝试...")
                            time.sleep(3)
                    if retry_count >= max_retries:
                        logger.error(f"从频道图标「{icon_name}」页面返回首页失败，已达到最大重试次数")
                        logger.info("重启应用...")
                        device_operate_external.restart_app(
                            driver=driver,
                            udid=udid,
                            logger=logger
                        )
                        time.sleep(5)
                        timestamp = time.strftime('%Y%m%d_%H%M%S')
                        after_restart_screenshot_path = os.path.join(
                            device_screenshot_dir,
                            f'重启后_{device_name}_{timestamp}.png'
                        )

                        device_operate_external.take_screenshot(
                            driver=driver,
                            udid=udid,
                            screenshot_path=after_restart_screenshot_path,
                            logger=logger)       
                                     
                        # 重启 app 后，需要检测是否到了首页
                        after_restart_screenshot_url = get_image_url(after_restart_screenshot_path)
                        logger.info(f"{device_name}重启 app 后截图已上传，URL: {after_restart_screenshot_url} 轮次: {current_round}")
                        if not check_is_homepage(driver, udid, after_restart_screenshot_path, after_restart_screenshot_url, logger):
                            logger.warning(f"{device_name}重启 app 后没有到首页")
                            continue
                        
                        # 重启 app 后，需要滑动到当前 icon 所在的屏数
                        restart_target_screen = None
                        for screen_idx, screen_icons in enumerate(all_screen_icons_list):
                            if icon_name in screen_icons:
                                restart_target_screen = screen_idx
                                break
                        
                        if restart_target_screen is not None and restart_target_screen > 0:
                            logger.info(f"重启 app 后，需要滑动到第 {restart_target_screen + 1} 屏（当前图标「{icon_name}」所在屏）")
                            # 滑动到对应屏数
                            for swipe_count in range(restart_target_screen):
                                logger.info(f"正在滑动到第 {swipe_count + 2} 屏...")
                                
                                # 使用缓存的纵坐标比例进行滑动
                                if cached_y_ratio is not None:
                                    from_x_ratio = 0.8
                                    to_x_ratio = 0.2
                                    logger.info(f"重启后滑动: from (0.8,{cached_y_ratio:.3f}) to (0.2,{cached_y_ratio:.3f})")
                                    device_operate_external.swipe(
                                        driver=driver,
                                        udid=udid,
                                        from_x_ratio=from_x_ratio,
                                        from_y_ratio=cached_y_ratio,
                                        to_x_ratio=to_x_ratio,
                                        to_y_ratio=cached_y_ratio,
                                        duration=0.5,
                                        logger=logger
                                    )
                                    time.sleep(1)
                                else:
                                    logger.warning("没有缓存的纵坐标比例，跳过滑动")
                                    break
                            
                            # 滑动完成后重新截图，更新 current_screenshot_path
                            timestamp = time.strftime('%Y%m%d_%H%M%S')
                            current_screenshot_path = os.path.join(
                                device_screenshot_dir,
                                f'重启后滑动到第{restart_target_screen + 1}屏_{device_name}_{timestamp}.png'
                            )
                            device_operate_external.take_screenshot(
                                driver=driver,
                                udid=udid,
                                screenshot_path=current_screenshot_path,
                                logger=logger
                            )
                            current_screenshot_url = get_image_url(current_screenshot_path)
                            logger.info(f"频道图标「{icon_name}」重启后滑动到第{restart_target_screen + 1}屏截图已上传，URL: {current_screenshot_url} 轮次: {current_round}")
                        else:
                            logger.info(f"当前图标「{icon_name}」在第 1 屏，无需滑动")
                            # 第1屏也需要重新截图用于重新识别
                            timestamp = time.strftime('%Y%m%d_%H%M%S')
                            current_screenshot_path = os.path.join(
                                device_screenshot_dir,
                                f'重启后第1屏_{device_name}_{timestamp}.png'
                            )
                            device_operate_external.take_screenshot(
                                driver=driver,
                                udid=udid,
                                screenshot_path=current_screenshot_path,
                                logger=logger
                            )
                            current_screenshot_url = get_image_url(current_screenshot_path)
                            logger.info(f"频道图标「{icon_name}」重启后第1屏截图已上传，URL: {current_screenshot_url} 轮次: {current_round}")
                        
                        # 重启app后，重新识别当前屏的图标位置并更新坐标
                        logger.info(f"重启app后，重新识别当前屏的图标位置...")
                        restart_icons_dict = get_category_icons_by_row_count(current_screenshot_path, channel_keyword="外卖", verbose=False)
                        logger.info(f"重启后识别到的图标: {list(restart_icons_dict.keys())}")
                        
                        # 更新当前图标的坐标
                        if icon_name in restart_icons_dict:
                            restart_original_pos = pos.copy()
                            pos.update(restart_icons_dict[icon_name])
                            x, y = pos['x'], pos['y']
                            logger.info(f"重启后更新图标「{icon_name}」坐标: {restart_original_pos} -> {pos}")
                            logger.info(f"重启app后图标位置重新识别完成")
                        else:
                            logger.error(f"重启后仍未找到图标「{icon_name}」，当前屏图标: {list(restart_icons_dict.keys())}")
                            logger.error(f"跳过图标「{icon_name}」的测试")
                            continue
                        
                        failure_time = time.strftime('%Y-%m-%d %H:%M:%S')
                        failure_message = (
                            f"时间：{failure_time}\n"
                            f"设备：{device_name}\n"
                            f"问题：从频道图标「{icon_name}」页面返回首页失败\n"
                            f"图片URL：{final_screenshot_url}"
                        )
                        send_individual_kingkong_message(failure_message, ['cuijie12'])
                        device_issues[udid]['app_issue'] = True
                        log_device_issue(
                            device_id=udid,
                            device_name=device_name,
                            round_num=current_round,
                            issue_type='app',
                            issue_details=f"从频道图标「{icon_name}」页面返回首页失败，已达到最大重试次数",
                            page_name=f"频道图标_{icon_name}",
                            screenshot_url=last_marked_image_url or final_screenshot_url,
                            execution_id=execution_id
                        )
                    else:
                        completed_count += 1
                        # 累加完成的图标数到设备状态
                        success = manager.add_icon_count(udid, total_add=0, completed_add=1)
                        if success:
                            logger.info(f"图标「{icon_name}」测试成功，累计完成图标数已更新")
                        else:
                            logger.warning(f"更新设备累计完成图标数失败")

                # 更新当前图标索引，准备测试下一个图标
                current_icon_index += 1

                logger.info(f"频道图标「{icon_name}」测试完成")
                logger.info("=" * 60)
                time.sleep(5)
            except Exception as e:
                logger.error(f"频道图标「{icon_name}」测试过程中发生错误: {e}")
                logger.info(f"频道图标「{icon_name}」测试异常结束")
                logger.info("=" * 60)
                device_issues[udid]['test_issue'] = True
                error_traceback = traceback.format_exc()
                logger.error(f"错误详情: {error_traceback}")
                log_device_issue(
                    device_id=udid,
                    device_name=device_name,
                    round_num=current_round,
                    issue_type='test',
                    issue_details=f"频道图标「{icon_name}」测试过程中发生错误: {str(e)}",
                    page_name=f"频道图标_{icon_name}",
                    screenshot_url=last_marked_image_url or final_screenshot_url,
                    execution_id=execution_id
                )
                continue

        # 9. 检查当前屏是否测试完成
        if current_icon_index >= len(current_screen_icons):
            logger.info(f"第 {current_screen_index + 1} 屏所有图标测试完成")

            # 检查是否为最后一屏
            if is_last_screen:
                logger.info("已到达最后一屏，测试完成")
                screen_count = current_screen_index + 1
                break

            # 滑动到下一屏
            if cached_y_ratio is not None and current_screen_icons:
                if len(current_screen_icons) < 2:
                    logger.info("当前屏图标不足，无法滑动下一屏")
                    break

                # 使用缓存的纵坐标比例进行滑动
                from_x_ratio = 0.8
                to_x_ratio = 0.2
                logger.info(f"滑动到下一屏: from (0.8,{cached_y_ratio:.3f}) to (0.2,{cached_y_ratio:.3f})")
                device_operate_external.swipe(
                    driver=driver,
                    udid=udid,
                    from_x_ratio=from_x_ratio,
                    from_y_ratio=cached_y_ratio,
                    to_x_ratio=to_x_ratio,
                    to_y_ratio=cached_y_ratio,
                    duration=0.5,
                    logger=logger
                )
                time.sleep(1)

                # 滑动后重新截图
                timestamp = time.strftime('%Y%m%d_%H%M%S')
                current_screenshot_path = os.path.join(
                    device_screenshot_dir,
                    f'滑动到第{current_screen_index + 2}屏_{device_name}_{timestamp}.png'
                )
                device_operate_external.take_screenshot(
                    driver=driver,
                    udid=udid,
                    screenshot_path=current_screenshot_path,
                    logger=logger
                )
                current_screenshot_url = get_image_url(current_screenshot_path)
                logger.info(f"滑动到第{current_screen_index + 2}屏截图已上传，URL: {current_screenshot_url}")

                # 更新屏数索引，重置图标索引
                current_screen_index += 1
                current_icon_index = 0
                screen_count += 1
            else:
                logger.warning("无法滑动到下一屏，测试结束")
                break
        else:
            # 当前屏还有图标未测试，继续下一轮循环
            continue
    logger.info(f"全量频道区图标测试完成")
    logger.info(f"测试统计: 共测试 {completed_count}/{total_count} 个图标，完成 {screen_count} 屏")
    logger.info(f"已测试图标: {all_tested_icons}")
    logger.info(f"每一屏的图标分布: {all_screen_icons_list}")
    return completed_count, total_count, screen_count, all_screen_icons_list