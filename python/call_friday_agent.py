import requests
import json
import sys

# 灵活导入easyocr_text模块
try:
    from python.easyocr_text import main as ocr_main
except ImportError:
    try:
        from easyocr_text import main as ocr_main
    except ImportError:
        print("警告：无法导入easyocr_text模块")
        ocr_main = None

REQUEST_URL = 'https://aigc.sankuai.com/v1/openai/native/chat/completions'
APP_ID = "1680849412324622382"
MODEL_NAME = "deepseek-v3-friday"
headers = {'Authorization': f'Bearer {APP_ID}'}

def analyze_image(image_path, debug=False):
    """
    分析图片并获取坐标
    
    参数:
        image_path: 图片路径
        debug: 是否显示详细输出信息，默认为False
        
    返回:
        提取出的坐标 [x, y] 或者 None（如果未找到坐标）
    """
    # 检查OCR模块是否可用
    if ocr_main is None:
        if debug:
            print("OCR模块不可用，无法分析图片")
        return None
    
    # 调用easyocr_text.py中的函数获取OCR文本，传递debug参数
    ocr_text = ocr_main(image_path, debug)
    
    # 构建用户内容
    user_content = f"""
        我现在给你一个手机页面的 ocr 结果，其中有一个弹窗，请你根据我的 ocr 文本告诉我点击哪个坐标能关闭弹窗。
        你的返回的结果要标准 JSON 格式，包含 `coordinates` 字段，其值为一个包含两个整数的列表 `[x, y]`，表示关闭弹窗按钮的中心点坐标。
        如果截图中没有弹窗，请返回 JSON 对象 `{{"coordinates": [-1, -1]}}`。
        {ocr_text}
        """

    # HTTP API 请求内容
    request_content = {
        'messages': [
            {
                'role': 'user', 
                'content': user_content
            }
        ], 
        'model': MODEL_NAME, 
        'max_tokens': 4096, 
        'stream': False
    }
    
    # 发送请求
    res = requests.post(REQUEST_URL, json=request_content, stream=True, headers=headers)
    
    if debug:
        print("响应头信息:")
        print(json.dumps(dict(res.headers), indent=2, ensure_ascii=False))

    # 收集完整响应
    full_response = ""
    for chunk in res.iter_content(chunk_size=1024):
        r = chunk.decode('utf-8', 'ignore')
        full_response += r

    # 格式化输出完整响应
    if debug:
        print("\n完整响应:")
        try:
            formatted_response = json.loads(full_response)
            print(json.dumps(formatted_response, indent=2, ensure_ascii=False))
        except:
            print(full_response)

    # 提取坐标信息
    coordinates = extract_coordinates(full_response)
    
    if debug:
        print("\n提取的坐标:")
        print(coordinates)

        # 如果找到了坐标，可以进一步处理
        if coordinates:
            x, y = coordinates
            print(f"\n可以点击坐标 ({x}, {y}) 来关闭弹窗")
    elif coordinates:
        # 非调试模式，直接输出坐标
        x, y = coordinates
        print(f"{x},{y}")
        
    return coordinates

def extract_coordinates(response_text):
    """
    从API返回的文本中提取坐标信息
    
    参数:
        response_text: API返回的响应文本
        
    返回:
        提取出的坐标 [x, y] 或者 None（如果未找到坐标）
    """
    try:
        # 解析响应JSON
        response_json = json.loads(response_text)
        
        # 获取模型回复的内容
        assistant_message = response_json.get('choices', [{}])[0].get('message', {}).get('content', '')
        
        # 在回复中查找JSON格式的坐标
        import re
        # 尝试匹配JSON对象
        json_pattern = r'```json\s*(\{[^}]*\})\s*```'
        json_match = re.search(json_pattern, assistant_message)
        
        if json_match:
            coordinates_json = json.loads(json_match.group(1))
            return coordinates_json.get('coordinates')
        
        # 直接搜索坐标格式
        coords_pattern = r'"coordinates":\s*\[(\d+),\s*(\d+)\]'
        coords_match = re.search(coords_pattern, assistant_message)
        
        if coords_match:
            return [int(coords_match.group(1)), int(coords_match.group(2))]
        
        return None
    except Exception as e:
        print(f"解析坐标时出错：{e}")
        return None

if __name__ == "__main__":
    # 获取命令行参数
    image_path = "/Users/<USER>/Desktop/work/图像识别测试/弹窗集合/小米 15 强制升级弹窗.png"
    debug = True
    
    # 添加命令行参数支持
    if len(sys.argv) > 1:
        if sys.argv[1].lower() == 'debug':
            debug = True
        else:
            # 如果提供了图片路径
            image_path = sys.argv[1]
            # 检查是否有debug参数作为第二个参数
            if len(sys.argv) > 2 and sys.argv[2].lower() == 'debug':
                debug = True
    
    # 分析图片并获取坐标
    coordinates = analyze_image(image_path, debug)