import time
import threading
import logging
import json
import os
import traceback
import sys
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
import glob

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from python.notify_user import send_individual_message, add_host_info_to_message
from python.device_status_manager import get_device_status, update_device_status as update_device_status_file, create_device_status, manager
import multiprocessing
from multiprocessing import Manager
import logging.handlers
import subprocess
from collections import deque
from python.log_manager import LogManager

# 添加自定义日志级别 HEART
HEART_LEVEL = 25  # 介于INFO(20)和WARNING(30)之间
logging.addLevelName(HEART_LEVEL, "HEART")

# 为Logger类添加heart方法
def heart(self, message, *args, **kwargs):
    if self.isEnabledFor(HEART_LEVEL):
        self._log(HEART_LEVEL, message, args, **kwargs)

# 将heart方法添加到Logger类
logging.Logger.heart = heart

# 获取日志对象并配置
logger = logging.getLogger("heartbeat_monitor")
logger.setLevel(logging.INFO)

# 设置日志格式
formatter = logging.Formatter("%(asctime)s - %(levelname)s - [%(name)s] - %(message)s")

# 如果logger没有处理器，添加一个处理器
if not logger.handlers:
    # 创建控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # 设置不传播到父记录器，避免重复日志
    logger.propagate = False

# 使用普通字典作为默认值，在初始化时再替换为共享字典
device_status = {}

# 注意：device_statistics 已被 calc_device_metrics() 和 calc_overall_metrics() 替代
# 注意：device_issue_logs 已被 IssueLogger 和问题日志文件替代，不再使用内存字典

# 添加用于跟踪设备测试会话的字典
device_session_first_round = {}

# 程序启动时间
start_time = datetime.now()  # 初始化为当前时间，而不是None
# 心跳通知锁，防止多线程冲突
heartbeat_lock = threading.Lock()
# 心跳定时器对象
heartbeat_timer = None
# 心跳间隔（秒）
HEARTBEAT_INTERVAL = 30 * 60  # 30分钟
# 设备轮次通知阈值
DEVICE_ROUND_THRESHOLD = 10  # 每10轮发送一次设备通知

# 设备状态判断相关常量
DEVICE_STATUS_UPDATE_TIMEOUT = 600  # 设备状态更新超时时间（秒），10分钟
INITIAL_NOTIFICATION_DELAY = 300  # 程序初始通知延迟时间（秒），5分钟
DEVICE_STATUS_CHECK_INTERVAL = 60  # 设备状态检查间隔（秒），1分钟
DEVICE_STATUS_CHANGE_THRESHOLD = 1  # 设备状态变化检测阈值（分钟），1分钟内的变化视为新变化
NOTIFICATION_MIN_INTERVAL = 30  # 通知间隔最小时间（分钟），改为30分钟避免频繁通知
DEVICE_OFFLINE_THRESHOLD = 60 * 60  # 设备掉线判断时间（秒），60分钟未更新视为掉线
DEVICE_INCONSISTENT_MIN = 600  # 设备状态不一致最小判断时间（秒），10分钟
DEVICE_INCONSISTENT_MAX = 60 * 60  # 设备状态不一致最大判断时间（秒），60分钟
DEVICE_STABLE_THRESHOLD = 3  # 设备稳定阈值（连续检测到相同数量的设备的次数）
DEVICE_CLEANUP_THRESHOLD = 24 * 60 * 60  # 设备清理阈值（秒），24小时未更新的设备将被清除

# 通知接收者
DEFAULT_RECEIVERS = ['cuijie12']

# 心跳线程
heartbeat_thread = None
# 停止标志
stop_event = threading.Event()
# 上次通知时间
last_notification_time = None
# 通知锁，防止并发发送
notification_lock = threading.Lock()

# 记录设备第一轮测试是否已通知
device_first_round_notified = {}

# 设备状态跟踪字典，用于检测离线状态变化
device_previous_status = {}

# 问题类型定义
ISSUE_TYPE_APP = "app"  # 应用问题（崩溃、UI错误等）
ISSUE_TYPE_TEST = "test"  # 测试流程问题（连接失败、设备断开等）

# 设备状态文件目录
DEVICE_STATUS_DIR = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "log", "device_status")
os.makedirs(DEVICE_STATUS_DIR, exist_ok=True)

# 问题日志相关常量
ISSUE_LOG_FORMAT = {
    "timestamp": "",          # 问题发生时间
    "device_id": "",         # 设备ID
    "device_name": "",       # 设备名称
    "round_num": 0,          # 测试轮次
    "issue_type": "",        # 问题类型（app/test）
    "issue_details": "",     # 问题详情
    "page_name": "",        # 问题发生页面
    "screenshot_url": "",    # 截图URL
    "status": "",           # 设备状态
    "resolution": "",       # 问题解决方案（如果有）
    "tags": []             # 问题标签，便于分类和查询
}

# 创建全局问题日志记录器实例
issue_logger = None

class IssueLogger:
    """问题日志记录器"""
    
    def __init__(self, log_root: str):
        """
        初始化问题日志记录器
        
        Args:
            log_root: 日志根目录
        """
        self.log_root = os.path.join(log_root, "log", "python_logs")
        self.issues_dir = os.path.join(self.log_root, "issues")
        os.makedirs(self.issues_dir, exist_ok=True)
        # 设置目录权限
        os.chmod(self.issues_dir, 0o755)
        
        # 设置日志格式
        self.formatter = logging.Formatter(
            "%(asctime)s - %(levelname)s - [%(name)s] - %(message)s"
        )
        
        # 问题日志处理器缓存
        self._handlers = {}
        # 进程ID
        self.process_id = os.getpid()
        logger.info(f"问题日志记录器初始化完成 - 进程ID: {self.process_id}")
    
    def _get_issue_logger(self, device_name: str) -> logging.Logger:
        """
        获取设备专属的问题日志记录器
        
        Args:
            device_name: 设备名称
            
        Returns:
            logging.Logger: 问题日志记录器
        """
        logger_name = f"issues_{device_name}_{self.process_id}"  # 添加进程ID以避免冲突
        logger = logging.getLogger(logger_name)
        
        # 如果已经配置过，直接返回
        if logger.handlers:
            return logger
            
        logger.setLevel(logging.INFO)
        
        # 创建问题日志文件
        log_file = os.path.join(self.issues_dir, f"{device_name}_issues.log")
        
        try:
            # 使用 TimedRotatingFileHandler 进行日志轮转
            handler = logging.handlers.TimedRotatingFileHandler(
                log_file,
                when='D',           # 按天轮转
                interval=1,         # 每1天轮转一次
                backupCount=30,     # 保留30天的日志
                encoding='utf-8',
                delay=False
            )
            
            handler.setFormatter(self.formatter)
            logger.addHandler(handler)
            
            # 添加到缓存
            self._handlers[device_name] = handler
            
            logger.info(f"设备 {device_name} 的问题日志记录器初始化完成 - 进程ID: {self.process_id}")
            return logger
            
        except Exception as e:
            logger.error(f"创建设备 {device_name} 的问题日志记录器时发生错误: {str(e)}")
            logger.error(traceback.format_exc())
            return None
    
    def log_issue(self, 
                  device_id: str,
                  device_name: str,
                  round_num: int,
                  issue_type: str,
                  issue_details: str,
                  execution_id: Optional[str] = None,  # 新增参数：测试执行ID
                  page_name: Optional[str] = None,
                  screenshot_url: Optional[str] = None,
                  status: Optional[str] = None,
                  resolution: Optional[str] = None,
                  tags: Optional[List[str]] = None) -> None:
        """
        记录问题
        
        Args:
            device_id: 设备ID
            device_name: 设备名称
            round_num: 测试轮次
            issue_type: 问题类型
            issue_details: 问题详情
            execution_id: 测试执行ID，用于区分不同的测试会话（可选）
            page_name: 问题发生页面（可选）
            screenshot_url: 截图URL（可选）
            status: 设备状态（可选）
            resolution: 问题解决方案（可选）
            tags: 问题标签（可选）
        """
        # 获取设备的问题日志记录器
        device_logger = self._get_issue_logger(device_name)
        if not device_logger:
            logger.error(f"无法获取设备 {device_name} 的问题日志记录器")
            return
        
        # 创建问题记录
        issue = {
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "device_id": device_id,
            "device_name": device_name,
            "round_num": round_num,
            "execution_id": execution_id or f"{device_id}_{round_num}_{int(time.time())}",  # 如果未提供，则自动生成
            "issue_type": issue_type,
            "issue_details": issue_details,
            "page_name": page_name or "",
            "screenshot_url": screenshot_url or "",
            "status": status or "",
            "resolution": resolution or "",
            "tags": tags or []
        }
        
        # 记录问题到文件
        device_logger.info(json.dumps(issue, ensure_ascii=False))
        
        # 注意：不再需要维护内存中的问题日志，所有问题记录都通过文件进行存储和查询
    
    def query_issues(self, device_name: str, limit: int = 10, hours: int = 1, execution_id: Optional[str] = None, round_num: Optional[int] = None) -> List[Dict]:
        """
        查询设备的问题记录
        
        Args:
            device_name: 设备名称
            limit: 返回的记录数量限制
            execution_id: 测试执行ID，如果提供则只返回该测试执行中的问题
            round_num: 测试轮次号，如果提供则只返回该轮次的问题
            
        Returns:
            List[Dict]: 问题记录列表
        """
        try:
            log_file = os.path.join(self.issues_dir, f"{device_name}_issues.log")
            logger.info(f"尝试读取问题日志文件: {log_file}")
            
            if not os.path.exists(log_file):
                logger.warning(f"设备 {device_name} 的问题日志文件不存在")
                return []
            
            # 读取日志文件的最后 limit*4 行（增加行数以确保获取足够的JSON记录）
            with open(log_file, 'r', encoding='utf-8') as f:
                # 使用 deque 高效获取最后几行
                last_lines = deque(f, limit * 4)
            
            issues = []
            for line in last_lines:
                try:
                    # 尝试提取JSON部分 - 寻找从第一个 { 到最后一个 } 的内容
                    start_index = line.find('{')
                    end_index = line.rfind('}')
                    
                    if start_index == -1 or end_index == -1 or start_index >= end_index:
                        logger.debug(f"行不包含有效的JSON: {line[:50]}...")
                        continue
                        
                    json_str = line[start_index:end_index+1]
                    # 验证是否是合法的JSON
                    issue = json.loads(json_str)
                    
                    # 确保至少有必要的字段
                    if not all(k in issue for k in ['timestamp', 'device_id', 'device_name']):
                        logger.debug(f"JSON缺少必要字段: {json_str[:50]}...")
                        continue
                    
                    # 如果提供了execution_id，筛选匹配的问题记录
                    if execution_id is not None:
                        # 兼容旧版本问题记录，可能没有execution_id字段
                        issue_execution_id = issue.get('execution_id', '')
                        if not issue_execution_id:
                            # 对于旧记录，如果还提供了轮次号，可以用设备ID+轮次号+时间戳前8位作为替代匹配
                            if round_num is not None:
                                timestamp_part = issue.get('timestamp', '')[:10].replace('-', '').replace(' ', '')
                                issue_device_id = issue.get('device_id', '')
                                issue_round_num = issue.get('round_num', -1)
                                if issue_round_num != round_num or not timestamp_part or not issue_device_id:
                                    continue
                            else:
                                # 如果没有提供轮次号，对于没有execution_id的记录不进行过滤
                                pass
                        elif issue_execution_id != execution_id:
                            # 如果有execution_id但不匹配，跳过
                            continue
                    
                    # 如果提供了round_num但未提供execution_id，则单独筛选轮次
                    if round_num is not None and execution_id is None:
                        if issue.get('round_num', -1) != round_num:
                            continue
                    
                    issues.append(issue)
                    logger.debug(f"成功解析问题记录: {issue.get('issue_type')} - {issue.get('timestamp')}")
                except json.JSONDecodeError as e:
                    logger.debug(f"JSON解析错误: {str(e)}, 行内容: {line[:50]}...")
                    continue
                except Exception as e:
                    logger.debug(f"解析日志行时发生错误: {str(e)}")
                    continue
            
            # 过滤1小时内的记录
            now = datetime.now()
            filtered_issues = []
            for issue in issues:
                try:
                    issue_time = datetime.strptime(issue['timestamp'], '%Y-%m-%d %H:%M:%S')
                    if (now - issue_time) <= timedelta(hours=hours):
                        filtered_issues.append(issue)
                except ValueError as e:
                    logger.error(f"解析问题记录时间戳时出错: {issue['timestamp']}, 错误信息: {str(e)}")
            # 按时间戳倒序排序并限制数量
            filtered_issues.sort(key=lambda x: x['timestamp'], reverse=True)
            logger.info(f"从文件 {log_file} 读取到 {len(filtered_issues)} 条最近{hours}小时问题记录，筛选后剩余 {len(filtered_issues[:limit])} 条")
            return filtered_issues[:limit]
            
        except Exception as e:
            logger.error(f"查询设备 {device_name} 的问题记录时发生错误: {str(e)}")
            logger.error(traceback.format_exc())
            return []

def initialize_heartbeat_monitor():
    """
    初始化心跳监控系统
    """
    global heartbeat_thread, device_status, stop_event, last_notification_time, start_time
    
    # 初始化问题日志记录器
    script_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.dirname(script_dir)
    initialize_issue_logger(project_root)
    
    # 检查是否已有心跳进程在运行
    try:
        import psutil
        current_pid = os.getpid()
        heartbeat_processes = []
        
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                if proc.pid == current_pid:
                    continue
                cmdline = proc.info.get('cmdline', [])
                # 确保cmdline不为None且是可迭代的
                if cmdline is None:
                    cmdline = []
                elif not isinstance(cmdline, (list, tuple)):
                    cmdline = []

                if any('heartbeat_monitor' in str(cmd) for cmd in cmdline):
                    heartbeat_processes.append(proc.info)
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        if heartbeat_processes:
            logger.warning(f"检测到已有 {len(heartbeat_processes)} 个心跳监控进程在运行，但将继续初始化当前进程的心跳监控")
            for proc in heartbeat_processes:
                logger.info(f"现有心跳进程: PID={proc['pid']}, 命令行={proc.get('cmdline', 'N/A')}")
    
    except ImportError:
        logger.info("psutil未安装，跳过进程检查")
    except Exception as e:
        logger.warning(f"检查现有心跳进程时发生错误: {str(e)}")
    
    # 如果已经有一个心跳线程在运行，则不再创建新的
    if heartbeat_thread is not None and heartbeat_thread.is_alive():
        logger.info("心跳监控系统已经在运行中，不再重复初始化")
        return
    
    # 重置停止标志
    stop_event.clear()
    
    # 初始化设备状态字典（现在主要从文件读取，共享字典仅作为兼容性缓存）
    device_status = {}
    logger.info("心跳监控系统将使用 device_status_manager 从文件加载设备状态")
    
    # 记录初始化时间
    start_time = datetime.now()  # 更新全局变量
    logger.info(f"心跳监控系统初始化时间: {start_time}")
    
    # 创建并启动心跳线程
    heartbeat_thread = threading.Thread(
        target=heartbeat_loop,
        args=(HEARTBEAT_INTERVAL,),
        daemon=True
    )
    heartbeat_thread.start()
    
    logger.info("心跳监控系统已初始化")
    
    # 不再设置last_notification_time，让heartbeat_loop自己决定何时发送第一次通知

def schedule_next_heartbeat():
    """
    安排下一次心跳通知
    """
    global heartbeat_timer
    # 取消之前的定时器（如果存在）
    if heartbeat_timer:
        heartbeat_timer.cancel()
    # 创建新的定时器
    heartbeat_timer = threading.Timer(HEARTBEAT_INTERVAL, send_heartbeat)
    heartbeat_timer.daemon = True  # 设置为守护线程，这样主程序退出时它会自动终止
    heartbeat_timer.start()
    logger.debug(f"已安排下一次心跳通知，将在 {HEARTBEAT_INTERVAL/60} 分钟后发送")

def initialize_issue_logger(log_root):
    """初始化问题日志记录器"""
    global issue_logger
    try:
        # 只在issue_logger为None时初始化，避免重复初始化
        if issue_logger is None:
            issue_logger = IssueLogger(log_root)
            # 仅在首次初始化时输出一次日志
            current_process = multiprocessing.current_process()
            logger.info(f"问题日志记录器初始化成功 - 进程名称: {current_process.name}, 进程ID: {os.getpid()}")
    except Exception as e:
        logger.error(f"初始化问题日志记录器时发生错误: {str(e)}")
        logger.error(traceback.format_exc())

def log_device_issue(device_id: str,
                    device_name: str,
                    round_num: int,
                    issue_type: str,
                    issue_details: str,
                    execution_id: Optional[str] = None,  # 新增参数：测试执行ID
                    page_name: Optional[str] = None,
                    screenshot_url: Optional[str] = None,
                    status: Optional[str] = None,
                    resolution: Optional[str] = None,
                    tags: Optional[List[str]] = None) -> None:
    """
    记录设备问题
    
    Args:
        device_id: 设备ID
        device_name: 设备名称
        round_num: 测试轮次
        issue_type: 问题类型
        issue_details: 问题详情
        execution_id: 测试执行ID，用于区分不同的测试会话（可选）
        page_name: 问题发生页面（可选）
        screenshot_url: 截图URL（可选）
        status: 设备状态（可选）
        resolution: 问题解决方案（可选）
        tags: 问题标签（可选）
    """
    global issue_logger
    
    try:
        # 获取设备专属的日志记录器
        from python.log_manager import LogManager
        log_manager = LogManager()
        device_logger = log_manager.get_device_logger(device_name)
        
        # 确保问题日志记录器已初始化
        if issue_logger is None:
            script_dir = os.path.dirname(os.path.abspath(__file__))
            project_root = os.path.dirname(script_dir)
            initialize_issue_logger(project_root)
            
        if issue_logger is None:
            device_logger.error(f"无法记录设备 {device_name} 的问题：问题日志记录器初始化失败")
            return
            
        # 记录问题
        issue_logger.log_issue(
            device_id=device_id,
            device_name=device_name,
            round_num=round_num,
            issue_type=issue_type,
            issue_details=issue_details,
            execution_id=execution_id,  # 传递测试执行ID
            page_name=page_name,
            screenshot_url=screenshot_url,
            status=status,
            resolution=resolution,
            tags=tags
        )
        device_logger.info(f"成功记录设备 {device_name} 的问题: {issue_type} - 进程ID: {os.getpid()}")
        
    except Exception as e:
        # 如果获取设备logger失败，回退到原始logger
        try:
            from python.log_manager import LogManager
            log_manager = LogManager()
            device_logger = log_manager.get_device_logger(device_name)
            device_logger.error(f"记录设备 {device_name} 的问题时发生错误: {str(e)}")
            device_logger.error(traceback.format_exc())
        except:
            logger.error(f"记录设备 {device_name} 的问题时发生错误: {str(e)}")
            logger.error(traceback.format_exc())

def get_recent_issues(device_id: str, limit: int = 5, execution_id: Optional[str] = None, round_num: Optional[int] = None, hours: int = 1) -> List[Dict]:
    """
    获取设备最近的问题记录
    
    Args:
        device_id: 设备ID
        limit: 返回的问题记录数量限制
        execution_id: 测试执行ID，如果提供则只返回该测试执行中的问题
        round_num: 测试轮次号，如果提供则只返回该轮次的问题
        hours: 返回的问题记录的时间范围
        
    Returns:
        List[Dict]: 最近的问题记录列表
    """
    global issue_logger
    
    try:
        # 确保问题日志记录器已初始化
        if issue_logger is None:
            script_dir = os.path.dirname(os.path.abspath(__file__))
            project_root = os.path.dirname(script_dir)
            initialize_issue_logger(project_root)
        
        # 从设备状态文件获取设备名称
        device_name = None
        
        # 首先尝试从设备状态文件获取设备名称
        device_status_data = get_device_status(device_id)
        if device_status_data and device_status_data != False:
            device_name = device_status_data.get('device_name')
        
        # 如果没有找到设备名称，尝试从设备问题日志文件中查找
        if not device_name and os.path.exists(issue_logger.issues_dir):
            # 遍历问题日志目录，查找匹配device_id的问题记录
            for filename in os.listdir(issue_logger.issues_dir):
                if filename.endswith('.log') and device_id in filename:
                    # 从文件名中提取设备名称部分（假设格式为 设备名_issues.log）
                    try:
                        device_name = filename.replace('_issues.log', '')
                        break
                    except Exception:
                        continue
        
        # 获取设备专属的日志记录器
        try:
            from python.log_manager import LogManager
            log_manager = LogManager()
            device_logger = log_manager.get_device_logger(device_name if device_name else f"Unknown_{device_id}")
        except:
            device_logger = logger  # 回退到原始logger
        
        if not device_name:
            device_logger.warning(f"无法找到设备 {device_id} 的名称信息")
            # 尝试直接使用device_id作为设备名称
            device_name = f"Unknown_{device_id}"
            device_logger.info(f"使用device_id创建临时设备名称: {device_name}")
        
        # 查询最近的问题记录
        recent_issues = []
        if issue_logger:
            try:
                # 传递execution_id和round_num参数
                recent_issues = issue_logger.query_issues(
                    device_name=device_name,
                    limit=limit,
                    hours=hours, # Pass hours parameter
                    execution_id=execution_id,
                    round_num=round_num
                )
                device_logger.info(f"获取到设备 {device_name} 的 {len(recent_issues)} 条最近问题记录")
            except Exception as e:
                device_logger.error(f"调用query_issues查询设备 {device_name} 的问题记录时发生错误: {e}")
                device_logger.error(traceback.format_exc())
        
        return recent_issues
    
    except Exception as e:
        # 如果获取设备logger失败，回退到原始logger
        try:
            from python.log_manager import LogManager
            log_manager = LogManager()
            device_logger = log_manager.get_device_logger(device_name if 'device_name' in locals() and device_name else f"Unknown_{device_id}")
            device_logger.error(f"获取设备 {device_id} 的最近问题记录时发生意外错误: {e}")
            device_logger.error(traceback.format_exc())
        except:
            logger.error(f"获取设备 {device_id} 的最近问题记录时发生意外错误: {e}")
            logger.error(traceback.format_exc())
        return []

def send_device_round_notification(device_id: str, device_name: str, platform: str, round_num: int, status: str, test_duration: float = None):
    """
    发送设备轮次通知
    
    :param device_id: 设备ID
    :param device_name: 设备名称
    :param platform: 平台
    :param round_num: 当前轮次
    :param status: 设备状态
    :param test_duration: 测试耗时（秒），对于轮次通知不再显示此字段
    """
    # 获取设备专属的日志记录器
    device_logger = logger  # 默认使用全局logger
    try:
        from python.log_manager import LogManager
        log_manager = LogManager()
        device_logger = log_manager.get_device_logger(device_name)
    except Exception as e:
        logger.error(f"获取设备 {device_name} 的专属日志记录器失败，使用全局logger: {str(e)}")
    
    try:
        device_logger.info(f"【设备轮次通知】开始准备设备轮次通知: 设备={device_name}, 历史轮次={round_num}, 状态={status}")
        
        # 使用 device_status_manager 获取设备状态
        current_status = get_device_status(device_id)
        
        if not current_status:
            device_logger.error(f"无法获取设备 {device_id} 的状态信息")
            return False
            
        # 使用新的指标计算函数
        metrics = calc_device_metrics(current_status)
        
        avg_duration = metrics['avg_icon_time']  # 平均每个图标耗时
        completion_rate = (metrics['completed_rounds'] / metrics['session_round_num']) * 100 if metrics['session_round_num'] else 0
        app_issue_rate = metrics['app_issue_rate']
        test_issue_rate = metrics['test_issue_rate']
        
        device_logger.info(f"计算统计指标: 平均图标耗时={avg_duration:.1f}秒, 完成率={completion_rate:.1f}%, 应用问题率={app_issue_rate:.1f}%, 测试问题率={test_issue_rate:.1f}%")
        
        # 根据状态显示不同的状态文本
        status_text = "已完成" if status == "completed" else "部分完成"
        
        # 获取当前会话轮次
        current_session_round = 1
        if current_status and hasattr(current_status, 'get'):
            current_session_round = current_status.get('session_round_num', 1)
        
        message = (
            f"设备轮次通知\n"
            f"时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
            f"设备：{device_name} ({platform.upper()})\n"
            f"UDID：{device_id}\n"
            f"状态：{status_text}第 {current_session_round} 轮测试（历史轮次: {round_num}）\n"
        )
        
        # 添加问题信息
        if current_status and hasattr(current_status, 'get'):
            app_issue = current_status.get('app_issue', False)
            test_issue = current_status.get('test_issue', False)
            
            if status != "completed" and (app_issue or test_issue):
                message += f"\n测试问题信息：\n"
                if app_issue:
                    message += f"- 检测到应用问题（如崩溃、UI错误等）\n"
                if test_issue:
                    message += f"- 检测到测试流程问题（如连接失败、设备断开等）\n"
        
        # 添加统计信息
        if metrics:
            # 计算总测试时长的小时分钟秒格式
            total_duration_seconds = time.time() - metrics['start_time']
            hours, remainder = divmod(total_duration_seconds, 3600)
            minutes, seconds = divmod(remainder, 60)
            duration_str = f"{int(hours)}小时{int(minutes)}分钟{int(seconds)}秒"
            
            message += f"\n测试统计信息：\n"
            message += f"当前轮次：{current_session_round}\n"
            message += f"历史轮次：{round_num}\n"
            message += f"总测试时长：{duration_str}\n"
            message += f"完成轮次：{metrics['completed_rounds']} ({completion_rate:.1f}%)\n"
            message += f"总图标数：{metrics['total_icons']}\n"
            message += f"完成图标总数：{metrics['completed_icons']}\n"
            message += f"应用问题率：{app_issue_rate:.1f}%\n"
            message += f"测试问题率：{test_issue_rate:.1f}%\n"
            message += f"平均图标耗时：{avg_duration:.1f}秒\n"
            
            # 修复图标完成率计算的除零错误
            if metrics['total_icons'] > 0:
                message += f"图标完成率：{metrics['completed_icons'] / metrics['total_icons'] * 100:.1f}%\n"
            else:
                message += f"图标完成率：0.0%\n"
            
        
        # 添加最近问题记录
        device_logger.info(f"获取设备 {device_id} 的近 1 小时问题记录")
        recent_issues = []
        try:
            # 获取最近5条问题记录，不限定execution_id和round_num
            recent_issues = get_recent_issues(
                device_id=device_id, 
                limit=10,
                hours=1
            )
            device_logger.info(f"获取到 {len(recent_issues)} 条最近问题记录")
        except Exception as e:
            device_logger.error(f"获取最近问题记录时发生错误: {str(e)}")
            # 添加详细的错误信息记录
            device_logger.error(f"错误详情: {traceback.format_exc()}")
        
        if recent_issues:
            message += f"\n最近 1 小时问题记录：\n"
            for i, issue in enumerate(recent_issues, 1):
                if not hasattr(issue, 'get'):
                    device_logger.warning(f"问题记录不支持get操作: {issue}")
                    continue
                    
                try:
                    # 修改：直接使用字符串格式的时间戳，不再尝试将其转换为timestamp再转回字符串
                    issue_time = issue.get('timestamp', '')
                    # 如果没有timestamp字段，使用当前时间
                    if not issue_time:
                        issue_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    
                    issue_type = "应用问题" if issue.get('issue_type') == ISSUE_TYPE_APP else "测试问题"
                    page_name = issue.get('page_name', '')
                    page_info = f"页面：{page_name}" if page_name else ""
                    issue_details = issue.get('issue_details', '')
                    screenshot_url = issue.get('screenshot_url', '')
                    
                    message += f"{i}. {issue_type} {issue_time}\n"
                    message += f"   轮次：{issue.get('round_num', 0)}\n"
                    if page_info:
                        message += f"   {page_info}\n"
                    message += f"   详情：{issue_details}\n"  # 不再截断详情
                    if screenshot_url:
                        message += f"   图片URL：{screenshot_url}\n"
                except Exception as e:
                    device_logger.error(f"处理问题记录时发生错误: {str(e)}")
                    device_logger.error(f"问题记录: {issue}")
                    device_logger.error(f"错误详情: {traceback.format_exc()}")
                    continue
        
        device_logger.info(f"【设备轮次通知】准备发送给: {DEFAULT_RECEIVERS}")
        device_logger.info(f"【设备轮次通知】通知内容摘要: {message[:150]}...")
        
        # 将通知内容写入设备日志
        try:
            # 添加主机信息到消息
            log_message = add_host_info_to_message(message)
            
            # 将换行符替换为 | 并写入日志
            log_message_formatted = log_message.replace("\n", " | ")
            device_logger.heart(f"设备轮次通知: {log_message_formatted}")
            device_logger.info(f"已将设备轮次通知写入设备 {device_name} 的日志")
        except Exception as e:
            device_logger.error(f"写入设备日志时发生错误: {str(e)}")
            device_logger.error(traceback.format_exc())
        
        # 设置超时时间，防止请求卡住
        try:
            success = send_individual_message(message, DEFAULT_RECEIVERS)
            if success:
                device_logger.info(f"【设备轮次通知】发送成功: 设备={device_name}, 会话轮次={current_session_round}, 历史轮次={round_num}")
            else:
                device_logger.error(f"【设备轮次通知】发送失败: 设备={device_name}, 会话轮次={current_session_round}, 历史轮次={round_num}")
                # 如果发送失败，不要阻塞程序继续运行
        except Exception as e:
            device_logger.error(f"发送设备轮次通知时发生异常: {str(e)}")
            # 发生异常时不要阻塞程序继续运行
            
        device_logger.info(f"【设备轮次通知】完成设备 {device_name} 的第 {current_session_round} 轮测试通知（历史轮次: {round_num}）")
        return True  # 返回成功标志
    except Exception as e:
        device_logger.error(f"发送设备轮次通知失败: {e}")
        # 添加详细的错误信息记录
        device_logger.error(f"错误详情: {traceback.format_exc()}")
        return False  # 返回失败标志

def heartbeat_loop(interval):
    """心跳循环，定期发送心跳通知"""
    global last_notification_time, start_time
    
    initial_notification_sent = False
    
    # 记录上次检测到的设备数量
    last_ios_count = 0
    last_android_count = 0
    
    # 设备稳定计数器
    device_stable_count = 0
    
    # 记录设备状态更新时间
    device_last_update_times = {}
    
    # 记录上次设备离线检查时间
    last_offline_check_time = datetime.now()
    
    while not stop_event.is_set():
        try:
            current_time = datetime.now()
            
            # 确保start_time不为None
            if start_time is None:
                start_time = current_time
                logger.warning("检测到start_time为None，已重置为当前时间")
            
            # 计算程序运行时间
            runtime = current_time - start_time
            
            # 检查是否有设备连接
            has_devices = False
            ios_count = 0
            android_count = 0
            
            # 检查设备状态更新情况
            if hasattr(device_status, 'items'):
                for device_id, status in device_status.items():
                    if hasattr(status, 'get'):
                        device_name = status.get('device_name', 'Unknown')
                        last_update = status.get('last_update', 0)
                        current_status = status.get('status', 'unknown')
                        
                        # 检查设备状态是否长时间未更新
                        if isinstance(last_update, (int, float)):
                            # 获取上次记录的更新时间
                            prev_update = device_last_update_times.get(device_id, last_update)
                            
                            # 如果状态未变化且超过设定时间未更新，记录警告
                            if last_update == prev_update and (current_time - datetime.fromtimestamp(last_update)).total_seconds() > DEVICE_STATUS_UPDATE_TIMEOUT:
                                logger.warning(f"设备 {device_name} 状态 '{current_status}' 已超过{DEVICE_STATUS_UPDATE_TIMEOUT}秒未更新，可能存在进程通信问题")
                                
                                # 检查进程是否存在
                                try:
                                    # 查找与设备相关的进程
                                    process_name_pattern = f"iOS_{device_name}|Android_{device_name}"
                                    cmd = f"ps aux | grep -E '{process_name_pattern}' | grep -v grep"
                                    result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
                                    
                                    if result.stdout.strip():
                                        logger.info(f"设备 {device_name} 的测试进程仍在运行:\n{result.stdout.strip()}")
                                    else:
                                        logger.warning(f"未找到设备 {device_name} 的测试进程，可能已终止")
                                except Exception as e:
                                    logger.error(f"检查设备 {device_name} 进程状态时发生错误: {str(e)}")
                            
                            # 更新记录的更新时间
                            device_last_update_times[device_id] = last_update
            
            if hasattr(device_status, 'items') and len(device_status) > 0:
                has_devices = True
                
                # 计算iOS和Android设备数量
                for _, status in device_status.items():
                    if hasattr(status, 'get'):
                        platform = status.get('platform', '').lower()
                        if platform == 'ios':
                            ios_count += 1
                        elif platform == 'android':
                            android_count += 1
                
                logger.debug(f"当前设备数量: iOS={ios_count}, Android={android_count}")
                
                # 检查设备数量是否稳定
                if ios_count == last_ios_count and android_count == last_android_count:
                    device_stable_count += 1
                    logger.debug(f"设备数量稳定计数: {device_stable_count}/{DEVICE_STABLE_THRESHOLD}")
                else:
                    device_stable_count = 0
                    logger.info(f"设备数量变化: iOS={last_ios_count}->{ios_count}, Android={last_android_count}->{android_count}")
                
                # 更新上次检测到的设备数量
                last_ios_count = ios_count
                last_android_count = android_count
            
            # 立即发送第一次通知的条件:
            # 1. 尚未发送初始通知
            # 2. 有设备连接
            # 3. 设备数量已经稳定（连续多次检测到相同数量的设备）或程序运行超过设定时间
            if not initial_notification_sent and has_devices and (device_stable_count >= DEVICE_STABLE_THRESHOLD or runtime.total_seconds() > INITIAL_NOTIFICATION_DELAY):
                logger.info(f"【初始心跳通知触发】设备数量已稳定 (iOS={ios_count}, Android={android_count}) 或程序运行超过{INITIAL_NOTIFICATION_DELAY/60}分钟，发送初始心跳通知...")
                send_heartbeat(start_time)
                initial_notification_sent = True
                last_notification_time = current_time
                device_stable_count = 0  # 重置稳定计数器
            # 如果没有设备连接且程序运行超过设定时间，也发送一次通知
            elif not initial_notification_sent and not has_devices and runtime.total_seconds() > INITIAL_NOTIFICATION_DELAY:
                logger.info(f"【初始心跳通知触发】程序运行超过{INITIAL_NOTIFICATION_DELAY/60}分钟但未检测到设备，发送初始心跳通知...")
                send_heartbeat(start_time)
                initial_notification_sent = True
                last_notification_time = current_time
            
            # 检查是否应该发送常规通知（使用HEARTBEAT_INTERVAL而不是固定的30分钟）
            elif last_notification_time and (current_time - last_notification_time).total_seconds() >= interval:
                logger.info(f"【定期心跳通知触发】距离上次心跳通知已经过去 {(current_time - last_notification_time).total_seconds()/60:.1f} 分钟，发送新的心跳通知...")
                send_heartbeat(start_time)
                last_notification_time = current_time
            
            # 检查是否应该检查设备离线状态
            if current_time - last_offline_check_time >= timedelta(minutes=5):
                check_device_status_changes()
                last_offline_check_time = current_time
            
            # 休眠一段时间再检查
            time.sleep(DEVICE_STATUS_CHECK_INTERVAL)  # 每隔设定时间检查一次
            
        except Exception as e:
            logger.error(f"心跳循环中发生错误: {str(e)}")
            time.sleep(DEVICE_STATUS_CHECK_INTERVAL)  # 发生错误后等待设定时间再继续

def check_device_status_changes():
    """检查设备状态变化，只在设备从其他状态变为离线时发送通知
    
    功能：
    - 每5分钟检查一次设备状态
    - 只在设备从非离线状态变为离线状态时发送通知
    - 每个设备离线只通知一次，重连后再离线会再次通知
    """
    global device_previous_status
    
    try:
        # 获取当前所有设备状态
        current_device_statuses = {}
        
        # 使用 device_status_manager 获取设备分类信息
        device_categories = manager.get_devices_by_categories(offline_threshold_seconds=DEVICE_OFFLINE_THRESHOLD)
        
        # 收集所有设备的当前状态
        all_devices = (device_categories['waiting_devices'] + 
                      device_categories['running_devices'] + 
                      device_categories['completed_devices'] + 
                      device_categories['error_devices'] + 
                      device_categories['offline_devices'] + 
                      device_categories['inconsistent_devices'])
        
        for device_info in all_devices:
            # 解析设备信息字符串，格式通常是："设备名 (UDID: xxx, 状态: xxx)"
            try:
                if '(UDID:' in device_info:
                    parts = device_info.split('(UDID:')
                    device_name = parts[0].strip()
                    udid_and_status = parts[1].split(',')[0].strip().rstrip(')')
                    
                    # 获取详细的设备状态
                    device_status_data = get_device_status(udid_and_status)
                    if device_status_data:
                        current_status = device_status_data.get('status', 'unknown')
                        current_device_statuses[udid_and_status] = {
                            'name': device_name,
                            'status': current_status,
                            'udid': udid_and_status
                        }
            except Exception as e:
                logger.debug(f"解析设备信息时出错: {device_info}, 错误: {str(e)}")
                continue
        
        # 检测新的离线设备
        newly_offline_devices = []
        
        for device_udid, current_info in current_device_statuses.items():
            current_status = current_info['status']
            device_name = current_info['name']
            
            # 获取设备的上一次状态
            previous_status = device_previous_status.get(device_udid, 'unknown')
            
            # 检测从非离线状态变为离线状态
            if current_status == 'offline' and previous_status != 'offline':
                newly_offline_devices.append({
                    'udid': device_udid,
                    'name': device_name,
                    'previous_status': previous_status,
                    'current_status': current_status
                })
                logger.warning(f"检测到设备 {device_name} (UDID: {device_udid}) 从 '{previous_status}' 状态变为离线")
            
            # 更新设备状态记录
            device_previous_status[device_udid] = current_status
        
        # 如果有设备新离线，发送通知
        if newly_offline_devices:
            send_offline_notification(newly_offline_devices)
            
    except Exception as e:
        logger.error(f"检查设备离线状态变化时发生错误: {str(e)}")
        logger.error(traceback.format_exc())

def send_offline_notification(offline_devices):
    """
    发送设备离线通知
    
    Args:
        offline_devices: 新离线的设备列表，每个元素包含 udid, name, previous_status, current_status
    """
    try:
        current_time = datetime.now()
        
        # 构建离线通知消息
        message = f"设备离线警告通知\n"
        message += f"时间：{current_time.strftime('%Y-%m-%d %H:%M:%S')}\n"
        message += f"检测到 {len(offline_devices)} 台设备新离线：\n\n"
        
        for i, device in enumerate(offline_devices, 1):
            device_name = device['name']
            device_udid = device['udid']
            previous_status = device['previous_status']
            
            message += f"{i}. 设备：{device_name}\n"
            message += f"   UDID：{device_udid}\n"
            message += f"   状态变化：{previous_status} → 离线\n\n"
        
        message += "请检查设备连接状态或相关测试进程。"
        
        logger.warning(f"发送设备离线通知，涉及 {len(offline_devices)} 台设备")
        
        # 发送通知
        success = send_individual_message(message, DEFAULT_RECEIVERS)
        if success:
            logger.info(f"设备离线通知发送成功")
        else:
            logger.error(f"设备离线通知发送失败")
            
    except Exception as e:
        logger.error(f"发送设备离线通知时发生错误: {str(e)}")
        logger.error(traceback.format_exc())

def send_heartbeat(start_time, is_status_update=False):
    """
    发送心跳通知
    
    Args:
        start_time: 程序启动时间
        is_status_update: 是否为状态更新通知
    
    Returns:
        bool: 通知是否成功发送
    """
    global device_status, last_notification_time
    
    try:
        # 获取当前时间
        current_time = datetime.now()
        
        # 防止并发通知：检查距离上次通知的时间
        with notification_lock:
            if last_notification_time and (current_time - last_notification_time) < timedelta(minutes=1):
                logger.info(f"跳过心跳通知：距离上次通知仅过去了 {(current_time - last_notification_time).total_seconds()} 秒")
                return False
            
            # 计算程序运行时间
            runtime = current_time - start_time
            hours, remainder = divmod(runtime.total_seconds(), 3600)
            minutes, seconds = divmod(remainder, 60)
            
            # 使用 device_status_manager 获取设备状态和相关信息
            
            # 清理掉线超过24小时的设备
            devices_to_cleanup = manager.cleanup_offline_devices(timeout_hours=24.0)
            
            if devices_to_cleanup:
                logger.info(f"清理掉线超过24小时的设备: {len(devices_to_cleanup)} 个")
                for device_info in devices_to_cleanup:
                    device_id = device_info['udid']
                    device_name = device_info['device_name']
                    hours_offline = device_info['hours_offline']
                    logger.info(f"已清除设备: {device_name} (ID: {device_id}, 掉线时间: {hours_offline}小时)")
                    
                    # 从共享字典中也删除（为了兼容性）
                    if hasattr(device_status, 'pop'):
                        try:
                            device_status.pop(device_id, None)
                        except Exception as e:
                            logger.error(f"从共享字典中清除设备时出错: {str(e)}")
            
            # 使用 device_status_manager 获取设备分类和统计信息
            device_categories = manager.get_devices_by_categories(offline_threshold_seconds=DEVICE_OFFLINE_THRESHOLD)
            platform_counts = manager.get_platform_device_counts(offline_threshold_seconds=DEVICE_OFFLINE_THRESHOLD)
            
            waiting_devices = device_categories['waiting_devices']
            running_devices = device_categories['running_devices']
            completed_devices = device_categories['completed_devices']
            error_devices = device_categories['error_devices']
            offline_devices = device_categories['offline_devices']
            inconsistent_devices = device_categories['inconsistent_devices']
            
            ios_count = platform_counts['ios']
            android_count = platform_counts['android']
            total_devices = platform_counts['total']
            
            logger.info(f"设备总数(不含掉线): {total_devices} (iOS: {ios_count}, Android: {android_count})")
            
            # 构建消息
            message = f"美团App测试{'状态更新' if is_status_update else '心跳通知'}\n"
            message += f"时间：{current_time.strftime('%Y-%m-%d %H:%M:%S')}\n"
            message += f"程序已运行：{int(hours)}小时{int(minutes)}分钟{int(seconds)}秒\n"
            message += f"设备总数：{total_devices} (iOS: {ios_count}, Android: {android_count})\n\n"
            
            # 添加进程状态信息 - 移除主进程信息
            process_info = get_process_status()
            message += f"进程状态：\n"
            message += f"- iOS测试进程：{process_info['ios_processes']} 个运行中\n"
            message += f"- Android测试进程：{process_info['android_processes']} 个运行中\n"
            
            # 添加进程详情信息 - 移除主进程信息
            if process_info['process_details']:
                message += "进程详情：\n"
                
                # 显示活跃的iOS进程
                ios_processes = [p for p in process_info['process_details'] 
                                if p.get('type') == 'ios' and p.get('active', False)]
                if ios_processes:
                    message += f"- 活跃的iOS进程 ({len(ios_processes)})：\n"
                    for proc in ios_processes:
                        proc_device = proc.get('device', '')
                        proc_pid = proc.get('pid', 'N/A')
                        message += f"  • {proc_device} [PID: {proc_pid}]\n"
                
                # 显示活跃的Android进程
                android_processes = [p for p in process_info['process_details'] 
                                    if p.get('type') == 'android' and p.get('active', False)]
                if android_processes:
                    message += f"- 活跃的Android进程 ({len(android_processes)})：\n"
                    for proc in android_processes:
                        proc_device = proc.get('device', '')
                        proc_pid = proc.get('pid', 'N/A')
                        message += f"  • {proc_device} [PID: {proc_pid}]\n"
            
            message += "\n"
            
            # 添加设备状态信息到消息
            if waiting_devices:
                message += f"等待中设备 ({len(waiting_devices)})：\n"
                for device in waiting_devices:
                    message += f"- {device}\n"
                message += "\n"
            
            if running_devices:
                message += f"测试中设备 ({len(running_devices)})：\n"
                for device in running_devices:
                    message += f"- {device}\n"
                message += "\n"
            
            if completed_devices:
                message += f"测试完成设备 ({len(completed_devices)})：\n"
                for device in completed_devices:
                    message += f"- {device}\n"
                message += "\n"
            
            if error_devices:
                message += f"异常状态设备 ({len(error_devices)})：\n"
                for device in error_devices:
                    message += f"- {device}\n"
                message += "\n"
            
            # 添加已掉线设备信息
            if offline_devices:
                message += f"已掉线设备 ({len(offline_devices)})：\n"
                for device in offline_devices:
                    message += f"- {device}\n"
                message += "\n"
            
            # 添加已清理设备信息
            if devices_to_cleanup:
                message += f"已清理设备 ({len(devices_to_cleanup)})：\n"
                for device_info in devices_to_cleanup:
                    device_id = device_info['udid']
                    device_name = device_info['device_name']
                    hours_offline = device_info['hours_offline']
                    message += f"- {device_name} (ID: {device_id}, 掉线时间: {hours_offline}小时)\n"
            
            # 添加状态不一致设备信息
            if inconsistent_devices:
                message += f"状态可能不一致的设备 ({len(inconsistent_devices)})：\n"
                for device in inconsistent_devices:
                    message += f"- {device}\n"
                message += "\n"
            
            # 发送通知
            notification_type = '状态更新通知' if is_status_update else '聚合心跳通知'
            logger.info(f"【{notification_type}】准备发送通知，当前设备总数: {total_devices}")
            
            # 更新最后通知时间
            last_notification_time = current_time
            
            # 异步发送通知
            notification_thread = threading.Thread(
                target=send_notification,
                args=(message,),
                daemon=True
            )
            notification_thread.start()
            logger.info(f"已启动异步通知线程: {notification_thread.name}")
            return True
    except Exception as e:
        logger.error(f"发送心跳通知时发生错误: {str(e)}")
        return False

def stop_heartbeat_monitor():
    """
    停止心跳监控系统
    """
    global heartbeat_thread, stop_event
    
    if heartbeat_thread is not None and heartbeat_thread.is_alive():
        logger.info("正在停止心跳监控系统...")
        stop_event.set()
        heartbeat_thread.join(timeout=5)
        logger.info("心跳监控系统已停止")
    else:
        logger.info("心跳监控系统未运行")

def get_device_status_summary():
    """
    获取设备状态摘要，用于外部查询
    
    :return: 设备状态字典的副本
    """
    # 现在直接从文件读取设备状态
    return manager.get_all_device_status()

def get_device_statistics_summary():
    """
    获取设备统计信息摘要，用于外部查询
    
    :return: 设备统计信息字典的副本
    """
    # 注意：现在使用 calc_overall_metrics() 获取整体统计信息
    all_device_statuses = list(manager.get_all_device_status().values())
    if all_device_statuses:
        return calc_overall_metrics(all_device_statuses)
    else:
        return {}

def load_device_status_from_file(device_id):
    """
    从文件加载设备状态（兼容性函数）
    
    Args:
        device_id: 设备ID
    
    Returns:
        dict: 设备状态数据，如果文件不存在则返回None
    """
    status_data = get_device_status(device_id)
    
    # 如果返回 False（表示不存在），则返回 None 以保持兼容性
    if status_data is False:
        return None
    
    # 确保有scale_ratio字段，如果没有则设为None
    if status_data and 'scale_ratio' not in status_data:
        status_data['scale_ratio'] = None
        
    return status_data

# 注意：load_all_device_status_from_files 函数已被 device_status_manager.get_all_device_status() 替代

def get_process_status():
    """
    获取当前测试进程的状态信息
    
    :return: 包含进程状态信息的字典
    """
    try:
        # 使用 device_status_manager 获取进程状态信息
        result = manager.get_process_status_info(offline_threshold_seconds=DEVICE_OFFLINE_THRESHOLD)
        
        logger.info(f"从设备状态中检测到 {result['ios_processes']} 个iOS进程和 {result['android_processes']} 个Android进程")
        
    except Exception as e:
        logger.error(f"获取进程状态时发生错误: {str(e)}")
        logger.error(traceback.format_exc())
        
        # 如果出错，返回空结果
        result = {
            'ios_processes': 0,
            'android_processes': 0,
            'process_details': []
        }
    
    return result

def send_notification(message):
    """
    发送通知消息
    
    :param message: 要发送的消息内容
    """
    try:
        # 接收者列表
        receivers = ["cuijie12"]
        logger.info(f"发送通知给: {receivers}")
        
        # 注意：移除在这里写入全局日志的代码，避免重复日志
        # 全局日志已在 send_heartbeat 函数中写入，包含了设备标识
        
        # 使用send_individual_message发送消息
        success = send_individual_message(message, receivers)
        
        if success:
            logger.info("通知发送成功")
        else:
            logger.error("通知发送失败")
    except Exception as e:
        logger.error(f"发送通知时发生错误: {str(e)}")
        logger.error(traceback.format_exc())

def get_device_issues_in_session(device_name, start_time):
    """
    获取本次会话（start_time 之后）的所有 issue，遍历所有轮转日志
    """
    issues_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), '../log/python_logs/issues')
    pattern = os.path.join(issues_dir, f"{device_name}_issues.log*")
    all_issues = []
    for log_file in glob.glob(pattern):
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if not line or '问题日志记录器初始化完成' in line:
                        continue
                    json_start = line.find('{')
                    if json_start == -1:
                        continue
                    try:
                        issue = json.loads(line[json_start:])
                        # 只保留 start_time 之后的 issue
                        ts = issue.get('timestamp')
                        if ts:
                            try:
                                issue_time = datetime.strptime(ts, '%Y-%m-%d %H:%M:%S')
                                if issue_time.timestamp() >= start_time:
                                    all_issues.append(issue)
                            except Exception:
                                continue
                    except Exception:
                        continue
        except Exception as e:
            logger.error(f"读取 issue 日志 {log_file} 失败: {e}")
    return all_issues

def calc_device_metrics(device_status):
    device_name = device_status['device_name']
    start_time = device_status.get('start_time', 0)
    session_round_num = device_status.get('session_round_num', 0)
    completed_icon_num = device_status.get('completed_icon_num', 0)
    total_icon_num = device_status.get('total_icon_num', 0)  # 当前轮次总图标数
    completed_icons_cumulative = device_status.get('completed_icons_cumulative', 0)  # 累积完成图标数
    total_icons_cumulative = device_status.get('total_icons_cumulative', 0)  # 累积总图标数
    now = time.time()

    issues = get_device_issues_in_session(device_name, start_time)
    app_issues = [i for i in issues if i.get('issue_type') == 'app']
    test_issues = [i for i in issues if i.get('issue_type') == 'test']

    app_issue_count = len(app_issues)
    # 使用累积完成图标数计算应用问题率
    app_issue_rate = (app_issue_count / completed_icons_cumulative * 100) if completed_icons_cumulative else 0

    test_issue_rounds = set(i.get('round_num') for i in test_issues if i.get('round_num') is not None)
    test_issue_rate = (len(test_issue_rounds) / session_round_num * 100) if session_round_num else 0

    completed_rounds = session_round_num - len(test_issue_rounds)
    total_time = now - start_time if start_time else 0
    # 使用累积完成图标数计算平均图标耗时
    avg_icon_time = (total_time / completed_icons_cumulative) if completed_icons_cumulative else 0

    return {
        'app_issue_rate': app_issue_rate,
        'test_issue_rate': test_issue_rate,
        'completed_rounds': completed_rounds,
        'avg_icon_time': avg_icon_time,
        'app_issue_count': app_issue_count,
        'test_issue_rounds': len(test_issue_rounds),
        'total_time': total_time,
        'completed_icon_num': completed_icon_num,
        'session_round_num': session_round_num,
        'start_time': start_time,  # 添加 start_time 字段
        'total_icons': total_icons_cumulative,  # 使用累积总图标数
        'completed_icons': completed_icons_cumulative,  # 使用累积完成图标数
        'total_icon_num': total_icon_num,  # 当前轮次总图标数
    }

def calc_overall_metrics(all_device_statuses):
    total_app_issues = 0
    total_test_issue_rounds = 0
    total_completed_icon_num = 0
    total_session_round_num = 0
    total_time = 0

    for status in all_device_statuses:
        metrics = calc_device_metrics(status)
        total_app_issues += metrics['app_issue_count']
        total_test_issue_rounds += metrics['test_issue_rounds']
        total_completed_icon_num += metrics['completed_icon_num']
        total_session_round_num += metrics['session_round_num']
        total_time += metrics['total_time']

    app_issue_rate = (total_app_issues / total_completed_icon_num * 100) if total_completed_icon_num else 0
    test_issue_rate = (total_test_issue_rounds / total_session_round_num * 100) if total_session_round_num else 0
    avg_icon_time = (total_time / total_completed_icon_num) if total_completed_icon_num else 0

    return {
        'app_issue_rate': app_issue_rate,
        'test_issue_rate': test_issue_rate,
        'avg_icon_time': avg_icon_time,
        'total_app_issues': total_app_issues,
        'total_test_issue_rounds': total_test_issue_rounds,
        'total_completed_icon_num': total_completed_icon_num,
        'total_session_round_num': total_session_round_num,
        'total_time': total_time,
    }