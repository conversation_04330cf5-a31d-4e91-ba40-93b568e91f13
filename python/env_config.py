#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
环境配置模块

这个模块用于管理整个项目的环境配置，包括API端点等。
所有需要区分环境的Python模块都应该导入并使用这个模块来获取正确的环境配置。
"""

import os
from pathlib import Path

# 从.env文件读取环境配置
def _read_env_file():
    # 获取项目根目录
    root_dir = Path(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    env_file = root_dir / '.env'
    
    if env_file.exists():
        try:
            with open(env_file, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#'):
                        key, value = line.split('=', 1)
                        os.environ[key.strip()] = value.strip()
        except Exception as e:
            print(f"读取.env文件时出错: {e}")

# 读取.env文件
_read_env_file()

# 可选环境: "prod" (生产环境) 或 "test" (测试环境)
# 默认使用生产环境
_ENV = os.environ.get("PROJECT_ENV", "prod")

# 环境对应的基础域名
BASE_DOMAINS = {
    "prod": "http://qaassist.sankuai.com",
    "test": "http://localhost:8080"
}

# 不同服务的API路径配置（不包含域名前缀）
API_PATHS = {
    # Horus 服务API
    "horus": "/compass/api/horus/process-image",
    # 日志服务API
    "log_service": "/compass/api/category/inspection/add",
    # 可以添加更多服务的API路径配置
}

# 当前设备/主机ID，可用于区分不同的执行设备
_HOST_ID = os.environ.get("HOST_ID", "")

def set_env(env):
    """
    设置当前环境
    
    参数:
        env: 环境名称，"prod" 或 "test"
        
    返回:
        设置后的环境名称
    """
    global _ENV
    if env in BASE_DOMAINS:
        _ENV = env
        # 也可以设置环境变量，使得新启动的进程也能获取到正确的环境
        os.environ["PROJECT_ENV"] = env
    return _ENV

def get_env():
    """
    获取当前环境
    
    返回:
        当前环境名称，"prod" 或 "test"
    """
    return _ENV

def get_base_domain():
    """
    获取当前环境的基础域名
    
    返回:
        当前环境的基础域名
    """
    return BASE_DOMAINS.get(_ENV, BASE_DOMAINS["prod"])

def get_api_url(service_name):
    """
    获取指定服务在当前环境下的完整API URL
    
    参数:
        service_name: 服务名称，如 "horus" 或 "log_service"
        
    返回:
        对应服务在当前环境下的完整API URL
    """
    if service_name in API_PATHS:
        base_domain = get_base_domain()
        path = API_PATHS[service_name]
        return f"{base_domain}{path}"
    return None

def add_service_path(service_name, path):
    """
    添加新的服务API路径
    
    参数:
        service_name: 服务名称
        path: API路径（不包含域名前缀）
    """
    API_PATHS[service_name] = path

# 便捷函数，获取特定服务的API URL
def get_horus_api_url():
    """获取Horus服务的API URL"""
    return get_api_url("horus")

def get_log_service_api_url():
    """获取日志服务的API URL"""
    return get_api_url("log_service")

# 如果需要自定义某个环境的特定服务URL，可以使用这个函数
def set_custom_service_url(service_name, env, url):
    """
    为特定环境的特定服务设置自定义URL
    
    参数:
        service_name: 服务名称
        env: 环境名称 ("prod" 或 "test")
        url: 完整的自定义URL
    """
    global API_PATHS, BASE_DOMAINS
    
    # 如果服务不存在，先添加
    if service_name not in API_PATHS:
        API_PATHS[service_name] = ""
    
    # 创建一个特殊标记，表示这个服务在特定环境下使用自定义URL
    # 实现方式：创建一个特殊的环境变量
    env_var_name = f"CUSTOM_URL_{service_name}_{env}".upper()
    os.environ[env_var_name] = url

def get_custom_service_url(service_name, env):
    """
    获取特定环境的特定服务的自定义URL
    
    参数:
        service_name: 服务名称
        env: 环境名称 ("prod" 或 "test")
        
    返回:
        自定义URL，如果没有设置则返回None
    """
    env_var_name = f"CUSTOM_URL_{service_name}_{env}".upper()
    return os.environ.get(env_var_name)

# 重写get_api_url函数以支持自定义URL
def get_api_url(service_name):
    """
    获取指定服务在当前环境下的完整API URL
    支持自定义URL
    
    参数:
        service_name: 服务名称，如 "horus" 或 "log_service"
        
    返回:
        对应服务在当前环境下的完整API URL
    """
    # 检查是否有自定义URL
    custom_url = get_custom_service_url(service_name, _ENV)
    if custom_url:
        return custom_url
        
    # 没有自定义URL，使用标准组合
    if service_name in API_PATHS:
        base_domain = get_base_domain()
        path = API_PATHS[service_name]
        return f"{base_domain}{path}"
    return None

def set_host_id(host_id):
    """
    设置当前设备/主机ID
    
    参数:
        host_id: 设备ID字符串
        
    返回:
        设置后的设备ID
    """
    global _HOST_ID
    _HOST_ID = host_id
    # 设置环境变量，使得新启动的进程也能获取到正确的设备ID
    os.environ["HOST_ID"] = host_id
    return _HOST_ID

def get_host_id():
    """
    获取当前设备/主机ID
    
    返回:
        当前设备/主机ID，如果未设置则返回空字符串
    """
    return _HOST_ID

if __name__ == "__main__":
    # 测试环境配置
    print(f"当前环境: {get_env()}")
    print(f"基础域名: {get_base_domain()}")
    print(f"Horus API URL: {get_horus_api_url()}")
    print(f"日志服务 API URL: {get_log_service_api_url()}")
    
    # 切换到测试环境
    set_env("test")
    print(f"\n切换到测试环境后:")
    print(f"当前环境: {get_env()}")
    print(f"基础域名: {get_base_domain()}")
    print(f"Horus API URL: {get_horus_api_url()}")
    print(f"日志服务 API URL: {get_log_service_api_url()}")
    
    # 测试自定义URL
    print("\n测试自定义URL:")
    set_custom_service_url("test_service", "test", "https://custom-test.example.com/api")
    print(f"自定义服务URL: {get_api_url('test_service')}")
    
    # 切换回生产环境
    set_env("prod") 