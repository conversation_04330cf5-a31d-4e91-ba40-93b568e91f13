#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re
import glob
import argparse
import shutil
from datetime import datetime

def clean_wda_log(log_file_path, backup=False):
    """
    清理WDA日志文件，删除编译过程的详细信息
    
    Args:
        log_file_path: 日志文件路径
        backup: 是否备份原始文件
    
    Returns:
        bool: 是否修改了文件
    """
    print(f"处理文件: {log_file_path}")
    
    # 备份原始文件
    if backup:
        backup_path = f"{log_file_path}.bak"
        try:
            shutil.copy2(log_file_path, backup_path)
            print(f"已备份原始文件到: {backup_path}")
        except Exception as e:
            print(f"备份文件失败: {e}")
            return False
    
    try:
        # 读取日志文件内容
        with open(log_file_path, 'r', encoding='utf-8', errors='ignore') as f:
            lines = f.readlines()
    except Exception as e:
        print(f"读取文件失败: {e}")
        return False
    
    # 查找编译开始和结束的行号
    start_line = -1
    end_line = -1
    
    # 编译开始的可能标记
    start_markers = [
        "CreateBuildDescription",
        "ComputeTargetDependencyGraph",
        "Prepare packages"
    ]
    
    # 编译结束的可能标记
    end_markers = [
        "Running tests...",
        "Test Suite 'All tests' started",
        "Test Suite 'WebDriverAgentRunner.xctest' started",
        "WebDriverAgentRunner-Runner"
    ]
    
    # 查找编译开始的行号
    for i, line in enumerate(lines):
        for marker in start_markers:
            if marker in line:
                start_line = i
                break
        if start_line != -1:
            break
    
    # 如果找到了编译开始的行号，查找编译结束的行号
    if start_line != -1:
        for i in range(start_line + 1, len(lines)):
            for marker in end_markers:
                if marker in lines[i]:
                    # 确保这是真正的测试开始标记，而不是编译过程中的日志
                    if "Test Suite" in lines[i] or "Running tests" in lines[i] or (
                        i + 1 < len(lines) and "Test Suite" in lines[i + 1]
                    ):
                        end_line = i
                        break
            if end_line != -1:
                break
    
    # 特殊情况：如果日志被中断，可能没有编译结束标记
    if start_line != -1 and end_line == -1:
        for i in range(start_line + 1, len(lines)):
            if "BUILD INTERRUPTED" in lines[i]:
                end_line = i
                break
    
    # 如果找到了编译开始和结束的行号，则删除这部分内容
    if start_line != -1 and end_line != -1:
        # 保留编译开始前和编译结束后的内容
        new_lines = lines[:start_line] + [f"...... (已简化编译过程) ......\n"] + lines[end_line:]
        
        try:
            # 写入新的日志文件
            with open(log_file_path, 'w', encoding='utf-8') as f:
                f.writelines(new_lines)
            
            print(f"已简化文件: {log_file_path}")
            return True
        except Exception as e:
            print(f"写入文件失败: {e}")
            return False
    else:
        print(f"未找到需要简化的编译过程: {log_file_path}")
        return False

def process_log_directory(directory_path, backup=False):
    """
    处理目录中的所有WDA日志文件
    
    Args:
        directory_path: 日志文件目录路径
        backup: 是否备份原始文件
    """
    # 获取目录中的所有.log文件
    log_files = glob.glob(os.path.join(directory_path, "*.log"))
    
    if not log_files:
        print(f"在目录 {directory_path} 中未找到日志文件")
        return
    
    print(f"找到 {len(log_files)} 个日志文件")
    
    # 统计处理结果
    processed_count = 0
    modified_count = 0
    error_count = 0
    
    # 处理每个日志文件
    for log_file in log_files:
        processed_count += 1
        try:
            if clean_wda_log(log_file, backup):
                modified_count += 1
        except Exception as e:
            print(f"处理文件 {log_file} 时出错: {e}")
            error_count += 1
    
    print(f"\n处理完成: 共处理 {processed_count} 个文件，简化了 {modified_count} 个文件，失败 {error_count} 个文件")

def clean_wda_logs(log_path, backup=False):
    """
    清理WDA日志的主函数
    
    Args:
        log_path: 日志文件路径或目录路径
        backup: 是否备份原始文件
    
    Returns:
        bool: 是否成功处理
    """
    try:
        if os.path.isdir(log_path):
            # 如果是目录，处理目录中的所有日志文件
            process_log_directory(log_path, backup)
            return True
        elif os.path.isfile(log_path) and log_path.endswith(".log"):
            # 如果是单个日志文件，直接处理
            return clean_wda_log(log_path, backup)
        else:
            print(f"错误: {log_path} 不是有效的日志文件或目录")
            return False
    except Exception as e:
        print(f"程序执行出错: {e}")
        return False

if __name__ == "__main__":
    
    
    clean_wda_logs("/Users/<USER>/Desktop/work/platform_autotest_frame_python/log/wda_logs", False) 