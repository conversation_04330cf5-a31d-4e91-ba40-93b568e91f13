import time
import threading
import subprocess
import logging

class Config:
    # ADB服务相关常量
    ADB_RESTART_INTERVAL = 4 * 60 * 60  # 4小时，单位为秒
    ADB_RESTART_TIMEOUT = 30  # ADB重启超时时间（秒）
    ADB_DEVICE_WAIT_TIME = 10  # 重启后等待设备连接的时间（秒）

class AdbServiceManager:
    """ADB服务管理器，负责ADB服务的重启和状态检查"""
    
    def __init__(self, logger):
        self.logger = logger
        self.last_restart_time = time.time()
        self._lock = threading.Lock()
    
    def restart_adb_service(self):
        """重启ADB服务
        
        Returns:
            bool: 重启是否成功
        """
        with self._lock:
            try:
                self.logger.info("开始重启ADB服务...")
                
                # 先终止ADB服务
                subprocess.run(['adb', 'kill-server'], check=True, timeout=Config.ADB_RESTART_TIMEOUT)
                time.sleep(2)  # 等待服务完全停止
                
                # 启动ADB服务
                subprocess.run(['adb', 'start-server'], check=True, timeout=Config.ADB_RESTART_TIMEOUT)
                time.sleep(Config.ADB_DEVICE_WAIT_TIME)  # 等待设备重新连接
                
                # 验证ADB服务是否正常运行
                result = subprocess.run(['adb', 'devices'], check=True, capture_output=True, text=True, timeout=Config.ADB_RESTART_TIMEOUT)
                if "List of devices attached" in result.stdout:
                    self.logger.info("ADB服务重启成功")
                    self.last_restart_time = time.time()
                    return True
                else:
                    self.logger.error("ADB服务重启后状态异常")
                    return False
                    
            except subprocess.TimeoutExpired:
                self.logger.error("重启ADB服务超时")
                return False
            except subprocess.CalledProcessError as e:
                self.logger.error(f"重启ADB服务失败: {e}")
                return False
            except Exception as e:
                self.logger.error(f"重启ADB服务时发生未知错误: {e}")
                return False
    
    def check_and_restart_if_needed(self):
        """检查是否需要重启ADB服务，如果需要则重启
        
        Returns:
            bool: 如果重启了返回True，否则返回False
        """
        current_time = time.time()
        if current_time - self.last_restart_time >= Config.ADB_RESTART_INTERVAL:
            self.logger.info("ADB服务运行时间已超过4小时，准备重启...")
            return self.restart_adb_service()
        return False
    
    def is_adb_service_healthy(self):
        """检查ADB服务是否正常运行
        
        Returns:
            bool: 服务是否正常
        """
        try:
            result = subprocess.run(['adb', 'devices'], capture_output=True, text=True, timeout=10)
            return result.returncode == 0 and "List of devices attached" in result.stdout
        except Exception as e:
            self.logger.error(f"检查ADB服务状态时发生错误: {e}")
            return False

# 获取完整的配置类
try:
    from config import Config as FullConfig
    # 更新本地Config类的常量
    Config.ADB_RESTART_INTERVAL = FullConfig.ADB_RESTART_INTERVAL
    Config.ADB_RESTART_TIMEOUT = FullConfig.ADB_RESTART_TIMEOUT
    Config.ADB_DEVICE_WAIT_TIME = FullConfig.ADB_DEVICE_WAIT_TIME
except ImportError:
    # 如果导入失败，使用默认值
    pass

# 导入全局日志记录器
try:
    from log_manager import global_logger
except ImportError:
    # 如果无法导入全局日志记录器，创建一个默认的
    global_logger = logging.getLogger("adb_manager")
    handler = logging.StreamHandler()
    formatter = logging.Formatter("%(asctime)s - %(levelname)s - [%(name)s] - %(message)s")
    handler.setFormatter(formatter)
    global_logger.addHandler(handler)
    global_logger.setLevel(logging.INFO)

# 创建全局ADB服务管理器实例
adb_service_manager = AdbServiceManager(global_logger) 