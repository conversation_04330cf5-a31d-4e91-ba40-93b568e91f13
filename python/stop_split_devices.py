#!/usr/bin/env python3
"""
优雅停止 split_devices.py 测试流程的所有服务

该脚本会：
1. 查找并优雅地停止 split_devices.py 主进程
2. 停止所有相关的子进程和服务
3. 清理残留的服务实例
4. 提供详细的停止流程日志

使用方法：
python3 stop_split_devices.py
"""

import os
import sys
import time
import signal
import subprocess
import psutil
import logging
from datetime import datetime
from typing import List, Dict, Optional

# 设置日志格式
log_dir = 'log/stop_logs'
os.makedirs(log_dir, exist_ok=True)
log_file = os.path.join(log_dir, f'stop_split_devices_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler(log_file)
    ]
)
logger = logging.getLogger(__name__)

class SplitDevicesKiller:
    """split_devices.py 服务停止器"""
    
    def __init__(self):
        self.stopped_processes = []
        self.failed_processes = []
        self.stopped_services = []
        
    def find_split_devices_processes(self) -> List[psutil.Process]:
        """查找所有与 split_devices.py 相关的进程"""
        processes = []
        current_pid = os.getpid()  # 获取当前脚本的PID
        
        logger.info("🔍 正在查找 split_devices.py 相关进程...")
        
        for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'cwd']):
            try:
                # 跳过当前停止脚本的进程
                if proc.pid == current_pid:
                    continue
                    
                cmdline = proc.info['cmdline']
                if not cmdline:
                    continue
                    
                cmdline_str = ' '.join(cmdline)
                
                # 跳过停止脚本相关的进程
                if 'stop_split_devices.py' in cmdline_str or 'check_split_devices_status.py' in cmdline_str:
                    continue
                
                # 查找主进程
                if 'split_devices.py' in cmdline_str:
                    processes.append(proc)
                    logger.info(f"  📍 找到主进程: PID={proc.pid}, CMD={cmdline_str}")
                
                # 查找相关的测试进程
                elif any(keyword in cmdline_str for keyword in [
                    'test_meituan_ios', 'test_meituan_android',
                    'iOS_', 'Android_', 'DeviceStartController'
                ]):
                    processes.append(proc)
                    logger.info(f"  📍 找到测试进程: PID={proc.pid}, CMD={cmdline_str}")
                
                # 查找multiprocessing spawn进程 - 这些是split_devices.py的子进程
                elif ('multiprocessing.spawn' in cmdline_str or 
                      'multiprocessing.resource_tracker' in cmdline_str):
                    # 检查是否是我们的工作目录下的进程
                    try:
                        cwd = proc.info.get('cwd', '')
                        if 'platform_autotest_frame_python' in cwd:
                            processes.append(proc)
                            logger.info(f"  📍 找到multiprocessing子进程: PID={proc.pid}")
                    except:
                        # 如果无法获取cwd，通过时间判断（最近启动的）
                        try:
                            create_time = proc.create_time()
                            if time.time() - create_time < 86400:  # 24小时内启动的
                                processes.append(proc)
                                logger.info(f"  📍 找到multiprocessing子进程: PID={proc.pid}")
                        except:
                            pass
                
                # 查找log_service.py进程
                elif 'log_service.py' in cmdline_str:
                    processes.append(proc)
                    logger.info(f"  📍 找到日志服务进程: PID={proc.pid}")
                
                # 查找心跳服务进程（更精确的匹配）
                elif ('heartbeat_monitor' in cmdline_str or 'heartbeat' in cmdline_str) and 'python' in cmdline_str:
                    processes.append(proc)
                    logger.info(f"  📍 找到心跳服务进程: PID={proc.pid}")
                    
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
                
        logger.info(f"  ✅ 共找到 {len(processes)} 个相关进程")
        return processes
    
    def find_service_processes(self) -> Dict[str, List[psutil.Process]]:
        """查找 Appium、WDA 和相关服务进程"""
        services = {
            'appium': [],
            'wda': [],
            'ios_runwda': [],
            'ios_forward': [],
            'xcodebuild': [],
            'heartbeat': []
        }

        logger.info("🔍 正在查找服务进程...")

        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                cmdline = proc.info['cmdline']
                name = proc.info['name'].lower()

                if not cmdline:
                    continue

                cmdline_str = ' '.join(cmdline)
                cmdline_str_lower = cmdline_str.lower()

                # 查找 Appium 进程
                if 'appium' in name or 'appium' in cmdline_str_lower:
                    services['appium'].append(proc)
                    logger.info(f"  📍 找到 Appium 进程: PID={proc.pid}")

                # 查找 iOS runwda 进程（新的go-ios工具链）
                elif 'ios runwda' in cmdline_str:
                    services['ios_runwda'].append(proc)
                    logger.info(f"  📍 找到 iOS runwda 进程: PID={proc.pid}")

                # 查找 iOS forward 进程（新的go-ios工具链）
                elif 'ios forward' in cmdline_str:
                    services['ios_forward'].append(proc)
                    logger.info(f"  📍 找到 iOS forward 进程: PID={proc.pid}")

                # 查找传统的 WDA/xcodebuild 进程
                elif ('xcodebuild' in name or 'xcodebuild' in cmdline_str_lower) and 'webdriveragent' in cmdline_str_lower:
                    services['wda'].append(proc)
                    logger.info(f"  📍 找到传统 WDA 进程: PID={proc.pid}")

                elif 'xcodebuild' in name or 'xcodebuild' in cmdline_str_lower:
                    services['xcodebuild'].append(proc)
                    logger.info(f"  📍 找到 xcodebuild 进程: PID={proc.pid}")

                # 查找心跳监控相关进程
                elif 'heartbeat' in cmdline_str_lower and 'python' in cmdline_str_lower:
                    services['heartbeat'].append(proc)
                    logger.info(f"  📍 找到心跳监控进程: PID={proc.pid}")

            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue

        total_services = sum(len(procs) for procs in services.values())
        logger.info(f"  ✅ 共找到 {total_services} 个服务进程")
        return services
    
    def send_graceful_shutdown_signal(self, processes: List[psutil.Process]) -> None:
        """发送优雅关闭信号"""
        if not processes:
            return
            
        logger.info("📤 发送优雅关闭信号 (SIGTERM)...")
        
        for proc in processes:
            try:
                logger.info(f"  🎯 向进程 PID={proc.pid} 发送 SIGTERM 信号")
                proc.send_signal(signal.SIGTERM)
            except (psutil.NoSuchProcess, psutil.AccessDenied) as e:
                logger.warning(f"  ⚠️  无法向进程 PID={proc.pid} 发送信号: {e}")
    
    def wait_for_processes_to_exit(self, processes: List[psutil.Process], timeout: int = 30) -> List[psutil.Process]:
        """等待进程优雅退出"""
        if not processes:
            return []
            
        logger.info(f"⏳ 等待进程优雅退出 (超时: {timeout}秒)...")
        
        remaining_processes = []
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            remaining_processes = []
            
            for proc in processes:
                try:
                    if proc.is_running():
                        remaining_processes.append(proc)
                    else:
                        logger.info(f"  ✅ 进程 PID={proc.pid} 已优雅退出")
                        self.stopped_processes.append(proc.pid)
                except psutil.NoSuchProcess:
                    logger.info(f"  ✅ 进程 PID={proc.pid} 已退出")
                    self.stopped_processes.append(proc.pid)
            
            if not remaining_processes:
                logger.info("  🎉 所有进程已优雅退出")
                return []
            
            time.sleep(1)
        
        logger.warning(f"  ⚠️  超时后仍有 {len(remaining_processes)} 个进程未退出")
        return remaining_processes
    
    def force_kill_processes(self, processes: List[psutil.Process]) -> None:
        """强制终止进程"""
        if not processes:
            return
            
        logger.info("💀 强制终止剩余进程 (SIGKILL)...")
        
        for proc in processes:
            try:
                logger.info(f"  🎯 强制终止进程 PID={proc.pid}")
                proc.kill()
                proc.wait(timeout=5)
                logger.info(f"  ✅ 进程 PID={proc.pid} 已被强制终止")
                self.stopped_processes.append(proc.pid)
            except (psutil.NoSuchProcess, psutil.TimeoutExpired, psutil.AccessDenied) as e:
                logger.error(f"  ❌ 无法强制终止进程 PID={proc.pid}: {e}")
                self.failed_processes.append(proc.pid)
    
    def stop_heartbeat_monitor(self) -> None:
        """停止心跳监控系统"""
        logger.info("💓 尝试优雅停止心跳监控系统...")

        try:
            # 尝试导入心跳监控模块并调用停止函数
            sys.path.append(os.path.dirname(os.path.abspath(__file__)))
            from heartbeat_monitor import stop_heartbeat_monitor

            stop_heartbeat_monitor()
            logger.info("  ✅ 心跳监控系统已优雅停止")
            self.stopped_services.append("HeartbeatMonitor")
        except ImportError:
            logger.warning("  ⚠️  无法导入心跳监控模块，将通过进程查找方式停止")
        except Exception as e:
            logger.warning(f"  ⚠️  优雅停止心跳监控失败: {e}")

    def stop_wda_services(self) -> None:
        """停止WDA相关服务"""
        logger.info("📱 停止WDA相关服务...")

        try:
            # 查找并停止 ios runwda 进程
            cmd = "ps -ax | grep 'ios runwda' | grep -v grep"
            result = subprocess.run(cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)

            if result.returncode == 0 and result.stdout.strip():
                wda_processes = result.stdout.strip().split('\n')
                for process_line in wda_processes:
                    parts = process_line.strip().split()
                    if parts:
                        pid = parts[0]
                        try:
                            logger.info(f"  🎯 停止 ios runwda 进程 PID={pid}")
                            subprocess.run(['kill', '-TERM', pid], check=False)
                            self.stopped_services.append(f"ios_runwda_{pid}")
                        except Exception as e:
                            logger.warning(f"  ⚠️  停止 ios runwda 进程 {pid} 失败: {e}")

            # 查找并停止 ios forward 进程
            cmd = "ps -ax | grep 'ios forward' | grep -v grep"
            result = subprocess.run(cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)

            if result.returncode == 0 and result.stdout.strip():
                forward_processes = result.stdout.strip().split('\n')
                for process_line in forward_processes:
                    parts = process_line.strip().split()
                    if parts:
                        pid = parts[0]
                        try:
                            logger.info(f"  🎯 停止 ios forward 进程 PID={pid}")
                            subprocess.run(['kill', '-TERM', pid], check=False)
                            self.stopped_services.append(f"ios_forward_{pid}")
                        except Exception as e:
                            logger.warning(f"  ⚠️  停止 ios forward 进程 {pid} 失败: {e}")

            # 查找并停止 ios tunnel 进程
            cmd = "ps -ax | grep 'ios tunnel' | grep -v grep"
            result = subprocess.run(cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)

            if result.returncode == 0 and result.stdout.strip():
                tunnel_processes = result.stdout.strip().split('\n')
                for process_line in tunnel_processes:
                    parts = process_line.strip().split()
                    if parts:
                        pid = parts[0]
                        try:
                            logger.info(f"  🎯 停止 ios tunnel 进程 PID={pid}")
                            subprocess.run(['kill', '-TERM', pid], check=False)
                            self.stopped_services.append(f"ios_tunnel_{pid}")
                        except Exception as e:
                            logger.warning(f"  ⚠️  停止 ios tunnel 进程 {pid} 失败: {e}")

            # 执行 ios tunnel stopagent 清理代理连接
            try:
                logger.info("  🔧 执行 ios tunnel stopagent 清理代理连接...")
                # 尝试找到 ios 命令路径
                ios_cmd = None
                for path in ["/Users/<USER>/.nvm/versions/node/v20.18.3/bin/ios", "ios"]:
                    try:
                        subprocess.run([path, "--version"], capture_output=True, timeout=5)
                        ios_cmd = path
                        break
                    except:
                        continue
                
                if ios_cmd:
                    subprocess.run([ios_cmd, 'tunnel', 'stopagent'], capture_output=True, timeout=10)
                    logger.info("  ✅ ios tunnel stopagent 执行完成")
                    self.stopped_services.append("ios_tunnel_stopagent")
                else:
                    logger.warning("  ⚠️  未找到 ios 命令，跳过 tunnel stopagent")
            except Exception as e:
                logger.warning(f"  ⚠️  执行 ios tunnel stopagent 失败: {e}")

            logger.info("  ✅ WDA服务停止完成")

        except Exception as e:
            logger.warning(f"  ⚠️  停止WDA服务时发生错误: {e}")

    def stop_additional_services(self) -> None:
        """停止可能的额外服务"""
        logger.info("🛠️  检查并停止额外服务...")

        # 停止心跳监控系统
        self.stop_heartbeat_monitor()

        # 停止WDA相关服务
        self.stop_wda_services()

        # 尝试停止可能的 ADB 服务问题
        try:
            logger.info("  🔧 重启 ADB 服务以清理连接...")
            subprocess.run(['adb', 'kill-server'], capture_output=True, timeout=10)
            time.sleep(2)
            subprocess.run(['adb', 'start-server'], capture_output=True, timeout=10)
            logger.info("  ✅ ADB 服务已重启")
            self.stopped_services.append("ADB")
        except Exception as e:
            logger.warning(f"  ⚠️  ADB 服务重启失败: {e}")
    
    def cleanup_temp_files(self) -> None:
        """清理临时文件（可选）"""
        logger.info("🧹 清理临时文件...")

        # 清理WDA锁文件
        try:
            wda_lock_file = 'wda_install.lock'
            if os.path.exists(wda_lock_file):
                os.remove(wda_lock_file)
                logger.info(f"  🗑️  已删除WDA锁文件: {wda_lock_file}")
                self.stopped_services.append("WDA_Lock")
            else:
                logger.info(f"  ℹ️  WDA锁文件不存在: {wda_lock_file}")
        except Exception as e:
            logger.warning(f"  ⚠️  清理WDA锁文件时出错: {e}")

        # 清理其他临时文件
        temp_patterns = [
            '*.tmp',
            '*.pid'
        ]

        for pattern in temp_patterns:
            try:
                import glob
                files = glob.glob(pattern)
                for file in files:
                    try:
                        os.remove(file)
                        logger.info(f"  🗑️  已删除临时文件: {file}")
                    except Exception as e:
                        logger.warning(f"  ⚠️  删除文件 {file} 时出错: {e}")
            except Exception as e:
                logger.warning(f"  ⚠️  清理 {pattern} 时出错: {e}")
    
    def generate_stop_report(self) -> None:
        """生成停止报告"""
        logger.info("\n" + "="*60)
        logger.info("📊 停止操作报告")
        logger.info("="*60)
        
        logger.info(f"✅ 成功停止的进程数量: {len(self.stopped_processes)}")
        if self.stopped_processes:
            logger.info(f"   进程 PIDs: {', '.join(map(str, self.stopped_processes))}")
        
        logger.info(f"❌ 停止失败的进程数量: {len(self.failed_processes)}")
        if self.failed_processes:
            logger.info(f"   进程 PIDs: {', '.join(map(str, self.failed_processes))}")
        
        logger.info(f"🛠️  停止的服务数量: {len(self.stopped_services)}")
        if self.stopped_services:
            logger.info(f"   服务列表: {', '.join(self.stopped_services)}")
        
        logger.info("="*60)
        
        if self.failed_processes:
            logger.warning("⚠️  存在停止失败的进程，可能需要手动处理")
            return False
        else:
            logger.info("🎉 所有服务已成功停止")
            return True
    
    def stop_all(self) -> bool:
        """执行完整的停止流程"""
        logger.info("🚀 开始停止 split_devices.py 测试流程...")
        logger.info(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        try:
            # 1. 查找主要进程
            main_processes = self.find_split_devices_processes()
            
            # 2. 查找服务进程
            service_processes = self.find_service_processes()
            all_service_processes = []
            for service_list in service_processes.values():
                all_service_processes.extend(service_list)
            
            # 3. 发送优雅关闭信号
            all_processes = main_processes + all_service_processes
            if all_processes:
                self.send_graceful_shutdown_signal(all_processes)
                
                # 4. 等待优雅退出
                remaining_processes = self.wait_for_processes_to_exit(all_processes, timeout=30)
                
                # 5. 强制终止剩余进程
                if remaining_processes:
                    self.force_kill_processes(remaining_processes)
            else:
                logger.info("✅ 没有找到需要停止的进程")
            
            # 6. 停止额外服务
            self.stop_additional_services()
            
            # 7. 清理临时文件
            self.cleanup_temp_files()
            
            # 8. 生成报告
            success = self.generate_stop_report()
            
            logger.info(f"⏰ 结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            
            return success
            
        except Exception as e:
            logger.error(f"❌ 停止流程中发生错误: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return False

def main():
    """主函数"""
    print("🛑 split_devices.py 服务停止器")
    print("="*50)
    
    # 检查是否有其他实例在运行
    killer = SplitDevicesKiller()
    
    # 询问用户确认
    try:
        confirm = input("\n❓ 确定要停止所有 split_devices.py 相关服务吗？ (y/N): ").strip().lower()
        if confirm not in ['y', 'yes']:
            print("❌ 操作已取消")
            return
    except KeyboardInterrupt:
        print("\n❌ 操作已取消")
        return
    
    print("\n🔄 开始执行停止流程...\n")
    
    # 执行停止流程
    success = killer.stop_all()
    
    if success:
        print("\n🎉 所有服务已成功停止！")
        print("💡 提示：可以安全地进行代码修改或系统维护了")
    else:
        print("\n⚠️  部分服务停止失败，请检查日志了解详情")
        print("💡 提示：可能需要手动处理某些进程")
    
    print(f"\n📋 详细日志已保存到: {log_file}")

if __name__ == "__main__":
    main() 