import os
import sys
import time
import logging
import re
import signal
import subprocess
from datetime import datetime, timedelta
import logging.handlers


class LogManager:
    """日志管理器，统一管理各种日志的创建、清理和服务控制"""
    
    def __init__(self, log_root=None):
        """初始化日志管理器
        
        Args:
            log_root: 日志根目录，如果为None则使用默认的LOG_ROOT
        """
        # 避免循环导入
        if log_root:
            self.log_root = log_root
        else:
            # 使用全局变量获取LOG_ROOT
            script_dir = os.path.dirname(os.path.abspath(__file__))
            project_root = os.path.dirname(script_dir)
            self.log_root = os.path.join(project_root, "log")
            
        self.python_logs_dir = os.path.join(self.log_root, "python_logs")
        self.wda_logs_dir = os.path.join(self.log_root, "wda_logs")
        self.appium_logs_dir = os.path.join(self.log_root, "appium_logs")
        self.ios_tool_logs_dir = os.path.join(self.log_root, "ios_tool_logs")
        self.stop_logs_dir = os.path.join(self.log_root, "stop_logs")
        self.compress_logs_dir = os.path.join(self.log_root, "compress_logs")
        self.device_status_dir = os.path.join(self.log_root, "device_status")
        
        # 创建日志目录
        os.makedirs(self.python_logs_dir, exist_ok=True)
        os.makedirs(self.wda_logs_dir, exist_ok=True)
        os.makedirs(self.appium_logs_dir, exist_ok=True)
        os.makedirs(self.ios_tool_logs_dir, exist_ok=True)
        os.makedirs(self.stop_logs_dir, exist_ok=True)
        os.makedirs(self.compress_logs_dir, exist_ok=True)
        os.makedirs(self.device_status_dir, exist_ok=True)
        
        # 确保日志目录有正确的权限
        os.chmod(self.python_logs_dir, 0o755)
        os.chmod(self.wda_logs_dir, 0o755)
        os.chmod(self.appium_logs_dir, 0o755)
        os.chmod(self.ios_tool_logs_dir, 0o755)
        os.chmod(self.stop_logs_dir, 0o755)
        os.chmod(self.compress_logs_dir, 0o755)
        os.chmod(self.device_status_dir, 0o755)
        
        # 日志服务进程ID
        self.log_service_pid = None
        
        # 添加自定义日志级别 HEART（如果尚未添加）
        if not hasattr(logging, 'HEART'):
            # 添加自定义日志级别 HEART
            HEART_LEVEL = 25  # 介于INFO(20)和WARNING(30)之间
            logging.addLevelName(HEART_LEVEL, "HEART")
            
            # 为Logger类添加heart方法
            def heart(self, message, *args, **kwargs):
                if self.isEnabledFor(HEART_LEVEL):
                    self._log(HEART_LEVEL, message, args, **kwargs)
            
            # 将heart方法添加到Logger类
            logging.Logger.heart = heart
            
            # 保存HEART级别为全局变量
            logging.HEART = HEART_LEVEL
        
        # 配置全局日志记录器
        self._setup_global_logger()
    
    def _get_next_rotation_time(self, handler):
        """获取下次轮转时间
        
        Args:
            handler: TimedRotatingFileHandler实例
            
        Returns:
            str: 下次轮转的时间字符串
        """
        current_time = time.time()
        next_rotation = handler.computeRollover(current_time)
        return time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(next_rotation))
    
    def _setup_global_logger(self):
        """配置全局日志记录器"""
        global global_logger
        
        # 获取全局 logger 并设置名称
        global_logger = logging.getLogger("global")
        global_logger.setLevel(logging.INFO)
        
        # 避免重复添加 handler
        if not global_logger.handlers:
            # 创建控制台处理器
            console_handler = logging.StreamHandler()
            console_handler.setLevel(logging.INFO)
            
            # 创建文件处理器（使用TimedRotatingFileHandler）
            log_file = os.path.join(self.python_logs_dir, "global.log")
            file_handler = logging.handlers.TimedRotatingFileHandler(
                log_file,
                when='H',           # 按小时轮转
                interval=8,         # 每8小时轮转一次
                backupCount=9,      # 保留9个备份（3天的日志）
                encoding='utf-8',
                delay=False,        # 立即创建文件
                utc=False          # 使用本地时间
            )
            file_handler.setLevel(logging.INFO)
            
            # 设置日志文件名后缀格式
            file_handler.suffix = "%Y-%m-%d_%H"
            
            # 设置日志格式
            formatter = logging.Formatter("%(asctime)s - %(levelname)s - [%(name)s] - %(message)s")
            console_handler.setFormatter(formatter)
            file_handler.setFormatter(formatter)
            
            # 添加处理器
            global_logger.addHandler(console_handler)
            global_logger.addHandler(file_handler)
            
            # 设置不传播到父记录器
            global_logger.propagate = False
            
            # 记录轮转配置信息
            global_logger.info("日志轮转配置已更新：每8小时轮转一次，保留9个备份文件（3天的日志）")
            global_logger.info(f"下次轮转时间：{self._get_next_rotation_time(file_handler)}")
    
    def get_device_logger(self, device_name):
        """获取设备专属的日志记录器

        Args:
            device_name: 设备名称

        Returns:
            logging.Logger: 设备专属的日志记录器
        """
        logger = logging.getLogger(device_name)
        logger.setLevel(logging.INFO)

        # 避免重复添加 handler
        if not logger.handlers:
            log_file = os.path.join(self.python_logs_dir, f"{device_name}.log")

            # 使用 TimedRotatingFileHandler
            file_handler = logging.handlers.TimedRotatingFileHandler(
                log_file,
                when='H',           # 按小时轮转
                interval=8,         # 每8小时轮转一次
                backupCount=9,      # 保留9个备份（3天的日志）
                encoding='utf-8',
                delay=False,        # 立即创建文件
                utc=False          # 使用本地时间
            )
            file_handler.setLevel(logging.INFO)

            # 设置日志文件名后缀格式
            file_handler.suffix = "%Y-%m-%d_%H"

            # 控制台处理器
            console_handler = logging.StreamHandler()
            console_handler.setLevel(logging.INFO)

            # 设置格式，格式中加入 %(name)s，name 对应 logger 的名称（即设备名称）
            formatter = logging.Formatter("%(asctime)s - %(levelname)s - [%(name)s] - %(message)s")
            file_handler.setFormatter(formatter)
            console_handler.setFormatter(formatter)

            logger.addHandler(file_handler)
            logger.addHandler(console_handler)

            # 设置不传播到父记录器
            logger.propagate = False

            # 只在第一次创建时记录轮转配置信息，避免重复日志
            logger.info("日志轮转配置已更新：每8小时轮转一次，保留9个备份文件（3天的日志）")
            logger.info(f"下次轮转时间：{self._get_next_rotation_time(file_handler)}")

        return logger
    
    def create_wda_log_file(self, udid, device_name=None):
        """为WDA创建日志文件
        
        Args:
            udid: 设备UDID
            device_name: 设备名称，如果提供则用于日志文件命名
            
        Returns:
            str: 日志文件路径
        """
        current_time = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
        
        # 使用新的命名方式：wda_{current_time}_{device_name}.log
        if device_name:
            # 替换设备名称中可能的非法字符
            safe_device_name = re.sub(r'[\\/*?:"<>|]', "_", device_name)
            log_file = os.path.join(self.wda_logs_dir, f"wda_{current_time}_{safe_device_name}.log")
        else:
            # 如果没有提供设备名称，则使用原来的命名方式作为后备
            log_file = os.path.join(self.wda_logs_dir, f"wda_{udid}_{current_time}.log")
        
        if not os.path.exists(log_file):
            try:
                with open(log_file, "w") as f:
                    init_message = f"Log file created at {current_time}\n"
                    f.write(init_message)
                global_logger.info(f"创建日志文件: {log_file}")
            except Exception as ex:
                global_logger.error(f"创建日志文件失败: {ex}")
        
        return log_file
    
    def get_appium_log_file(self, port):
        """获取Appium日志文件路径
        
        Args:
            port: Appium服务端口
            
        Returns:
            str: 日志文件路径
        """
        return os.path.join(self.appium_logs_dir, f"appium_{port}.log")
    
    def cleanup_old_logs(self, days=1, keep_latest=10):
        """清理旧日志文件
        
        Args:
            days: 保留最近几天的日志，默认为1
            keep_latest: 至少保留最新的几个文件，默认为10
        """
        self.cleanup_old_wda_logs(days)
        self.cleanup_old_python_logs(days, keep_latest)
        self.cleanup_old_ios_tool_logs(days=3)
        self.cleanup_old_stop_logs(days=3)
        self.cleanup_old_compress_logs(days=7)
        self.cleanup_old_device_status(days=7)
    
    def cleanup_old_wda_logs(self, days=1):
        """清理旧的WDA日志文件
        
        Args:
            days: 保留最近几天的日志，默认为1
        """
        if not os.path.exists(self.wda_logs_dir):
            return
            
        current_time = datetime.now()
        cutoff_time = current_time - timedelta(days=days)
        
        # 获取所有 wda 日志文件
        for filename in os.listdir(self.wda_logs_dir):
            if not filename.startswith("wda_") or not filename.endswith(".log"):
                continue
                
            try:
                # 从文件名中提取时间信息
                # 支持两种格式：
                # 1. 旧格式：wda_UDID_YYYY-MM-DD_HH-MM-SS.log
                # 2. 新格式：wda_YYYY-MM-DD_HH-MM-SS_设备名称.log
                parts = filename.split("_")
                
                # 尝试解析新格式 (wda_YYYY-MM-DD_HH-MM-SS_设备名称.log)
                if len(parts) >= 4 and parts[1].count('-') == 2:
                    # 新格式，日期在第1和第2部分
                    date_str = f"{parts[1]}_{parts[2]}"
                    file_time = datetime.strptime(date_str, "%Y-%m-%d_%H-%M-%S")
                else:
                    # 尝试旧格式 (wda_UDID_YYYY-MM-DD_HH-MM-SS.log)
                    try:
                        date_str = "_".join(parts[2:4]).replace(".log", "")
                        file_time = datetime.strptime(date_str, "%Y-%m-%d_%H-%M-%S")
                    except (ValueError, IndexError):
                        # 如果两种格式都解析失败，记录错误并跳过
                        global_logger.error(f"无法解析WDA日志文件名格式: {filename}")
                        continue
                
                # 如果文件超过指定天数，则删除
                if file_time < cutoff_time:
                    file_path = os.path.join(self.wda_logs_dir, filename)
                    os.remove(file_path)
                    global_logger.info(f"已删除旧的WDA日志文件: {filename}")
            except Exception as e:
                global_logger.error(f"处理WDA日志文件 {filename} 时发生错误: {str(e)}")
    
    def cleanup_device_wda_logs(self, device_udid=None, device_name=None, hours=24):
        """清理指定设备的旧WDA日志文件
        
        Args:
            device_udid: 设备UDID，用于匹配旧格式日志文件
            device_name: 设备名称，用于匹配新格式日志文件
            hours: 清理超过多少小时的日志，默认为24小时
        """
        if not os.path.exists(self.wda_logs_dir):
            return
            
        if not device_udid and not device_name:
            global_logger.warning("清理设备WDA日志时未指定设备UDID或设备名称，跳过清理")
            return
            
        current_time = datetime.now()
        cutoff_time = current_time - timedelta(hours=hours)
        deleted_count = 0
        
        global_logger.info(f"开始清理设备 {device_name or device_udid} 的旧WDA日志（超过{hours}小时）")
        
        # 获取所有 wda 日志文件
        for filename in os.listdir(self.wda_logs_dir):
            if not filename.startswith("wda_") or not filename.endswith(".log"):
                continue
                
            try:
                # 检查是否为目标设备的日志文件
                is_target_device = False
                file_time = None
                parts = filename.split("_")
                
                # 尝试解析新格式 (wda_YYYY-MM-DD_HH-MM-SS_设备名称.log)
                if len(parts) >= 4 and parts[1].count('-') == 2:
                    # 新格式，提取日期和设备名称
                    date_str = f"{parts[1]}_{parts[2]}"
                    file_time = datetime.strptime(date_str, "%Y-%m-%d_%H-%M-%S")
                    
                    # 提取设备名称 (剩余的所有部分，去掉.log后缀)
                    device_part = "_".join(parts[3:]).replace(".log", "")
                    
                    # 检查是否为目标设备
                    if device_name and device_part == device_name:
                        is_target_device = True
                    elif device_udid and device_part == device_udid:
                        is_target_device = True
                    
                else:
                    # 尝试旧格式 (wda_UDID_YYYY-MM-DD_HH-MM-SS.log)
                    try:
                        if device_udid and parts[1] == device_udid:
                            is_target_device = True
                            date_str = "_".join(parts[2:4]).replace(".log", "")
                            file_time = datetime.strptime(date_str, "%Y-%m-%d_%H-%M-%S")
                    except (ValueError, IndexError):
                        # 如果解析失败，记录错误并跳过
                        global_logger.error(f"无法解析WDA日志文件名格式: {filename}")
                        continue
                
                # 如果是目标设备的日志文件且超过指定时间，则删除
                if is_target_device and file_time and file_time < cutoff_time:
                    file_path = os.path.join(self.wda_logs_dir, filename)
                    os.remove(file_path)
                    deleted_count += 1
                    global_logger.info(f"已删除设备 {device_name or device_udid} 的旧WDA日志文件: {filename}")
            except Exception as e:
                global_logger.error(f"处理WDA日志文件 {filename} 时发生错误: {str(e)}")
        
        global_logger.info(f"设备 {device_name or device_udid} 的WDA日志清理完成，共删除 {deleted_count} 个文件")
        return deleted_count
    
    def cleanup_old_python_logs(self, days=1, keep_latest=10):
        """清理旧的Python日志文件，但保留最新的几个文件
        
        Args:
            days: 保留最近几天的日志，默认为1
            keep_latest: 至少保留最新的几个文件，默认为10
        """
        if not os.path.exists(self.python_logs_dir):
            return
            
        current_time = datetime.now()
        cutoff_time = current_time - timedelta(days=days)
        
        # 获取所有Python日志文件及其修改时间
        log_files = []
        for filename in os.listdir(self.python_logs_dir):
            if not filename.endswith(".log"):
                continue
                
            file_path = os.path.join(self.python_logs_dir, filename)
            try:
                mtime = os.path.getmtime(file_path)
                file_time = datetime.fromtimestamp(mtime)
                log_files.append((file_path, filename, file_time))
            except Exception as e:
                global_logger.error(f"获取文件 {filename} 修改时间时发生错误: {str(e)}")
        
        # 按修改时间排序
        log_files.sort(key=lambda x: x[2], reverse=True)
        
        # 保留最新的keep_latest个文件
        keep_files = set(item[0] for item in log_files[:keep_latest])
        
        # 删除旧文件
        for file_path, filename, file_time in log_files[keep_latest:]:
            if file_time < cutoff_time and file_path not in keep_files:
                try:
                    os.remove(file_path)
                    global_logger.info(f"已删除旧的Python日志文件: {filename}")
                except Exception as e:
                    global_logger.error(f"删除文件 {filename} 时发生错误: {str(e)}")
        
        # 清理完成后重置日志服务处理记录
        self.reset_log_service_records()
    
    def reset_log_service_records(self):
        """重置日志服务的处理记录，确保在测试开始前日志记录状态是最新的"""
        global_logger.info("正在重置日志服务处理记录...")
        
        # 构建重置命令
        from split_devices import Config
        reset_script = os.path.join(Config.SCRIPT_DIR, "log_service.py")
        if os.path.exists(reset_script):
            try:
                # 先尝试停止现有的日志服务
                self.stop_log_service()
                
                # 执行重置命令
                cmd = [sys.executable, reset_script, "--reset"]
                process = subprocess.Popen(
                    cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True
                )
                stdout, stderr = process.communicate(timeout=10)
                
                if process.returncode == 0:
                    global_logger.info("日志服务处理记录已重置")
                    
                    # 重新启动日志服务
                    self.start_log_service()
                    return True
                else:
                    global_logger.error(f"重置日志服务处理记录失败: {stderr}")
            except Exception as e:
                global_logger.error(f"重置日志服务处理记录时发生错误: {str(e)}")
        else:
            global_logger.error(f"日志服务脚本不存在: {reset_script}")
        
        return False
    
    def start_log_service(self):
        """启动日志服务"""
        global_logger.info("正在启动日志服务...")
        
        # 不再删除处理记录文件，保留之前的处理记录
        # 这样日志服务可以知道之前处理到哪一行，只处理新增的日志
        processed_lines_file = os.path.join(self.log_root, "processed_lines.json")
        if not os.path.exists(processed_lines_file):
            global_logger.info("未找到日志处理记录文件，将创建新的记录")
        else:
            global_logger.info("找到现有日志处理记录文件，将继续处理新增日志")
        
        # 启动脚本路径
        from split_devices import Config
        start_script = os.path.join(Config.PROJECT_ROOT, "shell", "start_log_service.sh")
        
        # 确保脚本有执行权限
        if os.path.exists(start_script):
            os.chmod(start_script, 0o755)
            
            try:
                # 执行启动脚本
                process = subprocess.Popen(
                    [start_script],
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,
                    encoding='utf-8',
                    errors='replace'
                )
                
                # 等待脚本执行完成
                stdout, stderr = process.communicate(timeout=10)
                
                if process.returncode == 0:
                    # 读取PID文件获取服务进程ID
                    pid_file = os.path.join(self.log_root, "log_service_pid.txt")
                    if os.path.exists(pid_file):
                        with open(pid_file, 'r') as f:
                            self.log_service_pid = int(f.read().strip())
                        global_logger.info(f"日志服务已启动，进程ID: {self.log_service_pid}")
                        return True
                    else:
                        global_logger.error("启动日志服务失败: 未找到PID文件")
                else:
                    global_logger.error(f"启动日志服务失败: {stderr}")
                    
            except Exception as e:
                global_logger.error(f"启动日志服务时发生错误: {str(e)}")
        else:
            global_logger.error(f"启动脚本不存在: {start_script}")
        
        return False
    
    def stop_log_service(self):
        """停止日志服务"""
        global_logger.info("正在停止日志服务...")
        
        # 停止脚本路径
        from split_devices import Config
        stop_script = os.path.join(Config.PROJECT_ROOT, "shell", "stop_log_service.sh")
        
        # 如果有进程ID，直接发送终止信号
        if self.log_service_pid:
            try:
                os.kill(self.log_service_pid, signal.SIGTERM)
                global_logger.info(f"已发送终止信号到日志服务进程 (PID: {self.log_service_pid})")
                
                # 等待进程结束
                for _ in range(10):
                    try:
                        # 检查进程是否存在
                        os.kill(self.log_service_pid, 0)
                        time.sleep(1)
                    except OSError:
                        # 进程已结束
                        global_logger.info("日志服务已停止")
                        self.log_service_pid = None
                        return True
                
                # 如果进程仍然存在，强制终止
                try:
                    os.kill(self.log_service_pid, signal.SIGKILL)
                    global_logger.info(f"已强制终止日志服务进程 (PID: {self.log_service_pid})")
                    self.log_service_pid = None
                    return True
                except OSError:
                    pass
            except OSError as e:
                global_logger.error(f"停止日志服务时发生错误: {str(e)}")
        
        # 如果直接终止失败或没有进程ID，尝试使用停止脚本
        if os.path.exists(stop_script):
            os.chmod(stop_script, 0o755)
            
            try:
                # 执行停止脚本
                process = subprocess.Popen(
                    [stop_script],
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,
                    encoding='utf-8',
                    errors='replace'
                )
                
                # 等待脚本执行完成
                stdout, stderr = process.communicate(timeout=10)
                
                if process.returncode == 0:
                    global_logger.info("日志服务已停止")
                    self.log_service_pid = None
                    return True
                else:
                    global_logger.error(f"停止日志服务失败: {stderr}")
                    
            except Exception as e:
                global_logger.error(f"停止日志服务时发生错误: {str(e)}")
        else:
            global_logger.error(f"停止脚本不存在: {stop_script}")
        
        return False
    
    def cleanup_old_ios_tool_logs(self, days=3):
        """清理旧的iOS工具日志文件
        
        Args:
            days: 保留最近几天的日志，默认为3天
        """
        if not os.path.exists(self.ios_tool_logs_dir):
            return
            
        current_time = datetime.now()
        cutoff_time = current_time - timedelta(days=days)
        
        global_logger.info(f"开始清理iOS工具日志，保留最近{days}天的日志")
        
        # 获取所有iOS工具日志文件
        deleted_count = 0
        for filename in os.listdir(self.ios_tool_logs_dir):
            if not filename.startswith("ios_tool_") or not filename.endswith(".log"):
                continue
                
            try:
                # 从文件名中提取时间信息
                # 格式：ios_tool_YYYY-MM-DD_HH-MM-SS_设备名称.log
                parts = filename.split("_")
                if len(parts) >= 4 and parts[2].count('-') == 2:
                    # 提取日期和时间
                    date_str = f"{parts[2]}_{parts[3]}"
                    if date_str.endswith('.log'):
                        # 处理只有设备名的情况
                        date_str = date_str.replace('.log', '')
                    file_time = datetime.strptime(date_str, "%Y-%m-%d_%H-%M-%S")
                    
                    # 如果文件超过指定天数，则删除
                    if file_time < cutoff_time:
                        file_path = os.path.join(self.ios_tool_logs_dir, filename)
                        os.remove(file_path)
                        deleted_count += 1
                        global_logger.info(f"已删除旧的iOS工具日志文件: {filename}")
                else:
                    global_logger.error(f"无法解析iOS工具日志文件名格式: {filename}")
            except Exception as e:
                global_logger.error(f"处理iOS工具日志文件 {filename} 时发生错误: {str(e)}")
        
        global_logger.info(f"iOS工具日志清理完成，共删除 {deleted_count} 个文件")
        return deleted_count
    
    def cleanup_old_stop_logs(self, days=3):
        """清理旧的停止日志文件
        
        Args:
            days: 保留最近几天的日志，默认为3天
        """
        if not os.path.exists(self.stop_logs_dir):
            return
            
        current_time = datetime.now()
        cutoff_time = current_time - timedelta(days=days)
        
        global_logger.info(f"开始清理停止日志，保留最近{days}天的日志")
        
        # 获取所有停止日志文件
        deleted_count = 0
        for filename in os.listdir(self.stop_logs_dir):
            if not filename.startswith("stop_") or not filename.endswith(".log"):
                continue
                
            try:
                # 从文件名中提取时间信息
                # 格式：stop_split_devices_YYYYMMDD_HHMMSS.log
                if "split_devices_" in filename:
                    # 提取时间戳部分
                    timestamp_part = filename.split("split_devices_")[1].replace(".log", "")
                    parts = timestamp_part.split("_")
                    if len(parts) >= 2:
                        date_str = parts[0]  # YYYYMMDD
                        time_str = parts[1]  # HHMMSS
                        
                        # 转换为datetime对象
                        file_time = datetime.strptime(f"{date_str}_{time_str}", "%Y%m%d_%H%M%S")
                        
                        # 如果文件超过指定天数，则删除
                        if file_time < cutoff_time:
                            file_path = os.path.join(self.stop_logs_dir, filename)
                            os.remove(file_path)
                            deleted_count += 1
                            global_logger.info(f"已删除旧的停止日志文件: {filename}")
                    else:
                        global_logger.error(f"无法解析停止日志文件名时间戳: {filename}")
                else:
                    global_logger.error(f"无法识别停止日志文件名格式: {filename}")
            except Exception as e:
                global_logger.error(f"处理停止日志文件 {filename} 时发生错误: {str(e)}")
        
        global_logger.info(f"停止日志清理完成，共删除 {deleted_count} 个文件")
        return deleted_count
    
    def cleanup_old_compress_logs(self, days=7):
        """清理旧的压缩日志文件
        
        Args:
            days: 保留最近几天的日志，默认为7天
        """
        if not os.path.exists(self.compress_logs_dir):
            return
            
        current_time = datetime.now()
        cutoff_time = current_time - timedelta(days=days)
        
        global_logger.info(f"开始清理压缩日志，保留最近{days}天的日志")
        
        # 获取所有压缩日志文件
        deleted_count = 0
        for filename in os.listdir(self.compress_logs_dir):
            if not filename.endswith(".log"):
                continue
                
            try:
                file_path = os.path.join(self.compress_logs_dir, filename)
                # 使用文件修改时间来判断是否需要删除
                mtime = os.path.getmtime(file_path)
                file_time = datetime.fromtimestamp(mtime)
                
                # 如果文件超过指定天数，则删除
                if file_time < cutoff_time:
                    os.remove(file_path)
                    deleted_count += 1
                    global_logger.info(f"已删除旧的压缩日志文件: {filename}")
            except Exception as e:
                global_logger.error(f"处理压缩日志文件 {filename} 时发生错误: {str(e)}")
        
        global_logger.info(f"压缩日志清理完成，共删除 {deleted_count} 个文件")
        return deleted_count
    
    def cleanup_old_device_status(self, days=7):
        """清理旧的设备状态文件
        
        Args:
            days: 保留最近几天的状态文件，默认为7天
        """
        if not os.path.exists(self.device_status_dir):
            return
            
        current_time = datetime.now()
        cutoff_time = current_time - timedelta(days=days)
        
        global_logger.info(f"开始清理设备状态文件，保留最近{days}天的文件")
        
        # 获取所有设备状态文件
        deleted_count = 0
        for filename in os.listdir(self.device_status_dir):
            if not filename.endswith(".json"):
                continue
                
            try:
                file_path = os.path.join(self.device_status_dir, filename)
                # 使用文件修改时间来判断是否需要删除
                mtime = os.path.getmtime(file_path)
                file_time = datetime.fromtimestamp(mtime)
                
                # 如果文件超过指定天数，则删除
                if file_time < cutoff_time:
                    os.remove(file_path)
                    deleted_count += 1
                    global_logger.info(f"已删除旧的设备状态文件: {filename}")
            except Exception as e:
                global_logger.error(f"处理设备状态文件 {filename} 时发生错误: {str(e)}")
        
        global_logger.info(f"设备状态文件清理完成，共删除 {deleted_count} 个文件")
        return deleted_count


# 创建全局日志管理器实例
log_manager = LogManager()

# global_logger 已经在 LogManager.__init__() 中通过 _setup_global_logger() 设置好了
# 这里只需要获取引用，不要重新创建
global_logger = logging.getLogger("global")