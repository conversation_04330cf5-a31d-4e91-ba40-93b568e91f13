#!/usr/bin/env python
# -*- coding: utf-8 -*-

import requests
import json
import time
from python import env_config  # 导入环境配置模块
import sys

def _call_horus_api_internal(image_url, prompt=None, scene_id=1, ability_id=30000574, 
                            max_retries=3, retry_delay=1, api_url=None):
    """
    内部函数，调用Horus服务的/process-image接口，获取原始响应
    
    参数:
        image_url: 图片URL
        prompt: 提示词，可选
        scene_id: 业务场景ID，默认为1
        ability_id: 能力ID，默认为30000574
        max_retries: 最大重试次数，默认为3
        retry_delay: 重试间隔秒数，默认为1
        api_url: 指定API URL，如果为None则使用当前环境配置
    
    返回:
        成功时返回API的原始响应字典
        失败时返回None
    """
    # API端点 - 如果未指定，则使用当前环境配置
    url = api_url if api_url else env_config.get_horus_api_url()
    
    # 准备请求参数
    params = {
        "imageUrl": image_url,
        "sceneId": scene_id,
        "abilityId": ability_id
    }
    
    # 如果有提示词，添加到参数中
    if prompt:
        params["prompt"] = prompt
    
    # 重试机制
    attempts = 0
    last_error = None
    
    while attempts < max_retries:
        try:
            # 发送POST请求
            response = requests.post(url, params=params)
            response.raise_for_status()  # 如果请求失败，抛出异常
            
            # 解析响应结果
            api_response = response.json()
            
            # 检查API调用是否成功
            if api_response.get('code') != 0:
                print(f"API调用失败: {api_response.get('message')}")
                attempts += 1
                last_error = api_response.get('message')
                time.sleep(retry_delay)
                continue
            
            # 成功获取结果
            return api_response
                
        except requests.exceptions.RequestException as e:
            print(f"请求失败: {e}")
            attempts += 1
            last_error = str(e)
            time.sleep(retry_delay)
        except json.JSONDecodeError:
            print("无法解析响应JSON")
            attempts += 1
            last_error = "无法解析响应JSON"
            time.sleep(retry_delay)
        except Exception as e:
            print(f"处理数据时出错: {e}")
            attempts += 1
            last_error = str(e)
            time.sleep(retry_delay)
    
    # 如果尝试最大次数后仍然失败
    print(f"在尝试 {max_retries} 次后仍然失败。最后的错误: {last_error}")
    return None

def extract_action_and_coordinates(api_response):
    """
    从API响应中提取动作和坐标
    
    参数:
        api_response: 完整的API响应字典
    
    返回:
        (action, center_point) 元组，action为动作名称，center_point为中心点坐标[x, y]
    """
    try:
        # 从responseData中提取result
        response_data = api_response.get('responseData', {})
        result_str = response_data.get('result')
        
        if not result_str:
            print("未找到结果数据")
            return None, None
            
        # 解析result字符串为JSON对象
        result = json.loads(result_str)
        
        # 提取动作名称
        action = result.get('action')
        
        # 首先尝试直接从result中获取坐标信息
        center_point = None
        
        # 检查是否有直接的坐标信息
        if 'coordinates' in result:
            coords = result.get('coordinates')
            if isinstance(coords, (list, tuple)) and len(coords) == 2:
                center_point = coords
                print(f"从coordinates字段获取到坐标: {center_point}")
                return action, center_point
        
        # 检查是否有center_point字段
        if 'center_point' in result:
            coords = result.get('center_point')
            if isinstance(coords, (list, tuple)) and len(coords) == 2:
                center_point = coords
                print(f"从center_point字段获取到坐标: {center_point}")
                return action, center_point
        
        # 最后尝试从box中提取坐标
        box = result.get('box')
        if box and len(box) == 4:
            # 计算中心点坐标
            center_x = (box[0] + box[2]) / 2
            center_y = (box[1] + box[3]) / 2
            center_point = [center_x, center_y]
            print(f"从box字段计算得到坐标: {center_point}")
            return action, center_point
        
        # 如果没有找到任何坐标信息，但返回了点击操作，创建一个默认坐标(中间位置)
        if action == 'tap' and not center_point:
            print("未找到有效的坐标数据，但有tap操作，将使用默认坐标")
            # 返回None作为坐标，由调用方处理默认坐标生成
            return action, None
            
        print("未找到有效的坐标数据")
        return action, None
            
    except Exception as e:
        print(f"提取动作和坐标时出错: {e}")
        return None, None

def call_horus_api(image_url, prompt=None, scene_id=1, ability_id=30000574, 
                   simple_mode=True, max_retries=3, retry_delay=1, api_url=None):
    """
    调用Horus服务的/process-image接口并返回动作名称和中心点坐标
    
    参数:
        image_url: 图片URL
        prompt: 提示词，可选
        scene_id: 业务场景ID，默认为1
        ability_id: 能力ID，默认为30000574
        simple_mode: 是否使用简单模式，默认为True，仅返回动作和坐标；
                    设为False时返回完整API响应
        max_retries: 最大重试次数，默认为3
        retry_delay: 重试间隔秒数，默认为1
        api_url: 指定API URL，如果为None则使用当前环境配置
    
    返回:
        在simple_mode=True时:
            成功时返回元组 (action, center_point)，action为动作名称，center_point为中心点坐标[x, y]
            失败时返回 (None, None)
        在simple_mode=False时:
            返回完整的API响应字典
    """
    # 调用内部函数获取API响应
    api_response = _call_horus_api_internal(
        image_url, prompt, scene_id, ability_id, 
        max_retries, retry_delay, api_url
    )
    
    # 如果API调用失败
    if not api_response:
        return (None, None) if simple_mode else None
    
    # 根据模式返回不同的结果
    if simple_mode:
        # 从API响应中提取动作和坐标
        return extract_action_and_coordinates(api_response)
    else:
        # 返回完整响应
        return api_response

if __name__ == "__main__":
    # 测试图片URL
    image_url = "http://p0.meituan.net/ptautotest/36bfe82e1ecc3c85d73021b55f9cdad62278432.png"
    
    # 提示词
    prompt = "关闭弹窗"
    
    # 设置环境（可以通过命令行参数或环境变量来设置）
    # env_config.set_env("test")  # 设置为测试环境
    # 打印当前环境
    print(f"当前环境: {env_config.get_env()}")
    print(f"Horus API URL: {env_config.get_horus_api_url()}")
    
    # 先调用API获取完整响应
    print("=== 调用Horus API ===")
    api_response = _call_horus_api_internal(
        image_url, 
        prompt
    )
    
    if not api_response:
        print("API调用失败")
        sys.exit(1)
    
    # 从同一个API响应中获取简单模式和完整模式的结果
    print("\n=== 简单模式结果 ===")
    action, center_point = extract_action_and_coordinates(api_response)
    
    # 打印简单模式结果
    if action:
        print(f"动作: {action}")
        if center_point:
            print(f"中心点坐标: [x={center_point[0]:.2f}, y={center_point[1]:.2f}]")
    else:
        print("无法提取动作和坐标")
    
    # 打印完整模式结果
    print("\n=== 完整模式结果 ===")
    print(f"完整响应: {json.dumps(api_response, indent=2, ensure_ascii=False)}")