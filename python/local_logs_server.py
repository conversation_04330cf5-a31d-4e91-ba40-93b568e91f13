from flask import Flask, jsonify, send_file, render_template_string
import os
import glob
from flask_cors import CORS
import logging
from datetime import datetime

app = Flask(__name__)
CORS(app)  # 允许跨域请求

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

# 获取项目根目录
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
LOG_ROOT     = os.path.join(PROJECT_ROOT, "log")

# HTML 模板，下载链接已指向 /api/logs/download/...
HTML_TEMPLATE = """
<!DOCTYPE html>
<html>
<head>
    <title>文件管理器</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin-bottom: 30px; }
        .file-list { margin-left: 20px; }
        .file-item { margin: 5px 0; }
        .file-link { text-decoration: none; color: #0066cc; }
        .file-link:hover { text-decoration: underline; }
        .file-info { color: #666; font-size: 0.9em; }
        .directory { color: #4a4a4a; font-weight: bold; }
        .directory::before { content: "📁 "; }
        .file::before { content: "📄 "; }
        .log-file::before { content: "📝 "; }
        .binary-file::before { content: "📎 "; }
    </style>
</head>
<body>
    <h1>文件管理器</h1>
    
    <div class="section">
        <h2>日志文件</h2>
        {% for log_type, files in log_files.items() %}
            <h3>{{ log_type }}</h3>
            <div class="file-list">
            {% for file in files %}
                <div class="file-item">
                    <a class="file-link log-file"
                       href="/api/logs/download/{{ file.relative_path }}"
                       target="_blank">
                        {{ file.relative_path }}
                    </a>
                    <span class="file-info">
                        ({{ (file.size / 1024)|round(2) }} KB, 最后修改: {{ file.modified }})
                    </span>
                </div>
            {% endfor %}
            </div>
        {% endfor %}
    </div>
</body>
</html>
"""

@app.route('/')
def index():
    """根路径：递归列出所有子目录下的日志文件"""
    log_files = {}
    for log_type in os.listdir(LOG_ROOT):
        type_dir = os.path.join(LOG_ROOT, log_type)
        if not os.path.isdir(type_dir):
            continue
        log_files[log_type] = []
        # 递归扫描
        for root, dirs, files in os.walk(type_dir):
            for fn in files:
                # 构建相对路径：log_type/子目录/.../文件名
                rel_dir = os.path.relpath(root, LOG_ROOT)
                rel_path = os.path.join(rel_dir, fn).replace("\\", "/")
                full_path = os.path.join(root, fn)
                log_files[log_type].append({
                    'relative_path': rel_path,
                    'size': os.path.getsize(full_path),
                    'modified': datetime.fromtimestamp(os.path.getmtime(full_path)).strftime('%Y-%m-%d %H:%M:%S')
                })
    return render_template_string(HTML_TEMPLATE, log_files=log_files)

@app.route('/api/logs/download/<path:relative_path>')
def download_log(relative_path):
    """下载指定日志文件"""
    file_path = os.path.join(LOG_ROOT, relative_path)
    if not os.path.exists(file_path):
        return jsonify({'error': 'File not found'}), 404
    return send_file(file_path, as_attachment=True)

if __name__ == '__main__':
    import socket
    hostname = socket.gethostname()
    local_ip = socket.gethostbyname(hostname)
    print(f"服务器启动在: http://{local_ip}:5999")
    # 允许外部访问
    app.run(host='0.0.0.0', port=5999, debug=True)
