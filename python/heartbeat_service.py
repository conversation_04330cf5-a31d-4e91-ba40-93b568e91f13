import threading
import time
import logging
import os
import traceback
import sys
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 从心跳监控模块导入必要的函数和变量
from python.heartbeat_monitor import (
    send_heartbeat, 
    HEARTBEAT_INTERVAL, 
    DEVICE_STATUS_CHECK_INTERVAL, 
    device_status
)

# 获取日志对象
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

# 设置日志格式
formatter = logging.Formatter("%(asctime)s - %(levelname)s - [%(name)s] - %(message)s")

# 如果logger没有处理器，添加一个处理器
if not logger.handlers:
    # 创建控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # 设置不传播到父记录器，避免重复日志
    logger.propagate = False

# 心跳服务类
class HeartbeatService:
    def __init__(self):
        self.heartbeat_thread = None
        self.service_stop_event = threading.Event()
        self.start_time = datetime.now()
        self.last_notification_time = None
    
    def start(self):
        """启动心跳服务"""
        if self.heartbeat_thread is not None and self.heartbeat_thread.is_alive():
            logger.info("心跳服务已经在运行中")
            return False
        
        # 重置停止标志
        self.service_stop_event.clear()
        
        # 更新开始时间
        self.start_time = datetime.now()
        
        # 创建并启动心跳线程
        self.heartbeat_thread = threading.Thread(
            target=self._heartbeat_loop,
            args=(HEARTBEAT_INTERVAL,),
            daemon=False  # 不设为守护线程，确保即使主进程退出也能继续运行
        )
        self.heartbeat_thread.start()
        logger.info("心跳服务已启动")
        return True
    
    def stop(self):
        """停止心跳服务"""
        if self.heartbeat_thread is not None and self.heartbeat_thread.is_alive():
            logger.info("正在停止心跳服务...")
            self.service_stop_event.set()
            self.heartbeat_thread.join(timeout=5)
            if self.heartbeat_thread.is_alive():
                logger.warning("心跳线程未能在5秒内正常退出")
            else:
                logger.info("心跳服务已停止")
            return True
        else:
            logger.info("心跳服务未运行")
            return False
    
    def is_running(self):
        """检查心跳服务是否在运行"""
        return self.heartbeat_thread is not None and self.heartbeat_thread.is_alive()
    
    def _heartbeat_loop(self, interval):
        """心跳循环，定期发送心跳通知"""
        initial_notification_sent = False
        
        logger.info(f"心跳循环启动，间隔: {interval/60} 分钟")
        
        # 设备稳定计数器和默认值
        device_stable_count = 0
        DEVICE_STABLE_THRESHOLD = 3
        INITIAL_NOTIFICATION_DELAY = 300  # 程序初始通知延迟时间（秒），改为5分钟
        
        # 记录上次检测到的设备数量
        last_ios_count = 0
        last_android_count = 0
        
        while not self.service_stop_event.is_set():
            try:
                current_time = datetime.now()
                
                # 计算程序运行时间
                runtime = current_time - self.start_time
                
                # 检查是否有设备连接
                has_devices = False
                ios_count = 0
                android_count = 0
                
                if hasattr(device_status, 'items') and len(device_status) > 0:
                    has_devices = True
                    
                    # 计算iOS和Android设备数量
                    for _, status in device_status.items():
                        if hasattr(status, 'get'):
                            platform = status.get('platform', '').lower()
                            if platform == 'ios':
                                ios_count += 1
                            elif platform == 'android':
                                android_count += 1
                    
                    logger.debug(f"当前设备数量: iOS={ios_count}, Android={android_count}")
                    
                    # 检查设备数量是否稳定
                    if ios_count == last_ios_count and android_count == last_android_count:
                        device_stable_count += 1
                        logger.debug(f"设备数量稳定计数: {device_stable_count}/{DEVICE_STABLE_THRESHOLD}")
                    else:
                        device_stable_count = 0
                        logger.info(f"设备数量变化: iOS={last_ios_count}->{ios_count}, Android={last_android_count}->{android_count}")
                    
                    # 更新上次检测到的设备数量
                    last_ios_count = ios_count
                    last_android_count = android_count
                
                # 立即发送第一次通知的条件与原来保持一致:
                # 1. 尚未发送初始通知
                # 2. 有设备连接
                # 3. 设备数量已经稳定（连续多次检测到相同数量的设备）或程序运行超过设定时间
                if not initial_notification_sent and has_devices and (device_stable_count >= DEVICE_STABLE_THRESHOLD or runtime.total_seconds() > INITIAL_NOTIFICATION_DELAY):
                    logger.info(f"设备数量已稳定 (iOS={ios_count}, Android={android_count}) 或程序运行超过{INITIAL_NOTIFICATION_DELAY/60}分钟，发送初始心跳通知...")
                    send_heartbeat(self.start_time)
                    initial_notification_sent = True
                    self.last_notification_time = current_time
                    device_stable_count = 0  # 重置稳定计数器
                # 如果没有设备连接且程序运行超过设定时间，也发送一次通知
                elif not initial_notification_sent and not has_devices and runtime.total_seconds() > INITIAL_NOTIFICATION_DELAY:
                    logger.info(f"程序运行超过{INITIAL_NOTIFICATION_DELAY/60}分钟但未检测到设备，发送初始心跳通知...")
                    send_heartbeat(self.start_time)
                    initial_notification_sent = True
                    self.last_notification_time = current_time
                
                # 后续定期通知
                elif self.last_notification_time and (current_time - self.last_notification_time).total_seconds() >= interval:
                    logger.info(f"距离上次心跳通知已过去 {(current_time - self.last_notification_time).total_seconds()/60:.1f} 分钟，发送新的心跳通知...")
                    send_heartbeat(self.start_time)
                    self.last_notification_time = current_time
                
                # 休眠一段时间再检查
                time.sleep(DEVICE_STATUS_CHECK_INTERVAL)
                
            except Exception as e:
                logger.error(f"心跳循环中发生错误: {str(e)}")
                logger.error(traceback.format_exc())
                # 发生错误后等待一段时间再继续
                time.sleep(DEVICE_STATUS_CHECK_INTERVAL)

# 创建全局实例
heartbeat_service = HeartbeatService()

def start_heartbeat_service():
    """启动心跳服务"""
    return heartbeat_service.start()

def stop_heartbeat_service():
    """停止心跳服务"""
    return heartbeat_service.stop()

def check_and_restart_heartbeat_service():
    """检查并在需要时重启心跳服务"""
    if not heartbeat_service.is_running():
        logger.warning("心跳服务不在运行状态，正在重启...")
        return heartbeat_service.start()
    return False 