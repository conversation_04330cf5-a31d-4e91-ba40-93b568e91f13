import ast
import requests
import logging
import time
import json
from PIL import Image
from io import BytesIO
from python import env_config

# 配置默认日志记录器，只在没有传入设备日志记录器时使用
logger = logging.getLogger(__name__)

# 定义设备名称与默认返回按钮坐标的映射
DEFAULT_BACK_BUTTON_COORDS = {
    "iPhone 11": (41, 141),
    "iPhone14pm": (63, 215),
    # 后续可以在这里添加更多设备的默认坐标
}

def extract_fields_and_centers(s, device_name=None, device_logger=None, debug=False):
    """
    解析字符串并提取字段和中心点坐标
    
    Args:
        s: 要解析的字符串
        device_name: 设备名称，用于日志记录
        device_logger: 设备特定的日志记录器，如果提供则使用它
        debug: 是否调试模式，True时直接返回原始内容
        
    Returns:
        list: 包含 (label, (center_x, center_y)) 元组的列表，或debug模式下原始内容
    """
    # debug模式直接返回原始内容
    if debug:
        return s
    # 使用传入的设备日志记录器，如果没有则使用默认日志记录器
    log = device_logger if device_logger else logger
    
    device_prefix = f"[{device_name}] - " if device_name and not device_logger else ""
    # 新增判空兜底
    if not s or not isinstance(s, str) or s.strip() in ('', 'null', 'None', '[]', '{}'):
        log.warning(f"{device_prefix}OCR返回内容为空或无效: {repr(s)}")
        return []
    try:
        # 优先尝试用json解析
        try:
            data = json.loads(s)
        except Exception:
            # json解析失败再用ast.literal_eval
            data = ast.literal_eval(s)
    except Exception as e:
        log.error(f"{device_prefix}解析字符串出错：{e}，原始内容: {repr(s)}")
        return []

    result = []  # 改用列表存储结果
    for item in data:
        if len(item) < 2:
            continue
        label = item[0]
        coord_str = item[1].strip()  # 例如 "[ 28  20 178  20 178  78  28  78   0]"
        # 去掉最外层的中括号
        if coord_str.startswith('[') and coord_str.endswith(']'):
            coord_str = coord_str[1:-1]
        # 根据空白字符切分
        tokens = coord_str.split()
        if len(tokens) < 8:
            log.warning(f"{device_prefix}坐标数据不完整，跳过处理：{coord_str}，完整data: {repr(data)}")
            continue
        try:
            # 只取前 8 个数字，即 4 个 (x,y) 点
            coords = [float(token) for token in tokens[:8]]
        except Exception as e:
            log.error(f"{device_prefix}转换坐标时出错: {e}")
            continue
        
        # 依次为 (x1,y1), (x2,y2), (x3,y3), (x4,y4)
        xs = [coords[0], coords[2], coords[4], coords[6]]
        ys = [coords[1], coords[3], coords[5], coords[7]]
        center_x = sum(xs) / 4
        center_y = sum(ys) / 4
        
        result.append((label, (center_x, center_y)))  # 将结果添加到列表中
    
    return result

def get_message_from_horus(PicUrl, device_name=None, device_logger=None, debug=False):
    """
    从Horus服务获取OCR结果
    
    Args:
        PicUrl: 图片URL
        device_name: 设备名称，用于日志记录
        device_logger: 设备特定的日志记录器，如果提供则使用它
        debug: 是否调试模式，True时直接返回原始结果
        
    Returns:
        list: 包含 (label, (center_x, center_y)) 元组的列表，或debug模式下原始内容
    """
    # 使用传入的设备日志记录器，如果没有则使用默认日志记录器
    log = device_logger if device_logger else logger
    
    device_prefix = f"[{device_name}] - " if device_name and not device_logger else ""
    max_retries = 3
    for attempt in range(max_retries):
        try:
            response = requests.get(f"http://qaassist.sankuai.com/compass/api/ocr/getTextAndLocation?PicUrl={PicUrl}")
            response.raise_for_status()
            # 判空
            if not response.text or response.text.strip() in ('', 'null', 'None', '[]', '{}'):
                log.warning(f"{device_prefix}OCR接口返回内容为空或无效（第{attempt+1}次）: {repr(response.text)}")
                if attempt < max_retries - 1:
                    time.sleep(1)
                    continue
                else:
                    return []
            log.info(f"{device_prefix}成功获取OCR数据")
            if debug:
                return response.text
            return extract_fields_and_centers(response.text, device_name, device_logger, debug=debug)
        except requests.exceptions.RequestException as e:
            log.error(f"{device_prefix}请求OCR服务失败（第{attempt+1}次）: {e}")
            if attempt < max_retries - 1:
                time.sleep(1)
                continue
            else:
                return []

def analyze_ui_bug_result(ocr_results, device_logger=None, device_name=None, image_width=None, image_height=None, area_threshold=8.0):
    """
    分析UI缺陷检测的结果并记录到日志
    
    Args:
        ocr_results (list): OCR API返回的结果，是一个列表，格式为 ['0:白屏,区域：[5,5,1070,2390]\n', 'URL']
        device_logger: 设备特定的日志记录器，如果提供则使用它
        device_name: 设备名称，用于日志记录
        image_width: 图片宽度，用于面积计算
        image_height: 图片高度，用于面积计算
        area_threshold: 缺陷面积占比阈值，低于此阈值将视为无缺陷，默认为8.0%
        
    Returns:
        tuple: (has_error, error_list, marked_image_url)
            - has_error: bool, 是否存在UI缺陷
            - error_list: list, 错误内容列表
            - marked_image_url: str, 标记了错误的图片URL，如果没有错误则为None
    """
    # 使用传入的设备日志记录器，如果没有则使用默认日志记录器
    log = device_logger if device_logger else logger
    
    # 设置设备前缀，只有在使用默认日志记录器时才添加前缀
    device_prefix = f"[{device_name}] - " if device_name and not device_logger else ""
    
    # 检查结果是否为空
    if not ocr_results:
        log.info(f"{device_prefix}UI检测结果：没有发现UI缺陷")
        return False, [], None
    
    # 检查结果是否为['', '']，这表示API调用成功但没有检测到缺陷
    if len(ocr_results) == 2 and all(item == '' for item in ocr_results):
        log.info(f"{device_prefix}UI检测结果：没有发现UI缺陷 (API返回空字符串)")
        return False, [], None
        
    error_list = []
    marked_image_url = None
    error_regions = []
    
    # OCR结果通常是固定格式: ['0:白屏,区域：[5,5,1070,2390]\n', 'URL']
    # 第一个元素包含错误信息和区域，第二个元素是标记了错误的图片URL
    for idx, item in enumerate(ocr_results):
        if isinstance(item, str):
            # 检查是否是URL（通常是第二个元素）
            if item.startswith('http://') or item.startswith('https://'):
                marked_image_url = item
                log.debug(f"{device_prefix}获取到标记后的图片URL: {marked_image_url}")
            
            # 处理错误描述和区域（通常是第一个元素）
            elif ':' in item and '区域：' in item:
                try:
                    # 先按换行符分割，处理多个缺陷的情况
                    defect_lines = item.strip().split('\n')
                    log.debug(f"{device_prefix}分割后的缺陷行数: {len(defect_lines)}")
                    
                    for line in defect_lines:
                        line = line.strip()
                        if not line or ':' not in line or '区域：' not in line:
                            continue
                            
                        log.debug(f"{device_prefix}处理缺陷行: {line}")
                        
                        # 解析错误内容
                        error_parts = line.split(',')
                        if len(error_parts) > 0:
                            # 从 "1:页面显示异常（纯色背景）" 中提取 "页面显示异常（纯色背景）"
                            if ':' in error_parts[0]:
                                error_name = error_parts[0].split(':')[1].strip()
                            else:
                                error_name = error_parts[0].strip()
                            
                            error_list.append(error_name)
                            log.debug(f"{device_prefix}发现UI缺陷: {error_name}")
                        
                        # 解析区域坐标
                        if '区域：' in line:
                            region_part = line.split('区域：')[1]
                            # 移除任何换行符和空白
                            region_part = region_part.strip().strip('\n')
                            # 移除方括号
                            region_part = region_part.strip('[]')
                            # 分割坐标
                            coords = region_part.split(',')
                            
                            if len(coords) >= 4:
                                # 提取坐标并转换
                                try:
                                    # 接口返回的格式为 [x, y, width, height]
                                    # x, y 是左上角坐标，width, height 是宽度和高度
                                    x1 = int(coords[0].strip())
                                    y1 = int(coords[1].strip())
                                    width = int(coords[2].strip())
                                    height = int(coords[3].strip())
                                    
                                    # 计算右下角坐标
                                    x2 = x1 + width
                                    y2 = y1 + height
                                    
                                    # 添加区域坐标 [x1, y1, x2, y2] 格式
                                    error_regions.append([x1, y1, x2, y2])
                                    log.debug(f"{device_prefix}缺陷区域坐标: 左上角[{x1}, {y1}], 宽高[{width}, {height}], 右下角[{x2}, {y2}]")
                                except Exception as e:
                                    log.error(f"{device_prefix}转换坐标时出错: {e}, 原始坐标数据: {coords}")
                                    continue
                except Exception as e:
                    log.error(f"{device_prefix}解析缺陷信息失败: {e}")
    
    # 如果有缺陷且提供了图片尺寸，则计算缺陷面积占比
    if error_list and error_regions and image_width and image_height:
        total_area = image_width * image_height
        log.debug(f"{device_prefix}图片总面积: {total_area} 像素")
        
        # 计算所有缺陷区域的总面积
        total_error_area = 0
        for region in error_regions:
            x1, y1, x2, y2 = region
            width = x2 - x1
            height = y2 - y1
            area = width * height
            area_ratio = (area / total_area) * 100
            total_error_area += area
            log.debug(f"{device_prefix}缺陷区域: {region}, 面积: {area} 像素, 占比: {area_ratio:.2f}%")
        
        # 计算缺陷总面积占比
        total_error_ratio = (total_error_area / total_area) * 100
        log.info(f"{device_prefix}缺陷总面积: {total_error_area} 像素, 占图片总面积的 {total_error_ratio:.2f}%")
        
        # 如果缺陷面积占比小于阈值，视为无缺陷
        if total_error_ratio < area_threshold:
            log.info(f"{device_prefix}缺陷面积占比 {total_error_ratio:.2f}% 小于阈值 {area_threshold}%，视为无缺陷")
            return False, [], marked_image_url
    
    if error_list:
        log.warning(f"{device_prefix}发现UI缺陷：{', '.join(error_list)}")
        if marked_image_url:
            log.info(f"{device_prefix}标记后的图片URL：{marked_image_url}")
        return True, error_list, marked_image_url
    else:
        log.info(f"{device_prefix}UI检测结果：没有发现UI缺陷")
        return False, [], None

def get_ui_bug_from_ocr(pic_url, device_name=None, device_logger=None):
    """
    调用OCR API获取UI缺陷信息
    
    Args:
        pic_url: 图片URL
        device_name: 设备名称，用于日志记录
        device_logger: 设备特定的日志记录器，如果提供则使用它
        
    Returns:
        list: OCR API返回的结果
    """
    # 使用传入的设备日志记录器，如果没有则使用默认日志记录器
    log = device_logger if device_logger else logger
    
    device_prefix = f"[{device_name}] - " if device_name and not device_logger else ""
    
    max_retries = 3
    for attempt in range(max_retries):
        try:
            log.info(f"{device_prefix}开始调用OCR API，URL: http://qaassist.sankuai.com/compass/api/ocr/getUIBug?PicUrl={pic_url}")
            response = requests.get(f"http://qaassist.sankuai.com/compass/api/ocr/getUIBug?PicUrl={pic_url}")
            
            # 记录响应详情，帮助调试
            log.debug(f"{device_prefix}API响应状态码: {response.status_code}")
            # 记录完整的响应内容
            log.info(f"{device_prefix}API完整响应内容: {response.text}")
            
            # 首先检查响应状态码
            if response.status_code != 200:
                log.error(f"{device_prefix}OCR API请求失败: 状态码 {response.status_code}, 响应内容: {response.text}")
                if attempt < max_retries - 1:
                    log.info(f"{device_prefix}1秒后进行第{attempt+2}次尝试...")
                    time.sleep(1)
                    continue
                return []
            
            # 如果状态码是200，检查响应内容是否为空
            if not response.text.strip():
                log.info(f"{device_prefix}API返回空响应，表示没有发现UI缺陷")
                return []  # 直接返回空列表，因为空响应表示没有bug
            
            try:
                # 尝试解析JSON
                result = response.json()
                log.info(f"{device_prefix}成功获取UI缺陷检测结果，结果类型: {type(result)}, 内容: {result}")
                return result
            except json.JSONDecodeError as je:
                log.error(f"{device_prefix}JSON解析失败: {je}, 原始响应: {response.text}")
                if attempt < max_retries - 1:
                    log.info(f"{device_prefix}1秒后进行第{attempt+2}次尝试...")
                    time.sleep(1)
                    continue
                
        except requests.exceptions.RequestException as e:
            log.error(f"{device_prefix}调用OCR API网络请求失败: {str(e)}")
            if attempt < max_retries - 1:
                log.info(f"{device_prefix}1秒后进行第{attempt+2}次尝试...")
                time.sleep(1)
                continue
    
    return []

def find_back_button_location(pic_url, device_name=None, image_height=None, device_logger=None, image_width=None):
    """
    查找返回按钮位置，查找箭头按钮
    只考虑在屏幕左上角区域（横向前15%和纵向前10%）的箭头
    
    Args:
        pic_url: 图片URL
        device_name: 设备名称，用于日志记录
        image_height: 图片高度，用于计算区域阈值
        image_width: 图片宽度，用于计算区域阈值
        device_logger: 设备特定的日志记录器，如果提供则使用它
        
    Returns:
        tuple: 返回按钮的坐标 (x, y)，如果未找到则返回 None
    """
    # 使用传入的设备日志记录器，如果没有则使用默认日志记录器
    log = device_logger if device_logger else logger
    
    device_prefix = f"[{device_name}] - " if device_name and not device_logger else ""
    max_retries = 3
    for attempt in range(max_retries):
        try:
            response = requests.get(f"http://qaassist.sankuai.com/compass/api/ocr/getUiPageParseRequest?PicUrl={pic_url}")
            if response.status_code != 200:
                log.error(f"{device_prefix}页面解析API请求失败: 状态码 {response.status_code}")
                if attempt < max_retries - 1:
                    log.info(f"{device_prefix}1秒后进行第{attempt+2}次尝试...")
                    time.sleep(1)
                    continue
                return None

            data = response.json()
            if data.get('code') != 0 or 'result_info' not in data:
                log.error(f"{device_prefix}API返回数据格式错误或请求失败")
                if attempt < max_retries - 1:
                    log.info(f"{device_prefix}1秒后进行第{attempt+2}次尝试...")
                    time.sleep(1)
                    continue
                return None

            # 检查图片尺寸
            if not image_height or not image_width:
                log.error(f"{device_prefix}未提供图片高度或宽度，无法进行区域筛选")
                return None
            
            # 计算左上角区域阈值（横向前15%和纵向前10%）
            width_threshold = image_width * 0.15
            height_threshold = image_height * 0.10
            log.debug(f"{device_prefix}区域阈值设置为: 宽度 {width_threshold} (总宽度: {image_width}), 高度 {height_threshold} (总高度: {image_height})")

            arrow_centers = {}
            result_list = data['result_info'].get('result_info_list', [])
            
            # 遍历寻找箭头元素，只考虑在左上角区域的元素
            for idx, item in enumerate(result_list):
                if item.get('elem_detail_info') == "箭头":
                    region = item.get('elem_det_region', [])
                    if len(region) == 4:
                        center_x = (region[0] + region[2]) / 2
                        center_y = (region[1] + region[3]) / 2
                        # 只添加在左上角区域的元素
                        if center_x <= width_threshold and center_y <= height_threshold:
                            arrow_centers[idx] = (center_x, center_y)
                            log.debug(f"{device_prefix}找到符合条件的箭头: 坐标 ({center_x}, {center_y})")
            
            if not arrow_centers:
                log.info(f"{device_prefix}未找到符合条件的箭头元素，尝试在预设字典中查找设备默认坐标")
                # 尝试从预设字典中获取坐标
                fixed_coords = DEFAULT_BACK_BUTTON_COORDS.get(device_name)
                if fixed_coords:
                    log.info(f"{device_prefix}设备 {device_name} 在预设字典中找到坐标 {fixed_coords}")
                    return fixed_coords
                else:
                    log.warning(f"{device_prefix}未找到箭头且在预设字典中未找到 {device_name} 的坐标，返回 None")
                    return None
            
            # 如果左上角区域有多个箭头，选择距离左上角最近的（使用欧几里得距离）
            if len(arrow_centers) > 1:
                nearest_index = min(arrow_centers.keys(), 
                                   key=lambda k: arrow_centers[k][0]**2 + arrow_centers[k][1]**2)
                nearest_coords = arrow_centers[nearest_index]
                # 直接返回原始坐标，不做横坐标缩小
                log.info(f"{device_prefix}选择距离左上角最近的箭头元素: 坐标 {nearest_coords}")
                return nearest_coords
            else:
                # 只有一个箭头
                only_index = list(arrow_centers.keys())[0]
                coords = arrow_centers[only_index]
                # 直接返回原始坐标，不做横坐标缩小
                log.info(f"{device_prefix}左上角区域只有一个箭头元素: 坐标 {coords}")
                return coords
        except Exception as e:
            log.error(f"{device_prefix}调用页面解析API出错: {str(e)}")
            if attempt < max_retries - 1:
                log.info(f"{device_prefix}1秒后进行第{attempt+2}次尝试...")
                time.sleep(1)
                continue
            return None

def get_ui_page_parse(pic_url, device_name=None, device_logger=None, env=None):
    """
    获取页面元素解析结果，支持prod/test环境切换。
    Args:
        pic_url: 图片URL
        device_name: 设备名称，用于日志记录
        device_logger: 设备特定的日志记录器，如果提供则使用它
        env: 指定环境("prod"或"test")，不传则用当前环境
    Returns:
        list: 每个元素为dict，包含type/detail/center/region/probability等
    """
    log = device_logger if device_logger else logger
    device_prefix = f"[{device_name}] - " if device_name and not device_logger else ""
    max_retries = 3
    for attempt in range(max_retries):
        try:
            # 选择环境
            if env:
                base_domain = env_config.BASE_DOMAINS.get(env, env_config.BASE_DOMAINS["prod"])
            else:
                base_domain = env_config.get_base_domain()
            url = f"{base_domain}/compass/api/ocr/getUiPageParseResult?PicUrl={pic_url}"
            log.info(f"{device_prefix}请求页面解析API: {url}")
            response = requests.get(url)
            if response.status_code != 200:
                log.error(f"{device_prefix}页面解析API请求失败: 状态码 {response.status_code}")
                if attempt < max_retries - 1:
                    log.info(f"{device_prefix}1秒后进行第{attempt+2}次尝试...")
                    time.sleep(1)
                    continue
                return []
            data = response.json()
            if data.get('code') != 0 or 'result_info' not in data:
                log.error(f"{device_prefix}API返回数据格式错误或请求失败")
                if attempt < max_retries - 1:
                    log.info(f"{device_prefix}1秒后进行第{attempt+2}次尝试...")
                    time.sleep(1)
                    continue
                return []
            result_list = data['result_info'].get('result_info_list', [])
            results = []
            for item in result_list:
                elem_type = item.get('elem_det_type')
                elem_detail = item.get('elem_detail_info', None)
                region = item.get('elem_det_region', [])
                probability = item.get('probability', None)
                # 计算中心点
                if len(region) == 4:
                    center_x = (region[0] + region[2]) / 2
                    center_y = (region[1] + region[3]) / 2
                    center = (center_x, center_y)
                else:
                    center = None
                results.append({
                    'type': elem_type,
                    'detail': elem_detail,
                    'region': region,
                    'center': center,
                    'probability': probability
                })
            return results
        except Exception as e:
            log.error(f"{device_prefix}调用页面解析API(getUiPageParseResult)出错: {str(e)}")
            if attempt < max_retries - 1:
                log.info(f"{device_prefix}1秒后进行第{attempt+2}次尝试...")
                time.sleep(1)
                continue
            return []

def find_back_button_location_especial(business_name, image_path=None):
    """
    针对特殊 icon 设定的返回按钮位置。
    由外部保证只对特殊 icon 调用。
    Args:
        business_name (str): 业务名称
        image_path (str): 截图本地路径
    Returns:
        tuple: (x, y) 的像素坐标，若未检测到则返回 None
    """
    if image_path:
        try:
            from python.find_shape_in_photo import find_best_circle_center
            coords = find_best_circle_center(image_path)
            return coords
        except Exception as e:
            logger.warning(f"调用 find_best_circle_center 失败: {e}")
            return None
    return None

def main():
    # # 测试OCR API调用
    # test_pic_url = "http://p0.meituan.net/ptautotest/b81a609341c093d9aeb669577aac9bde1333613.png"
    # test_device_name = "test_device"
    # logger.info(f"[{test_device_name}] - 开始检测图片: {test_pic_url}")
    
    # # 调用UI缺陷检测API并打印结果
    # ui_bug_results = get_ui_bug_from_ocr(test_pic_url, test_device_name, logger)
    # logger.info(f"[{test_device_name}] - UI缺陷检测结果: {ui_bug_results}")
    # logger.info(f"[{test_device_name}] - UI缺陷检测结果类型: {type(ui_bug_results)}")
    # if ui_bug_results:
    #     logger.info(f"[{test_device_name}] - 检测到{len(ui_bug_results)}个结果项")
    #     for idx, item in enumerate(ui_bug_results):
    #         logger.info(f"[{test_device_name}] - 结果项 {idx+1}: 类型={type(item)}, 值={item}")
    # else:
    #     logger.info(f"[{test_device_name}] - 未检测到任何UI缺陷")
    
    # # 获取测试图片的尺寸
    # try:
    #     response = requests.get(test_pic_url, stream=True)
    #     if response.status_code == 200:
    #         img = Image.open(BytesIO(response.content))
    #         image_width, image_height = img.size
    #         logger.info(f"[{test_device_name}] - 图片尺寸: 宽={image_width}, 高={image_height}")
            
    #         # 分析UI缺陷检测结果，加入面积判断
    #         has_error, error_list, marked_image_url = analyze_ui_bug_result(
    #             ui_bug_results,
    #             device_name=test_device_name,
    #             device_logger=logger,
    #             image_width=image_width,
    #             image_height=image_height,
    #             area_threshold=8.0  # 设置面积阈值为8%
    #         )
    #         logger.info(f"[{test_device_name}] - UI缺陷分析结果: 有缺陷={has_error}, 缺陷列表={error_list}")
            
    #         ocr_results = find_back_button_location(
    #             test_pic_url, 
    #             device_name=test_device_name, 
    #             image_height=image_height,
    #             image_width=image_width,
    #             device_logger=logger
    #         )
    #         logger.info(f"[{test_device_name}] - 返回按钮位置: {ocr_results}")
    #     else:
    #         logger.error(f"无法获取测试图片: {response.status_code}")
    # except Exception as e:
    #     logger.error(f"处理测试图片时出错: {str(e)}")
    # 测试元素识别
    test_pic_url = "http://p0.meituan.net/ptautotest/dc3bd072c8a5dba7041bf11408bd5982276125.jpg"
    test_device_name = "test_device"
    logger.info(f"[{test_device_name}] - 开始检测图片: {test_pic_url}")
    ocr_results = get_ui_page_parse(test_pic_url, test_device_name, logger)
    logger.info(f"[{test_device_name}] - 元素识别结果: {ocr_results}")
    find_back_button_location(test_pic_url, test_device_name,2796,logger,1290)

if __name__ == "__main__":
    # 配置日志格式
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    main()
    