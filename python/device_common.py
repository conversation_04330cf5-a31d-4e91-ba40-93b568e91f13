"""
设备通用操作模块 - 支持iOS和Android设备的通用操作
包含截图、点击、应用重启、弹窗处理等功能
"""

import time
import logging
import os
import sys
import json
import socket
import subprocess
import asyncio
import traceback
import re
from typing import Optional, Tuple, List, Dict, Any, Union
from datetime import datetime
from PIL import Image
import imagehash
from dataclasses import dataclass, field
from python.config import Config
from python.upload_image import get_image_url
from python.notify_user import send_individual_kingkong_message, send_group_kingkong_message
from python.pop_up_detector import quick_detect_popup, handle_popup, detect_popup_with_connected_components  
from python.heartbeat_monitor import (
    log_device_issue, ISSUE_TYPE_APP, ISSUE_TYPE_TEST, 
    initialize_issue_logger, load_device_status_from_file
)
from python.get_message_from_horus import get_message_from_horus, get_ui_bug_from_ocr, analyze_ui_bug_result
from python.device_status_manager import get_device_status, update_device_status, get_app_package_name
from python.get_message_from_horus import get_message_from_horus

# 获取日志对象
logger = logging.getLogger("device_common")
logger.setLevel(logging.INFO)

# 设置日志格式
formatter = logging.Formatter("%(asctime)s - %(levelname)s - [%(name)s] - %(message)s")

# 如果logger没有处理器，添加一个处理器
if not logger.handlers:
    # 创建控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # 设置不传播到父记录器，避免重复日志
    logger.propagate = False

# 默认日志记录器（保持向后兼容）
DEFAULT_LOGGER = logger

class device_operate_internal:
    """
    设备内部操作方法集合：仅供 device_operate_external 调用
    """

    @staticmethod
    def _take_screenshot_with_adb(udid, output_path, logger, max_retries=3, retry_interval=3):
        try:
            logger.info(f"开始截图: {os.path.basename(output_path)}")
            output_dir = os.path.dirname(output_path)
            if not os.path.exists(output_dir):
                os.makedirs(output_dir, exist_ok=True)
            for attempt in range(max_retries):
                try:
                    online_check, _ = device_operate_external.execute_adb_command(udid, "devices", timeout=10, retries=1)
                    if not online_check or udid not in online_check:
                        logger.warning(f"设备 {udid} 可能不在线，第 {attempt+1}/{max_retries} 次尝试")
                        time.sleep(retry_interval)
                        continue
                    device_path = "/sdcard/temp_screenshot.png"
                    logger.debug(f"执行设备截图命令，尝试次数 {attempt+1}/{max_retries}")
                    stdout, stderr = device_operate_external.execute_adb_command(udid, f"shell screencap -p {device_path}", timeout=30, retries=2)
                    if stderr:
                        logger.warning(f"在设备上截图失败: {stderr}，第 {attempt+1}/{max_retries} 次尝试")
                        time.sleep(retry_interval)
                        continue
                    logger.debug(f"检查设备上的截图文件，尝试次数 {attempt+1}/{max_retries}")
                    check_stdout, check_stderr = device_operate_external.execute_adb_command(udid, f"shell ls -la {device_path}", timeout=10, retries=2)
                    if "No such file" in check_stderr or not check_stdout:
                        logger.warning(f"设备上没有生成截图文件: {check_stderr}，第 {attempt+1}/{max_retries} 次尝试")
                        time.sleep(retry_interval)
                        continue
                    logger.debug(f"从设备拉取截图文件，尝试次数 {attempt+1}/{max_retries}")
                    pull_stdout, pull_stderr = device_operate_external.execute_adb_command(udid, f"pull {device_path} \"{output_path}\"", timeout=30, retries=2)
                    if "does not exist" in pull_stderr:
                        logger.warning(f"拉取截图失败，设备上文件不存在: {pull_stderr}，第 {attempt+1}/{max_retries} 次尝试")
                        time.sleep(retry_interval)
                        continue
                    if not os.path.exists(output_path):
                        logger.warning(f"本地文件不存在: {output_path}，第 {attempt+1}/{max_retries} 次尝试")
                        time.sleep(retry_interval)
                        continue
                    file_size = os.path.getsize(output_path)
                    if file_size == 0:
                        logger.warning(f"截图文件为空: {output_path}，第 {attempt+1}/{max_retries} 次尝试")
                        time.sleep(retry_interval)
                        continue
                    logger.debug(f"删除设备上的临时截图文件")
                    device_operate_external.execute_adb_command(udid, f"shell rm {device_path}", timeout=10, retries=1)
                    logger.info(f"截图完成: {os.path.basename(output_path)}")
                    return "success"
                except Exception as e:
                    logger.warning(f"截图过程中发生错误: {e}，第 {attempt+1}/{max_retries} 次尝试")
                    if attempt < max_retries - 1:
                        time.sleep(retry_interval)
            logger.error(f"截图失败: 已达到最大尝试次数 ({max_retries})")
            is_online = device_operate_internal._is_device_online(udid, logger)
            if not is_online:
                return "device_offline"
            else:
                return "operation_failed"
        except Exception as e:
            logger.error(f"使用adb截图时发生错误: {e}")
            logger.error(f"错误详情: {traceback.format_exc()}")
            is_online = device_operate_internal._is_device_online(udid, logger)
            if not is_online:
                return "device_offline"
            else:
                return "operation_failed"

    def _is_device_online(udid, logger=None):
        """
        检查设备是否在线
        :param udid: 设备唯一标识
        :param platform: 'ios' 或 'android'
        :param logger: 日志对象
        :return: True 在线，False 掉线
        """
        if get_device_status(udid) is False:
            logger.error(f"设备UDID '{udid}' 不是已知设备，跳过在线检测")
            return False
        platform = get_device_status(udid)["platform"]
        try:
            if platform == 'ios':
                try:
                    output = subprocess.check_output(["idevice_id", "-l"], universal_newlines=True)
                    ios_devices = output.strip().split('\n') if output and output.strip() else []
                    online = udid in ios_devices
                    if logger:
                        logger.debug(f"iOS设备在线检测: {udid} {'在线' if online else '掉线'}")
                    return online
                except Exception as e:
                    if logger:
                        logger.error(f"检测iOS设备在线状态异常: {e}")
                    return False
            elif platform == 'android':
                try:
                    output = subprocess.check_output(["adb", "devices"], universal_newlines=True)
                    android_devices = [line.split()[0] for line in output.strip().split('\n')[1:] if line.strip() and 'device' in line]
                    online = udid in android_devices
                    if logger:
                        logger.debug(f"Android设备在线检测: {udid} {'在线' if online else '掉线'}")
                    return online
                except Exception as e:
                    if logger:
                        logger.error(f"检测Android设备在线状态异常: {e}")
                    return False
            else:
                if logger:
                    logger.error(f"不支持的平台类型: {platform}")
                return False
        except Exception as e:
            if logger:
                logger.error(f"检测设备在线状态时发生异常: {e}")
            return False
        
    def _get_error_reason(crash_result, homepage_result):
        """提取错误原因的辅助函数"""
        if crash_result and crash_result.app_crashed:
            return "应用闪退"
        if homepage_result and homepage_result.is_homepage:
            return "跳转失败，仍在首页"
        return ""

class device_operate_external:
    """
    设备对外操作方法集合：点击、滑动、截图、重启
    """
    @staticmethod
    def swipe(
            driver=None, 
            udid=None, 
            from_x_ratio=None, 
            from_y_ratio=None, 
            to_x_ratio=None, 
            to_y_ratio=None, 
            duration=0.5, 
            logger=None
        ):
        if logger is None:
            logger = DEFAULT_LOGGER
        if not udid:
            logger.error("swipe: 必须提供udid参数")
            return "unknown_device"
        platform = get_device_status(udid)["platform"]
        for v in [from_x_ratio, from_y_ratio, to_x_ratio, to_y_ratio]:
            if v is None or not (0 <= float(v) <= 1):
                logger.error("swipe: 必须传入0~1之间的比例坐标参数")
                return "operation_failed"
        status = get_device_status(udid)
        if status is False:
            logger.error(f"设备UDID '{udid}' 不是已知设备，跳过滑动操作")
            return "unknown_device"
        device_name = status.get('device_name', udid)
        try:
            if platform == 'ios':
                if not driver:
                    logger.error("swipe: iOS平台需要提供driver参数")
                    return "operation_failed"
                size = driver.get_window_size()
                width, height = size['width'], size['height']
            elif platform == 'android':
                if not udid:
                    logger.error("swipe: Android平台需要提供udid参数")
                    return "operation_failed"
                info = device_operate_external.get_device_screen_info(driver=None, udid=udid, logger=logger)
                width, height = info.get('width'), info.get('height')
                if not width or not height:
                    logger.error(f"swipe: 获取Android屏幕尺寸失败")
                    raise Exception("获取屏幕尺寸失败")
            else:
                logger.error(f"swipe: 不支持的平台类型: {platform}")
                return "operation_failed"
            from_x = int(width * float(from_x_ratio))
            from_y = int(height * float(from_y_ratio))
            to_x = int(width * float(to_x_ratio))
            to_y = int(height * float(to_y_ratio))
            if platform == 'ios':
                duration = float(duration)
                if duration < 0.01:
                    logger.warning(f"滑动时间过短 ({duration}秒)，已调整为0.01秒")
                    duration = 0.01
                logger.info(f"iOS滑动: 从[{from_x}, {from_y}]到[{to_x}, {to_y}], 持续时间: {duration}秒")
                driver.execute_script('mobile: dragFromToForDuration', {
                    'fromX': from_x,
                    'fromY': from_y,
                    'toX': to_x,
                    'toY': to_y,
                    'duration': duration
                })
                logger.info(f"iOS滑动成功: 从[{from_x}, {from_y}]到[{to_x}, {to_y}], 持续时间: {duration}秒")
                return "success"
            elif platform == 'android':
                duration_ms = int(float(duration) * 1000)
                if duration_ms < 10:
                    logger.warning(f"滑动时间过短 ({duration_ms}ms)，已调整为10ms")
                    duration_ms = 10
                swipe_command = f"shell input swipe {from_x} {from_y} {to_x} {to_y} {duration_ms}"
                stdout, stderr = device_operate_external.execute_adb_command(udid, swipe_command)
                if stderr:
                    logger.error(f"Android滑动操作失败: {stderr}")
                    raise Exception(f"滑动命令失败: {stderr}")
                logger.info(f"Android滑动成功: 从[{from_x}, {from_y}]到[{to_x}, {to_y}], 持续时间: {duration}秒 ({duration_ms}ms)")
                return "success"
        except Exception as e:
            logger.error(f"{platform}滑动操作异常: {e}")
            is_online = device_operate_internal._is_device_online(udid, logger)
            if not is_online:
                update_device_status(udid, {"status": "offline"})
                from python.notify_user import send_individual_kingkong_message
                msg = f"设备【{device_name}】在{platform}滑动操作失败后检测到掉线，已同步为offline。"
                send_individual_kingkong_message(msg, ['cuijie12'])
                return "device_offline"
            return "operation_failed"

    @staticmethod
    def tap(
            driver=None,
            udid=None,
            x=None,
            y=None,
            logger=None,
        ):
        if logger is None:
            logger = DEFAULT_LOGGER
        if not udid:
            logger.error("tap: 必须提供udid参数")
            return "unknown_device"
        if x is None or y is None:
            logger.error("tap: 未提供有效的坐标")
            return "operation_failed"
        platform = get_device_status(udid)["platform"]
        status = get_device_status(udid)
        if status is False:
            logger.error(f"设备UDID '{udid}' 不是已知设备，跳过点击操作")
            return "unknown_device"
        device_name = status.get('device_name', udid)
        try:
            if platform == 'ios':
                if not driver:
                    logger.error("tap: iOS平台需要提供driver参数")
                    return "operation_failed"
                logger.debug(f"iOS点击坐标: ({x}, {y})")
                driver.execute_script('mobile: tap', {'x': x, 'y': y})
                return "success"
            elif platform == 'android':
                logger.debug(f"Android点击坐标: ({int(x)}, {int(y)})")
                tap_command = f"shell input tap {int(x)} {int(y)}"
                stdout, stderr = device_operate_external.execute_adb_command(udid, tap_command)
                if stderr:
                    logger.warning(f"点击命令失败: {stderr}")
                    if "not found" in stderr or "device offline" in stderr:
                        raise Exception(f"设备掉线: {stderr}")
                return "success"
            else:
                logger.error(f"tap: 不支持的平台类型: {platform}")
                return "operation_failed"
        except Exception as e:
            logger.error(f"{platform}点击操作异常: {e}")
            is_online = device_operate_internal._is_device_online(udid, logger)
            if not is_online:
                update_device_status(udid, {"status": "offline"})
                from python.notify_user import send_individual_kingkong_message
                msg = f"设备【{device_name}】在{platform}点击操作失败后检测到掉线，已同步为offline。"
                send_individual_kingkong_message(msg, ['cuijie12'])
                return "device_offline"
            return "operation_failed"

    @staticmethod
    def take_screenshot(
            driver=None,
            udid=None,
            screenshot_path=None,
            logger=None,
        ):
        if logger is None:
            logger = DEFAULT_LOGGER
        if not udid:
            logger.error("take_screenshot: 必须提供udid参数")
            return "unknown_device"
        platform = get_device_status(udid)["platform"]
        if not screenshot_path:
            logger.error("take_screenshot: 未提供截图路径")
            return "operation_failed"
        status = get_device_status(udid)
        if status is False:
            logger.error(f"设备UDID '{udid}' 不是已知设备，跳过截图操作")
            return "unknown_device"
        device_name = status.get('device_name', udid)
        try:
            if platform == 'ios':
                if not driver:
                    logger.error("take_screenshot: iOS平台需要提供driver参数")
                    return "operation_failed"
                logger.info(f"开始iOS截图: {os.path.basename(screenshot_path)}")
                driver.get_screenshot_as_file(screenshot_path)
                if not os.path.exists(screenshot_path) or os.path.getsize(screenshot_path) == 0:
                    logger.error(f"iOS截图失败，文件不存在或为空: {screenshot_path}")
                    raise Exception("截图文件无效")
                logger.info(f"iOS截图完成: {os.path.basename(screenshot_path)}")
                return "success"
            elif platform == 'android':
                logger.info(f"开始Android截图: {os.path.basename(screenshot_path)}")
                result = device_operate_internal._take_screenshot_with_adb(udid, screenshot_path, logger)
                if result == "success":
                    return "success"
                elif result == "device_offline":
                    update_device_status(udid, {"status": "offline"})
                    from python.notify_user import send_individual_kingkong_message
                    msg = f"设备【{device_name}】在Android截图操作失败后检测到掉线，已同步为offline。"
                    send_individual_kingkong_message(msg, ['cuijie12'])
                    return "device_offline"
                else:
                    return "operation_failed"
            else:
                logger.error(f"take_screenshot: 不支持的平台类型: {platform}")
                return "operation_failed"
        except Exception as e:
            logger.error(f"{platform}截图操作异常: {e}")
            is_online = device_operate_internal._is_device_online(udid, logger)
            if not is_online:
                update_device_status(udid, {"status": "offline"})
                from python.notify_user import send_individual_kingkong_message
                msg = f"设备【{device_name}】在{platform}截图操作异常后检测到掉线，已同步为offline。"
                send_individual_kingkong_message(msg, ['cuijie12'])
                return "device_offline"
            return "operation_failed"

    @staticmethod
    def restart_app(
            driver=None,
            udid=None,
            logger=None,
        ):
        if logger is None:
            logger = DEFAULT_LOGGER
        if not udid:
            logger.error("restart_app: 必须提供udid参数")
            return "unknown_device"
        platform = get_device_status(udid)["platform"]
        status = get_device_status(udid)
        if status is False:
            logger.error(f"设备UDID '{udid}' 不是已知设备，跳过重启操作")
            return "unknown_device"
        device_name = status.get('device_name', udid)
        package_name = get_app_package_name(udid)
        try:
            if platform == 'ios':
                if not driver or not package_name:
                    logger.error("restart_app: iOS平台需要提供driver和package_name参数")
                    return "operation_failed"
                logger.info(f"正在重启iOS应用{package_name}...")
                driver.terminate_app(package_name)
                time.sleep(2)
                driver.activate_app(package_name)
                logger.info(f"iOS应用{package_name}重启完成")
                return "success"
            elif platform == 'android':
                if not udid or not package_name:
                    logger.error("restart_app: Android平台需要提供udid和package_name参数")
                    return "operation_failed"
                logger.info(f"正在重启Android应用{package_name}...")
                stdout1, stderr1 = device_operate_external.execute_adb_command(udid, f"shell am force-stop {package_name}")
                if stderr1:
                    logger.warning(f"强制停止应用命令失败: {stderr1}")
                    if "not found" in stderr1 or "device offline" in stderr1:
                        raise Exception(f"设备掉线: {stderr1}")
                time.sleep(2)
                stdout2, stderr2 = device_operate_external.execute_adb_command(udid, f"shell monkey -p {package_name} -c android.intent.category.LAUNCHER 1")
                # 只有当stderr包含真正的错误信息时才报告失败
                # monkey命令在某些设备（如华为）上会在stderr输出调试信息，这些不是错误
                if stderr2 and ("Error" in stderr2 or "error" in stderr2 or "not found" in stderr2 or "device offline" in stderr2 or "failed" in stderr2.lower() or "denied" in stderr2.lower()):
                    logger.warning(f"启动应用命令失败: {stderr2}")
                    if "not found" in stderr2 or "device offline" in stderr2:
                        raise Exception(f"设备掉线: {stderr2}")
                elif stderr2:
                    # 如果stderr有输出但不包含错误关键词，则记录为调试信息而不是警告
                    logger.debug(f"monkey命令调试输出: {stderr2}")
                logger.info(f"Android应用{package_name}重启完成")
                return "success"
            else:
                logger.error(f"restart_app: 不支持的平台类型: {platform}")
                return "operation_failed"
        except Exception as e:
            logger.error(f"{platform}应用重启操作异常: {e}")
            is_online = device_operate_internal._is_device_online(udid, logger)
            if not is_online:
                update_device_status(udid, {"status": "offline"})
                from python.notify_user import send_individual_kingkong_message
                msg = f"设备【{device_name}】在{platform}应用重启操作失败后检测到掉线，已同步为offline。"
                send_individual_kingkong_message(msg, ['cuijie12'])
                return "device_offline"
            return "operation_failed"

    @staticmethod
    def swipe_by_coordinate(
            driver=None,
            udid=None,
            from_x=None,
            from_y=None,
            to_x=None,
            to_y=None,
            duration=0.5,
            logger=None,
        ):
        """
        根据实际像素坐标进行滑动操作。

        参数说明：
        - from_x, from_y, to_x, to_y: 均为截图（如OCR/图像识别后）得到的实际像素坐标。
          - 对于 iOS，必须传入截图像素坐标（不是 driver.get_window_size() 得到的逻辑坐标）。
          - 对于 Android，直接传入像素坐标即可。
        - udid: 设备唯一标识，iOS 用于查找 scale_ratio。
        - duration: 滑动持续时间（秒）。
        - platform: 'ios' 或 'android'。
        - driver: iOS 必需。
        - logger: 日志对象。

        注意事项：
        - iOS 设备会自动根据 scale_ratio 进行坐标换算。
        - Android 设备无需换算。
        """
        if logger is None:
            logger = DEFAULT_LOGGER
        if not udid:
            logger.error("swipe_by_coordinate: 必须提供udid参数")
            return "unknown_device"
        for v in [from_x, from_y, to_x, to_y]:
            if v is None or not isinstance(v, (int, float)):
                logger.error("swipe_by_coordinate: 必须传入有效的实际像素坐标参数")
                return "operation_failed"
        platform = get_device_status(udid)["platform"]
        status = get_device_status(udid)
        if status is False:
            logger.error(f"设备UDID '{udid}' 不是已知设备，跳过滑动操作")
            return "unknown_device"
        device_name = status.get('device_name', udid)
        try:
            if platform == 'ios':
                if not driver:
                    logger.error("swipe_by_coordinate: iOS平台需要提供driver参数")
                    return "operation_failed"
                # 获取 scale_ratio
                scale_ratio = status.get('scale_ratio', 3.0)
                # 获取逻辑分辨率
                win_size = driver.get_window_size()
                logic_width = win_size.get('width')
                logic_height = win_size.get('height')
                # 换算为逻辑坐标
                logic_from_x = int(float(from_x) / scale_ratio)
                logic_from_y = int(float(from_y) / scale_ratio)
                logic_to_x = int(float(to_x) / scale_ratio)
                logic_to_y = int(float(to_y) / scale_ratio)
                # 计算比例坐标
                from_x_ratio = logic_from_x / logic_width
                from_y_ratio = logic_from_y / logic_height
                to_x_ratio = logic_to_x / logic_width
                to_y_ratio = logic_to_y / logic_height
                # 复用 swipe 方法
                logger.info(f"iOS滑动（截图坐标自动换算）: 从[{from_x},{from_y}]到[{to_x},{to_y}], scale_ratio={scale_ratio}, 换算后逻辑坐标: 从[{logic_from_x},{logic_from_y}]到[{logic_to_x},{logic_to_y}]，比例: 从[{from_x_ratio:.3f},{from_y_ratio:.3f}]到[{to_x_ratio:.3f},{to_y_ratio:.3f}]，持续时间: {duration}s")
                return device_operate_external.swipe(
                    driver=driver,
                    udid=udid,
                    from_x_ratio=from_x_ratio,
                    from_y_ratio=from_y_ratio,
                    to_x_ratio=to_x_ratio,
                    to_y_ratio=to_y_ratio,
                    duration=duration,
                    platform=platform,
                    logger=logger
                )
            elif platform == 'android':
                # 直接用像素坐标
                duration_ms = int(float(duration) * 1000)
                if duration_ms < 10:
                    logger.warning(f"滑动时间过短 ({duration_ms}ms)，已调整为10ms")
                    duration_ms = 10
                swipe_command = f"shell input swipe {int(from_x)} {int(from_y)} {int(to_x)} {int(to_y)} {duration_ms}"
                stdout, stderr = device_operate_external.execute_adb_command(udid, swipe_command)
                if stderr:
                    logger.error(f"Android滑动操作失败: {stderr}")
                    raise Exception(f"滑动命令失败: {stderr}")
                logger.info(f"Android滑动成功: 从[{from_x}, {from_y}]到[{to_x}, {to_y}], 持续时间: {duration}s ({duration_ms}ms)")
                return "success"
            else:
                logger.error(f"swipe_by_coordinate: 不支持的平台类型: {platform}")
                return "operation_failed"
        except Exception as e:
            logger.error(f"{platform}滑动操作异常: {e}")
            is_online = device_operate_internal._is_device_online(udid, logger)
            if not is_online:
                update_device_status(udid, {"status": "offline"})
                from python.notify_user import send_individual_kingkong_message
                msg = f"设备【{device_name}】在{platform}滑动操作失败后检测到掉线，已同步为offline。"
                send_individual_kingkong_message(msg, ['cuijie12'])
                return "device_offline"
            return "operation_failed"

    @staticmethod
    def get_device_screen_info(
            driver=None,
            udid=None,
            logger=None,
        ):
        if logger is None:
            logger = DEFAULT_LOGGER
        if not udid:
            logger.error("get_device_screen_info: 必须提供udid参数")
            return None
        if get_device_status(udid) is False:
            logger.error(f"设备UDID '{udid}' 不是已知设备，跳过获取屏幕信息")
            return None
        platform = get_device_status(udid)["platform"]
        scale_ratio = get_device_status(udid)["scale_ratio"]
        info = {'platform': platform, 'scale_ratio': scale_ratio}
        try:
            if platform == 'android':
                stdout, _ = device_operate_external.execute_adb_command(udid, "shell wm size")
                m = re.search(r'Physical size: (\d+)x(\d+)', stdout)
                if m:
                    width, height = int(m.group(1)), int(m.group(2))
                    info.update({'width': width, 'height': height})
                else:
                    logger.error(f"get_device_screen_info: 未获取到 Android 分辨率: {stdout}")
            elif platform == 'ios':
                if driver:
                    win_size = driver.get_window_size()
                    logic_width = win_size.get('width')
                    logic_height = win_size.get('height')
                    if udid:
                        status_path = os.path.join('log', 'device_status', f'{udid}.json')
                        if os.path.exists(status_path):
                            try:
                                with open(status_path, 'r', encoding='utf-8') as f:
                                    status_data = json.load(f)
                                if 'scale_ratio' in status_data:
                                    scale_ratio = status_data['scale_ratio']
                            except Exception as e:
                                logger.warning(f'读取 device_status 文件失败: {e}')
                    width = int(logic_width * scale_ratio)
                    height = int(logic_height * scale_ratio)
                    info['width'] = width
                    info['height'] = height
                else:
                    logger.error("get_device_screen_info: iOS 需要 driver 参数")
                    return None
            else:
                logger.error(f"get_device_screen_info: 不支持的平台类型: {platform}")
                return None
        except Exception as e:
            logger.error(f"get_device_screen_info: 获取屏幕信息异常: {e}")
        return info

    @staticmethod
    def move_screenshot_to_error(
            screenshot_path: str,
            error_dir: str,
        ) -> Optional[str]:
            """
            将截图移动到error文件夹
            
            Args:
                screenshot_path: 截图文件路径
                error_dir: error目录路径
                
            Returns:
                移动后的文件路径，如果失败返回None
            """
            if os.path.exists(screenshot_path):
                filename = os.path.basename(screenshot_path)
                error_path = os.path.join(error_dir, filename)
                os.rename(screenshot_path, error_path)
                return error_path
            return None
    
    @staticmethod
    def execute_adb_command(
            udid,
            command,
            timeout=30,
            retries=2,
        ):
        """执行adb命令的函数，添加超时和重试机制"""
        if not udid:
            logger.error("execute_adb_command: 必须提供udid参数")
            return "unknown_device"
        full_command = f"adb -s {udid} {command}" if udid else f"adb {command}"
        for attempt in range(retries + 1):
            try:
                process = subprocess.Popen(full_command, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
                stdout, stderr = process.communicate(timeout=timeout)
                stdout_str = stdout.decode('utf-8').strip()
                stderr_str = stderr.decode('utf-8').strip()
                if "pull" in command and process.returncode == 0 and stderr_str and not stdout_str:
                    if "file pulled" in stderr_str or "pulled" in stderr_str:
                        stdout_str = stderr_str
                        stderr_str = ""
                if process.returncode != 0:
                    error_msg = f"命令执行失败，退出码: {process.returncode}, stderr: {stderr_str}"
                    if attempt < retries:
                        print(f"{error_msg}，尝试重试 ({attempt+1}/{retries})")
                        time.sleep(2)
                        continue
                    return "", error_msg
                return stdout_str, stderr_str
            except subprocess.TimeoutExpired:
                process.kill()
                error_msg = f"命令执行超时: {full_command}"
                if attempt < retries:
                    print(f"{error_msg}，尝试重试 ({attempt+1}/{retries})")
                    time.sleep(2)
                    continue
                return "", error_msg
            except Exception as e:
                error_msg = f"执行命令时发生错误: {e}"
                if attempt < retries:
                    print(f"{error_msg}，尝试重试 ({attempt+1}/{retries})")
                    time.sleep(2)
                    continue
                return "", error_msg
        return "", f"所有重试都失败: {full_command}"

def check_and_handle_popup_after_screenshot(
        driver=None,
        udid=None,
        logger=None,
        page_name=None,
    ):
    """
    通用弹窗检测和处理方法，支持iOS和Android平台，传入一个设备对应的 udid，然后自动根据其提供的截图目录进行截图来判断是否有弹窗
    如果有弹窗还会将弹窗进行保存（移入 error 目录），并返回处理后的新截图路径和截图 URL
    
    Args:
        driver: WebDriver实例（iOS必需，Android可选）
        udid: 设备UDID（Android必需，iOS可选）
        logger: 日志对象
        page_name: 当前页面名称，用于日志记录
    
    Returns:
        tuple: (是否成功处理弹窗, 处理后的新截图路径, 处理后的新截图URL)
        如果没有弹窗就直接返回 True, 对应的截图路径和截图 URL
    """
    if logger is None:
        logger = DEFAULT_LOGGER
    if not udid:
        logger.error("check_and_handle_popup_after_screenshot: 必须提供udid参数")
        return "unknown_device", None, None
    platform = get_device_status(udid)["platform"]
    if platform == "ios":
        if not driver:
            logger.error("check_and_handle_popup_after_screenshot: iOS平台需要提供driver参数")
            return False, None, None
    
    device_screenshot_dir = get_device_status(udid)["device_screenshot_dir"]
    device_error_dir = get_device_status(udid)["device_error_dir"]
    device_name = get_device_status(udid)["device_name"]
    current_round = get_device_status(udid)["session_round_num"]
    timestamp = time.strftime('%Y%m%d_%H%M%S')
    popup_screenshot_path = os.path.join(device_screenshot_dir, f'{page_name}_弹窗检测_{device_name}_{timestamp}.png')
    
    # 首先对当前页面进行截图
    screenshot_result = device_operate_external.take_screenshot(
        driver=driver,
        udid=udid,
        screenshot_path=popup_screenshot_path,
        logger=logger
    )
    if screenshot_result != "success":
        logger.error(f"当前页面弹窗检测尝试截图失败，路径为：{popup_screenshot_path}")
        return False, None, None
    
    popup_screenshot_url = get_image_url(popup_screenshot_path)
    # 检查是否有弹窗
    has_popup, _ = detect_popup_with_connected_components(popup_screenshot_path, debug=False, device_logger=logger, udid=udid)
    if has_popup:
        page_info = f"「{page_name}」页面" if page_name else "页面"
        logger.info(f"检测到{page_info}弹窗截图已上传，URL: {popup_screenshot_url} 轮次: {current_round}")
        device_operate_external.move_screenshot_to_error(popup_screenshot_path, device_error_dir)
        # 尝试处理弹窗
        success, coordinates = handle_popup(
            screenshot_path=popup_screenshot_path,
            screenshot_url=popup_screenshot_url,
            device_logger=logger,
            scale_ratio=get_device_status(udid).get('scale_ratio'),
            device_id=udid
        )
        if success and coordinates:
            logger.info(f"尝试点击关闭弹窗，坐标: [x={coordinates[0]:.2f}, y={coordinates[1]:.2f}]")
            tap_result = device_operate_external.tap(
                driver=driver,
                udid=udid,
                x=coordinates[0],
                y=coordinates[1],
                logger=logger
            )
            if tap_result != "success":
                logger.error(f"关闭弹窗中点击动作失败: {tap_result}")
                device_operate_external.restart_app(driver=driver, udid=udid, logger=logger)
                return False, None, None
            time.sleep(2)
            # 获取关闭弹窗后的截图
            timestamp = time.strftime('%Y%m%d_%H%M%S')
            after_close_popup_path = os.path.join(
                device_screenshot_dir,
                f'关闭弹窗后_{os.path.basename(popup_screenshot_path).split("_")[0]}_{device_name}_{timestamp}.png'
            )
            after_close_popup_screenshot_result = device_operate_external.take_screenshot(
                driver=driver,
                udid=udid,
                screenshot_path=after_close_popup_path,
                logger=logger
            )
            if after_close_popup_screenshot_result != "success":
                logger.error(f"获取关闭弹窗后截图失败: {after_close_popup_screenshot_result}")
                return False, None, None
            after_close_popup_url = get_image_url(after_close_popup_path)   
            logger.info(f"关闭弹窗后的截图已上传，URL: {after_close_popup_url} 轮次: {current_round}")
            # 检查关闭弹窗后是否还有弹窗
            has_popup_after, _ = detect_popup_with_connected_components(after_close_popup_path, debug=False, device_logger=logger, udid=udid)
            if has_popup_after:
                logger.warning("点击后仍然存在弹窗，将重启应用...")
                restart_result = device_operate_external.restart_app(
                    driver=driver,
                    udid=udid,
                    logger=logger
                )
                time.sleep(5)
                timestamp = time.strftime('%Y%m%d_%H%M%S')
                restart_screenshot_path = os.path.join(
                    device_screenshot_dir,
                    f'重启后_{os.path.basename(popup_screenshot_path).split("_")[0]}_{device_name}_{timestamp}.png'
                )
                restart_screenshot_result = device_operate_external.take_screenshot(
                    driver=driver,
                    udid=udid,
                    screenshot_path=restart_screenshot_path,
                    logger=logger
                )
                
                restart_screenshot_url = get_image_url(restart_screenshot_path)
                logger.info(f"重启应用后的截图已上传，URL: {restart_screenshot_url} 轮次: {current_round}")
                
                return True, restart_screenshot_path, restart_screenshot_url
            else:
                logger.info("成功关闭弹窗")
                return True, after_close_popup_path, after_close_popup_url
        else:
            logger.warning("无法获取关闭弹窗的坐标，将重启应用...")
            restart_result = device_operate_external.restart_app(
                driver=driver,
                udid=udid,
                logger=logger
            )
            time.sleep(5)
            timestamp = time.strftime('%Y%m%d_%H%M%S')
            restart_screenshot_path = os.path.join(
                device_screenshot_dir,
                f'重启后_{os.path.basename(popup_screenshot_path).split("_")[0]}_{device_name}_{timestamp}.png'
            )
            restart_screenshot_result = device_operate_external.take_screenshot(
                driver=driver,
                udid=udid,
                screenshot_path=restart_screenshot_path,
                logger=logger
            )
            if restart_screenshot_result != "success":
                logger.error(f"重启应用后截图失败: {restart_screenshot_result}")
                return False, None, None
            
            restart_screenshot_url = get_image_url(restart_screenshot_path)
            logger.info(f"重启应用后的截图已上传，URL: {restart_screenshot_url} 轮次: {current_round}")
            return True, restart_screenshot_path, restart_screenshot_url
    # 没有弹窗
    return True, popup_screenshot_path, popup_screenshot_url

def check_and_handle_app_crash(
    driver=None,
    udid=None,
    screenshot_url=None,
    current_page=None,
    execution_id=None,
    logger=None
):
    """
    通用检查应用是否闪退并处理，兼容iOS和Android。
    :param driver: iOS为WebDriver实例，Android为None
    :param udid: 设备UDID
    :param screenshot_url: 截图URL
    :param current_page: 当前页面名称
    :param execution_id: 测试执行ID
    :param logger: 日志对象
    :return: True表示正常，False表示发生闪退并已处理
    """
    if logger is None:
        logger = DEFAULT_LOGGER
    if udid is None:
        logger.error("check_and_handle_app_crash: udid 不能为空")
        return False
    if get_device_status(udid) is False:
        logger.error(f"设备UDID '{udid}' 不是已知设备，跳过检查应用是否闪退")
        return False
    platform = get_device_status(udid)["platform"]
    package_name = get_app_package_name(udid)
    if platform == 'ios':
        if package_name is None:
            logger.error("check_and_handle_app_crash: iOS平台需要提供package_name参数")
            return False
    elif platform == 'android':
        if package_name is None:
            logger.error("check_and_handle_app_crash: Android平台需要提供package_name参数")
            return False
    device_id = udid
    device_name = get_device_status(udid)["device_name"]
    # 轮次推算逻辑
    current_round = None
    session_round_num = get_device_status(udid)["session_round_num"]
    base_round = get_device_status(udid)["round_num"]
    current_round = base_round + session_round_num
    # iOS 逻辑
    if platform == 'ios':
        if not driver or not package_name:
            logger.error("check_and_handle_app_crash: iOS平台需要driver和package_name")
            return False
        try:
            app_state = driver.query_app_state(package_name)
            logger.info(f"iOS应用状态: {app_state}")
        except Exception as e:
            logger.error(f"无法获取iOS应用状态: {e}")
            app_state = -1
        # ====== 状态码适配 ======
        if app_state == 4:
            # 前台正常
            logger.info(f"应用在「{current_page}」运行正常，当前轮次: {current_round}")
            return True
        elif app_state == 3:
            # 后台
            logger.error(f"应用在「{current_page}」页面进入后台，当前状态: {app_state}")
            failure_time = time.strftime('%Y-%m-%d %H:%M:%S')
            failure_message = (
                f"时间：{failure_time}\n"
                f"设备：{device_name}\n"
                f"轮次：第 {session_round_num} 轮（历史轮次: {current_round}）\n"
                f"问题：应用在「{current_page}」页面进入后台\n"
                f"图片URL：{screenshot_url}"
            )
            send_individual_kingkong_message(failure_message, ['cuijie12'])
            send_group_kingkong_message(failure_message,69694646454)
            if not execution_id:
                execution_id = f"{device_id}_{current_round}_{int(time.time())}"
            log_device_issue(
                device_id=device_id,
                device_name=device_name,
                round_num=current_round,
                issue_type=ISSUE_TYPE_APP,
                issue_details=f"应用在「{current_page}」页面进入后台",
                page_name=current_page,
                screenshot_url=screenshot_url,
                execution_id=execution_id
            )
            # 自动拉前台
            try:
                driver.activate_app(package_name)
                logger.info(f"已自动将应用拉回前台: {package_name}")
            except Exception as e:
                logger.error(f"自动拉前台失败: {e}")
            return 'background'
        elif app_state == 1:
            # 闪退
            logger.error(f"应用在「{current_page}」页面发生闪退，当前状态: {app_state}")
            failure_time = time.strftime('%Y-%m-%d %H:%M:%S')
            failure_message = (
                f"时间：{failure_time}\n"
                f"设备：{device_name}\n"
                f"轮次：第 {session_round_num} 轮（历史轮次: {current_round}）\n"
                f"问题：应用在「{current_page}」页面发生闪退\n"
                f"图片URL：{screenshot_url}"
            )
            send_individual_kingkong_message(failure_message, ['cuijie12'])
            send_group_kingkong_message(failure_message,69694646454)
            if not execution_id:
                execution_id = f"{device_id}_{current_round}_{int(time.time())}"
            log_device_issue(
                device_id=device_id,
                device_name=device_name,
                round_num=current_round,
                issue_type=ISSUE_TYPE_APP,
                issue_details=f"应用在「{current_page}」页面发生闪退",
                page_name=current_page,
                screenshot_url=screenshot_url,
                execution_id=execution_id
            )
            logger.info("正在重启应用...")
            device_operate_external.restart_app(
                driver=driver,
                udid=device_id,
                logger=logger
            )
            time.sleep(5)
            return False
        else:
            logger.error(f"check_and_handle_app_crash: iOS应用状态异常: {app_state}")
            return False
    # Android 逻辑
    elif platform == 'android':
        if not device_id or not package_name:
            logger.error("check_and_handle_app_crash: Android平台需要udid和package_name")
            return False
        try:
            stdout, _ = device_operate_external.execute_adb_command(device_id, "shell dumpsys window | grep mCurrentFocus")
        except Exception as e:
            logger.error(f"无法获取Android前台应用: {e}")
            stdout = ''
        current_package = None
        current_activity = None
        if stdout:
            lines = stdout.strip().split('\n')
            valid_lines = [line for line in lines if 'null' not in line]
            for line in valid_lines:
                # 用正则提取包名/Activity
                match = re.search(r'mCurrentFocus=.* (\S+?)/(\S+?)[\s}]', line)
                if match:
                    current_package = match.group(1)
                    current_activity = match.group(2)
                    logger.info(f"当前前台应用: 包名={current_package}, 活动={current_activity}")
                    break
            if not current_package:
                logger.warning(f"未能从 mCurrentFocus 行正确提取包名，原始输出: {stdout}")
        else:
            logger.warning("dumpsys window 命令没有输出")
        is_meituan_app = current_package and current_package.startswith(package_name)
        if is_meituan_app:
            # 前台正常
            logger.info(f"应用在「{current_page}」运行正常，当前轮次: {current_round}")
            return True
        else:
            # 非前台，判断进程是否存在
            process_exists = False
            try:
                ps_out, _ = device_operate_external.execute_adb_command(device_id, f"shell ps | grep {package_name}")
                if ps_out and package_name in ps_out:
                    process_exists = True
            except Exception as e:
                logger.warning(f"检查进程时发生异常: {e}")
            if process_exists:
                # 进入后台
                logger.error(f"应用在「{current_page}」页面进入后台，当前前台应用: {current_package}")
                failure_time = time.strftime('%Y-%m-%d %H:%M:%S')
                failure_message = (
                    f"时间：{failure_time}\n"
                    f"设备：{device_name}\n"
                    f"轮次：第 {session_round_num} 轮（历史轮次: {current_round}）\n"
                    f"问题：应用在「{current_page}」页面进入后台\n"
                    f"当前前台应用: {current_package}/{current_activity}\n"
                    f"图片URL：{screenshot_url}"
                )
                send_individual_kingkong_message(failure_message, ['cuijie12'])
                if not execution_id:
                    execution_id = f"{device_id}_{current_round}_{int(time.time())}"
                log_device_issue(
                    device_id=device_id,
                    device_name=device_name,
                    round_num=current_round,
                    issue_type=ISSUE_TYPE_APP,
                    issue_details=f"应用在「{current_page}」页面进入后台",
                    page_name=current_page,
                    screenshot_url=screenshot_url,
                    execution_id=execution_id
                )
                # 自动拉前台
                try:
                    device_operate_external.execute_adb_command(device_id, f"shell monkey -p {package_name} -c android.intent.category.LAUNCHER 1")
                    logger.info(f"已自动将应用拉回前台: {package_name}")
                except Exception as e:
                    logger.error(f"自动拉前台失败: {e}")
                return 'background'
            else:
                # 闪退
                logger.error(f"应用在「{current_page}」页面发生闪退，当前前台应用: {current_package}")
                failure_time = time.strftime('%Y-%m-%d %H:%M:%S')
                failure_message = (
                    f"时间：{failure_time}\n"
                    f"设备：{device_name}\n"
                    f"轮次：第 {session_round_num} 轮（历史轮次: {current_round}）\n"
                    f"问题：应用在「{current_page}」页面发生闪退\n"
                    f"当前前台应用: {current_package}/{current_activity}\n"
                    f"图片URL：{screenshot_url}"
                )
                send_individual_kingkong_message(failure_message, ['cuijie12'])
                if not execution_id:
                    execution_id = f"{device_id}_{current_round}_{int(time.time())}"
                log_device_issue(
                    device_id=device_id,
                    device_name=device_name,
                    round_num=current_round,
                    issue_type=ISSUE_TYPE_APP,
                    issue_details=f"应用在「{current_page}」页面发生闪退",
                    page_name=current_page,
                    screenshot_url=screenshot_url,
                    execution_id=execution_id
                )
                logger.info("正在重启应用...")
                device_operate_external.restart_app(
                    driver=None,
                    udid=device_id,
                    logger=logger
                )
                # 拉前台，防止部分机型重启后未自动切前台
                try:
                    device_operate_external.execute_adb_command(device_id, f"shell monkey -p {package_name} -c android.intent.category.LAUNCHER 1")
                    logger.info(f"重启后已自动将应用拉回前台: {package_name}")
                except Exception as e:
                    logger.error(f"重启后自动拉前台失败: {e}")
                time.sleep(5)
                return False
    else:
        logger.error(f"check_and_handle_app_crash: 不支持的平台类型: {platform}")
        return False 
    
def check_is_homepage(
    driver=None,
    udid=None,
    screenshot_path=None,
    screenshot_url=None,
    logger=None
):
    """
    检查当前页面是否为首页
    通过udid查询设备信息，兼容iOS和Android平台
    
    :param driver: iOS为WebDriver实例，Android为None
    :param udid: 设备UDID，用于查询设备信息
    :param screenshot_path: 截图路径
    :param screenshot_url: 截图URL
    :param logger: 日志对象
    :return: bool 是否为首页
    """
    if logger is None:
        logger = DEFAULT_LOGGER
    
    if udid is None:
        logger.error("check_is_homepage: udid 不能为空")
        return False
    
    if get_device_status(udid) is False:
        logger.error(f"设备UDID '{udid}' 不是已知设备，跳过首页检查")
        return False
    
    # 通过udid查询设备信息
    device_status = get_device_status(udid)
    platform = device_status["platform"]
    device_name = device_status["device_name"]
    
    # 验证必需参数
    if not all([screenshot_path, screenshot_url]):
        logger.error("check_is_homepage: 缺少必需参数 screenshot_path 或 screenshot_url")
        return False
    
    # iOS平台需要driver参数
    if platform == 'ios' and driver is None:
        logger.error("check_is_homepage: iOS平台需要提供driver参数")
        return False
    
    try:
        # 获取页面元素
        page_elements = get_message_from_horus(screenshot_url, device_name, device_logger=logger)
        
        # 获取首页目标图标列表
        HOMEPAGE_TARGET_ICONS = Config.HOMEPAGE_TARGET_ICONS
        
        # 检查首页图标
        element_labels = [element[0] for element in page_elements]  # 获取所有元素的标签
        found_icons = []
        for target in HOMEPAGE_TARGET_ICONS:
            # 对所有选项进行严格相等匹配
            if target in element_labels:  # 这里使用 in 是因为 element_labels 是列表
                found_icons.append(True)
        
        # 检查是否找到了足够的图标
        min_required_icons = 4  # 只要存在4个及以上的HOMEPAGE_TARGET_ICONS元素就算返回首页
        all_icons_present = len(found_icons) >= min_required_icons
        
        logger.info(f"首页检查结果: {'是首页' if all_icons_present else '不是首页'}")
        return all_icons_present
        
    except Exception as e:
        logger.error(f"检查首页时发生错误: {e}")
        return False 

def perform_ui_check_only(
    screenshot_path,
    screenshot_url,
    udid,
    logger,
    execution_id,
    page_name,
    device_error_dir,
    device_issues
):
    """
    单独执行UI检查的函数
    
    Args:
        screenshot_path: 截图路径
        screenshot_url: 截图URL
        device_info: 设备信息
        logger: 日志对象
        current_round: 当前历史轮次
        session_round_num: 当前会话轮次
        execution_id: 执行ID
        page_name: 页面名称
        device_error_dir: 错误截图目录
        device_issues: 设备问题状态字典
    
    Returns:
        tuple: (是否有UI问题, UI问题列表, 标记图URL)
    """
    if logger is None:
        logger = logging.getLogger(__name__)
    if udid is None:
        raise ValueError("udid 参数不能为空")
    if device_issues is None:
        raise ValueError("device_issues 参数不能为空")
    
    current_round = get_device_status(udid)["round_num"]
    session_round_num = get_device_status(udid)["session_round_num"]
    device_id = udid
    page_info = f"「{page_name}」页面" if page_name else "页面"
    has_ui_error = False
    ui_error_list = []
    marked_image_url = None
    
    try:
        logger.info(f"开始检查{page_info}的 UI bug...")
        device_name = get_device_status(udid)["device_name"]
        ui_bug_results = get_ui_bug_from_ocr(screenshot_url, device_name, device_logger=logger)
        screenshot_image = Image.open(screenshot_path)
        image_width, image_height = screenshot_image.size
        has_ui_error, ui_error_list, marked_image_url = analyze_ui_bug_result(
            ui_bug_results,
            device_logger=logger,
            device_name=device_name,
            image_width=image_width,
            image_height=image_height,
            area_threshold=8.0
        )
        if has_ui_error:
            logger.warning(f"{page_info}存在 UI 缺陷:")
            for error in ui_error_list:
                logger.warning(f"- {error}")
            if marked_image_url:
                logger.info(f"标记了 UI 缺陷的截图已上传，URL: {marked_image_url} 轮次: {current_round}")
            if device_error_dir:
                error_path = device_operate_external.move_screenshot_to_error(screenshot_path, device_error_dir)
                logger.info(f"UI bug截图已移动到: {error_path}") 
            failure_time = time.strftime('%Y-%m-%d %H:%M:%S')
            failure_message = (
                f"时间：{failure_time}\n"
                f"设备：{device_name}\n"
                f"页面：{page_name or '未知页面'}\n"
                f"轮次：第 {session_round_num} 轮（历史轮次: {current_round}）\n"
                f"问题：发现 UI 缺陷\n"
                f"详情：\n{chr(10).join(['- ' + error for error in ui_error_list])}\n"
                f"图片：{marked_image_url or screenshot_url}"
            )
            send_individual_kingkong_message(failure_message, ['cuijie12'])
            send_group_kingkong_message(failure_message,69694646454)
            device_issues[device_id]['app_issue'] = True
            log_device_issue(
                device_id=device_id,
                device_name=device_name,
                round_num=current_round,
                issue_type='app',
                issue_details=f"{page_info}存在UI缺陷: {', '.join(ui_error_list[:2])}...",
                page_name=page_name,
                screenshot_url=marked_image_url or screenshot_url,
                execution_id=execution_id
            )
        else:
            logger.info(f"{page_info} UI 检查通过，未发现缺陷")
    except Exception as e:
        logger.error(f"{page_info} UI 检查过程中发生错误: {e}")
    
    return has_ui_error, ui_error_list, marked_image_url

def check_page_changed(
        before_screenshot_path,
        after_screenshot_path,
        logger=None,
        hash_threshold=8,
    ):
    """
    通过感知哈希算法检查两张截图是否表示页面发生了变化
    
    Args:
        before_screenshot_path: 操作前截图路径
        after_screenshot_path: 操作后截图路径
        logger: 日志对象，可选
        hash_threshold: 哈希差异阈值，默认8（越小越严格）
    
    Returns:
        tuple: (是否发生变化(bool), 哈希差异值(int), 详细信息(str))
    """
    if logger is None:
        logger = logging.getLogger(__name__)
    
    try:
        # 检查文件是否存在
        if not os.path.exists(before_screenshot_path):
            error_msg = f"操作前截图文件不存在: {before_screenshot_path}"
            logger.error(error_msg)
            return False, -1, error_msg
        
        if not os.path.exists(after_screenshot_path):
            error_msg = f"操作后截图文件不存在: {after_screenshot_path}"
            logger.error(error_msg)
            return False, -1, error_msg
        
        # 打开图片并计算感知哈希
        logger.debug(f"开始计算图片哈希值...")
        
        with Image.open(before_screenshot_path) as img1:
            hash1 = imagehash.phash(img1)
        
        with Image.open(after_screenshot_path) as img2:
            hash2 = imagehash.phash(img2)
        
        # 计算哈希差异（汉明距离）
        hash_diff = hash1 - hash2
        
        # 判断是否发生变化
        page_changed = hash_diff > hash_threshold
        
        # 生成详细信息
        before_filename = os.path.basename(before_screenshot_path)
        after_filename = os.path.basename(after_screenshot_path)
        detail_info = (
            f"页面变化检测: {before_filename} -> {after_filename}, "
            f"哈希差异: {hash_diff}, 阈值: {hash_threshold}, "
            f"结果: {'页面已变化' if page_changed else '页面未变化'}"
        )
        
        logger.info(detail_info)
        
        return page_changed, hash_diff, detail_info
        
    except Exception as e:
        error_msg = f"页面变化检测发生错误: {e}"
        logger.error(error_msg)
        logger.error(f"错误详情: {traceback.format_exc()}")
        return False, -1, error_msg

def after_tap_test(
    driver=None,
    udid=None,
    screenshot_path=None,
    screenshot_url=None,
    logger=None,
    execution_id=None,
    page_name=None,
    device_error_dir=None,
    device_issues=None,
    perform_ui_check=True,
    **kwargs
):
    """
    使用原子化检测系统重写的点击后检测函数
    相比原版after_tap_test，参数更简洁，逻辑更清晰
    
    优势：
    - 每个原子组件内部已处理错误、通知、日志记录等
    - 外层函数只需解析结果，无需重复处理逻辑
    - 代码更简洁，维护性更好
    
    Args:
        driver: WebDriver实例（iOS必需）
        udid: 设备UDID（必需）
        screenshot_path: 截图路径（必需）
        screenshot_url: 截图URL（必需）
        logger: 日志对象
        execution_id: 执行ID
        page_name: 页面名称
        device_error_dir: 错误截图目录
        device_issues: 设备问题状态字典
        perform_ui_check: 是否执行UI检查，默认True
        **kwargs: 其他参数
    
    Returns:
        dict: 检测结果，格式与原after_tap_test兼容
    """
    # 构建检测序列
    test_sequence = ['HANDLE_POPUP', 'HANDLE_CRASH', 'IS_HOMEPAGE']
    if perform_ui_check:
        test_sequence.append('HANDLE_UIBUG')
    
    # 调用原子化检测系统（内部已处理所有错误、通知、日志等）
    results = custom_test(
        test_sequence=test_sequence,
        udid=udid,
        driver=driver,
        screenshot_path=screenshot_path,
        screenshot_url=screenshot_url,
        logger=logger,
        execution_id=execution_id,
        page_name=page_name,
        device_error_dir=device_error_dir,
        device_issues=device_issues,
        stop_on_first_failure=False,
        **kwargs
    )
    
    # 解析结果（原子组件已处理所有错误情况）
    crash_result = results['test_results'].get('HANDLE_CRASH')
    homepage_result = results['test_results'].get('IS_HOMEPAGE')
    uibug_result = results['test_results'].get('HANDLE_UIBUG')
    
    # 构造兼容的返回结果
    return {
        'success': not any([
            crash_result and crash_result.app_crashed,  # 闪退 = 失败
            homepage_result and homepage_result.is_homepage  # 仍在首页 = 跳转失败
        ]),
        'screenshot_path': results['context'].screenshot_path,
        'screenshot_url': results['context'].screenshot_url,
        'app_crashed': crash_result.app_crashed if crash_result else False,
        'app_background': crash_result.app_background if crash_result else False,
        'jump_failed': homepage_result.is_homepage if homepage_result else False,
        'has_ui_error': uibug_result.has_ui_error if uibug_result else False,
        'ui_error_list': uibug_result.ui_error_list if uibug_result else [],
        'marked_image_url': uibug_result.marked_image_url if uibug_result else None,
        'error_reason': device_operate_internal._get_error_reason(crash_result, homepage_result)
    }

def get_screenshot_test(
        driver=None,
        udid=None,
        screenshot_path=None,
        logger=None,
        execution_id=None,
        page_name=None,
        device_error_dir=None,
        perform_ui_check=True,
        device_issues=None
    ):
    """
    使用原子化检测系统重写的截图测试函数
    
    原功能：
    1. 截图并上传
    2. 弹窗检测和处理
    3. UI检查（如果启用）
    4. 错误处理和通知
    
    现采用原子化方式：
    - 直接调用现有的 take_screenshot 函数
    - 使用 HANDLE_POPUP 原子化组件处理弹窗
    - 使用 HANDLE_UIBUG 原子化组件处理UI检查
    """
    if logger is None:
        logger = logging.getLogger(__name__)
    if device_issues is None:
        raise ValueError("device_issues 参数不能为空，需传入设备问题状态字典")
    
    # 获取设备信息
    device_status = get_device_status(udid)
    if device_status is False:
        logger.error(f"设备UDID '{udid}' 不是已知设备")
        return False, screenshot_path, None, False, [], None
    
    device_name = device_status["device_name"]
    current_round = device_status["round_num"]
    session_round_num = device_status["session_round_num"]
    page_info = f"「{page_name}」页面" if page_name else "页面"
    
    try:
        # 1. 截图并上传（使用现有函数）
        logger.info(f"开始获取{page_info}截图...")
        screenshot_result = device_operate_external.take_screenshot(
            driver=driver,
            udid=udid,
            screenshot_path=screenshot_path,
            logger=logger
        )
        
        # 处理截图失败的情况
        if screenshot_result == "unknown_device":
            logger.error(f"{page_info}截图失败: 设备不在系统中")
            device_issues[udid]['test_issue'] = True
            log_device_issue(
                device_id=udid,
                device_name=device_name,
                round_num=current_round,
                issue_type='test',
                issue_details=f"{page_info}截图失败: 设备不在系统中",
                page_name=page_name,
                execution_id=execution_id
            )
            return False, screenshot_path, None, False, [], None
        elif screenshot_result == "device_offline":
            logger.error(f"{page_info}截图时设备掉线")
            device_issues[udid]['test_issue'] = True
            log_device_issue(
                device_id=udid,
                device_name=device_name,
                round_num=current_round,
                issue_type='test',
                issue_details=f"{page_info}截图时设备掉线",
                page_name=page_name,
                execution_id=execution_id
            )
            return False, screenshot_path, None, False, [], None
        elif screenshot_result != "success" or not os.path.exists(screenshot_path) or os.path.getsize(screenshot_path) == 0:
            logger.error(f"{page_info}截图失败，文件不存在或为空: {screenshot_path}")
            device_issues[udid]['test_issue'] = True
            log_device_issue(
                device_id=udid,
                device_name=device_name,
                round_num=current_round,
                issue_type='test',
                issue_details=f"{page_info}截图失败",
                page_name=page_name,
                execution_id=execution_id
            )
            # 发送通报
            failure_time = time.strftime('%Y-%m-%d %H:%M:%S')
            failure_message = (
                f"时间：{failure_time}\n"
                f"设备：{device_name}\n"
                f"轮次：第 {session_round_num} 轮（历史轮次: {current_round}）\n"
                f"问题：{page_info}截图失败"
            )
            send_individual_kingkong_message(failure_message, ['cuijie12'])
            return False, screenshot_path, None, False, [], None
        
        logger.info(f"{page_info}截图已保存: {screenshot_path}")
        
        # 2. 上传截图
        try:
            screenshot_url = get_image_url(screenshot_path)
            logger.info(f"{page_info}截图已上传，URL: {screenshot_url} 轮次: {current_round}")
        except Exception as e:
            logger.error(f"上传{page_info}截图失败: {e}")
            device_issues[udid]['test_issue'] = True
            log_device_issue(
                device_id=udid,
                device_name=device_name,
                round_num=current_round,
                issue_type='test',
                issue_details=f"上传{page_info}截图失败: {str(e)}",
                page_name=page_name,
                execution_id=execution_id
            )
            failure_time = time.strftime('%Y-%m-%d %H:%M:%S')
            failure_message = (
                f"时间：{failure_time}\n"
                f"设备：{device_name}\n"
                f"轮次：第 {session_round_num} 轮（历史轮次: {current_round}）\n"
                f"问题：上传{page_info}截图失败\n"
                f"错误：{str(e)}"
            )
            send_individual_kingkong_message(failure_message, ['cuijie12'])
            return False, screenshot_path, None, False, [], None
        
        # 3. 使用原子化检测系统处理弹窗和UI检查
        test_sequence = ['HANDLE_POPUP']
        if perform_ui_check:
            test_sequence.append('HANDLE_UIBUG')
        
        # 调用原子化检测系统
        results = custom_test(
            test_sequence=test_sequence,
            udid=udid,
            driver=driver,
            screenshot_path=screenshot_path,
            screenshot_url=screenshot_url,
            logger=logger,
            execution_id=execution_id,
            page_name=page_name,
            device_error_dir=device_error_dir,
            device_issues=device_issues,
            current_round=current_round,
            session_round_num=session_round_num,
            stop_on_first_failure=False
        )
        
        # 解析结果
        popup_result = results['test_results'].get('HANDLE_POPUP')
        uibug_result = results['test_results'].get('HANDLE_UIBUG')
        
        # 更新截图路径和URL（如果弹窗处理更新了）
        final_screenshot_path = results['context'].screenshot_path
        final_screenshot_url = results['context'].screenshot_url
        
        # 获取UI检查结果
        has_ui_error = uibug_result.has_ui_error if uibug_result else False
        ui_error_list = uibug_result.ui_error_list if uibug_result else []
        marked_image_url = uibug_result.marked_image_url if uibug_result else None
        
        # 检查整体是否成功
        overall_success = results['overall_success']
        if not overall_success:
            logger.warning(f"{page_info}检测过程中出现问题: {'; '.join(results['error_summary'])}")
        
        # 返回结果（保持与原函数相同的返回格式）
        return True, final_screenshot_path, final_screenshot_url, has_ui_error, ui_error_list, marked_image_url

    except Exception as e:
        logger.error(f"{page_info}截图测试流程发生错误: {e}")
        device_issues[udid]['test_issue'] = True
        log_device_issue(
            device_id=udid,
            device_name=device_name,
            round_num=current_round,
            issue_type='test',
            issue_details=f"{page_info}截图测试流程发生错误: {str(e)}",
            page_name=page_name,
            execution_id=execution_id
        )
        return False, screenshot_path, None, False, [], None

def get_screenshot_ocr_elements(
        udid,
        driver,
        logger,
        screenshot_path
    ):
    """
    根据 udid 截图并获取 ocr 结果，返回 ocr 结果
    """
    if udid is None:
        raise ValueError("udid 参数不能为空")
    if logger is None:
        logger = logging.getLogger(__name__)
    device_name = get_device_status(udid).get('device_name')
    
    logger.info(f"开始获取截图，路径为：{screenshot_path}")
    
    screenshot_result = device_operate_external.take_screenshot(
        driver=driver,
        udid=udid,
        screenshot_path=screenshot_path,
        logger=logger
    )
    if screenshot_result != "success":
        logger.error(f"截图失败，路径为：{screenshot_path}")
        return False, None
    
    logger.info(f"截图成功，路径为：{screenshot_path}")
    
    screenshot_url = get_image_url(screenshot_path)
    page_elements = get_message_from_horus(screenshot_url, device_name, device_logger=logger)
    return page_elements
    
# ==================== 原子化检测组件系统 ====================

@dataclass
class TestContext:
    """测试上下文，包含所有检测所需的参数和状态信息"""
    # 设备基础信息
    driver: Any = None
    udid: str = None
    platform: str = 'ios'
    device_info: Dict = None
    
    # 测试状态信息
    screenshot_path: str = None
    screenshot_url: str = None
    logger: Any = None
    scale_ratio: float = None
    current_round: int = None
    session_round_num: int = None
    execution_id: str = None
    page_name: str = None
    device_error_dir: str = None
    device_issues: Dict = None
    
    # 应用信息
    bundle_id: str = None
    package_name: str = None
    
    # 动态数据（检测过程中更新）
    previous_screenshot_path: str = None  # 用于页面变化检测
    before_screenshot_path: str = None   # 操作前截图路径
    test_results: Dict[str, Any] = field(default_factory=dict)
    
    # 检测配置
    hash_threshold: int = 8  # 页面变化检测的哈希阈值

@dataclass  
class TestResult:
    """检测结果的标准化数据结构"""
    success: bool
    detection_type: str
    details: Dict[str, Any] = field(default_factory=dict)
    error_message: str = None
    context_updates: Dict[str, Any] = field(default_factory=dict)  # 需要更新到context的数据
    
    # 检测特定结果
    is_homepage: Optional[bool] = None
    has_popup: Optional[bool] = None
    popup_handled: Optional[bool] = None
    app_crashed: Optional[bool] = None
    app_background: Optional[bool] = None
    page_changed: Optional[bool] = None
    has_ui_error: Optional[bool] = None
    ui_error_list: List[str] = field(default_factory=list)
    marked_image_url: Optional[str] = None

# 原子化检测组件执行函数实现
def _execute_is_homepage(context: TestContext) -> TestResult:
    """检测是否为首页"""
    try:
        # 使用统一的check_is_homepage函数调用
        is_home = check_is_homepage(
            driver=context.driver,
            udid=context.udid,
            screenshot_path=context.screenshot_path,
            screenshot_url=context.screenshot_url,
            logger=context.logger
        )
        
        return TestResult(
            success=True,
            detection_type='IS_HOMEPAGE',
            is_homepage=is_home,
            details={
                'result': '是首页' if is_home else '不是首页',
                'platform': context.platform,
                'screenshot_path': context.screenshot_path,
                'screenshot_url': context.screenshot_url
            }
        )
    except Exception as e:
        context.logger.error(f"IS_HOMEPAGE检测发生错误: {e}")
        return TestResult(
            success=False,
            detection_type='IS_HOMEPAGE',
            error_message=str(e),
            is_homepage=None
        )

def _execute_not_homepage(context: TestContext) -> TestResult:
    """检测是否不是首页"""
    try:
        # 复用IS_HOMEPAGE的逻辑，然后取反
        homepage_result = _execute_is_homepage(context)
        
        if not homepage_result.success:
            # 如果首页检测失败，直接返回失败
            return TestResult(
                success=False,
                detection_type='NOT_HOMEPAGE',
                error_message=homepage_result.error_message,
                is_homepage=None
            )
        
        is_home = homepage_result.is_homepage
        not_home = not is_home
        
        return TestResult(
            success=True,
            detection_type='NOT_HOMEPAGE',
            is_homepage=is_home,  # 保留原始检测结果
            details={
                'result': '不是首页' if not_home else '是首页',
                'not_homepage': not_home,
                'platform': context.platform,
                'screenshot_path': context.screenshot_path,
                'screenshot_url': context.screenshot_url
            }
        )
    except Exception as e:
        context.logger.error(f"NOT_HOMEPAGE检测发生错误: {e}")
        return TestResult(
            success=False,
            detection_type='NOT_HOMEPAGE',
            error_message=str(e),
            is_homepage=None
        )

def _execute_handle_popup(context: TestContext) -> TestResult:
    """检测并处理弹窗"""
    try:
        # 调用现有的弹窗检测和处理函数
        popup_handled, final_screenshot_path, final_screenshot_url = check_and_handle_popup_after_screenshot(
            driver=context.driver,
            udid=context.udid,
            logger=context.logger,
            page_name=context.page_name,
        )
        
        # 构造返回结果
        result = TestResult(
            success=True,
            detection_type='HANDLE_POPUP',
            has_popup=popup_handled,  # 是否检测到并处理了弹窗
            popup_handled=popup_handled,
            details={
                'result': '检测到弹窗并已处理' if popup_handled else '未检测到弹窗',
                'popup_handled': popup_handled,
                'original_screenshot': context.screenshot_path,
                'final_screenshot': final_screenshot_path,
                'original_url': context.screenshot_url,
                'final_url': final_screenshot_url,
                'platform': context.platform
            }
        )
        
        # 如果处理了弹窗，更新上下文中的截图信息
        if popup_handled:
            result.context_updates = {
                'screenshot_path': final_screenshot_path,
                'screenshot_url': final_screenshot_url
            }
            context.logger.info(f"弹窗处理完成，截图已更新: {final_screenshot_path}")
        
        return result
        
    except Exception as e:
        context.logger.error(f"HANDLE_POPUP检测发生错误: {e}")
        return TestResult(
            success=False,
            detection_type='HANDLE_POPUP',
            error_message=str(e),
            has_popup=None,
            popup_handled=False
        )

def _execute_handle_crash(context: TestContext) -> TestResult:
    """检测并处理闪退/后台"""
    try:
        # 调用现有的闪退检测和处理函数
        crash_result = check_and_handle_app_crash(
            driver=context.driver,
            udid=context.udid,
            screenshot_url=context.screenshot_url,
            current_page=context.page_name,
            execution_id=context.execution_id,
            logger=context.logger
        )
        
        # 解析检测结果
        app_crashed = False
        app_background = False
        app_normal = True
        
        if crash_result is False:
            # 应用闪退
            app_crashed = True
            app_normal = False
            result_desc = "应用发生闪退，已重启应用"
        elif crash_result == 'background':
            # 应用进入后台
            app_background = True
            app_normal = False
            result_desc = "应用进入后台，已拉回前台"
        else:
            # 应用正常
            result_desc = "应用状态正常"
        
        return TestResult(
            success=True,
            detection_type='HANDLE_CRASH',
            app_crashed=app_crashed,
            app_background=app_background,
            details={
                'result': result_desc,
                'app_crashed': app_crashed,
                'app_background': app_background,
                'app_normal': app_normal,
                'platform': context.platform,
                'current_page': context.page_name
            }
        )
        
    except Exception as e:
        context.logger.error(f"HANDLE_CRASH检测发生错误: {e}")
        return TestResult(
            success=False,
            detection_type='HANDLE_CRASH',
            error_message=str(e),
            app_crashed=None,
            app_background=None
        )

def _execute_is_different(context: TestContext) -> TestResult:
    """检测页面是否发生跳转"""
    try:
        # 调用现有的check_page_changed函数
        page_changed, hash_diff, detail_info = check_page_changed(
            before_screenshot_path=context.before_screenshot_path,
            after_screenshot_path=context.screenshot_path,
            logger=context.logger,
            hash_threshold=context.hash_threshold
        )
        
        return TestResult(
            success=True,
            detection_type='IS_DIFFERENT',
            page_changed=page_changed,
            details={
                'result': '页面已变化' if page_changed else '页面未变化',
                'hash_diff': hash_diff,
                'hash_threshold': context.hash_threshold,
                'detail_info': detail_info,
                'before_screenshot': context.before_screenshot_path,
                'after_screenshot': context.screenshot_path
            }
        )
    except Exception as e:
        context.logger.error(f"IS_DIFFERENT检测发生错误: {e}")
        return TestResult(
            success=False,
            detection_type='IS_DIFFERENT',
            error_message=str(e),
            page_changed=None
        )

def _execute_handle_uibug(context: TestContext) -> TestResult:
    """检测并处理UI bug"""
    try:
        # 调用现有的UI检查函数
        has_ui_error, ui_error_list, marked_image_url = perform_ui_check_only(
            screenshot_path=context.screenshot_path,
            screenshot_url=context.screenshot_url,
            udid=context.udid,
            logger=context.logger,
            execution_id=context.execution_id,
            page_name=context.page_name,
            device_error_dir=context.device_error_dir,
            device_issues=context.device_issues
        )
        
        # 构造返回结果
        result = TestResult(
            success=True,
            detection_type='HANDLE_UIBUG',
            has_ui_error=has_ui_error,
            ui_error_list=ui_error_list,
            marked_image_url=marked_image_url,
            details={
                'result': f'检测到 {len(ui_error_list)} 个UI缺陷' if has_ui_error else '未发现UI缺陷',
                'has_ui_error': has_ui_error,
                'ui_error_count': len(ui_error_list),
                'ui_error_list': ui_error_list,
                'marked_image_url': marked_image_url,
                'original_screenshot': context.screenshot_path,
                'original_url': context.screenshot_url,
                'page_name': context.page_name
            }
        )
        
        # 如果有UI错误，上下文中可能需要更新截图路径（如果移动到了error目录）
        if has_ui_error and context.device_error_dir:
            # perform_ui_check_only 函数内部已经处理了截图移动，这里不需要额外操作
            context.logger.info(f"UI缺陷检测完成，发现 {len(ui_error_list)} 个问题")
        
        return result
        
    except Exception as e:
        context.logger.error(f"HANDLE_UIBUG检测发生错误: {e}")
        return TestResult(
            success=False,
            detection_type='HANDLE_UIBUG',
            error_message=str(e),
            has_ui_error=None,
            ui_error_list=[],
            marked_image_url=None
        )

# 原子化检测组件字典
DETECTION_ATOMS = {
    'IS_HOMEPAGE': _execute_is_homepage,
    'NOT_HOMEPAGE': _execute_not_homepage,
    'HANDLE_POPUP': _execute_handle_popup,
    'HANDLE_CRASH': _execute_handle_crash,
    'IS_DIFFERENT': _execute_is_different,
    'HANDLE_UIBUG': _execute_handle_uibug,
}

# 检测组件的依赖要求定义
DETECTION_REQUIREMENTS = {
    'IS_HOMEPAGE': {
        'required_fields': ['screenshot_path', 'screenshot_url', 'logger', 'device_info'],
        'ios_fields': ['driver'],
        'android_fields': [],
        'description': '检测当前页面是否为首页'
    },
    'NOT_HOMEPAGE': {
        'required_fields': ['screenshot_path', 'screenshot_url', 'logger', 'device_info'],
        'ios_fields': ['driver'],
        'android_fields': [],
        'description': '检测当前页面是否不是首页'
    },
    'HANDLE_POPUP': {
        'required_fields': ['screenshot_path', 'screenshot_url', 'device_info', 'logger', 'scale_ratio'],
        'ios_fields': ['driver'],
        'android_fields': ['udid'],
        'optional_fields': ['current_round', 'page_name', 'bundle_id', 'package_name'],
        'description': '检测并处理弹窗，包括关闭弹窗和重新截图'
    },
    'HANDLE_CRASH': {
        'required_fields': ['device_info', 'logger'],
        'ios_fields': ['driver'],
        'android_fields': ['udid'],
        'optional_fields': ['screenshot_url', 'page_name', 'execution_id', 'session_round_num', 'bundle_id', 'package_name'],
        'description': '检测并处理应用闪退或进入后台'
    },
    'IS_DIFFERENT': {
        'required_fields': ['screenshot_path', 'before_screenshot_path', 'logger'],
        'optional_fields': ['hash_threshold'],
        'description': '检测页面是否发生变化（跳转）'
    },
    'HANDLE_UIBUG': {
        'required_fields': ['screenshot_path', 'screenshot_url', 'device_info', 'logger'],
        'optional_fields': ['current_round', 'session_round_num', 'execution_id', 'page_name', 'device_error_dir', 'device_issues'],
        'description': '检测并处理UI缺陷，包括通知和截图移动'
    }
}

def validate_context_for_detection(context: TestContext, detection_type: str) -> tuple[bool, str]:
    """
    验证测试上下文是否满足指定检测类型的要求
    
    Args:
        context: 测试上下文
        detection_type: 检测类型
        
    Returns:
        tuple: (是否验证通过, 错误信息)
    """
    if detection_type not in DETECTION_REQUIREMENTS:
        return False, f"未知的检测类型: {detection_type}"
    
    requirements = DETECTION_REQUIREMENTS[detection_type]
    
    # 检查必需字段
    for field in requirements.get('required_fields', []):
        if not hasattr(context, field) or getattr(context, field) is None:
            return False, f"检测类型 {detection_type} 缺少必需字段: {field}"
    
    # 检查平台特定字段
    if context.platform == 'ios':
        for field in requirements.get('ios_fields', []):
            if not hasattr(context, field) or getattr(context, field) is None:
                return False, f"iOS平台的检测类型 {detection_type} 缺少必需字段: {field}"
    elif context.platform == 'android':
        for field in requirements.get('android_fields', []):
            if not hasattr(context, field) or getattr(context, field) is None:
                return False, f"Android平台的检测类型 {detection_type} 缺少必需字段: {field}"
    
    return True, ""

def get_detection_info(detection_type: str = None) -> Union[Dict, List[str]]:
    """
    获取检测组件信息
    
    Args:
        detection_type: 指定检测类型，如果为None则返回所有可用检测类型
        
    Returns:
        Dict or List: 检测信息或检测类型列表
    """
    if detection_type is None:
        return list(DETECTION_ATOMS.keys())
    
    if detection_type not in DETECTION_REQUIREMENTS:
        return {}
    
    return DETECTION_REQUIREMENTS[detection_type]

def custom_test(
    test_sequence: List[str],
    udid: str,
    driver=None,  # iOS必需
    screenshot_path=None,
    screenshot_url=None,
    logger=None,
    execution_id=None,
    page_name=None,
    device_error_dir=None,
    device_issues=None,
    before_screenshot_path=None,
    hash_threshold=8,
    stop_on_first_failure=False,
    **kwargs
) -> Dict[str, Any]:
    """
    原子化检测系统的核心接口函数 (简化版 - 自动查询设备信息)
    
    Args:
        test_sequence: 检测序列列表，如 ["IS_HOMEPAGE", "IS_DIFFERENT", "HANDLE_POPUP"]
        udid: 设备UDID，用于自动查询设备信息
        driver: WebDriver实例 (仅iOS必需)
        screenshot_path: 当前截图路径
        screenshot_url: 当前截图URL
        logger: 日志对象 (可选)
        before_screenshot_path: 操作前截图路径 (页面变化检测需要)
        hash_threshold: 页面变化检测的哈希阈值
        stop_on_first_failure: 是否在第一个失败时停止执行
        其他参数: 测试上下文相关参数
        
    Returns:
        Dict: 包含所有检测结果的字典
        {
            'overall_success': bool,  # 整体是否成功
            'executed_tests': List[str],  # 已执行的检测列表
            'test_results': Dict[str, TestResult],  # 每个检测的详细结果
            'context': TestContext,  # 最终的测试上下文
            'error_summary': List[str],  # 错误摘要
            'total_count': int,  # 总检测数量
            'success_count': int,  # 成功数量
            'failed_count': int,  # 失败数量
            'device_info': Dict  # 查询到的设备信息
        }
    """
    if logger is None:
        logger = DEFAULT_LOGGER
    
    # 1. 通过udid查询设备信息
    logger.info(f"正在查询设备信息: {udid}")
    device_info = get_device_status(udid)
    
    if device_info is False:
        error_msg = f"设备UDID '{udid}' 不在系统中，无法执行检测"
        logger.error(error_msg)
        return {
            'overall_success': False,
            'executed_tests': [],
            'test_results': {},
            'context': None,
            'error_summary': [error_msg],
            'total_count': len(test_sequence),
            'success_count': 0,
            'failed_count': len(test_sequence),
            'device_info': None
        }
    
    # 2. 从设备信息中自动获取参数
    platform = device_info.get('platform')
    scale_ratio = device_info.get('scale_ratio')
    
    logger.info(f"设备信息获取成功: 平台={platform}, 缩放比例={scale_ratio}, 设备名={device_info.get('device_name')}")
    
    # 3. 初始化测试上下文
    context = TestContext(
        driver=driver,
        udid=udid,
        platform=platform,
        device_info=device_info,
        screenshot_path=screenshot_path,
        screenshot_url=screenshot_url,
        logger=logger,
        scale_ratio=scale_ratio,
        current_round=kwargs.get('current_round'),
        session_round_num=kwargs.get('session_round_num'),
        execution_id=execution_id,
        page_name=page_name,
        device_error_dir=device_error_dir,
        device_issues=device_issues,
        bundle_id=device_info.get('bundle_id'),
        package_name=device_info.get('package_name'),
        before_screenshot_path=before_screenshot_path,
        hash_threshold=hash_threshold
    )
    
    # 4. 初始化结果容器
    results = {
        'overall_success': True,
        'executed_tests': [],
        'test_results': {},
        'context': context,
        'error_summary': [],
        'total_count': len(test_sequence),
        'success_count': 0,
        'failed_count': 0,
        'device_name': device_info.get('device_name')  # 返回查询到的设备信息
    }
    
    logger.info(f"开始执行原子化检测序列: {test_sequence}")
    
    # 5. 按序执行检测
    for i, detection_name in enumerate(test_sequence):
        logger.info(f"执行检测 {i+1}/{len(test_sequence)}: {detection_name}")
        
        # 检查检测类型是否存在
        if detection_name not in DETECTION_ATOMS:
            error_msg = f"未知的检测类型: {detection_name}"
            logger.error(error_msg)
            results['error_summary'].append(error_msg)
            results['overall_success'] = False
            results['failed_count'] += 1
            continue
        
        # 验证上下文
        is_valid, validation_error = validate_context_for_detection(context, detection_name)
        if not is_valid:
            error_msg = f"检测 {detection_name} 上下文验证失败: {validation_error}"
            logger.error(error_msg)
            results['error_summary'].append(error_msg)
            results['overall_success'] = False
            results['failed_count'] += 1
            
            # 创建失败的结果记录
            results['test_results'][detection_name] = TestResult(
                success=False,
                detection_type=detection_name,
                error_message=validation_error
            )
            results['executed_tests'].append(detection_name)
            
            if stop_on_first_failure:
                logger.warning(f"由于检测 {detection_name} 失败，停止后续检测")
                break
            continue
        
        try:
            # 执行检测
            result = DETECTION_ATOMS[detection_name](context)
            
            # 记录结果
            results['test_results'][detection_name] = result
            results['executed_tests'].append(detection_name)
            
            # 更新统计
            if result.success:
                results['success_count'] += 1
                logger.info(f"检测 {detection_name} 成功: {result.details.get('result', 'N/A')}")
            else:
                results['failed_count'] += 1
                results['overall_success'] = False
                error_msg = f"{detection_name}: {result.error_message}"
                results['error_summary'].append(error_msg)
                logger.error(f"检测 {detection_name} 失败: {result.error_message}")
            
            # 更新上下文
            if result.context_updates:
                for key, value in result.context_updates.items():
                    setattr(context, key, value)
                    logger.debug(f"上下文更新: {key} = {value}")
            
            # 检查是否需要停止
            if not result.success and stop_on_first_failure:
                logger.warning(f"由于检测 {detection_name} 失败，停止后续检测")
                break
                
        except Exception as e:
            error_msg = f"检测 {detection_name} 执行时发生异常: {e}"
            logger.error(error_msg)
            logger.error(f"异常详情: {traceback.format_exc()}")
            
            results['error_summary'].append(error_msg)
            results['overall_success'] = False
            results['failed_count'] += 1
            
            # 创建异常结果记录
            results['test_results'][detection_name] = TestResult(
                success=False,
                detection_type=detection_name,
                error_message=str(e)
            )
            results['executed_tests'].append(detection_name)
            
            if stop_on_first_failure:
                logger.warning(f"由于检测 {detection_name} 异常，停止后续检测")
                break
    
    # 6. 输出最终摘要
    logger.info(f"检测序列执行完成: 总计 {results['total_count']}, 成功 {results['success_count']}, 失败 {results['failed_count']}")
    if results['error_summary']:
        logger.warning(f"失败摘要: {'; '.join(results['error_summary'])}")
    
    return results

def check_element_exists(
    screenshot_url: str,
    target_text: str,
    device_name: str = None,
    logger=None,
    match_mode: str = 'contains'
) -> bool:
    """
    检查页面截图中是否存在指定的文本元素
    
    Args:
        screenshot_url (str): 截图URL
        target_text (str): 要查找的目标文本
        device_name (str, optional): 设备名称，用于日志记录
        logger (optional): 日志对象
        match_mode (str, optional): 匹配模式，支持以下选项：
            - 'exact': 精确匹配
            - 'contains': 包含匹配（默认）
            - 'startswith': 以目标文本开头
            - 'endswith': 以目标文本结尾
    
    Returns:
        bool: True表示找到目标文本，False表示未找到
    """
    if logger is None:
        logger = DEFAULT_LOGGER
    
    # 参数验证
    if not screenshot_url or not isinstance(screenshot_url, str):
        logger.error("check_element_exists: screenshot_url 参数无效")
        return False
    
    if not target_text or not isinstance(target_text, str):
        logger.error("check_element_exists: target_text 参数无效")
        return False
    
    if match_mode not in ['exact', 'contains', 'startswith', 'endswith']:
        logger.warning(f"check_element_exists: 不支持的匹配模式 '{match_mode}'，使用默认的 'contains' 模式")
        match_mode = 'contains'
    
    device_prefix = f"[{device_name}] - " if device_name else ""
    
    try:
        logger.info(f"{device_prefix}开始检查页面中是否存在文本: '{target_text}' (匹配模式: {match_mode})")
        
        # 调用现有的OCR接口获取页面元素
        page_elements = get_message_from_horus(
            screenshot_url, 
            device_name=device_name, 
            device_logger=logger
        )
        
        if not page_elements:
            logger.warning(f"{device_prefix}OCR未获取到任何页面元素")
            return False
        
        logger.debug(f"{device_prefix}OCR获取到 {len(page_elements)} 个页面元素")
        
        # 在页面元素中查找目标文本
        found_elements = []
        for element in page_elements:
            if len(element) >= 2:
                element_text = element[0]  # 元素的文本内容
                element_coords = element[1]  # 元素的坐标
                
                # 根据匹配模式进行匹配
                is_match = False
                if match_mode == 'exact':
                    is_match = element_text == target_text
                elif match_mode == 'contains':
                    is_match = target_text in element_text
                elif match_mode == 'startswith':
                    is_match = element_text.startswith(target_text)
                elif match_mode == 'endswith':
                    is_match = element_text.endswith(target_text)
                
                if is_match:
                    found_elements.append({
                        'text': element_text,
                        'coords': element_coords
                    })
                    logger.debug(f"{device_prefix}找到匹配元素: '{element_text}' at {element_coords}")
        
        # 输出结果
        if found_elements:
            logger.info(f"{device_prefix}成功找到目标文本 '{target_text}'，共找到 {len(found_elements)} 个匹配元素")
            for i, elem in enumerate(found_elements, 1):
                logger.info(f"{device_prefix}匹配元素 {i}: '{elem['text']}' at {elem['coords']}")
            return True
        else:
            logger.info(f"{device_prefix}未找到目标文本 '{target_text}'")
            # 输出所有检测到的元素，便于调试
            if page_elements:
                all_texts = [elem[0] for elem in page_elements if len(elem) >= 1]
                logger.debug(f"{device_prefix}页面中检测到的所有文本元素: {all_texts}")
            return False
    
    except Exception as e:
        logger.error(f"{device_prefix}检查页面元素时发生错误: {e}")
        logger.error(f"{device_prefix}错误详情: {traceback.format_exc()}")
        return False

def check_multiple_elements_exist(
    screenshot_url: str,
    target_texts: list,
    device_name: str = None,
    logger=None,
    match_mode: str = 'contains',
    require_all: bool = True
) -> dict:
    """
    检查页面截图中是否存在多个指定的文本元素
    
    Args:
        screenshot_url (str): 截图URL
        target_texts (list): 要查找的目标文本列表
        device_name (str, optional): 设备名称，用于日志记录
        logger (optional): 日志对象
        match_mode (str, optional): 匹配模式，参考 check_element_exists
        require_all (bool, optional): True表示需要找到所有元素才返回True，False表示找到任意一个即可
    
    Returns:
        dict: 包含检查结果的字典
        {
            'overall_result': bool,  # 整体结果
            'found_count': int,      # 找到的元素数量
            'total_count': int,      # 总查找元素数量
            'found_elements': list,  # 找到的元素详情
            'missing_elements': list # 未找到的元素列表
        }
    """
    if logger is None:
        logger = DEFAULT_LOGGER
    
    # 参数验证
    if not isinstance(target_texts, list) or not target_texts:
        logger.error("check_multiple_elements_exist: target_texts 必须是非空列表")
        return {
            'overall_result': False,
            'found_count': 0,
            'total_count': 0,
            'found_elements': [],
            'missing_elements': []
        }
    
    device_prefix = f"[{device_name}] - " if device_name else ""
    
    try:
        logger.info(f"{device_prefix}开始批量检查页面元素，目标数量: {len(target_texts)}")
        
        # 只调用一次OCR接口，获取所有页面元素
        page_elements = get_message_from_horus(
            screenshot_url, 
            device_name=device_name, 
            device_logger=logger
        )
        
        if not page_elements:
            logger.warning(f"{device_prefix}OCR未获取到任何页面元素")
            return {
                'overall_result': False,
                'found_count': 0,
                'total_count': len(target_texts),
                'found_elements': [],
                'missing_elements': target_texts.copy()
            }
        
        # 提取所有页面文本
        page_texts = [elem[0] for elem in page_elements if len(elem) >= 1]
        logger.debug(f"{device_prefix}页面中检测到的所有文本: {page_texts}")
        
        found_elements = []
        missing_elements = []
        
        # 逐个检查目标文本
        for target_text in target_texts:
            found = False
            for element in page_elements:
                if len(element) >= 2:
                    element_text = element[0]
                    element_coords = element[1]
                    
                    # 根据匹配模式进行匹配
                    is_match = False
                    if match_mode == 'exact':
                        is_match = element_text == target_text
                    elif match_mode == 'contains':
                        is_match = target_text in element_text
                    elif match_mode == 'startswith':
                        is_match = element_text.startswith(target_text)
                    elif match_mode == 'endswith':
                        is_match = element_text.endswith(target_text)
                    
                    if is_match:
                        found_elements.append({
                            'target': target_text,
                            'matched_text': element_text,
                            'coords': element_coords
                        })
                        found = True
                        break
            
            if not found:
                missing_elements.append(target_text)
        
        # 计算结果
        found_count = len(found_elements)
        total_count = len(target_texts)
        
        if require_all:
            overall_result = found_count == total_count
        else:
            overall_result = found_count > 0
        
        # 输出日志
        logger.info(f"{device_prefix}批量检查完成: 找到 {found_count}/{total_count} 个元素")
        if found_elements:
            logger.info(f"{device_prefix}找到的元素:")
            for elem in found_elements:
                logger.info(f"{device_prefix}  - 目标: '{elem['target']}' → 匹配: '{elem['matched_text']}' at {elem['coords']}")
        
        if missing_elements:
            logger.info(f"{device_prefix}未找到的元素: {missing_elements}")
        
        return {
            'overall_result': overall_result,
            'found_count': found_count,
            'total_count': total_count,
            'found_elements': found_elements,
            'missing_elements': missing_elements
        }
    
    except Exception as e:
        logger.error(f"{device_prefix}批量检查页面元素时发生错误: {e}")
        logger.error(f"{device_prefix}错误详情: {traceback.format_exc()}")
        return {
            'overall_result': False,
            'found_count': 0,
            'total_count': len(target_texts),
            'found_elements': [],
            'missing_elements': target_texts.copy()
        }

def go_back_homepage(
    driver=None,
    udid=None,
    logger=None,
    execution_id=None,
    device_issues=None,
    test_context_name="测试"
):
    """
    检查是否在美团首页，如果不在则尝试返回首页
    
    Args:
        driver: WebDriver实例（iOS必需）
        udid: 设备UDID（必需）
        logger: 日志对象
        execution_id: 执行ID
        device_issues: 设备问题状态字典
        test_context_name: 测试上下文名称，用于日志记录（如："固定图标测试"、"全量图标测试"等）
        
    Returns:
        bool: True表示成功在首页或成功回到首页，False表示失败
    """
    if udid is None:
        if logger:
            logger.error("go_back_homepage: udid 参数不能为空")
        return False
    
    if logger is None:
        import logging
        logger = logging.getLogger(__name__)
    
    if device_issues is None:
        if logger:
            logger.error("go_back_homepage: device_issues 参数不能为空")
        return False
    
    # 获取设备状态信息
    device_name = get_device_status(udid).get('device_name')
    current_round = get_device_status(udid).get('round_num')
    scale_ratio = get_device_status(udid).get('scale_ratio')
    device_screenshot_dir = get_device_status(udid).get('device_screenshot_dir')
    platform = get_device_status(udid).get('platform')
    
    logger.info(f"开始检测{device_name}是否在美团首页...")
    max_homepage_retries = 3
    homepage_retry_count = 0
    last_homepage_check_url = None  # 保存最后一次检查的截图URL
    
    while homepage_retry_count < max_homepage_retries:
        # 先截图
        timestamp = time.strftime('%Y%m%d_%H%M%S')
        homepage_check_screenshot_path = os.path.join(
            device_screenshot_dir, 
            f'{test_context_name}_首页检查_{device_name}_{timestamp}_第{homepage_retry_count + 1}次.png'
        )
        
        screenshot_result = device_operate_external.take_screenshot(
            driver=driver,
            udid=udid,
            screenshot_path=homepage_check_screenshot_path,
            logger=logger
        )
        
        if screenshot_result != "success":
            logger.error(f"{device_name}首页检查截图失败: {screenshot_result}")
            device_issues[udid]['test_issue'] = True
            return False
        
        # 上传截图获取URL
        homepage_check_screenshot_url = get_image_url(homepage_check_screenshot_path)
        last_homepage_check_url = homepage_check_screenshot_url  # 保存最后一次检查的URL
        logger.info(f"{test_context_name}：{device_name}第{homepage_retry_count + 1}次首页检查截图已上传，URL: {homepage_check_screenshot_url} 轮次: {current_round}")
        
        # 检查是否是首页
        is_homepage_result = check_is_homepage(
            driver=driver,
            udid=udid,
            screenshot_path=homepage_check_screenshot_path,
            screenshot_url=homepage_check_screenshot_url,
            logger=logger
        )
        
        if is_homepage_result:
            logger.info(f"{test_context_name}：{device_name}首页检查通过，当前在美团首页")
            return True
        else:
            logger.warning(f"{test_context_name}：{device_name}当前不在首页（第{homepage_retry_count + 1}次检查），尝试返回首页")
            
            # 获取图片尺寸用于查找返回按钮
            try:
                screenshot_image = Image.open(homepage_check_screenshot_path)
                image_width, image_height = screenshot_image.size
            except Exception as e:
                logger.error(f"获取截图尺寸失败: {e}")
                image_width = image_height = None
            
            # 查找返回按钮
            from python.get_message_from_horus import find_back_button_location
            back_button_location = None
            try:
                back_button_location = find_back_button_location(
                    homepage_check_screenshot_url,
                    device_name,
                    image_height,
                    device_logger=logger,
                    image_width=image_width
                )
            except Exception as e:
                logger.warning(f"查找返回按钮异常: {e}")
            
            if back_button_location:
                bx, by = back_button_location
                scaled_bx = bx / scale_ratio
                scaled_by = by / scale_ratio
                logger.info(f"{test_context_name}：{device_name}找到返回按钮，正在点击返回: ({bx}, {by}) -> ({scaled_bx}, {scaled_by})")
                tap_success = device_operate_external.tap(
                    driver=driver,
                    udid=udid,
                    x=scaled_bx,
                    y=scaled_by,
                    logger=logger
                )
                if not tap_success:
                    logger.error(f"{test_context_name}：{device_name}返回按钮点击失败")
                time.sleep(5)
            else:
                # 兜底方案
                if platform == 'ios':
                    logger.warning(f"{test_context_name}：{device_name}未找到返回按钮，尝试点击左上角兜底区域")
                    if driver:
                        screen_size = driver.get_window_size()
                        fallback_x = screen_size['width'] * 0.05
                        fallback_y = screen_size['height'] * 0.08
                    else:
                        fallback_x = fallback_y = 10
                    tap_success = device_operate_external.tap(
                        driver=driver,
                        udid=udid,
                        x=fallback_x,
                        y=fallback_y,
                        logger=logger
                    )
                    if not tap_success:
                        logger.error(f"{test_context_name}：{device_name}兜底区域点击失败")
                    time.sleep(5)
                else:  # Android
                    logger.info(f"{test_context_name}：{device_name}未找到返回按钮，尝试使用系统返回键")
                    device_operate_external.execute_adb_command(udid, "shell input keyevent 4")
                    time.sleep(5)
            
            homepage_retry_count += 1
    
    # 检查是否成功回到首页
    if homepage_retry_count >= max_homepage_retries:
        logger.error(f"{test_context_name}：{device_name}无法回到美团首页，已达到最大重试次数")
        device_issues[udid]['test_issue'] = True
        
        # 记录问题到日志系统
        log_device_issue(
            device_id=udid,
            device_name=device_name,
            round_num=current_round,
            issue_type='app',
            issue_details=f"{test_context_name}：无法回到美团首页，已达到最大重试次数",
            page_name="首页检查",
            screenshot_url=last_homepage_check_url,
            execution_id=execution_id
        )
        
        return False
    
    return True

