#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
环境配置切换工具

这个脚本用于切换整个项目的环境配置。
可以通过命令行参数或交互式方式设置环境。
还可以设置设备/主机的唯一ID，便于多设备运行同一份代码。
"""

import os
import sys
import argparse
import env_config

def main():
    parser = argparse.ArgumentParser(description='项目环境配置切换工具')
    parser.add_argument('--env', '-e', type=str, choices=['prod', 'test'],
                      help='设置环境: prod(生产环境)或test(测试环境)')
    parser.add_argument('--show', '-s', action='store_true',
                      help='显示当前环境配置')
    parser.add_argument('--interactive', '-i', action='store_true',
                      help='交互式设置环境')
    parser.add_argument('--custom-url', '-c', action='store_true',
                      help='设置自定义服务URL')
    parser.add_argument('--id', type=str,
                      help='设置设备/主机ID，用于区分不同执行设备')
    
    args = parser.parse_args()
    
    # 设置设备/主机ID
    if args.id:
        host_id = args.id
        env_config.set_host_id(host_id)
        
        # 保存到.env文件
        env_file = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), '.env')
        
        # 先读取现有.env文件内容
        env_content = {}
        if os.path.exists(env_file):
            try:
                with open(env_file, 'r') as f:
                    for line in f:
                        line = line.strip()
                        if line and not line.startswith('#'):
                            key, value = line.split('=', 1)
                            env_content[key.strip()] = value.strip()
            except Exception as e:
                print(f"读取.env文件时出错: {e}")
        
        # 更新HOST_ID并写回文件
        env_content['HOST_ID'] = host_id
        
        with open(env_file, 'w') as f:
            for key, value in env_content.items():
                f.write(f"{key}={value}\n")
        
        print(f"设备/主机ID已设置为: {host_id}")
        
        # 如果只设置了ID，不需要显示其他信息，直接返回
        if not (args.show or args.env or args.interactive or args.custom_url):
            return 0
    
    # 显示当前环境配置
    if args.show or (not args.env and not args.interactive and not args.custom_url):
        current_env = env_config.get_env()
        host_id = env_config.get_host_id()
        print(f"当前环境: {current_env}")
        print(f"设备/主机ID: {host_id or '未设置'}")
        print(f"基础域名: {env_config.get_base_domain()}")
        print(f"Horus API URL: {env_config.get_horus_api_url()}")
        print(f"日志服务 API URL: {env_config.get_log_service_api_url()}")
        
        # 显示所有服务的API URL
        print("\n所有服务的API URL:")
        for service_name in env_config.API_PATHS:
            api_url = env_config.get_api_url(service_name)
            print(f"  {service_name}: {api_url}")
        
        # 检查是否有自定义URL
        print("\n自定义URL配置:")
        custom_urls_found = False
        for service_name in env_config.API_PATHS:
            for env_name in ['prod', 'test']:
                custom_url = env_config.get_custom_service_url(service_name, env_name)
                if custom_url:
                    print(f"  {service_name} ({env_name}): {custom_url}")
                    custom_urls_found = True
        
        if not custom_urls_found:
            print("  没有配置自定义URL")
        
        return 0
    
    # 设置自定义服务URL
    if args.custom_url:
        print("设置自定义服务URL:")
        
        # 列出所有可用服务
        print("\n可用服务:")
        for i, service_name in enumerate(env_config.API_PATHS, 1):
            print(f"{i}. {service_name}")
        
        # 选择服务
        while True:
            service_choice = input("\n请输入服务编号，或输入服务名称: ").strip()
            try:
                # 尝试将输入解析为数字
                service_idx = int(service_choice) - 1
                if 0 <= service_idx < len(env_config.API_PATHS):
                    service_name = list(env_config.API_PATHS.keys())[service_idx]
                    break
                else:
                    print("无效的服务编号，请重新输入")
            except ValueError:
                # 如果不是数字，则检查输入的是否为服务名称
                if service_choice in env_config.API_PATHS:
                    service_name = service_choice
                    break
                else:
                    # 允许添加新服务
                    confirm = input(f"服务 '{service_choice}' 不存在，是否添加新服务? (y/n): ").strip().lower()
                    if confirm == 'y':
                        service_name = service_choice
                        # 添加新服务路径
                        path = input("请输入服务API路径（不含域名，以/开头）: ").strip()
                        env_config.add_service_path(service_name, path)
                        print(f"已添加新服务 '{service_name}' 路径为 '{path}'")
                        break
                    else:
                        print("请重新输入")
        
        # 选择环境
        print("\n请选择要为哪个环境设置自定义URL:")
        print("1. prod - 生产环境")
        print("2. test - 测试环境")
        
        while True:
            env_choice = input("请输入环境编号(1/2): ").strip()
            if env_choice == '1':
                env = 'prod'
                break
            elif env_choice == '2':
                env = 'test'
                break
            else:
                print("无效的选项，请重新输入")
        
        # 输入自定义URL
        while True:
            custom_url = input(f"\n请输入 {service_name} 在 {env} 环境下的自定义URL: ").strip()
            if not custom_url:
                print("URL不能为空，请重新输入")
                continue
                
            # 检查URL格式
            if not (custom_url.startswith('http://') or custom_url.startswith('https://')):
                confirm = input("URL应以http://或https://开头，是否继续？(y/n): ").strip().lower()
                if confirm != 'y':
                    continue
            
            # 设置自定义URL
            env_config.set_custom_service_url(service_name, env, custom_url)
            print(f"已为服务 '{service_name}' 在 '{env}' 环境下设置自定义URL: {custom_url}")
            
            # 显示当前URL
            current_env = env_config.get_env()
            if current_env == env:
                print(f"当前环境 ({current_env}) 下该服务的URL: {env_config.get_api_url(service_name)}")
            break
        
        return 0
    
    # 交互式设置环境
    if args.interactive:
        print("请选择要设置的环境:")
        print("1. prod - 生产环境")
        print("2. test - 测试环境")
        
        while True:
            choice = input("请输入选项编号(1/2): ").strip()
            if choice == '1':
                env = 'prod'
                break
            elif choice == '2':
                env = 'test'
                break
            else:
                print("无效的选项，请重新输入")
    else:
        env = args.env
    
    # 设置环境
    env_config.set_env(env)
    
    # 保存环境配置到文件
    env_file = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), '.env')
    
    # 先读取现有.env文件内容以保留其他配置
    env_content = {}
    if os.path.exists(env_file):
        try:
            with open(env_file, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#'):
                        key, value = line.split('=', 1)
                        env_content[key.strip()] = value.strip()
        except Exception as e:
            print(f"读取.env文件时出错: {e}")
    
    # 更新PROJECT_ENV并写回文件
    env_content['PROJECT_ENV'] = env
    
    with open(env_file, 'w') as f:
        for key, value in env_content.items():
            f.write(f"{key}={value}\n")
    
    print(f"环境已设置为: {env}")
    print(f"基础域名: {env_config.get_base_domain()}")
    print(f"Horus API URL: {env_config.get_horus_api_url()}")
    print(f"日志服务 API URL: {env_config.get_log_service_api_url()}")
    
    return 0

if __name__ == "__main__":
    sys.exit(main()) 