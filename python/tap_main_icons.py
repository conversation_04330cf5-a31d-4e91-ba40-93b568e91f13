import time
import os
import json
from PIL import Image
from python.device_common import (
    check_is_homepage, check_and_handle_popup_after_screenshot,
    after_tap_test, device_operate_external, get_screenshot_ocr_elements
)
from python.upload_image import get_image_url
from python.notify_user import send_individual_kingkong_message
from python.heartbeat_monitor import log_device_issue
from python.config import Config
import traceback
from python.get_message_from_horus import find_back_button_location, find_back_button_location_especial
from python.device_status_manager import get_device_status, update_device_status, manager
def tap_main_icons(
    driver=None,
    udid=None,
    logger=None,
    execution_id=None,
    device_issues=None
):
    """
    固定TARGET_ICONS的点击测试流程
    
    Args:
        driver: WebDriver实例（iOS必需）
        udid: 设备UDID（必需）
        logger: 日志对象
        execution_id: 执行ID
        device_issues: 设备问题状态字典
        
    Returns:
        tuple: (completed_count, total_count) 完成数量和总数量
    """
    if udid is None:
        raise ValueError("udid 参数不能为空")
    device_name = get_device_status(udid).get('device_name')
    current_round = get_device_status(udid).get('round_num')
    session_round_num = get_device_status(udid).get('session_round_num')
    scale_ratio = get_device_status(udid).get('scale_ratio')
    device_screenshot_dir = get_device_status(udid).get('device_screenshot_dir')
    device_error_dir = get_device_status(udid).get('device_error_dir')

    if logger is None:
        import logging
        logger = logging.getLogger(__name__)
    if device_issues is None:
        raise ValueError("device_issues 参数不能为空")
    
    TARGET_ICONS = Config.TARGET_ICONS
    completed_count = 0
    total_count = Config.get_actual_icon_count()
    
    # 累加固定图标总数到设备状态
    success = manager.add_icon_count(udid, total_add=total_count, completed_add=0)
    if success:
        logger.info(f"固定图标测试累加图标总数: {total_count}，当前设备累计要测试图标数已更新")
    else:
        logger.warning(f"更新设备累计图标总数失败")
    

    # 首先检测是否到了首页
    from python.device_common import go_back_homepage
    homepage_success = go_back_homepage(
        driver=driver,
        udid=udid,
        logger=logger,
        execution_id=execution_id,
        device_issues=device_issues,
        test_context_name="固定图标测试"
    )
    
    if not homepage_success:
        logger.error("固定图标测试：无法确保在美团首页，终止测试")
        return 0, total_count
    
    # 首先检测首页是否有弹窗
    logger.info("开始检测首页是否有弹窗...")
    popup_handled, after_close_popup_path, after_close_popup_url = check_and_handle_popup_after_screenshot(
        driver=driver,
        udid=udid,
        page_name='美团首页',
        logger=logger
    )

    logger.info("开始获取首页截图并进行OCR识别...")
    
    try:
        page_elements = get_screenshot_ocr_elements(
            udid=udid,
            driver=driver,
            logger=logger,
            screenshot_path=after_close_popup_path
        )
        if not page_elements:
            logger.error("获取首页元素失败，无法继续测试")
            device_issues[udid]['test_issue'] = True
            return 0, total_count
        logger.info(f"成功获取首页元素，共 {len(page_elements)} 个")
        
    except Exception as e:
        logger.error(f"获取首页截图和OCR识别失败: {e}")
        device_issues[udid]['test_issue'] = True
        return 0, total_count
    
    # 从page_elements中提取目标图标信息并进行坐标转换
    icons_info = {}
    if page_elements:
        # 创建一个映射，将实际识别到的图标名称映射到配置中的标准名称
        recognized_elements = {element_name: coords for element_name, coords in page_elements}
        
        for target_icon in TARGET_ICONS:
            # 获取该图标在特殊图标组中的代表图标（组中第一个图标）
            representative_icon = target_icon
            for group in Config.SPECIAL_ICON_GROUPS:
                if target_icon in group:
                    representative_icon = group[0]  # 使用组中第一个图标作为代表
                    break
            
            # 如果代表图标已经被处理过，跳过
            if representative_icon in icons_info:
                if representative_icon != target_icon:
                    logger.info(f"图标「{target_icon}」跳过处理，因为代表图标「{representative_icon}」已被处理")
                continue
            
            matching_elements = []
            
            # 获取目标图标的所有可能名称（包括别名）
            possible_names = Config.get_icon_group(target_icon)
            
            # 收集所有名称等于 target_icon 的元素
            matching_elements = [
                (element_name, coords)
                for element_name, coords in page_elements
                if element_name == target_icon
            ]
            for possible_name in possible_names:
                # 兼容别名逻辑（如需保留，可加）
                if possible_name != target_icon:
                    alias_elements = [
                        (element_name, coords)
                        for element_name, coords in page_elements
                        if element_name == possible_name
                    ]
                    for alias_name, alias_coords in alias_elements:
                        matching_elements.append((alias_name, alias_coords))
                        logger.info(f"通过别名找到目标图标：「{target_icon}」-> 实际识别为「{alias_name}」")
            # 去重，防止同一个元素被重复加入
            matching_elements = list({(name, coords): (name, coords) for name, coords in matching_elements}.values())
            
            logger.info(f"图标「{target_icon}」匹配结果: 找到 {len(matching_elements)} 个匹配元素")
            
            # 如果找到匹配的元素，选择最合适的那个
            if matching_elements:
                if len(matching_elements) > 1:
                    # 如果有多个匹配元素，尝试选择离"看病买药"和"美食"最近的
                    logger.info(f"图标「{target_icon}」找到 {len(matching_elements)} 个匹配元素，尝试选择最佳位置")
                    
                    # 获取参考图标的坐标
                    reference_coords = []
                    for ref_icon in ["看病买药", "美食"]:
                        if ref_icon in recognized_elements:
                            reference_coords.append(recognized_elements[ref_icon])
                            logger.info(f"找到参考图标「{ref_icon}」，坐标: {recognized_elements[ref_icon]}")
                        else:
                            logger.info(f"未找到参考图标「{ref_icon}」")
                    
                    if reference_coords:
                        # 计算每个候选元素到参考点的平均距离
                        def calculate_distance_to_references(coords):
                            total_distance = 0
                            for ref_coord in reference_coords:
                                dx = coords[0] - ref_coord[0]
                                dy = coords[1] - ref_coord[1]
                                distance = (dx**2 + dy**2)**0.5
                                total_distance += distance
                            return total_distance / len(reference_coords)
                        
                        # 按距离排序，选择距离最小的
                        for i, element in enumerate(matching_elements):
                            distance = calculate_distance_to_references(element[1])
                            logger.info(f"候选元素 {i+1}: 名称「{element[0]}」，坐标 {element[1]}，到参考点平均距离: {distance:.2f}")
                        
                        matching_elements.sort(key=lambda x: calculate_distance_to_references(x[1]))
                        selected_element = matching_elements[0]
                        logger.info(f"基于距离参考图标的位置，选择了坐标为 {selected_element[1]} 的元素「{selected_element[0]}」")
                    else:
                        # 如果没有参考坐标，回退到原逻辑：按y坐标排序
                        matching_elements.sort(key=lambda x: x[1][1])  # x[1][1]是y坐标
                        selected_element = matching_elements[0]
                        logger.info(f"未找到参考图标，按y坐标选择了最靠上的元素")
                else:
                    # 只有一个匹配元素，直接选择
                    selected_element = matching_elements[0]
                
                original_x, original_y = selected_element[1]
                # 将坐标除以scale_ratio转换为实际屏幕坐标
                scaled_x = original_x / scale_ratio
                scaled_y = original_y / scale_ratio
                
                # 使用代表图标作为key，确保特殊图标组只有一个条目
                icons_info[representative_icon] = (scaled_x, scaled_y)
                
                # 记录实际识别到的名称，便于调试
                logger.debug(f"图标「{representative_icon}」坐标: ({scaled_x:.1f}, {scaled_y:.1f})，实际识别名称: 「{selected_element[0]}」")
        
        # 保存转换后的坐标
        current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        location_dir = os.path.join(current_dir, 'location')
        os.makedirs(location_dir, exist_ok=True)
        
        safe_name = device_name
        save_path = os.path.join(location_dir, f'meituan_icons_{safe_name}.json')
        with open(save_path, 'w', encoding='utf-8') as f:
            json.dump(icons_info, f, ensure_ascii=False, indent=2)
        logger.info(f"转换后的图标信息已保存到: {save_path}")
    
    logger.info(f"最终获取到的图标信息: {icons_info}")
    
    # 检查是否有缺失的固定图标（考虑特殊图标组）
    missing_icons = []
    for target_icon in TARGET_ICONS:
        is_found, matched_icon = Config.is_icon_found_in_group(target_icon, icons_info.keys())
        if not is_found:
            missing_icons.append(target_icon)
        elif matched_icon != target_icon:
            logger.info(f"目标图标「{target_icon}」未直接找到，但在同组中找到了「{matched_icon}」")
    
    if missing_icons:
        logger.error(f"固定图标测试：在首页未找到以下目标图标: {missing_icons}")
        
        # 发送通知
        failure_time = time.strftime('%Y-%m-%d %H:%M:%S')
        failure_message = (
            f"时间：{failure_time}\n"
            f"设备：{device_name}\n"
            f"轮次：第 {session_round_num} 轮（历史轮次: {current_round}）\n"
            f"问题：固定图标测试缺失图标\n"
            f"详情：在首页未找到以下目标图标: {missing_icons}\n"
            f"已找到的图标: {list(icons_info.keys())}\n"
            f"图片URL：{after_close_popup_url}"
        )
        send_individual_kingkong_message(failure_message, ['cuijie12'])
        
        # 标记本轮测试有应用问题
        device_issues[udid]['app_issue'] = True
        
        # 记录问题到日志系统
        log_device_issue(
            device_id=udid,
            device_name=device_name,
            round_num=current_round,
            issue_type='app',
            issue_details=f"固定图标测试：在首页未找到目标图标 {missing_icons}，已找到图标: {list(icons_info.keys())}",
            page_name="美团首页_固定图标识别",
            screenshot_url=after_close_popup_url,
            execution_id=execution_id
        )
        
        logger.warning(f"固定图标测试：缺失 {len(missing_icons)} 个目标图标，但继续测试已找到的 {len(icons_info)} 个图标")
    else:
        logger.info(f"固定图标测试：成功找到所有 {len(TARGET_ICONS)} 个目标图标")
    
    # 图标点击循环
    for icon_name, coordinates in icons_info.items():
        try:
            logger.info(f"==================== 开始测试「{icon_name}」 ====================")
            update_device_status(udid, {
                'status': 'running',
                'last_update': time.time()
            })
            # 点击图标
            logger.info(f"正在点击「{icon_name}」图标...")
            tap_success = device_operate_external.tap(
                driver=driver,
                udid=udid,
                x=coordinates[0],
                y=coordinates[1],
                logger=logger
            )
            if not tap_success:
                logger.error(f"点击「{icon_name}」图标失败")
                continue
            time.sleep(10)  # 等待点击操作完成
            
            # 使用 after_tap_test 进行完整检测
            timestamp = time.strftime('%Y%m%d_%H%M%S')
            after_click_screenshot_path = os.path.join(
                device_screenshot_dir, 
                f'点击_{icon_name}_{device_name}_{timestamp}.png'
            )
            device_operate_external.take_screenshot(
                driver=driver,
                udid=udid,
                screenshot_path=after_click_screenshot_path,
                logger=logger,
            )
            after_click_screenshot_url = get_image_url(after_click_screenshot_path)
            
            logger.info(f"点击「{icon_name}」后截图已保存: {after_click_screenshot_path}")
            logger.info(f"点击「{icon_name}」后截图已上传，URL: {after_click_screenshot_url} 轮次: {current_round}")

            # 开始点击后混合测试，包含UI检测
            test_result = after_tap_test(
                driver=driver,
                udid=udid,
                screenshot_path=after_click_screenshot_path,
                screenshot_url=after_click_screenshot_url,
                logger=logger,
                current_round=current_round,
                session_round_num=session_round_num,
                execution_id=execution_id,
                page_name=icon_name,
                device_error_dir=device_error_dir,
                perform_ui_check=True,
                device_issues=device_issues
            )
            
            # 更新截图信息
            final_screenshot_path = test_result['screenshot_path']
            final_screenshot_url = test_result['screenshot_url']
            
            # 根据检测结果决定是否继续
            if not test_result['success']:
                if test_result['app_crashed']:
                    logger.error(f"「{icon_name}」测试因 app 闪退中断")
                elif test_result['jump_failed']:
                    logger.error(f"「{icon_name}」测试因跳转失败中断")
                else:
                    logger.error(f"「{icon_name}」测试失败: {test_result['error_reason']}")
                logger.info(f"「{icon_name}」测试异常结束")
                continue
            
            # 如果应用进入后台但已恢复，记录警告信息
            if test_result['app_background']:
                logger.warning(f"「{icon_name}」页面app异常退到后台，已重新拉回前台，继续当前图标测试流程")
            
            logger.info(f"点击「{icon_name}」后检测完成：已成功跳转到对应页面")

            # 在执行返回操作前添加短暂延迟，确保页面稳定
            time.sleep(5)
            
            # 添加页面文本检测逻辑
            logger.info(f"开始进行「{icon_name}」页面文本存在性检测...")
            
            # 等待页面稳定后进行文本检测
            time.sleep(5)
            
            # 获取用于文本检测的截图
            timestamp = time.strftime('%Y%m%d_%H%M%S')
            text_check_screenshot_path = os.path.join(
                device_screenshot_dir,
                f'文本检测_{icon_name}_{device_name}_{timestamp}.png'
            )
            
            # 截图
            text_check_screenshot_result = device_operate_external.take_screenshot(
                driver=driver,
                udid=udid,
                screenshot_path=text_check_screenshot_path,
                logger=logger
            )
            
            if text_check_screenshot_result == "success":
                logger.info(f"「{icon_name}」页面文本检测截图已保存: {text_check_screenshot_path}")
                
                # 上传截图获取URL
                try:
                    text_check_screenshot_url = get_image_url(text_check_screenshot_path)
                    logger.info(f"「{icon_name}」页面文本检测截图已上传，URL: {text_check_screenshot_url} 轮次: {current_round}")
                    
                    # 获取对应的检测文本
                    icon_index = TARGET_ICONS.index(icon_name)
                    expected_text = Config.TARGET_ICONS_FLAG[icon_index]
                    
                    logger.info(f"「{icon_name}」页面开始检测目标文本: '{expected_text}'")
                    
                    # 调用文本检测函数
                    from python.device_common import check_element_exists
                    text_exists = check_element_exists(
                        screenshot_url=text_check_screenshot_url,
                        target_text=expected_text,
                        device_name=device_name,
                        logger=logger,
                        match_mode='contains'  # 使用包含匹配模式
                    )
                    
                    if text_exists:
                        logger.info(f"「{icon_name}」页面文本检测通过：成功找到目标文本 '{expected_text}'")
                    else:
                        logger.error(f"「{icon_name}」页面文本检测失败：未找到目标文本 '{expected_text}'，可能未正确跳转到目标页面")
                        
                        # 移动检测失败的截图到error目录
                        error_path = device_operate_external.move_screenshot_to_error(text_check_screenshot_path, device_error_dir)
                        logger.info(f"文本检测失败截图已移动到: {error_path}")
                        
                        # 发送通知
                        failure_time = time.strftime('%Y-%m-%d %H:%M:%S')
                        failure_message = (
                            f"时间：{failure_time}\n"
                            f"设备：{device_name}\n"
                            f"轮次：第 {session_round_num} 轮（历史轮次: {current_round}）\n"
                            f"问题：「{icon_name}」页面文本检测失败\n"
                            f"详情：未找到目标文本 '{expected_text}'，可能未正确跳转到目标页面\n"
                            f"图片URL：{text_check_screenshot_url}"
                        )
                        send_individual_kingkong_message(failure_message, ['cuijie12'])
                        
                        # 标记本轮测试有应用问题
                        device_issues[udid]['app_issue'] = True
                        
                        # 记录问题到日志系统
                        log_device_issue(
                            device_id=udid,
                            device_name=device_name,
                            round_num=current_round,
                            issue_type='app',
                            issue_details=f"「{icon_name}」页面文本检测失败，未找到目标文本 '{expected_text}'",
                            page_name=icon_name,
                            screenshot_url=text_check_screenshot_url,
                            execution_id=execution_id
                        )
                        
                        logger.warning(f"「{icon_name}」页面文本检测失败，但继续执行返回操作...")
                        
                except Exception as e:
                    logger.error(f"「{icon_name}」页面文本检测过程中发生错误: {e}")
                    logger.error(f"错误详情: {traceback.format_exc()}")
                    # 文本检测失败不影响整体流程，继续执行
                    
            else:
                logger.error(f"「{icon_name}」页面文本检测截图失败: {text_check_screenshot_result}")
                # 截图失败不影响整体流程，继续执行
            
            logger.info(f"「{icon_name}」页面文本检测流程完成")
            
            # 返回操作
            logger.info(f"开始从「{icon_name}」页面执行返回操作...")
            retry_count = 0
            max_retries = 3
            
            while retry_count < max_retries:
                # 获取图片尺寸
                try:
                    screenshot_image = Image.open(final_screenshot_path)
                    image_width, image_height = screenshot_image.size
                except Exception:
                    image_width = image_height = None

                # 查找返回按钮
                back_button_location = None
                
                # 首先尝试特殊业务返回按钮比例
                try:
                    especial_ratio = find_back_button_location_especial(icon_name)
                    if especial_ratio and image_width and image_height:
                        bx = especial_ratio[0] * image_width
                        by = especial_ratio[1] * image_height
                        back_button_location = (bx, by)
                        logger.info(f"使用特殊业务返回按钮比例，坐标: ({bx}, {by})")
                except Exception as e:
                    logger.warning(f"查找特殊业务返回按钮比例异常: {e}")
                
                # 如果没有特殊返回按钮，使用通用方法
                if not back_button_location:
                    try:
                        back_button_location = find_back_button_location(
                            final_screenshot_url,
                            device_name,
                            image_height,
                            device_logger=logger,
                            image_width=image_width
                        )
                    except Exception as e:
                        logger.warning(f"查找返回按钮异常: {e}")
                
                if back_button_location:
                    x, y = back_button_location
                    scaled_x = x / get_device_status(udid)["scale_ratio"]
                    scaled_y = y / get_device_status(udid)["scale_ratio"]
                    logger.info(f"找到返回按钮，正在点击返回...")
                    tap_success = device_operate_external.tap(
                        driver=driver,
                        udid=udid,
                        x=scaled_x,
                        y=scaled_y,
                        logger=logger
                    )
                    if not tap_success:
                        logger.error("返回按钮点击失败")
                    
                    time.sleep(5)
                else:
                    # 兜底方案
                    if get_device_status(udid)["platform"] == 'ios':
                        logger.warning("未找到返回按钮，尝试点击左上角兜底区域 (5.6%, 7.9%) ...")
                        if driver:
                            screen_size = driver.get_window_size()
                            fallback_x = screen_size['width'] * 0.05
                            fallback_y = screen_size['height'] * 0.08
                        else:
                            fallback_x = fallback_y = 10
                        tap_success = device_operate_external.tap(
                            driver=driver,
                            udid=udid,
                            x=fallback_x,
                            y=fallback_y,
                            logger=logger
                        )
                        if not tap_success:
                            logger.error("兜底区域点击失败")
                        logger.info(f"已点击兜底返回区域: x={fallback_x}, y={fallback_y}")
                        time.sleep(5)
                    else:  # Android
                        logger.info("未找到返回按钮，尝试使用系统返回键...")
                        device_operate_external.execute_adb_command(udid, "shell input keyevent 4")
                        time.sleep(10)
                
                # 截图检查是否返回到首页
                timestamp = time.strftime('%Y%m%d_%H%M%S')
                after_return_screenshot_path = os.path.join(
                    device_screenshot_dir,
                    f'返回后_{icon_name}_第{retry_count + 1}次_{device_name}_{timestamp}.png'
                )
                logger.info(f"开始获取「{icon_name}」页面第{retry_count + 1}次返回操作后的状态截图...")
                
                screenshot_success = device_operate_external.take_screenshot(
                    driver=driver,
                    udid=udid,
                    screenshot_path=after_return_screenshot_path,
                    logger=logger
                )
                if not screenshot_success:
                    logger.error(f"「{icon_name}」页面第{retry_count + 1}次返回操作后截图失败")
                    continue
                
                logger.info(f"「{icon_name}」页面第{retry_count + 1}次返回操作后的状态截图已保存: {after_return_screenshot_path}")
                after_return_screenshot_url = get_image_url(after_return_screenshot_path)
                logger.info(f"「{icon_name}」页面第{retry_count + 1}次返回操作后的状态截图已上传，URL: {after_return_screenshot_url} 轮次: {current_round}")
                
                # 检查并处理弹窗
                popup_handled, after_return_screenshot_path, after_return_screenshot_url = check_and_handle_popup_after_screenshot(
                    driver=driver,
                    udid=udid,
                    logger=logger,
                    page_name=f"{icon_name}_返回后"
                )
                if popup_handled:
                    logger.info(f"已处理「{icon_name}」页面返回后的弹窗")
                
                # 检查是否返回到首页
                if get_device_status(udid)["platform"] == 'ios':
                    is_home = check_is_homepage(driver=driver, udid=udid, screenshot_path=after_return_screenshot_path, screenshot_url=after_return_screenshot_url, logger=logger)
                else:
                    is_home = check_is_homepage(udid=udid, screenshot_path=after_return_screenshot_path, screenshot_url=after_return_screenshot_url, logger=logger)
                    
                if is_home:
                    logger.info(f"从「{icon_name}」页面返回后首页检查结果：已成功返回首页")
                    break
                else:
                    logger.warning(f"从「{icon_name}」页面返回后首页检查结果：未能返回首页")
                    # 移动返回失败的截图到error目录
                    error_path = device_operate_external.move_screenshot_to_error(after_return_screenshot_path, device_error_dir)
                    logger.info(f"返回失败截图已移动到: {error_path}")
                
                retry_count += 1
                if retry_count < max_retries:
                    logger.info(f"从「{icon_name}」页面返回失败，正在进行第 {retry_count + 1} 次尝试...")
            
            if retry_count >= max_retries:
                logger.error(f"从「{icon_name}」页面返回首页失败，已达到最大重试次数")
                # 使用点击图标后的截图 URL 作为失败时的报告 URL
                fallback_screenshot_url = final_screenshot_url
                logger.info(f"3次尝试后返回首页失败截图已上传，URL: {fallback_screenshot_url} 轮次: {current_round}")
                logger.info("重启应用...")
                device_operate_external.restart_app(
                    driver=driver,
                    udid=udid,
                    logger=logger
                )
                time.sleep(5)
                failure_time = time.strftime('%Y-%m-%d %H:%M:%S')
                failure_message = f"时间：{failure_time}\n设备：{device_name}\n轮次：第 {session_round_num} 轮（历史轮次: {current_round}）\n问题：从「{icon_name}」页面返回首页失败\n图片URL：{fallback_screenshot_url}"
                send_individual_kingkong_message(failure_message, ['cuijie12'])
                # 标记本轮测试有应用问题
                device_issues[udid]['app_issue'] = True
                
                # 记录问题
                log_device_issue(
                    device_id=udid,
                    device_name=device_name,
                    round_num=current_round,
                    issue_type='app',
                    issue_details=f"从「{icon_name}」页面返回首页失败，已达到最大重试次数",
                    page_name=icon_name,
                    screenshot_url=fallback_screenshot_url,
                    execution_id=execution_id
                )
            else:
                # 成功完成这个图标的测试
                completed_count += 1
                # 累加完成的图标数到设备状态
                success = manager.add_icon_count(udid, total_add=0, completed_add=1)
                if success:
                    logger.info(f"图标「{icon_name}」测试成功，累计完成图标数已更新")
                else:
                    logger.warning(f"更新设备累计完成图标数失败")
            
            logger.info(f"「{icon_name}」测试完成")
            logger.info("=" * 60)
            
            time.sleep(5)
            
        except Exception as e:
            logger.error(f"「{icon_name}」测试过程中发生错误: {e}")
            logger.info(f"「{icon_name}」测试异常结束")
            logger.info("=" * 60)
            # 标记本轮测试有测试流程问题
            device_issues[udid]['test_issue'] = True
            
            # 获取详细的错误信息和堆栈跟踪
            error_traceback = traceback.format_exc()
            logger.error(f"错误详情: {error_traceback}")
            
            # 提取更有用的错误信息
            error_type = type(e).__name__
            error_message = str(e)
            
            # 只保留到 stacktrace 之前的错误信息
            if "Stacktrace:" in error_message:
                error_message = error_message.split("Stacktrace:")[0].strip()
            
            # 根据错误类型提供更具体的错误描述
            error_description = f"{error_type}: {error_message}"
            if "timeout" in error_message.lower():
                error_description = f"操作超时: {error_message}"
            elif "no such element" in error_message.lower():
                error_description = f"未找到元素: {error_message}"
            elif "stale element reference" in error_message.lower():
                error_description = f"元素已过期: {error_message}"
            
            # 记录问题
            log_device_issue(
                device_id=udid,
                device_name=device_name,
                round_num=current_round,
                issue_type='test',
                issue_details=f"「{icon_name}」测试过程中发生错误: {error_description}",
                page_name=icon_name,
                execution_id=execution_id
            )
            
            # 发生测试问题后直接结束本轮测试
            logger.info("由于发生测试问题，提前结束本轮测试")
            break
    
    logger.info(f"固定图标测试完成，共测试 {total_count} 个图标，成功完成 {completed_count} 个")
    return completed_count, total_count 