#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import os
import shutil
import tempfile
import smtplib
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.mime.application import MIMEApplication
from email.header import Header
import zipfile


def get_size(path):
    """
    获取文件或文件夹的大小（字节）
    :param path: 文件或文件夹路径
    :return: 大小（字节）
    """
    if os.path.isfile(path):
        return os.path.getsize(path)
    
    total_size = 0
    for dirpath, dirnames, filenames in os.walk(path):
        for filename in filenames:
            file_path = os.path.join(dirpath, filename)
            if os.path.exists(file_path):  # 避免链接断开等情况
                total_size += os.path.getsize(file_path)
    
    return total_size


def format_size(size_bytes):
    """
    将字节大小转换为人类可读格式
    :param size_bytes: 字节大小
    :return: 格式化后的大小字符串
    """
    for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
        if size_bytes < 1024.0 or unit == 'TB':
            return f"{size_bytes:.2f} {unit}"
        size_bytes /= 1024.0


def zip_directory(directory_path):
    """
    将文件夹压缩为zip文件
    :param directory_path: 文件夹路径
    :return: 生成的zip文件路径
    """
    # 创建临时zip文件
    base_name = os.path.basename(directory_path)
    temp_dir = tempfile.gettempdir()
    zip_file_path = os.path.join(temp_dir, f"{base_name}.zip")
    
    # 如果已存在同名zip文件，先删除
    if os.path.exists(zip_file_path):
        os.remove(zip_file_path)
    
    # 压缩文件夹
    try:
        shutil.make_archive(os.path.join(temp_dir, base_name), 'zip', directory_path)
        print(f"文件夹 {directory_path} 已压缩为 {zip_file_path}")
        return zip_file_path
    except Exception as e:
        print(f"压缩文件夹失败: {e}")
        return None


def zip_directory_with_filter(directory_path, keyword=None):
    """
    将文件夹中包含指定关键词的文件压缩为zip文件
    :param directory_path: 文件夹路径
    :param keyword: 关键词，如果为None则压缩所有文件
    :return: 生成的zip文件路径
    """
    # 创建临时zip文件
    base_name = os.path.basename(directory_path)
    temp_dir = tempfile.gettempdir()
    
    if keyword:
        zip_file_path = os.path.join(temp_dir, f"{base_name}_filtered_{keyword}.zip")
    else:
        zip_file_path = os.path.join(temp_dir, f"{base_name}.zip")
    
    # 如果已存在同名zip文件，先删除
    if os.path.exists(zip_file_path):
        os.remove(zip_file_path)
    
    try:
        with zipfile.ZipFile(zip_file_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            filtered_count = 0
            total_count = 0
            
            for root, dirs, files in os.walk(directory_path):
                for file in files:
                    total_count += 1
                    file_path = os.path.join(root, file)
                    
                    # 如果指定了关键词，检查文件名是否包含该关键词
                    if keyword is None or keyword.lower() in file.lower():
                        # 计算相对路径，保持文件夹结构
                        relative_path = os.path.relpath(file_path, directory_path)
                        zipf.write(file_path, relative_path)
                        filtered_count += 1
            
            if keyword:
                print(f"文件夹 {directory_path} 中包含关键词 '{keyword}' 的 {filtered_count}/{total_count} 个文件已压缩为 {zip_file_path}")
            else:
                print(f"文件夹 {directory_path} 中的所有 {filtered_count} 个文件已压缩为 {zip_file_path}")
                
            return zip_file_path
            
    except Exception as e:
        print(f"压缩文件夹失败: {e}")
        return None


def get_filtered_files(directory_path, keyword):
    """
    获取文件夹中包含指定关键词的文件列表
    :param directory_path: 文件夹路径
    :param keyword: 关键词
    :return: 匹配的文件列表
    """
    matched_files = []
    
    if not os.path.exists(directory_path):
        print(f"错误：文件夹 '{directory_path}' 不存在")
        return matched_files
    
    for root, dirs, files in os.walk(directory_path):
        for file in files:
            if keyword.lower() in file.lower():
                file_path = os.path.join(root, file)
                matched_files.append(file_path)
    
    return matched_files


def send_email_with_attachment(subject, body_text, file_path, to_email):
    """
    发送带附件的邮件
    :param subject: 邮件主题
    :param body_text: 邮件正文内容
    :param file_path: 附件文件路径或文件夹路径
    :param to_email: 收件人邮箱
    """
    # 配置信息
    smtp_server = 'smtp.qq.com'
    smtp_port = 465  # 使用 SSL
    username = '<EMAIL>'
    password = 'vmpyyrajsljfbecf'
    
    # 检查路径是否存在
    if not os.path.exists(file_path):
        print(f"错误：文件或文件夹 '{file_path}' 不存在")
        return False
    
    # 检查文件大小
    file_size = get_size(file_path)
    formatted_size = format_size(file_size)
    print(f"附件大小: {formatted_size}")
    
    # 如果大小超过1GB，提示并退出
    GB_SIZE = 1024 * 1024 * 1024  # 1GB in bytes
    if file_size > GB_SIZE:
        print(f"警告: 文件/文件夹过大 ({formatted_size})，超过1GB限制，操作取消")
        return False
    
    # 如果是文件夹，先压缩为zip
    is_temp_file = False
    if os.path.isdir(file_path):
        print(f"检测到文件夹，正在压缩 {file_path}...")
        compressed_file = zip_directory(file_path)
        if compressed_file:
            file_path = compressed_file
            is_temp_file = True
        else:
            print("压缩文件夹失败，无法发送邮件")
            return False

    # 创建邮件对象
    msg = MIMEMultipart()
    msg['Subject'] = Header(subject, 'utf-8')
    msg['From'] = username
    msg['To'] = to_email

    # 添加邮件正文
    body = MIMEText(body_text, 'plain', 'utf-8')
    msg.attach(body)

    # 添加附件
    file_name = os.path.basename(file_path)
    with open(file_path, 'rb') as f:
        attachment = MIMEApplication(f.read())
        attachment.add_header('Content-Disposition', 'attachment', filename=file_name)
        msg.attach(attachment)

    try:
        server = smtplib.SMTP_SSL(smtp_server, smtp_port)
        server.login(username, password)
        server.sendmail(username, [to_email], msg.as_string())
        print(f"邮件已成功发送到 {to_email}")
        result = True
    except Exception as e:
        print(f"邮件发送失败：{e}")
        result = False
    finally:
        server.quit()
        # 如果使用了临时文件，删除它
        if is_temp_file and os.path.exists(file_path):
            try:
                os.remove(file_path)
                print(f"临时文件 {file_path} 已删除")
            except Exception as e:
                print(f"删除临时文件失败: {e}")
        return result


def send_email_with_filtered_attachment(subject, body_text, directory_path, keyword, to_email):
    """
    发送带筛选附件的邮件
    :param subject: 邮件主题
    :param body_text: 邮件正文内容
    :param directory_path: 文件夹路径
    :param keyword: 筛选关键词
    :param to_email: 收件人邮箱
    """
    # 配置信息
    smtp_server = 'smtp.qq.com'
    smtp_port = 465  # 使用 SSL
    username = '<EMAIL>'
    password = 'vmpyyrajsljfbecf'
    
    # 检查路径是否存在
    if not os.path.exists(directory_path):
        print(f"错误：文件夹 '{directory_path}' 不存在")
        return False
    
    # 先获取匹配的文件列表，检查是否有匹配的文件
    matched_files = get_filtered_files(directory_path, keyword)
    if not matched_files:
        print(f"警告：在文件夹 '{directory_path}' 中未找到包含关键词 '{keyword}' 的文件")
        return False
    
    print(f"找到 {len(matched_files)} 个包含关键词 '{keyword}' 的文件:")
    for file_path in matched_files:
        file_size = format_size(os.path.getsize(file_path))
        print(f"  - {os.path.relpath(file_path, directory_path)} ({file_size})")
    
    # 压缩筛选后的文件
    print(f"正在压缩包含关键词 '{keyword}' 的文件...")
    compressed_file = zip_directory_with_filter(directory_path, keyword)
    if not compressed_file:
        print("压缩文件失败，无法发送邮件")
        return False
    
    # 检查压缩文件大小
    file_size = get_size(compressed_file)
    formatted_size = format_size(file_size)
    print(f"压缩文件大小: {formatted_size}")
    
    # 如果大小超过1GB，提示并退出
    GB_SIZE = 1024 * 1024 * 1024  # 1GB in bytes
    if file_size > GB_SIZE:
        print(f"警告: 压缩文件过大 ({formatted_size})，超过1GB限制，操作取消")
        # 删除临时文件
        if os.path.exists(compressed_file):
            os.remove(compressed_file)
        return False

    # 创建邮件对象
    msg = MIMEMultipart()
    msg['Subject'] = Header(subject, 'utf-8')
    msg['From'] = username
    msg['To'] = to_email

    # 添加邮件正文，包含筛选信息
    detailed_body = f"{body_text}\n\n附件信息：\n"
    detailed_body += f"- 源文件夹：{directory_path}\n"
    detailed_body += f"- 筛选关键词：{keyword}\n"
    detailed_body += f"- 匹配文件数量：{len(matched_files)}\n"
    detailed_body += f"- 压缩文件大小：{formatted_size}\n"
    
    body = MIMEText(detailed_body, 'plain', 'utf-8')
    msg.attach(body)

    # 添加附件
    file_name = os.path.basename(compressed_file)
    with open(compressed_file, 'rb') as f:
        attachment = MIMEApplication(f.read())
        attachment.add_header('Content-Disposition', 'attachment', filename=file_name)
        msg.attach(attachment)

    try:
        server = smtplib.SMTP_SSL(smtp_server, smtp_port)
        server.login(username, password)
        server.sendmail(username, [to_email], msg.as_string())
        print(f"邮件已成功发送到 {to_email}")
        result = True
    except Exception as e:
        print(f"邮件发送失败：{e}")
        result = False
    finally:
        server.quit()
        # 删除临时压缩文件
        if os.path.exists(compressed_file):
            try:
                os.remove(compressed_file)
                print(f"临时文件 {compressed_file} 已删除")
            except Exception as e:
                print(f"删除临时文件失败: {e}")
        return result


def main():
    """
    主函数，处理命令行参数并发送邮件
    
    使用方法:
    1. 发送单个文件:
       python send_file_to_email.py "邮件内容" "/path/to/file.txt" [收件人邮箱]
    
    2. 发送整个文件夹(压缩后发送):
       python send_file_to_email.py "邮件内容" "/path/to/folder" [收件人邮箱]
    
    3. 发送文件夹中包含特定关键词的文件(新功能):
       python send_file_to_email.py "邮件内容" "/path/to/folder" "关键词" [收件人邮箱]
       
    示例:
    - python send_file_to_email.py "测试邮件" "/Users/<USER>/Desktop/work/platform_autotest_frame_python/log/python_logs" "global"
    - python send_file_to_email.py "日志文件" "/path/to/logs" "error" "<EMAIL>"
    
    参数说明:
    - 邮件内容: 邮件正文内容
    - 文件路径: 可以是文件路径或文件夹路径
    - 关键词: (可选) 当指定文件夹时，只包含文件名包含该关键词的文件
    - 收件人邮箱: (可选) 默认为 <EMAIL>
    """
    # 检查参数数量，支持3-5个参数
    if len(sys.argv) < 3:
        print("用法: python send_file_to_email.py <邮件正文内容> <附件文件路径或文件夹路径> [<关键词>] [<收件人邮箱>]")
        print("\n详细使用说明请查看main函数中的注释")
        sys.exit(1)

    body_text = sys.argv[1]
    file_path = sys.argv[2]
    
    # 判断是否提供了关键词参数
    if len(sys.argv) >= 4 and os.path.isdir(file_path):
        # 如果第3个参数存在且第2个参数是文件夹，则第3个参数可能是关键词
        third_param = sys.argv[3]
        
        # 检查第3个参数是否像邮箱地址（包含@符号）
        if '@' in third_param:
            # 第3个参数是邮箱，没有关键词
            keyword = None
            to_email = third_param
        else:
            # 第3个参数是关键词
            keyword = third_param
            to_email = sys.argv[4] if len(sys.argv) > 4 else '<EMAIL>'
    else:
        # 没有关键词参数
        keyword = None
        to_email = sys.argv[3] if len(sys.argv) > 3 else '<EMAIL>'
    
    # 设置邮件主题
    if keyword:
        subject = f"筛选附件邮件 - 关键词: {keyword}"
    else:
        subject = "带附件的通知邮件"
    
    # 根据是否有关键词选择不同的发送方式
    if keyword and os.path.isdir(file_path):
        # 使用新的筛选功能
        success = send_email_with_filtered_attachment(subject, body_text, file_path, keyword, to_email)
    else:
        # 使用原有功能
        success = send_email_with_attachment(subject, body_text, file_path, to_email)
    
    # 根据邮件发送结果设置退出码
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()