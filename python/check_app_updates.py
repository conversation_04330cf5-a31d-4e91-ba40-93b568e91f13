import time
import logging
import subprocess
import os
import json
import requests
import sys
import traceback
from appium import webdriver
from appium.options.ios import XCUITestOptions
from PIL import Image
import threading

# 添加当前目录的父目录到 sys.path
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
if parent_dir not in sys.path:
    sys.path.append(parent_dir)

# 导入自定义模块
try:
    from upload_image import get_image_url
    from get_message_from_horus import get_message_from_horus
except ImportError:
    # 如果直接导入失败，尝试从 python 包导入
    try:
        from python.upload_image import get_image_url
        from python.get_message_from_horus import get_message_from_horus
    except ImportError:
        logging.error("无法导入必要的模块，请检查路径设置")

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 需要手动确认安装的设备白名单（vivo等需要手动确认的设备）
MANUAL_CONFIRM_WHITELIST = [
    "10AD5F1KJ4002B6",  # vivo设备示例
    # 添加其他需要手动确认的设备UDID
]

def check_ios_device_online(udid):
    """
    检查iOS设备是否连接
    
    :param udid: 设备 UDID
    :return: 布尔值，表示设备是否在线
    """
    try:
        # 使用 xcrun 命令列出已连接的设备
        result = subprocess.run(['xcrun', 'xctrace', 'list', 'devices'], 
                               capture_output=True, text=True, check=True)
        
        # 检查输出中是否包含指定的 UDID
        return udid in result.stdout
    except subprocess.SubprocessError as e:
        logger.error(f"检查设备连接状态时出错: {e}")
        return False

def check_ios_app_updates_by_safari(udid, device_name=None, ios_version=None, appium_port=4723, wda_port=8100, app_id="423084029", logger=None):
    """
    通过 Safari 打开 App Store 中指定 ID 的应用页面，检查应用状态，如果需要更新则点击更新按钮
    
    :param udid: 设备 UDID
    :param device_name: 设备名称，如果为 None 则使用 UDID 作为名称
    :param ios_version: iOS 版本，如果为 None 则尝试自动获取
    :param appium_port: Appium 服务器端口
    :param wda_port: WDA 本地端口
    :param app_id: App Store 应用 ID，默认为美团的 ID (423084029)
    :param logger: 日志记录器，如果为 None 则使用全局 logger
    :return: 应用状态字符串，可能的值: "最新版本", "需要更新", "已点击更新", "未知状态"
    """
    # 使用传入的logger或默认的全局logger
    log = logger or logging.getLogger(__name__)
    
    # 如果未提供设备名称，使用 UDID
    if device_name is None:
        device_name = udid
    
    # 如果未提供 iOS 版本，尝试获取（此处简化处理，实际可能需要更复杂的逻辑）
    if ios_version is None:
        # 尝试从设备列表中获取 iOS 版本
        try:
            result = subprocess.run(['xcrun', 'xctrace', 'list', 'devices'], 
                                  capture_output=True, text=True, check=True)
            for line in result.stdout.splitlines():
                if udid in line:
                    # 尝试提取版本号，格式通常是 "设备名 (版本号) (UDID)"
                    import re
                    match = re.search(r'\(([\d\.]+)\)', line)
                    if match:
                        ios_version = match.group(1)
                        log.info(f"自动检测到设备 iOS 版本: {ios_version}")
                        break
        except Exception as e:
            log.warning(f"自动检测 iOS 版本失败: {e}")
        
        # 如果仍然没有版本号，使用默认值
        if not ios_version:
            ios_version = "15.0"  # 更新为更可能的默认版本
            log.info(f"使用默认 iOS 版本: {ios_version}")
    
    log.info(f"开始测试设备：{device_name} (UDID: {udid}, iOS {ios_version})")

    # 在测试开始前检查设备是否在线
    if not check_ios_device_online(udid):
        log.error(f"设备 {device_name} (UDID: {udid}) 未连接")
        return "未知状态"

    # 创建截图目录
    screenshot_dir = os.path.join(parent_dir, 'screenshot', device_name)
    os.makedirs(screenshot_dir, exist_ok=True)

    # 构建 App Store 应用页面的 URL
    app_store_url = f"https://apps.apple.com/cn/app/id{app_id}"
    log.info(f"将通过 Safari 打开 App Store 页面: {app_store_url}")

    # 配置 XCUITestOptions - 适配 Appium 2.0
    options = XCUITestOptions()
    options.platform_name = 'iOS'  # 明确指定平台名称
    options.device_name = device_name
    options.platform_version = ios_version
    options.udid = udid
    options.automation_name = 'XCUITest'
    options.bundle_id = 'com.apple.mobilesafari'  # 直接使用 Safari 的 bundle ID
    options.no_reset = True
    # 对于新的go-ios工具链，不再使用wda_local_port，而是通过webDriverAgentUrl指定
    # WDA服务已经通过ios forward在指定端口上运行
    options.web_driver_agent_url = f"http://127.0.0.1:{wda_port}"

    # 使用预构建的WDA，跳过构建过程
    options.use_prebuilt_wda = True
    
    # 防止Appium尝试启动WDA，因为我们已经通过ios工具启动了WDA
    options.use_new_wda = False
    options.wda_startup_retries = 0
    options.wda_startup_retry_interval = 0
    
    # 设置 Safari 的初始 URL - 修正方式
    options.set_capability('safari:openLinks', True)
    options.set_capability('safari:initialUrl', app_store_url)
    
    # Appium 2.0 需要明确指定驱动程序路径
    appium_url_with_driver = f"http://localhost:{appium_port}/wd/hub"
    log.info(f"连接 Appium 服务器：{appium_url_with_driver}")

    driver = None
    app_status = "未知状态"
    
    try:
        # 初始化 driver
        driver = webdriver.Remote(appium_url_with_driver, options=options)
        log.info("成功连接到 Appium 服务器并启动 Safari")
        
        # 确保 Safari 已加载
        time.sleep(5)
        log.info("等待 Safari 完全加载")
        
        # 获取iOS版本号，转换为浮点数进行比较
        ios_version_float = float('.'.join(ios_version.split('.')[:2]))
        log.info(f"iOS版本: {ios_version}, 转换为浮点数: {ios_version_float}")
        
        # 使用安全导航方法，传入 iOS 版本号
        log.info(f"使用安全导航方法导航到 App Store 页面")
        navigation_success = navigate_to_url_safely(driver, app_store_url, log, ios_version_float)
        if not navigation_success:
            log.error("导航到 App Store 页面失败")
        
        # 等待页面加载
        time.sleep(15)
        log.info("等待页面加载完成")
        
        # 截图
        timestamp = time.strftime('%Y%m%d_%H%M%S')
        screenshot_path = os.path.join(screenshot_dir, f'AppStore_美团_{device_name}_{timestamp}.png')
        log.info(f"正在截图...")
        driver.get_screenshot_as_file(screenshot_path)
        log.info(f"截图已保存到: {screenshot_path}")
        
        # 显示截图尺寸
        screenshot_image = Image.open(screenshot_path)
        log.info(f"截图尺寸: {screenshot_image.size}")
        
        # 上传图片获取URL
        try:
            log.info(f"开始上传截图到图片服务器...")
            image_url = get_image_url(screenshot_path)
            if image_url:
                log.info(f"截图上传成功，URL: {image_url}")
                
                # 获取OCR识别结果
                try:
                    log.info(f"开始获取OCR识别结果...")
                    ocr_results = get_message_from_horus(image_url, device_name, log)
                    log.info(f"成功获取OCR识别结果，共 {len(ocr_results)} 项")
                    
                    # 判断应用状态：检查OCR结果中是否包含"打开"/"open"或"更新"/"update"
                    has_open = False
                    has_update = False
                    update_coordinates = None
                    min_update_y = float('inf')  # 初始化为无穷大，用于找到最小的纵坐标
                    
                    for text, coordinates in ocr_results:
                        text_lower = text.lower()
                        if "打开" in text or "open" in text_lower:
                            has_open = True
                            log.info(f"OCR结果中发现'打开'或'open'字样: {text}，坐标: {coordinates}")
                        # 修改检测逻辑，只有完全匹配"更新"或"update"的文本才被识别为更新按钮
                        if text == "更新" or text_lower == "update":
                            has_update = True
                            # 提取坐标中的纵坐标（y值）
                            _, y = coordinates
                            log.info(f"OCR结果中发现完全匹配的'更新'或'update'文本: {text}，坐标: {coordinates}，纵坐标: {y}")
                            
                            # 如果当前找到的"更新"按钮纵坐标比之前找到的更小（更靠上），则更新坐标
                            if y < min_update_y:
                                min_update_y = y
                                update_coordinates = coordinates
                                log.info(f"更新为当前最靠上的'更新'按钮，坐标: {coordinates}")
                    
                    # 优化后的判断逻辑
                    if has_update:
                        app_status = "需要更新"
                        log.info("根据OCR结果判断：应用需要更新")
                        
                        # 如果需要更新且找到了更新按钮的坐标，点击更新按钮
                        if update_coordinates:
                            try:
                                # 提取坐标
                                x, y = update_coordinates
                                log.info(f"OCR识别的'更新'按钮原始坐标: ({x}, {y})")
                                
                                # 获取实际屏幕尺寸
                                screen_size = driver.get_window_size()
                                log.info(f"实际屏幕尺寸: {screen_size}")
                                
                                # 获取截图尺寸
                                screenshot_image = Image.open(screenshot_path)
                                screenshot_size = screenshot_image.size
                                log.info(f"截图尺寸: {screenshot_size}")
                                
                                # 计算缩放比例
                                width_ratio = screenshot_size[0] / screen_size['width']
                                height_ratio = screenshot_size[1] / screen_size['height']
                                scale_ratio = (width_ratio + height_ratio) / 2
                                log.info(f"计算得到的缩放比例: {scale_ratio}")
                                
                                # 将OCR坐标转换为实际屏幕坐标
                                scaled_x = x / scale_ratio
                                scaled_y = y / scale_ratio
                                log.info(f"转换后的'更新'按钮实际坐标: ({scaled_x}, {scaled_y})")
                                
                                # 使用 Appium 的 execute_script 方法点击坐标
                                log.info(f"正在点击'更新'按钮，实际坐标: ({scaled_x}, {scaled_y})...")
                                driver.execute_script('mobile: tap', {'x': scaled_x, 'y': scaled_y})
                                log.info("已点击'更新'按钮")
                                
                                # 等待更新操作开始
                                time.sleep(5)
                                
                                # 再次截图确认状态
                                timestamp = time.strftime('%Y%m%d_%H%M%S')
                                after_click_screenshot_path = os.path.join(screenshot_dir, f'AppStore_更新后_{device_name}_{timestamp}.png')
                                driver.get_screenshot_as_file(after_click_screenshot_path)
                                log.info(f"点击'更新'按钮后的截图已保存: {after_click_screenshot_path}")
                                
                                app_status = "已点击更新"
                            except Exception as e:
                                log.error(f"点击'更新'按钮时出错: {e}")
                    elif has_open:
                        app_status = "最新版本"
                        log.info("根据OCR结果判断：应用已是最新版本")
                    else:
                        app_status = "异常状态"
                        log.warning("OCR结果中既没有'更新'也没有'打开'，判定为异常状态")
                    
                except Exception as e:
                    log.error(f"获取OCR识别结果时出错: {e}")
            else:
                log.error("截图上传失败，无法获取图片URL")
        except Exception as e:
            log.error(f"上传图片时出错: {e}")
            # 打印更详细的错误信息
            log.error(f"详细错误: {traceback.format_exc()}")
        
        return app_status
        
    except Exception as e:
        log.error(f"测试过程中发生错误: {e}")
        return "未知状态"
    finally:
        if driver:
            driver.quit()
            log.info("已关闭 Appium 会话")

def check_android_app_updates_by_sigma(udid, device_name=None, download_apk=False, install_apk=False, package_name="com.sankuai.meituan", logger=None):
    """
    通过 Sigma 后端获取安卓安装包的更新信息，并可选择下载APK到本地
    会通过adb命令获取设备上已安装的应用版本，并与最新版本进行比较
    
    :param udid: 设备 UDID
    :param device_name: 设备名称，如果为 None 则使用 UDID 作为名称
    :param download_apk: 是否下载APK到本地，默认为False
    :param install_apk: 是否安装APK到设备，默认为False
    :param package_name: 应用包名，默认为美团的包名 com.sankuai.meituan
    :param logger: 日志记录器，如果为 None 则使用全局 logger
    :return: 字典，包含应用状态、版本号和构件号等信息
    """
    # 使用传入的logger或默认的全局logger
    log = logger or logging.getLogger(__name__)
    
    log.info(f"开始通过 Sigma 后端检查安卓设备 {device_name or udid} 的应用更新")
    
    try:
        # 构建请求 URL 和参数
        url = "http://qaassist.sankuai.com/compass/api/getApk/getLatestFullReleaseApkUrlFromSigma"
        params = {
            "os": "Android",
            "style": "打全量包"
        }
        
        log.info(f"请求 Sigma 后端 API: {url}")
        response = requests.get(url, params=params)
        
        # 检查响应状态
        if response.status_code != 200:
            log.error(f"请求 Sigma 后端失败，状态码: {response.status_code}")
            return {
                "status": "请求失败",
                "version": None,
                "build_number": None,
                "apk_url": None,
                "local_path": None,
                "is_updated": False,
                "installed_version": None,
                "needs_update": False,
                "is_installed": False
            }
        
        # 获取响应内容
        apk_url = response.text.strip()
        log.info(f"获取到 APK URL: {apk_url}")
        
        # 检查 URL 格式是否正确
        if not (apk_url.startswith("@https://") or apk_url.startswith("https://")) or not apk_url.endswith(".apk"):
            log.error(f"返回的 APK URL 格式不正确: {apk_url}")
            return {
                "status": "URL格式错误",
                "version": None,
                "build_number": None,
                "apk_url": apk_url,
                "local_path": None,
                "is_updated": False,
                "installed_version": None,
                "needs_update": False,
                "is_installed": False
            }
        
        # 去掉 URL 前面的 @ 符号
        apk_url = apk_url[1:] if apk_url.startswith("@") else apk_url
        
        # 从 URL 中提取版本号和构件号
        # 示例 URL: https://apptest.sankuai.com/download/aimeituan-release_12.30.200-366113-aarch64.apk
        try:
            # 提取文件名部分
            filename = apk_url.split("/")[-1]
            
            # 从文件名中提取版本号和构件号
            # 格式可能是: aimeituan-release_12.30.200-366113-aarch64.apk
            try:
                if '_' in filename and '-' in filename:
                    # 尝试解析格式: aimeituan-release_12.30.200-366113-aarch64.apk
                    parts = filename.split("_")[1].split("-")
                    version = parts[0]  # 12.30.200
                    build_number = parts[1]  # 366113
                else:
                    # 如果格式不匹配，尝试其他可能的格式
                    # 例如可能是: aimeituan-12.30.200-366113.apk
                    parts = filename.replace('.apk', '').split('-')
                    if len(parts) >= 3:
                        # 假设倒数第二个部分是版本号，最后一个部分是构建号
                        version = parts[-2]
                        build_number = parts[-1]
                    else:
                        raise ValueError(f"无法从文件名 {filename} 中提取版本信息")
            except Exception as e:
                log.error(f"解析文件名 {filename} 时出错: {e}")
                raise  # 重新抛出异常，让外层的异常处理捕获
                
            log.info(f"成功提取版本信息 - 版本号: {version}, 构件号: {build_number}")
            
            # 获取设备上已安装的应用版本
            installed_version = None
            needs_update = False
            
            try:
                # 使用adb命令获取已安装的应用版本
                adb_command = f"adb -s {udid} shell dumpsys package {package_name} | grep versionName"
                log.info(f"执行adb命令: {adb_command}")
                
                result = subprocess.run(adb_command, shell=True, capture_output=True, text=True)
                
                if result.returncode == 0 and result.stdout:
                    # 提取版本号，格式可能是 "versionName=12.30.200"
                    version_line = result.stdout.strip()
                    installed_version = version_line.split('=')[1].strip() if '=' in version_line else None
                    
                    if installed_version:
                        log.info(f"设备上已安装的应用版本: {installed_version}")
                        
                        # 比较版本号
                        needs_update = compare_versions(installed_version, version)
                        
                        if needs_update:
                            log.info(f"发现新版本 {version}，当前安装版本 {installed_version}，需要更新")
                        else:
                            log.info(f"当前安装版本 {installed_version} 已是最新，无需更新")
                    else:
                        log.warning("无法从adb输出中提取版本号")
                else:
                    log.warning(f"adb命令执行失败或未返回结果: {result.stderr}")
            except Exception as e:
                log.error(f"获取已安装应用版本时出错: {e}")
                log.error(f"详细错误: {traceback.format_exc()}")
            
            # 初始化本地路径为None
            local_path = None
            is_updated = False
            is_installed = False
            
            # 检查是否需要下载APK
            if download_apk:
                try:
                    # 创建保存目录
                    save_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "Android_apk")
                    if not os.path.exists(save_dir):
                        os.makedirs(save_dir)
                        log.info(f"创建目录: {save_dir}")
                    
                    # 构建保存路径
                    local_path = os.path.join(save_dir, filename)
                    
                    # 检查目录中是否已有其他APK文件
                    existing_apks = [f for f in os.listdir(save_dir) if f.endswith('.apk')]
                    
                    # 检查本地是否已有相同的APK文件，并且不需要重新下载
                    if os.path.exists(local_path) and (not needs_update and installed_version):
                        log.info(f"本地已存在相同的APK文件且已安装最新版本: {local_path}")
                        # 不需要重新下载
                        is_updated = False
                        
                        # 删除其他旧版本APK，只保留当前最新的APK
                        for old_apk in existing_apks:
                            if old_apk != filename:  # 不删除已有的最新APK
                                try:
                                    old_path = os.path.join(save_dir, old_apk)
                                    os.remove(old_path)
                                    log.info(f"已删除旧版本APK: {old_path}")
                                except Exception as e:
                                    log.error(f"删除旧版本APK失败: {e}")
                    elif not needs_update and installed_version:
                        log.info("当前已安装最新版本，无需下载APK")
                        
                        # 检查是否已有最新版本的APK文件
                        if not os.path.exists(local_path) and existing_apks:
                            log.info("清理目录中的旧版本APK文件...")
                            for old_apk in existing_apks:
                                try:
                                    old_path = os.path.join(save_dir, old_apk)
                                    os.remove(old_path)
                                    log.info(f"已删除旧版本APK: {old_path}")
                                except Exception as e:
                                    log.error(f"删除旧版本APK失败: {e}")
                        
                        is_updated = False
                    else:
                        # 删除所有旧版本APK，准备下载新版本
                        for old_apk in existing_apks:
                            try:
                                old_path = os.path.join(save_dir, old_apk)
                                os.remove(old_path)
                                log.info(f"已删除旧版本APK: {old_path}")
                            except Exception as e:
                                log.error(f"删除旧版本APK失败: {e}")
                        
                        # 下载APK
                        log.info(f"开始下载APK到: {local_path}")
                        download_response = requests.get(apk_url, stream=True)
                        
                        if download_response.status_code == 200:
                            with open(local_path, 'wb') as f:
                                for chunk in download_response.iter_content(chunk_size=8192):
                                    if chunk:
                                        f.write(chunk)
                            log.info(f"APK下载成功: {local_path}")
                            is_updated = True
                        else:
                            log.error(f"下载APK失败，状态码: {download_response.status_code}")
                            local_path = None
                
                    # 如果需要安装APK到设备
                    if install_apk and local_path and os.path.exists(local_path) and (needs_update or installed_version is None):
                        try:
                            # 检查设备是否在白名单中，需要手动确认安装
                            if is_device_in_whitelist(udid):
                                log.info(f"设备 {udid} 在白名单中，将使用手动确认安装流程")
                                install_result = install_apk_with_manual_confirmation(udid, local_path, package_name, device_name, version)
                                is_installed = install_result["is_installed"]
                                
                                # 检查是否提前完成安装
                                early_installation_completed = install_result.get("early_detection", False)
                                if early_installation_completed:
                                    log.info(f"安装已提前完成，无需等待额外时间")
                                
                                if not is_installed:
                                    log.error(f"手动确认安装失败: {install_result['error_message']}")
                            else:
                                # 使用常规adb命令安装APK
                                log.info(f"设备 {udid} 不在白名单中，使用常规安装流程")
                                install_command = f"adb -s {udid} install -r {local_path}"
                                log.info(f"执行安装命令: {install_command}")
                                
                                install_result = subprocess.run(install_command, shell=True, capture_output=True, text=True)
                                
                                if install_result.returncode == 0 and "Success" in install_result.stdout:
                                    log.info(f"APK安装成功")
                                    is_installed = True
                                    early_installation_completed = True
                                else:
                                    log.error(f"APK安装失败: {install_result.stderr or install_result.stdout}")
                                    early_installation_completed = False
                        except Exception as e:
                            log.error(f"安装APK时出错: {e}")
                            log.error(f"详细错误: {traceback.format_exc()}")
                            early_installation_completed = False
                    elif install_apk and not needs_update and installed_version:
                        log.info("当前已安装最新版本，无需重新安装")
                        early_installation_completed = True
                    else:
                        early_installation_completed = False
                
                except Exception as e:
                    log.error(f"下载或安装APK时出错: {e}")
                    log.error(f"详细错误: {traceback.format_exc()}")
                    local_path = None
            
            return {
                "status": "成功",
                "version": version,
                "build_number": build_number,
                "apk_url": apk_url,
                "local_path": local_path,
                "is_updated": is_updated,
                "installed_version": installed_version,
                "needs_update": needs_update,
                "is_installed": is_installed,
                "early_installation_completed": early_installation_completed if 'early_installation_completed' in locals() else False
            }
            
        except Exception as e:
            log.error(f"从 URL 提取版本信息时出错: {e}")
            log.error(f"详细错误: {traceback.format_exc()}")
            return {
                "status": "解析错误",
                "version": None,
                "build_number": None,
                "apk_url": apk_url,
                "local_path": None,
                "is_updated": False,
                "installed_version": None,
                "needs_update": False,
                "is_installed": False
            }
            
    except Exception as e:
        log.error(f"请求 Sigma 后端时发生错误: {e}")
        log.error(f"详细错误: {traceback.format_exc()}")
        return {
            "status": "未知错误",
            "version": None,
            "build_number": None,
            "apk_url": None,
            "local_path": None,
            "is_updated": False,
            "installed_version": None,
            "needs_update": False,
            "is_installed": False
        }

def install_apk_with_manual_confirmation(udid, apk_path, package_name, device_name=None, expected_version=None):
    """
    安装APK并处理手动确认过程
    
    :param udid: 设备UDID
    :param apk_path: APK文件路径
    :param package_name: 应用包名
    :param device_name: 设备名称，用于日志和截图
    :param expected_version: 预期安装的版本号
    :return: 字典，包含安装结果信息
    """
    if device_name is None:
        device_name = udid
    
    logger.info(f"开始为设备 {device_name} 安装APK并处理手动确认")
    
    try:
        # 创建截图目录 - 使用线程安全的方式
        screenshot_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'screenshot', device_name)
        try:
            os.makedirs(screenshot_dir, exist_ok=True)
        except Exception as e:
            logger.warning(f"创建截图目录时出错: {e}，将使用临时目录")
            import tempfile
            screenshot_dir = tempfile.mkdtemp(prefix=f"apk_install_{device_name}_")
        
        # 生成唯一的文件名，避免多进程冲突
        timestamp = time.strftime('%Y%m%d_%H%M%S')
        process_id = os.getpid()
        screenshot_filename = f'install_confirm_{device_name}_{timestamp}_{process_id}.png'
        screenshot_path = os.path.join(screenshot_dir, screenshot_filename)
        
        # 1. 执行安装命令
        install_command = f"adb -s {udid} install -r {apk_path}"
        logger.info(f"执行安装命令: {install_command}")
        
        # 使用Popen而不是run，这样可以不等待命令完成
        # 设置超时机制，避免进程卡死
        try:
            install_process = subprocess.Popen(install_command, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
            
            # 2. 等待1分钟，让安装界面出现
            logger.info(f"等待1分钟，让安装确认界面出现...")
            
            # 使用轮询方式检查进程状态，避免无限等待
            wait_start_time = time.time()
            max_wait_time = 60  # 1分钟
            
            while time.time() - wait_start_time < max_wait_time:
                # 检查进程是否已经结束
                if install_process.poll() is not None:
                    stdout, stderr = install_process.communicate()
                    if "Success" in stdout:
                        logger.info(f"APK安装已自动完成，无需手动确认: {stdout}")
                        return {
                            "is_installed": True,
                            "installed_version": expected_version,
                            "auto_completed": True
                        }
                    else:
                        logger.warning(f"APK安装命令提前结束，可能失败: {stderr or stdout}")
                        # 继续执行，尝试截图确认
                
                time.sleep(5)  # 每 5 秒检查一次
            
            # 如果进程仍在运行，继续后续步骤
            logger.info(f"安装命令仍在运行，继续执行手动确认步骤")
            
        except Exception as e:
            logger.error(f"启动安装进程时出错: {e}")
            return {
                "is_installed": False,
                "error_message": f"启动安装进程失败: {str(e)}"
            }
        
        # 3. 截图并上传
        # 执行截图命令
        screenshot_command = f"adb -s {udid} exec-out screencap -p > {screenshot_path}"
        logger.info(f"执行截图命令: {screenshot_command}")
        
        # 设置超时机制
        try:
            screenshot_result = subprocess.run(screenshot_command, shell=True, check=False, timeout=30)
        except subprocess.TimeoutExpired:
            logger.error(f"截图命令超时")
            return {
                "is_installed": False,
                "error_message": "截图命令超时，无法获取安装确认界面"
            }
        
        if screenshot_result.returncode != 0 or not os.path.exists(screenshot_path) or os.path.getsize(screenshot_path) == 0:
            logger.error(f"截图失败或截图为空")
            return {
                "is_installed": False,
                "error_message": "截图失败，无法获取安装确认界面"
            }
        
        # 4. 上传图片获取URL
        logger.info(f"上传截图到图片服务器...")
        image_url = None
        
        # 添加重试机制
        for attempt in range(3):
            try:
                image_url = get_image_url(screenshot_path)
                if image_url:
                    break
                logger.warning(f"上传截图失败，尝试重试 ({attempt+1}/3)")
                time.sleep(2)
            except Exception as e:
                logger.warning(f"上传截图时出错: {e}，尝试重试 ({attempt+1}/3)")
                time.sleep(2)
        
        if not image_url:
            logger.error(f"上传截图失败，已重试3次")
            return {
                "is_installed": False,
                "error_message": "上传截图失败，无法进行OCR识别"
            }
        
        logger.info(f"截图上传成功，URL: {image_url}")
        
        # 5. 获取OCR识别结果
        logger.info(f"开始获取OCR识别结果...")
        ocr_results = []
        
        # 添加重试机制
        for attempt in range(3):
            try:
                ocr_results = get_message_from_horus(image_url, device_name, logger)
                if ocr_results:
                    break
                logger.warning(f"OCR识别结果为空，尝试重试 ({attempt+1}/3)")
                time.sleep(2)
            except Exception as e:
                logger.warning(f"获取OCR识别结果时出错: {e}，尝试重试 ({attempt+1}/3)")
                time.sleep(2)
        
        if not ocr_results:
            logger.error(f"获取OCR识别结果失败，已重试3次")
            return {
                "is_installed": False,
                "error_message": "OCR识别失败，无法找到安装确认按钮"
            }
        
        logger.info(f"成功获取OCR识别结果，共 {len(ocr_results)} 项")
        
        # 6. 查找"继续安装"或类似按钮
        install_button_coordinates = None
        install_button_text = None
        
        for text, coordinates in ocr_results:
            # 查找包含"安装"、"继续"等关键词的文本
            if "继续安装" in text or "安装" in text or "确认" in text or "继续" in text:
                logger.info(f"找到安装确认按钮: '{text}'，坐标: {coordinates}")
                install_button_coordinates = coordinates
                install_button_text = text
                break
        
        if not install_button_coordinates:
            logger.warning(f"未找到安装确认按钮，尝试查找其他可能的按钮...")
            
            # 尝试查找其他可能的按钮（例如"确定"、"OK"等）
            for text, coordinates in ocr_results:
                if "确定" in text or "同意" in text or "ok" in text.lower() or "确认" in text:
                    logger.info(f"找到可能的确认按钮: '{text}'，坐标: {coordinates}")
                    install_button_coordinates = coordinates
                    install_button_text = text
                    break
        
        if not install_button_coordinates:
            logger.error(f"未找到任何可能的确认按钮")
            return {
                "is_installed": False,
                "error_message": "未找到安装确认按钮"
            }
        
        # 7. 点击确认按钮
        x, y = install_button_coordinates
        tap_command = f"adb -s {udid} shell input tap {x} {y}"
        logger.info(f"执行点击命令: {tap_command}，点击按钮文本: '{install_button_text}'")
        
        # 设置超时机制
        try:
            tap_result = subprocess.run(tap_command, shell=True, check=False, timeout=10)
        except subprocess.TimeoutExpired:
            logger.error(f"点击命令超时")
            return {
                "is_installed": False,
                "error_message": "点击确认按钮命令超时"
            }
        
        if tap_result.returncode != 0:
            logger.error(f"点击确认按钮失败")
            return {
                "is_installed": False,
                "error_message": "点击确认按钮失败"
            }
        
        # 8. 先等待30秒，然后检查安装状态
        logger.info(f"等待30秒，然后检查安装状态...")
        time.sleep(30)
        
        # 检查应用是否已安装到预期版本
        try:
            version_command = f"adb -s {udid} shell dumpsys package {package_name} | grep versionName"
            logger.info(f"执行初步版本检查命令: {version_command}")
            
            version_result = subprocess.run(version_command, shell=True, capture_output=True, text=True, timeout=10)
            
            if version_result.returncode == 0 and version_result.stdout:
                # 提取版本号
                version_line = version_result.stdout.strip()
                current_version = version_line.split('=')[1].strip() if '=' in version_line else None
                
                if current_version:
                    logger.info(f"初步检测到应用版本: {current_version}")
                    
                    # 如果有预期版本，检查是否匹配或兼容
                    if expected_version:
                        if current_version == expected_version or is_version_compatible(current_version, expected_version):
                            logger.info(f"应用已成功安装到兼容版本 {current_version}（预期版本 {expected_version}），提前结束等待")
                            return {
                                "is_installed": True,
                                "installed_version": current_version,
                                "is_compatible": True,
                                "early_detection": True
                            }
                        else:
                            logger.info(f"应用已安装，但版本 {current_version} 与预期版本 {expected_version} 不兼容，继续等待以确保安装完成")
                    else:
                        # 如果没有预期版本，但检测到了版本号，也认为安装成功
                        logger.info(f"应用已成功安装到版本 {current_version}，提前结束等待")
                        return {
                            "is_installed": True,
                            "installed_version": current_version,
                            "early_detection": True
                        }
            else:
                logger.info(f"初步检查未检测到应用版本，继续等待完整安装...")
        except Exception as e:
            logger.info(f"初步检查应用版本时出错: {e}，继续等待完整安装...")
        
        # 如果初步检查未能确认安装成功，继续等待
        logger.info(f"继续等待，让安装完成...")
        
        # 使用轮询方式检查安装状态，而不是简单的sleep
        wait_start_time = time.time()
        max_wait_time = 180  # 3分钟
        check_interval = 20  # 每20秒检查一次
        
        while time.time() - wait_start_time < max_wait_time:
            # 检查安装进程是否已经结束
            if install_process.poll() is not None:
                stdout, stderr = install_process.communicate()
                if "Success" in stdout:
                    logger.info(f"APK安装已完成: {stdout}")
                    break
                else:
                    logger.warning(f"APK安装可能失败: {stderr or stdout}")
                    # 继续等待，因为有些设备可能不会在stdout中返回Success
            
            # 尝试检查应用是否已安装
            try:
                version_command = f"adb -s {udid} shell dumpsys package {package_name} | grep versionName"
                version_result = subprocess.run(version_command, shell=True, capture_output=True, text=True, timeout=5)
                
                if version_result.returncode == 0 and version_result.stdout:
                    # 提取版本号
                    version_line = version_result.stdout.strip()
                    current_version = version_line.split('=')[1].strip() if '=' in version_line else None
                    
                    if current_version:
                        # 使用新的版本比对逻辑，允许小版本号差异
                        if expected_version and (current_version == expected_version or is_version_compatible(current_version, expected_version)):
                            logger.info(f"检测到应用已安装到兼容版本 {current_version}（预期版本 {expected_version}），提前结束等待")
                            break
                        else:
                            logger.info(f"检测到应用已安装，当前版本: {current_version}")
                            # 如果没有预期版本或版本不匹配，继续等待以确保安装完全完成
            except Exception as e:
                logger.debug(f"检查应用版本时出错: {e}")
                # 忽略错误，继续等待
            
            time.sleep(check_interval)
        
        # 9. 检查安装结果
        # 使用adb命令获取已安装的应用版本
        version_command = f"adb -s {udid} shell dumpsys package {package_name} | grep versionName"
        logger.info(f"执行版本检查命令: {version_command}")
        
        try:
            version_result = subprocess.run(version_command, shell=True, capture_output=True, text=True, timeout=10)
        except subprocess.TimeoutExpired:
            logger.error(f"版本检查命令超时")
            return {
                "is_installed": False,
                "error_message": "版本检查命令超时，无法确认安装结果"
            }
        
        if version_result.returncode != 0 or not version_result.stdout:
            logger.error(f"获取应用版本失败，可能安装未成功")
            return {
                "is_installed": False,
                "error_message": "安装后无法获取应用版本，可能安装失败"
            }
        
        # 提取版本号
        version_line = version_result.stdout.strip()
        installed_version = version_line.split('=')[1].strip() if '=' in version_line else None
        
        if not installed_version:
            logger.error(f"无法从输出中提取版本号")
            return {
                "is_installed": False,
                "error_message": "无法获取安装后的应用版本"
            }
        
        logger.info(f"安装后的应用版本: {installed_version}")
        
        # 如果提供了预期版本，检查是否匹配或兼容
        if expected_version and installed_version != expected_version:
            # 检查版本是否兼容（允许小版本号差异）
            if is_version_compatible(installed_version, expected_version):
                logger.info(f"安装后的版本 {installed_version} 与预期版本 {expected_version} 兼容")
                return {
                    "is_installed": True,
                    "installed_version": installed_version,
                    "is_compatible": True
                }
            else:
                logger.warning(f"安装后的版本 {installed_version} 与预期版本 {expected_version} 不匹配且不兼容")
                return {
                    "is_installed": True,
                    "warning": f"安装的版本 {installed_version} 与预期版本 {expected_version} 不匹配且不兼容",
                    "installed_version": installed_version,
                    "is_compatible": False
                }
        
        # 清理临时文件
        try:
            if os.path.exists(screenshot_path):
                os.remove(screenshot_path)
                logger.debug(f"已删除临时截图文件: {screenshot_path}")
        except Exception as e:
            logger.debug(f"删除临时截图文件时出错: {e}")
        
        return {
            "is_installed": True,
            "installed_version": installed_version
        }
        
    except Exception as e:
        logger.error(f"手动确认安装过程中出错: {e}")
        logger.error(f"详细错误: {traceback.format_exc()}")
        return {
            "is_installed": False,
            "error_message": f"安装过程出错: {str(e)}"
        }

def compare_versions(version1, version2):
    """
    比较两个版本号的大小，允许小版本号（最后一位）有差异
    
    :param version1: 第一个版本号，如 "12.30.200"
    :param version2: 第二个版本号，如 "12.30.400"
    :return: 如果 version2 > version1 返回 True，否则返回 False
    """
    try:
        # 将版本号按点分割成列表
        v1_parts = [int(x) for x in version1.split('.')]
        v2_parts = [int(x) for x in version2.split('.')]
        
        # 确保两个列表长度相同
        while len(v1_parts) < len(v2_parts):
            v1_parts.append(0)
        while len(v2_parts) < len(v1_parts):
            v2_parts.append(0)
        
        # 只比较主版本号和次版本号（忽略最后一位）
        for i in range(len(v1_parts) - 1):
            if v2_parts[i] > v1_parts[i]:
                return True
            elif v2_parts[i] < v1_parts[i]:
                return False
        
        # 如果主版本号和次版本号相同，检查最后一位的差异
        if len(v1_parts) >= 3 and len(v2_parts) >= 3:
            # 如果最后一位的差异超过10，则认为需要更新
            if abs(v2_parts[-1] - v1_parts[-1]) > 10:
                return v2_parts[-1] > v1_parts[-1]
        
        # 如果主版本号和次版本号相同，且最后一位差异不大，则认为版本相同，不需要更新
        return False
    except Exception as e:
        logger.error(f"比较版本号时出错: {e}")
        # 出错时保守处理，返回需要更新
        return True

def is_device_in_whitelist(udid):
    """
    检查设备是否在需要手动确认安装的白名单中
    
    :param udid: 设备UDID
    :return: 布尔值，表示设备是否在白名单中
    """
    return udid in MANUAL_CONFIRM_WHITELIST

# 添加新函数用于判断两个版本是否兼容（允许小版本号差异）
def is_version_compatible(version1, version2):
    """
    判断两个版本是否兼容（允许小版本号差异）
    
    :param version1: 第一个版本号，如 "12.30.202"
    :param version2: 第二个版本号，如 "12.30.200"
    :return: 如果两个版本兼容返回 True，否则返回 False
    """
    try:
        # 将版本号按点分割成列表
        v1_parts = [int(x) for x in version1.split('.')]
        v2_parts = [int(x) for x in version2.split('.')]
        
        # 确保两个列表长度相同
        while len(v1_parts) < len(v2_parts):
            v1_parts.append(0)
        while len(v2_parts) < len(v1_parts):
            v2_parts.append(0)
        
        # 检查主版本号和次版本号是否相同
        for i in range(len(v1_parts) - 1):
            if v1_parts[i] != v2_parts[i]:
                return False
        
        # 检查最后一位的差异是否在可接受范围内（这里设置为10）
        if len(v1_parts) >= 3 and len(v2_parts) >= 3:
            return abs(v1_parts[-1] - v2_parts[-1]) <= 10
        
        return True
    except Exception as e:
        logger.error(f"判断版本兼容性时出错: {e}")
        return False

def navigate_to_url_safely(driver, url, logger, ios_version_float=None):
    """
    安全地在 Safari 中导航到指定 URL，处理各种可能的异常
    
    :param driver: Appium WebDriver 实例
    :param url: 目标 URL
    :param logger: 日志记录器
    :param ios_version_float: iOS 版本号（浮点数），如果提供，将用于决定导航策略
    :return: 布尔值，表示是否成功导航
    """
    try:
        # 只有在 iOS 版本 >= 17.0 或未指定版本时，才尝试使用 get 方法
        if ios_version_float is None or ios_version_float >= 17.0:
            try:
                driver.get(url)
                logger.info(f"使用 driver.get() 导航到 URL: {url}")
                return True
            except Exception as e:
                logger.info(f"使用 driver.get() 导航失败: {e}，尝试手动导航")
        else:
            logger.info(f"iOS 版本 {ios_version_float} < 17.0，跳过 driver.get() 方法，直接使用手动导航")
        
        # 尝试点击地址栏并输入 URL
        max_attempts = 3
        for attempt in range(max_attempts):
            try:
                # 查找地址栏
                text_fields = driver.find_elements('class name', 'XCUIElementTypeTextField')
                if not text_fields:
                    # 尝试点击屏幕顶部区域以显示地址栏
                    screen_size = driver.get_window_size()
                    driver.tap([(screen_size['width'] // 2, 40)], 500)
                    logger.info("已点击屏幕顶部区域")
                    time.sleep(1)
                    text_fields = driver.find_elements('class name', 'XCUIElementTypeTextField')
                
                if text_fields:
                    address_bar = text_fields[0]
                    logger.info(f"找到地址栏 (尝试 {attempt+1}/{max_attempts})")
                    
                    # 点击地址栏
                    address_bar.click()
                    logger.info("已点击地址栏")
                    time.sleep(1)
                    
                    # 重新获取地址栏元素，避免 StaleElementReferenceException
                    text_fields = driver.find_elements('class name', 'XCUIElementTypeTextField')
                    if text_fields:
                        address_bar = text_fields[0]
                        
                        # 直接输入 URL
                        address_bar.send_keys(url)
                        logger.info(f"已在地址栏输入 URL: {url}")
                        time.sleep(1)
                        
                        # 点击键盘上的"前往"按钮
                        try:
                            go_button = driver.find_element('accessibility id', 'Go')
                            go_button.click()
                            logger.info("已点击'前往'按钮")
                            return True
                        except Exception as e:
                            logger.info(f"点击'前往'按钮失败: {e}，尝试发送回车键")
                            try:
                                address_bar.send_keys("\n")
                                logger.info("已发送回车键")
                                return True
                            except Exception as e2:
                                logger.warning(f"发送回车键失败: {e2}，尝试搜索按钮")
                                try:
                                    search_button = driver.find_element('accessibility id', '搜索')
                                    search_button.click()
                                    logger.info("已点击'搜索'按钮")
                                    return True
                                except Exception as e3:
                                    logger.warning(f"点击'搜索'按钮失败: {e3}")
                    else:
                        logger.warning(f"点击后无法重新获取地址栏 (尝试 {attempt+1}/{max_attempts})")
                else:
                    logger.warning(f"无法找到地址栏 (尝试 {attempt+1}/{max_attempts})")
                
                # 如果失败，等待一下再重试
                time.sleep(2)
            
            except Exception as e:
                logger.warning(f"导航尝试 {attempt+1}/{max_attempts} 失败: {e}")
                time.sleep(2)
        
        # 所有尝试都失败了
        logger.error(f"在 {max_attempts} 次尝试后仍无法导航到 URL: {url}")
        return False
        
    except Exception as e:
        logger.error(f"导航到 URL 时发生未预期的错误: {e}")
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False

# 示例用法
if __name__ == "__main__":
    # 示例：检查iOS应用更新
    # udid = "00008030-001550602192802E"  # 替换为实际设备的 UDID
    # app_status = check_ios_app_updates_by_safari(udid)
    # print(f"iOS应用状态: {app_status}")
    
    # 示例：通过Sigma后端检查安卓应用更新
    android_udid = "d6f09d4a"  # 替换为实际安卓设备的 UDID
    
    # 获取信息、下载APK（如果有新版本）并安装到设备
    result = check_android_app_updates_by_sigma(android_udid, download_apk=True, install_apk=True)
    print(f"安卓应用更新信息: {result}")
    
    #     if result['status'] == "成功":
    #         print(f"最新版本号: {result['version']}, 构件号: {result['build_number']}")
    #         print(f"已安装版本: {result['installed_version'] or '未安装'}")
    #         print(f"是否需要更新: {'是' if result['needs_update'] else '否'}")
    #         print(f"APK下载链接: {result['apk_url']}")
        
    #         if result['local_path']:
    #             print(f"APK本地路径: {result['local_path']}")
    #             if result['is_updated']:
    #                 print("已下载新版本APK")
    #             else:
    #                 print("本地已有相同版本APK，无需重新下载")
                
    #         if result['is_installed']:
    #             print("已成功安装APK到设备")
    #         elif result['needs_update']:
    #             print("APK安装失败或未执行安装")
    #         else:
    #             print("当前已安装最新版本，无需安装")
    

