import requests
import json
import time
from collections import Counter, OrderedDict
from PIL import Image
import numpy as np
from upload_image import get_image_url


def get_category_icons(pic_path, channel_keyword="外卖", verbose=False):
    """
    【传统方法】识别美团首页频道区域的所有频道元素（如外卖、团购等）

    适用场景：
        - 适用于频道区块背景色较为统一、结构规则的页面
        - 依赖OCR接口返回的textline元素和坐标

    参数：
        pic_path (str): 待识别的图片路径
        channel_keyword (str): 频道区块的起始关键词（如"外卖"），用于定位频道区域
        verbose (bool): 是否输出详细调试信息

    返回：
        dict: 频道元素及其中心点坐标，格式如 {"外卖": {"x": 133, "y": 841}, ...}
    """
    # 步骤1: 上传图片并请求接口（带重试机制）
    page_data = _get_page_parse_result_with_retry(pic_path, verbose)
    if not page_data:
        return {}
    # 步骤2: 筛选textline元素并添加中心点坐标
    textlines = _filter_textline_elements(page_data)
    if not textlines:
        return {}
    # 获取屏幕尺寸（从图片中推断）
    screen_width, screen_height = _get_screen_size(pic_path)
    if verbose:
        print(f"屏幕尺寸: {screen_width} x {screen_height}")
    # 步骤3: 找到指定关键词的元素作为起点
    start_element = _find_keyword_element(textlines, channel_keyword)
    if not start_element:
        if verbose:
            print(f"未找到'{channel_keyword}'元素")
        return {}
    start_x = start_element["center_x"]
    start_y = start_element["center_y"]
    if verbose:
        print(f"起点元素'{channel_keyword}': ({start_x}, {start_y})")
    # 步骤4: 找到右边界元素
    right_element = _find_right_boundary(textlines, start_y, screen_height, verbose)
    if not right_element:
        if verbose:
            print("未找到右边界元素")
        return {}
    right_x = right_element["center_x"]
    if verbose:
        print(f"右边界元素'{right_element.get('elem_detail_info', '')}': ({right_x}, {right_element['center_y']})")
    # 步骤5: 找到下边界元素
    bottom_element = _find_bottom_boundary(textlines, right_x, screen_width, verbose)
    if not bottom_element:
        if verbose:
            print("未找到下边界元素")
        return {}
    bottom_y = bottom_element["center_y"]
    if verbose:
        print(f"下边界元素'{bottom_element.get('elem_detail_info', '')}': ({bottom_element['center_x']}, {bottom_y})")
    # 步骤6: 确定矩形区域并筛选元素
    region_bounds = {
        'min_x': start_x,
        'max_x': right_x,
        'min_y': start_y,
        'max_y': bottom_y
    }
    if verbose:
        print(f"频道区域范围: ({start_x}, {start_y}) 到 ({right_x}, {bottom_y})")
    result = _extract_elements_in_region(textlines, region_bounds, verbose)
    return result


def get_category_icons_by_row_scanning(pic_path, channel_keyword="外卖", verbose=False):
    """
    【行扫描主导色方法】自动识别美团首页频道区域的所有频道元素（如外卖、团购等）

    适用场景：
        - 频道区块背景色为浅色或近似色，且有较明显的色带分割
        - 适合新版美团首页、频道区块背景色不纯白的情况
        - 自动根据主导色和行扫描动态确定频道区块上下边界

    参数：
        pic_path (str): 待识别的图片路径
        channel_keyword (str): 频道区块的起始关键词（如"外卖"），用于定位频道区域
        verbose (bool): 是否输出详细调试信息

    返回：
        dict: 频道元素及其中心点坐标，格式如 {"外卖": {"x": 133, "y": 841}, ...}
    """
    # 步骤1: 获取页面解析结果
    page_data = _get_page_parse_result_with_retry(pic_path, verbose)
    if not page_data:
        return OrderedDict()
    # 步骤2: 筛选textline元素
    textlines = _filter_textline_elements(page_data)
    if not textlines:
        return OrderedDict()
    # 步骤3: 找到指定关键词的元素作为起点
    start_element = _find_keyword_element(textlines, channel_keyword)
    if not start_element:
        if verbose:
            print(f"未找到'{channel_keyword}'元素")
        return OrderedDict()
    # 步骤4: 使用行扫描方法找到频道区域
    try:
        region_bounds = _find_channel_region_by_row_scanning(
            pic_path, start_element, verbose
        )
        if not region_bounds:
            if verbose:
                print("行扫描分析失败，回退到传统方法")
            return get_category_icons(pic_path, channel_keyword, verbose)
        # 步骤5: 筛选区域内的元素
        result = _extract_elements_in_region(textlines, region_bounds, verbose)
        # 新增：排序，先按y从小到大，再按x从小到大
        sorted_items = sorted(result.items(), key=lambda item: (item[1]['y'], item[1]['x']))
        ordered_result = OrderedDict(sorted_items)
        return ordered_result
    except Exception as e:
        if verbose:
            print(f"行扫描分析出错: {str(e)}，回退到传统方法")
        return get_category_icons(pic_path, channel_keyword, verbose)


def get_category_icons_by_connected_components(pic_path, channel_keyword="外卖", verbose=False):
    """
    使用连通域分析识别美团首页频道区域的元素
    """
    # ... 保持原有实现 ...
    page_data = _get_page_parse_result_with_retry(pic_path, verbose)
    if not page_data:
        return {}
    textlines = _filter_textline_elements(page_data)
    if not textlines:
        return {}
    start_element = _find_keyword_element(textlines, channel_keyword)
    if not start_element:
        if verbose:
            print(f"未找到'{channel_keyword}'元素")
        return {}
    try:
        region_bounds = _find_channel_region_by_connected_components(
            pic_path, start_element, verbose
        )
        if not region_bounds:
            if verbose:
                print("连通域分析失败，回退到传统方法")
            return get_category_icons(pic_path, channel_keyword, verbose)
        result = _extract_elements_in_region(textlines, region_bounds, verbose)
        return result
    except Exception as e:
        if verbose:
            print(f"连通域分析出错: {str(e)}，回退到传统方法")
        return get_category_icons(pic_path, channel_keyword, verbose)


def get_category_icons_by_row_count(pic_path, channel_keyword="外卖", verbose=False):
    """
    通过"每行固定数量textline"的规则发现频道区。
    适用于频道区每行元素数量固定、排布整齐的场景。

    参数：
        pic_path (str): 待识别的图片路径
        channel_keyword (str): 频道区块的起始关键词（可选，未用到，仅为接口统一）
        verbose (bool): 是否输出详细调试信息

    返回：
        OrderedDict: 频道元素及其中心点坐标，已排序，格式如 {"外卖": {"x": 133, "y": 841}, ...}
    """
    per_row_count = 5
    y_threshold = 30
    page_data = _get_page_parse_result_with_retry(pic_path, verbose)
    if not page_data:
        return OrderedDict()
    def get_center(region):
        x1, y1, x2, y2 = region
        return (x1 + x2) // 2, (y1 + y2) // 2
    # 1. 提取所有textline元素
    textlines = []
    result_list = page_data.get("result_info", {}).get("result_info_list", [])
    for element in result_list:
        if element.get("elem_det_type") == "textline":
            region = element.get("elem_det_region", [])
            if len(region) == 4:
                center_x, center_y = get_center(region)
                element["center_x"] = center_x
                element["center_y"] = center_y
            textlines.append(element)
    if not textlines:
        if verbose:
            print("未找到textline元素")
        return OrderedDict()
    # 2. 按y中心点排序
    textlines = sorted(textlines, key=lambda t: t["center_y"])
    # 新增：排除y方向前5%的元素
    screen_width, screen_height = _get_screen_size(pic_path)
    y_min = int(screen_height * 0.05)
    textlines = [t for t in textlines if t["center_y"] >= y_min]
    rows = []
    current_row = []
    last_y = None
    for t in textlines:
        y = t["center_y"]
        if last_y is None or abs(y - last_y) < y_threshold:
            current_row.append(t)
        else:
            if len(current_row) == per_row_count:
                rows.append(current_row)
            current_row = [t]
        last_y = y
    # 最后一行
    if len(current_row) == per_row_count:
        rows.append(current_row)
    # 整理输出为 dict 格式
    result = {}
    for row in rows:
        for t in row:
            text = t.get('elem_detail_info', '')
            if text:
                result[text] = {"x": t["center_x"], "y": t["center_y"]}
    # 排序：先按y从小到大，再按x从小到大，允许y坐标误差
    ordered_result = _sort_icons_with_y_tolerance(result, y_tolerance=5)
    if verbose:
        print(f"共找到{len(ordered_result)}个频道元素：")
        for k, v in ordered_result.items():
            print(f"  {k}: {v}")
    return ordered_result

# ================= 辅助函数区域 =================

def _get_page_parse_result_with_retry(pic_path, verbose=False, max_retries=3):
    """
    带重试机制的接口请求，上传图片并获取页面解析结果
    """
    for attempt in range(max_retries):
        try:
            pic_url = get_image_url(pic_path)
            if not pic_url:
                if verbose:
                    print(f"第{attempt + 1}次上传图片失败")
                continue
            api_url = f"http://qaassist.sankuai.com/compass/api/ocr/getUiPageParseResult?PicUrl={pic_url}"
            response = requests.get(api_url, timeout=30)
            if response.status_code == 200:
                data = response.json()
                if data.get("code") == 0:
                    return data
                else:
                    if verbose:
                        print(f"第{attempt + 1}次请求失败，返回码: {data.get('code')}")
            else:
                if verbose:
                    print(f"第{attempt + 1}次请求失败，状态码: {response.status_code}")
        except Exception as e:
            if verbose:
                print(f"第{attempt + 1}次请求异常: {str(e)}")
        if attempt < max_retries - 1:
            time.sleep(2)
    if verbose:
        print(f"经过{max_retries}次重试后仍然失败")
    return None

def _filter_textline_elements(page_data):
    """
    从接口返回数据中筛选textline类型元素，并补充中心点坐标
    """
    textlines = []
    result_list = page_data.get("result_info", {}).get("result_info_list", [])
    for element in result_list:
        if element.get("elem_det_type") == "textline":
            region = element.get("elem_det_region", [])
            if len(region) == 4:
                x1, y1, x2, y2 = region
                element["center_x"] = (x1 + x2) // 2
                element["center_y"] = (y1 + y2) // 2
            textlines.append(element)
    return textlines

def _get_screen_size(pic_path):
    """
    从图片文件获取屏幕尺寸（宽高）
    """
    try:
        from PIL import Image
        with Image.open(pic_path) as img:
            return img.width, img.height
    except Exception:
        return 1200, 2600

def _find_keyword_element(textlines, keyword):
    """
    在textlines中查找指定关键词的元素
    """
    for element in textlines:
        if element.get("elem_detail_info") == keyword:
            return element
    return None

def _find_right_boundary(textlines, start_y, screen_height, verbose=False):
    """
    找到与起点纵坐标一致的所有元素中，横坐标最大的元素
    """
    y_tolerance = int(screen_height * 0.01)
    same_row_elements = []
    for element in textlines:
        element_y = element.get("center_y")
        if element_y and abs(element_y - start_y) <= y_tolerance:
            same_row_elements.append(element)
    if verbose:
        print(f"同一水平线上的元素: {[e.get('elem_detail_info', '') for e in same_row_elements]}")
    if same_row_elements:
        right_element = max(same_row_elements, key=lambda x: x.get("center_x", 0))
        return right_element
    return None

def _find_bottom_boundary(textlines, right_x, screen_width, verbose=False):
    """
    找到与右边界横坐标一致的所有元素中，纵坐标最大的元素，排除底部导航栏
    """
    x_tolerance = int(screen_width * 0.01)
    bottom_nav_keywords = ["我的", "视频", "消息", "购物车", "推荐"]
    same_column_elements = []
    for element in textlines:
        element_x = element.get("center_x")
        text = element.get("elem_detail_info", "")
        if text in bottom_nav_keywords:
            continue
        if element_x and abs(element_x - right_x) <= x_tolerance:
            same_column_elements.append(element)
    if verbose:
        print(f"同一竖直线上的元素（排除底部导航）: {[e.get('elem_detail_info', '') for e in same_column_elements]}")
    if same_column_elements:
        bottom_element = max(same_column_elements, key=lambda x: x.get("center_y", 0))
        return bottom_element
    return None

def _extract_elements_in_region(textlines, region_bounds, verbose=False):
    """
    提取矩形区域内的所有textline元素，并按坐标排序，排除无关元素
    """
    result = {}
    elements_in_region = []
    excluded_keywords = ["更多", "新人团购1分抢"]
    for element in textlines:
        center_x = element.get("center_x")
        center_y = element.get("center_y")
        text = element.get("elem_detail_info", "")
        if text in excluded_keywords:
            if verbose:
                print(f"排除元素: {text}")
            continue
        if (center_x and center_y and text and
            region_bounds['min_x'] <= center_x <= region_bounds['max_x'] and
            region_bounds['min_y'] <= center_y <= region_bounds['max_y']):
            elements_in_region.append(element)
    elements_in_region.sort(key=lambda x: (x.get("center_y", 0), x.get("center_x", 0)))
    if verbose:
        print(f"区域内找到{len(elements_in_region)}个元素:")
        for element in elements_in_region:
            text = element.get("elem_detail_info", "")
            x, y = element.get("center_x"), element.get("center_y")
            print(f"  {text}: ({x}, {y})")
    for element in elements_in_region:
        text = element.get("elem_detail_info", "")
        if text:
            result[text] = {
                "x": element.get("center_x"),
                "y": element.get("center_y")
            }
    return result

def _find_channel_region_by_connected_components(pic_path, start_element, verbose=False):
    """
    使用连通域分析找到频道区域的边界
    """
    try:
        from PIL import Image
        import numpy as np
        from scipy import ndimage
        from collections import Counter
    except ImportError as e:
        if verbose:
            print(f"缺少必要的库: {str(e)}")
        return None
    try:
        image = Image.open(pic_path).convert('RGB')
        img_array = np.array(image)
    except Exception as e:
        if verbose:
            print(f"图片加载失败: {str(e)}")
        return None
    region = start_element.get("elem_det_region", [])
    if len(region) != 4:
        if verbose:
            print("起始元素区域信息不完整")
        return None
    x1, y1, x2, y2 = region
    height, width = img_array.shape[:2]
    x1 = max(0, min(x1, width-1))
    x2 = max(0, min(x2, width-1))
    y1 = max(0, min(y1, height-1))
    y2 = max(0, min(y2, height-1))
    if verbose:
        print(f"起始元素区域: ({x1}, {y1}) 到 ({x2}, {y2})")
    element_region = img_array[y1:y2, x1:x2]
    dominant_color = _get_actual_dominant_color(element_region, verbose)
    if dominant_color is None:
        if verbose:
            print("无法确定主导颜色")
        return None
    if verbose:
        print(f"主导颜色: RGB{dominant_color}")
    color_mask = _create_color_mask(img_array, dominant_color, tolerance=30)
    labeled_array, num_features = ndimage.label(color_mask)
    if verbose:
        print(f"找到 {num_features} 个连通域")
    center_x = (x1 + x2) // 2
    center_y = (y1 + y2) // 2
    if center_y >= labeled_array.shape[0] or center_x >= labeled_array.shape[1]:
        if verbose:
            print("起始点超出图片范围")
        return None
    target_label = labeled_array[center_y, center_x]
    if target_label == 0:
        if verbose:
            print("起始点不在任何连通域内")
        return None
    target_region = (labeled_array == target_label)
    coords = np.where(target_region)
    if len(coords[0]) == 0:
        if verbose:
            print("目标连通域为空")
        return None
    min_y, max_y = coords[0].min(), coords[0].max()
    min_x, max_x = coords[1].min(), coords[1].max()
    margin = 20
    min_x = max(0, min_x - margin)
    max_x = min(width - 1, max_x + margin)
    min_y = max(0, min_y - margin)
    max_y = min(height - 1, max_y + margin)
    if verbose:
        print(f"连通域边界: ({min_x}, {min_y}) 到 ({max_x}, {max_y})")
    return {
        'min_x': min_x,
        'max_x': max_x,
        'min_y': min_y,
        'max_y': max_y
    }

def _get_actual_dominant_color(image_region, verbose=False):
    """
    分析像素区域，获取主导颜色（面积占比最大）
    """
    if image_region.size == 0:
        return None
    import numpy as np
    from collections import Counter
    pixels = image_region.reshape(-1, 3)
    total_pixels = len(pixels)
    quantized_pixels = (pixels // 8) * 8
    color_counts = Counter()
    for pixel in quantized_pixels:
        color_counts[tuple(pixel)] += 1
    if verbose:
        print(f"区域像素总数: {total_pixels}")
        print(f"唯一颜色数: {len(color_counts)}")
        most_common = color_counts.most_common(5)
        print("面积占比前5的颜色:")
        for i, (color, count) in enumerate(most_common):
            ratio = (count / total_pixels) * 100
            print(f"  {i+1}. RGB{color}: {count} 像素 ({ratio:.1f}%)")
    if color_counts:
        dominant_color = color_counts.most_common(1)[0][0]
        dominant_count = color_counts.most_common(1)[0][1]
        dominant_ratio = (dominant_count / total_pixels) * 100
        if verbose:
            print(f"主导颜色: RGB{dominant_color} ({dominant_ratio:.1f}%)")
        return dominant_color
    return None

def _create_color_mask(img_array, target_color, tolerance=30):
    """
    创建颜色掩码，标记与目标颜色相近的像素
    """
    import numpy as np
    diff = img_array.astype(np.float32) - np.array(target_color, dtype=np.float32)
    distance = np.sqrt(np.sum(diff ** 2, axis=2))
    mask = distance <= tolerance
    return mask

def _find_channel_region_by_row_scanning(pic_path, start_element, verbose=False):
    """
    使用行扫描方法找到频道区域的边界，基于主导颜色的行占比进行上下扫描
    """
    try:
        from PIL import Image
        import numpy as np
    except ImportError as e:
        if verbose:
            print(f"缺少必要的库: {str(e)}")
        return None
    try:
        image = Image.open(pic_path).convert('RGB')
        img_array = np.array(image)
    except Exception as e:
        if verbose:
            print(f"图片加载失败: {str(e)}")
        return None
    region = start_element.get("elem_det_region", [])
    if len(region) != 4:
        if verbose:
            print("起始元素区域信息不完整")
        return None
    x1, y1, x2, y2 = region
    center_x = (x1 + x2) // 2
    center_y = (y1 + y2) // 2
    height, width = img_array.shape[:2]
    if center_y >= height or center_x >= width:
        if verbose:
            print("中心点超出图片范围")
        return None
    if verbose:
        print(f"起始元素中心点: ({center_x}, {center_y})")
    element_region = img_array[y1:y2, x1:x2]
    dominant_color = _get_actual_dominant_color(element_region, verbose)
    if dominant_color is None:
        if verbose:
            print("无法确定主导颜色")
        return None
    if verbose:
        print(f"主导颜色: RGB{dominant_color}")
    baseline_ratio = _calculate_row_color_ratio(img_array, center_y, dominant_color, tolerance=15)
    if baseline_ratio is None:
        if verbose:
            print("无法计算基准行颜色占比")
        return None
    scan_threshold = 0.5
    if verbose:
        print(f"基准行({center_y})颜色占比: {baseline_ratio:.1%}")
        print(f"扫描阈值: {scan_threshold:.1%} (固定阈值)")
    top_boundary = _scan_boundary_up(img_array, center_y, dominant_color, scan_threshold, verbose, tolerance=15)
    bottom_boundary = _scan_boundary_down(img_array, center_y, dominant_color, scan_threshold, verbose, tolerance=15)
    if verbose:
        print(f"扫描结果 - 上边界: {top_boundary}, 下边界: {bottom_boundary}")
    margin = 10
    min_y = max(0, top_boundary - margin)
    max_y = min(height - 1, bottom_boundary + margin)
    min_x = 0
    max_x = width - 1
    if verbose:
        print(f"最终区域边界: ({min_x}, {min_y}) 到 ({max_x}, {max_y})")
    return {
        'min_x': min_x,
        'max_x': max_x,
        'min_y': min_y,
        'max_y': max_y
    }

def _calculate_row_color_ratio(img_array, row_y, target_color, tolerance=15):
    """
    计算指定行目标颜色的占比
    """
    if row_y >= img_array.shape[0]:
        return None
    import numpy as np
    row_pixels = img_array[row_y, :]
    target_r, target_g, target_b = target_color
    r_min = max(0, target_r - tolerance)
    r_max = min(255, target_r + tolerance)
    g_min = max(0, target_g - tolerance)
    g_max = min(255, target_g + tolerance)
    b_min = max(0, target_b - tolerance)
    b_max = min(255, target_b + tolerance)
    r_match = (row_pixels[:, 0] >= r_min) & (row_pixels[:, 0] <= r_max)
    g_match = (row_pixels[:, 1] >= g_min) & (row_pixels[:, 1] <= g_max)
    b_match = (row_pixels[:, 2] >= b_min) & (row_pixels[:, 2] <= b_max)
    color_mask = r_match & g_match & b_match
    color_count = np.sum(color_mask)
    ratio = color_count / len(row_pixels)
    return ratio

def _scan_boundary_up(img_array, start_y, target_color, threshold, verbose=False, tolerance=15):
    """
    从起始行向上扫描，找到上边界，允许连续3行异常
    """
    current_y = start_y
    consecutive_failures = 0
    max_failures = 3
    while current_y > 0:
        current_y -= 1
        ratio = _calculate_row_color_ratio(img_array, current_y, target_color, tolerance)
        if ratio is None:
            break
        if ratio >= threshold:
            consecutive_failures = 0
            if verbose and current_y % 50 == 0:
                print(f"  向上扫描 y={current_y}, 占比={ratio:.1%} ✓")
        else:
            consecutive_failures += 1
            if verbose:
                print(f"  向上扫描 y={current_y}, 占比={ratio:.1%} ✗ (连续失败{consecutive_failures}次)")
            if consecutive_failures >= max_failures:
                current_y += max_failures
                break
    return max(0, current_y)

def _scan_boundary_down(img_array, start_y, target_color, threshold, verbose=False, tolerance=15):
    """
    从起始行向下扫描，找到下边界，允许连续3行异常
    """
    height = img_array.shape[0]
    current_y = start_y
    consecutive_failures = 0
    max_failures = 3
    while current_y < height - 1:
        current_y += 1
        ratio = _calculate_row_color_ratio(img_array, current_y, target_color, tolerance)
        if ratio is None:
            break
        if ratio >= threshold:
            consecutive_failures = 0
            if verbose and current_y % 50 == 0:
                print(f"  向下扫描 y={current_y}, 占比={ratio:.1%} ✓")
        else:
            consecutive_failures += 1
            if verbose:
                print(f"  向下扫描 y={current_y}, 占比={ratio:.1%} ✗ (连续失败{consecutive_failures}次)")
            if consecutive_failures >= max_failures:
                current_y -= max_failures
                break
    return min(height - 1, current_y)

def _sort_icons_with_y_tolerance(result_dict, y_tolerance=5):
    """
    对频道元素进行排序，y坐标允许一定误差，同一行内再按x排序。
    
    参数:
        result_dict: 频道元素字典，格式为 {name: {"x": x, "y": y}}
        y_tolerance: y坐标容错阈值，默认5像素
    
    返回:
        OrderedDict: 排序后的频道元素
    """
    if not result_dict:
        return OrderedDict()
    
    # 1. 转为列表 [(name, x, y)]
    items = [(k, v['x'], v['y']) for k, v in result_dict.items()]
    # 2. 按y排序
    items.sort(key=lambda item: item[2])
    # 3. 分组：将y坐标接近的元素归为一行
    rows = []
    current_row = []
    last_y = None
    for item in items:
        if last_y is None or abs(item[2] - last_y) <= y_tolerance:
            current_row.append(item)
        else:
            rows.append(current_row)
            current_row = [item]
        last_y = item[2]
    if current_row:
        rows.append(current_row)
    # 4. 每行内按x排序
    sorted_items = []
    for row in rows:
        row_sorted = sorted(row, key=lambda item: item[1])  # 按x坐标排序
        sorted_items.extend(row_sorted)
    # 5. 还原为OrderedDict
    return OrderedDict((name, {'x': x, 'y': y}) for name, x, y in sorted_items)

# 测试函数
if __name__ == "__main__":
    pic_path = "/Users/<USER>/Desktop/work/platform_autotest_frame_python/photo/redmi 美团首页.png"
    # print("测试get_category_icons_by_row_scanning")
    # result = get_category_icons_by_row_scanning(pic_path)
    # # 输出result的长度
    # print(result)
    # print(len(result))
    # print("测试get_category_icons_by_connected_components")
    # result = get_category_icons_by_connected_components(pic_path)
    # print(result)
    # print(len(result))
    print("测试get_category_icons_by_row_count")
    result = get_category_icons_by_row_count(pic_path)
    print(result)
    print(len(result))
    
    

    