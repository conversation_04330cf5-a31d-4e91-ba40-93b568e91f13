import os
import sys
import time
import re
import threading
import multiprocessing
import subprocess
import socket
import queue
from datetime import datetime  # 修改导入方式，从datetime模块导入datetime类
from typing import List, Dict, Optional, Union, Tuple, Set, Any
from queue import Queue, Empty
from multiprocessing import Process, Event, Manager
from dataclasses import dataclass
import traceback

# 添加当前目录的父目录到sys.path，确保能正确导入模块
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 本地模块导入
from python.heartbeat_monitor import log_device_issue, ISSUE_TYPE_TEST, ISSUE_TYPE_APP, initialize_heartbeat_monitor, stop_heartbeat_monitor, DEVICE_ROUND_THRESHOLD
from python.check_app_updates import check_ios_app_updates_by_safari, check_android_app_updates_by_sigma
from python.log_manager import log_manager, global_logger
from python.config import Config
from python.adb_manager import adb_service_manager
from python.wda_manager import wda_service_manager
from python.appium_manager import appium_service_manager
from python.device_status_manager import get_device_status, update_device_status, create_device_status, initialize_device_status
from python.device_common import device_operate_external
from python.test_meituan_ios import test_meituan_ios
from python.test_meituan_android import test_meituan_android
from python.env_config import get_host_id


def get_device_status_dict():
    """获取所有设备的状态字典，用于兼容原有代码"""
    status_dict = {}
    if initial_devices_ios:
        for device_udid in initial_devices_ios:
            status = get_device_status(device_udid)
            if status:
                status_dict[device_udid] = status
    if initial_devices_android:
        for device_udid in initial_devices_android:
            status = get_device_status(device_udid)
            if status:
                status_dict[device_udid] = status
    return status_dict

@dataclass
class TestResult:
    """测试结果数据类"""
    device_id: str
    device_name: str
    platform: str  # 'ios' 或 'android'
    timestamp: str
    status: str  # 'success', 'failed', 'error', 'completed', 'partial_completed'
    error_message: Optional[str] = None
    test_duration: float = 0.0
    app_issue: bool = False  # 标记是否有应用问题
    test_issue: bool = False  # 标记是否有测试流程问题
    completed_icons: int = 0  # 完成测试的图标数量
    total_icons: int = 0  # 总图标数量

class DeviceManager:
    """设备管理器，负责设备检测、连接和状态检查"""
    
    def __init__(self, logger):
        self.logger = logger
        # 使用全局的 log_manager 实例，避免重复创建
        self.log_manager = log_manager
    
    def standardize_device_name(self, device_name):
        """
        标准化设备名称，确保命名一致性
        
        Args:
            device_name (str): 原始设备名称
            
        Returns:
            str: 标准化后的设备名称
        """
        if not device_name:
            return "Unknown_Device"
        
        # 移除首尾空白
        name = device_name.strip()
        
        # 替换空格为下划线
        name = name.replace(" ", "_")
        
        # 替换其他可能的特殊字符为下划线
        import re
        name = re.sub(r'[^\w\-.]', '_', name)
        
        # 移除连续的下划线
        name = re.sub(r'_+', '_', name)
        
        # 移除首尾的下划线
        name = name.strip('_')
        
        # 确保名称不为空
        if not name:
            return "Unknown_Device"
            
        return name
        
    def is_port_in_use(self, port, device_logger):
        """检查端口是否被占用"""
        try:
            # 使用socket方法检查端口是否被占用
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                return s.connect_ex(('localhost', port)) == 0
        except Exception as e:
            device_logger.error(f"检查端口 {port} 是否被占用时发生错误: {e}")
            return True  # 出错时保守处理，认为端口被占用

    def find_available_port(self, start_port, end_port, device_logger):
        """在指定范围内查找可用端口"""
        for port in range(start_port, end_port + 1):
            if not self.is_port_in_use(port, device_logger):
                device_logger.info(f"找到可用端口: {port}")
                return port
        device_logger.error(f"在范围 {start_port}-{end_port} 内未找到可用端口")
        return None
    
    def get_connected_devices(self, platform, target_udids=None):
        """获取已连接的设备列表及其属性
        
        Args:
            platform (str): 设备平台，'ios'或'android'
            target_udids (list): 目标设备 UDID 列表，如果为 None 则获取所有设备
            
        Returns:
            list: 设备信息字典列表
        """
        devices = []
        
        if platform == 'ios':
            # iOS设备检测逻辑
            output = execute_command(["idevice_id", "-l"])
            if not output:
                global_logger.warning("没有检测到iOS设备")
                return devices

            udids = output.strip().split('\n')
            
            # 如果指定了目标设备列表，则筛选设备
            if target_udids:
                udids = [udid for udid in udids if udid in target_udids]
                global_logger.info(f"iOS目标设备列表: {target_udids}")
                missing_devices = set(target_udids) - set(udids)
                if missing_devices:
                    global_logger.warning(f"以下iOS目标设备未连接: {missing_devices}")

            for udid in udids:
                info = execute_command(["ideviceinfo", "-u", udid])
                if not info:
                    global_logger.error(f"获取设备 {udid} 信息失败")
                    continue
                name_match = re.search(r"DeviceName: (.+)", info)
                version_match = re.search(r"ProductVersion: (.+)", info)
                if name_match and version_match:
                    # 使用标准化函数处理设备名称
                    raw_name = name_match.group(1)
                    name = self.standardize_device_name(raw_name)
                    version = version_match.group(1)
                    devices.append({
                        "udid": udid,
                        "name": name,
                        "version": version
                    })
                    global_logger.info(f"检测到iOS设备: {udid} - {name} - {version}")
                else:
                    global_logger.error(f"正则匹配设备信息失败，UDID: {udid}")
        
        elif platform == 'android':
            # Android设备检测逻辑
            output = execute_command(["adb", "devices"])
            if not output:
                global_logger.warning("没有检测到Android设备")
                return devices

            # 解析 adb devices 输出
            lines = output.strip().split('\n')[1:]  # 跳过第一行 "List of devices attached"
            device_ids = [line.split()[0] for line in lines if line.strip() and 'device' in line]

            # 如果指定了目标设备列表，则筛选设备
            if target_udids:
                device_ids = [did for did in device_ids if did in target_udids]
                global_logger.info(f"Android目标设备列表: {target_udids}")
                missing_devices = set(target_udids) - set(device_ids)
                if missing_devices:
                    global_logger.warning(f"以下Android目标设备未连接: {missing_devices}")

            for device_id in device_ids:
                try:
                    # 获取设备型号和品牌
                    model, model_stderr = device_operate_external.execute_adb_command(device_id, "shell getprop ro.product.model")
                    brand, brand_stderr = device_operate_external.execute_adb_command(device_id, "shell getprop ro.product.brand")
                    # 获取Android系统版本
                    android_version, version_stderr = device_operate_external.execute_adb_command(device_id, "shell getprop ro.build.version.release")
                    
                    if model and brand and not model_stderr and not brand_stderr:
                        # 使用品牌+型号作为设备名称，并进行标准化处理
                        raw_device_name = f"{brand.strip()}_{model.strip()}"
                        device_name = self.standardize_device_name(raw_device_name)
                        # 使用具体的Android版本，如果获取失败则使用默认值
                        if android_version and not version_stderr and android_version.strip():
                            version = android_version.strip()
                        else:
                            version = "Android"
                            global_logger.warning(f"获取Android设备 {device_id} 系统版本失败，使用默认值: {version}")
                        
                        devices.append({
                            "udid": device_id,
                            "name": device_name,
                            "version": version
                        })
                        global_logger.info(f"检测到Android设备: {device_id} - {device_name} - {version}")
                    else:
                        global_logger.error(f"获取Android设备 {device_id} 型号或品牌失败: model_stderr={model_stderr}, brand_stderr={brand_stderr}")
                except Exception as e:
                    global_logger.error(f"获取Android设备 {device_id} 信息失败: {e}")
        
        else:
            global_logger.error(f"不支持的平台: {platform}")
        
        # 输出设备检测总结
        if devices:
            global_logger.info(f"{platform.capitalize()}设备检测总结:")
            for device in devices:
                global_logger.info(f"- 设备名称: {device['name']}, UDID: {device['udid']}, 版本: {device['version']}")
        else:
            global_logger.warning(f"未检测到任何{platform.capitalize()}设备")
        
        return devices
    
    def start_service(self, service_name, start_port, port_step, max_retries, device_logger, 
               start_service_func, check_status_func, wait_time=10):
        """通用的服务启动函数，封装重试逻辑和端口检测
        
        Args:
            service_name: 服务名称，用于日志记录
            start_port: 起始端口号
            port_step: 每次重试时端口号的增加步长
            max_retries: 最大重试次数
            device_logger: 日志记录器
            start_service_func: 启动服务的函数，接收参数(port, attempt, max_retries)，返回(success, result)
            check_status_func: 检查服务状态的函数，接收参数(port)，返回布尔值
            wait_time: 每次重试之间的等待时间（秒）
            
        Returns:
            成功返回端口号，失败返回None
        """
        for attempt in range(max_retries):
            current_port = start_port + (attempt * port_step)
            device_logger.info(f"正在尝试启动 {service_name} (尝试 {attempt + 1}/{max_retries}), 使用端口: {current_port}")
            
            # 检查端口是否可用
            if self.is_port_in_use(current_port, device_logger):
                device_logger.warning(f"端口 {current_port} 已被占用，尝试下一个端口")
                continue
                
            # 启动服务
            success, result = start_service_func(current_port, attempt, max_retries)
            
            if success and check_status_func(current_port):
                device_logger.info(f"{service_name} 启动成功: Port={current_port}")
                return current_port
            
            # 如果不是最后一次尝试，等待一段时间后重试
            if attempt < max_retries - 1:
                device_logger.info(f"等待 {wait_time} 秒后进行下一次尝试...")
                time.sleep(wait_time)
        
        device_logger.error(f"{service_name} 启动失败: 已尝试 {max_retries} 次")
        return None
    
    def check_device_online_unified(self, device, platform):
        """统一的设备在线检测函数
        
        Args:
            device (dict): 设备信息字典，包含udid, name等
            platform (str): 平台类型，'ios' 或 'android'
            
        Returns:
            bool: 设备是否在线
        """
        device_udid = device['udid']
        device_name = device['name']
        
        try:
            if platform == 'ios':
                # iOS设备检测
                output = execute_command(["idevice_id", "-l"])
                if not output:
                    return False
                connected_devices = output.strip().split('\n')
                is_online = device_udid in connected_devices
                
            elif platform == 'android':
                # Android设备检测
                output = execute_command(["adb", "devices"])
                if not output:
                    return False
                # 解析 adb devices 输出
                lines = output.strip().split('\n')[1:]  # 跳过第一行
                connected_devices = [line.split()[0] for line in lines if line.strip() and 'device' in line]
                is_online = device_udid in connected_devices
                
            else:
                global_logger.error(f"不支持的平台: {platform}")
                return False
            
            if not is_online:
                global_logger.debug(f"设备 {device_name} (UDID: {device_udid}) 检测为离线")
            
            return is_online
            
        except Exception as e:
            global_logger.error(f"检查设备 {device_name} 在线状态时发生错误: {e}")
            return False

    def handle_offline_device_with_hourly_check(self, device, platform, device_logger):
        """
        处理离线设备，每小时检查一次是否重新连接
        如果设备重连成功且是iOS设备，则需要重新设置WDA和Appium环境
        
        Args:
            device (dict): 设备信息字典
            platform (str): 平台类型，'ios' 或 'android'
            device_logger: 设备日志记录器
        """
        device_udid = device['udid']
        device_name = device['name']
        HOURLY_CHECK_INTERVAL = 60 * 60  # 1小时
        
        device_logger.info(f"设备 {device_name} (UDID: {device_udid}) 进入离线状态，将每小时检查一次重连状态")
        
        while True:
            # 等待一小时
            device_logger.info(f"等待 {HOURLY_CHECK_INTERVAL/60} 分钟后进行下一次设备重连检查...")
            time.sleep(HOURLY_CHECK_INTERVAL)
            
            # 检查设备是否重新连接
            if self.check_device_online_unified(device, platform):
                device_logger.info(f"设备 {device_name} (UDID: {device_udid}) 已重新连接!")
                
                # 更新设备状态为在线
                update_device_status(device_udid, {
                    'status': 'online',
                    'last_update': time.time()
                })
                device_logger.info(f"设备 {device_name} (UDID: {device_udid}) 状态已更新为在线")
                
                # 如果是iOS设备，需要重新设置WDA和Appium环境
                if platform == 'ios':
                    device_logger.info(f"iOS设备 {device_name} (UDID: {device_udid}) 重连成功，开始重新设置WDA和Appium环境...")
                    
                    try:
                        # 重新获取设备状态信息
                        device_status = get_device_status(device_udid)
                        device_base_round = device_status.get('round_num', 0) if device_status else 0
                        
                        # 查找可用端口并重新设置服务
                        # 使用原有的端口分配逻辑
                        appium_port = Config.IOS_BASE_APPIUM_PORT
                        wda_port = Config.IOS_BASE_WDA_PORT
                        
                        # 重新设置iOS环境
                        appium_port, wda_port = self.setup_ios_requirements(
                            device,
                            appium_port,
                            wda_port,
                            device_logger,
                            device_base_round,
                            current_round=device_base_round + 1  # 当前轮次
                        )
                        
                        device_logger.info(f"iOS设备 {device_name} (UDID: {device_udid}) WDA和Appium环境重新设置完成")
                        
                        # 更新设备状态为运行中
                        update_device_status(device_udid, {
                            'status': 'running',
                            'last_update': time.time(),
                            'test_issue': False
                        })
                        device_logger.info(f"iOS设备 {device_name} (UDID: {device_udid}) 已准备好继续测试")
                        
                    except Exception as e:
                        device_logger.error(f"iOS设备 {device_name} (UDID: {device_udid}) 重新设置环境时发生错误: {str(e)}")
                        # 如果设置失败，将状态设为错误
                        update_device_status(device_udid, {
                            'status': 'reconnect_setup_failed',
                            'last_update': time.time(),
                            'test_issue': True
                        })
                        # 继续等待下一次检查
                        continue
                        
                else:  # Android设备
                    device_logger.info(f"Android设备 {device_name} (UDID: {device_udid}) 重连成功，无需额外设置")
                    
                    # 更新设备状态为运行中
                    update_device_status(device_udid, {
                        'status': 'running',
                        'last_update': time.time(),
                        'test_issue': False
                    })
                    device_logger.info(f"Android设备 {device_name} (UDID: {device_udid}) 已准备好继续测试")
                
                # 设备重连成功，退出离线等待循环
                break
                
            else:
                device_logger.info(f"设备 {device_name} (UDID: {device_udid}) 仍然离线，继续等待...")
                # 保持离线状态
                update_device_status(device_udid, {
                    'status': 'offline',
                    'last_update': time.time()
                })
    
    def start_services_base(self, device, platform, appium_port=None, wda_port=None, idx=0, device_logger=None):
        """
        通用测试服务启动和测试循环基类函数

        Args:
            device: 设备信息字典
            platform: 平台类型，'ios' 或 'android'
            appium_port: Appium服务端口（iOS专用）
            wda_port: WDA服务端口（iOS专用）
            idx: 设备索引
            device_logger: 可选的设备日志器，如果提供则使用，否则创建新的
        """

        if device_logger is None:
            device_logger = self.log_manager.get_device_logger(device["name"])
        device_udid = device['udid']
        
        # 会话内的轮次计数
        session_round_num = 0
        
        # 从设备状态文件读取历史轮次基数
        device_base_round = 0
        try:
            saved_status = get_device_status(device_udid)
            if saved_status and 'round_num' in saved_status:
                device_base_round = saved_status['round_num']
                device_logger.info(f"从持久化存储加载的初始历史轮次基数: {device_base_round}")
            else:
                device_logger.info("没有找到历史轮次信息，初始化历史轮次基数为0")
        except Exception as e:
            device_logger.error(f"加载设备 {device['name']} 的历史轮次信息失败: {e}，初始化历史轮次基数为0")
        
        # iOS平台特有变量
        wda_last_restart_time = time.time() if platform == 'ios' else None
        
        # App更新检查相关变量
        last_app_update_check_time = 0  # 上次App更新检查时间
        
        try:
            # 使用预先导入的测试模块，而不是动态导入
            if platform == 'ios':
                test_func = test_meituan_ios
                # 启动iOS特有服务
                appium_port, wda_port = self.setup_ios_requirements(
                    device,
                    appium_port,
                    wda_port,
                    device_logger,
                    device_base_round,
                    current_round=1  # 初始为第1轮
                )
                    
            else:  # android
                test_func = test_meituan_android
                # Android无需特殊服务启动
                device_logger.info(f"为设备 {device['udid']} 开始测试")
            
            # 更新设备状态为"运行中"
            update_device_status(device_udid, {
                'start_time': time.time(),
                'status': 'running',
                'last_update': time.time(),
                'test_issue': False
            })
            
            # 开始测试循环
            while True:
                # 新增：检查设备是否处于暂停状态
                device_status = get_device_status(device_udid)
                if device_status and device_status.get('status') == 'paused':
                    device_logger.info(f"设备 {device['name']} (UDID: {device_udid}) 处于暂停状态，等待恢复...")
                    pause_end_time = device_status.get('pause_end_time', 0)
                    current_time = time.time()
                    
                    if pause_end_time > 0 and current_time < pause_end_time:
                        # 计算剩余暂停时间
                        remaining_time = pause_end_time - current_time
                        minutes, seconds = divmod(int(remaining_time), 60)
                        hours, minutes = divmod(minutes, 60)
                        time_str = f"{hours:02d}:{minutes:02d}:{seconds:02d}"
                        device_logger.info(f"设备 {device['name']} 暂停中，剩余时间: {time_str}")
                        
                        # 等待30秒后再次检查，避免频繁检查
                        time.sleep(30)
                        continue
                    elif pause_end_time > 0 and current_time >= pause_end_time:
                        # 暂停时间已到但状态未恢复，可能是自动恢复定时器没有工作
                        device_logger.warning(f"设备 {device['name']} 暂停时间已到但状态未恢复，尝试自动恢复...")
                        original_status = device_status.get('original_status_before_pause', 'running')
                        update_device_status(device_udid, {
                            'status': original_status,
                            'last_update': time.time(),
                            'pause_start_time': None,
                            'pause_duration': None,
                            'pause_end_time': None,
                            'original_status_before_pause': None
                        })
                        device_logger.info(f"设备 {device['name']} 已自动恢复到状态: {original_status}")
                    else:
                        # 没有设置结束时间的暂停，等待手动恢复
                        device_logger.info(f"设备 {device['name']} 等待手动恢复...")
                        time.sleep(30)
                        continue
                
                # 预先检查设备是否在线 (新增逻辑)
                if not self.check_device_online_unified(device, platform):
                    device_logger.warning(f"设备 {device['name']} (UDID: {device_udid}) 离线，跳过本轮测试")
                    
                    # 更新设备状态为离线，但不增加轮次
                    update_device_status(device_udid, {
                        'status': 'offline',
                        'last_update': time.time()
                    })
                    
                    # 进入离线等待和重连检查循环
                    self.handle_offline_device_with_hourly_check(device, platform, device_logger)
                    continue
                
                # 设备在线，更新会话轮次计数
                session_round_num += 1
                
                # 计算历史轮次 = 初始历史轮次基数 + 当前会话轮次
                current_round = device_base_round + session_round_num
                
                # 更新设备状态，包含当前轮次信息
                update_device_status(device_udid, {
                    'round_num': current_round,
                    'session_round_num': session_round_num,
                    'last_update': time.time(),
                    'test_issue': False
                })
                
                # iOS平台特有的WDA定期重启逻辑
                if platform == 'ios':
                    # 修复 async 函数调用问题，改为同步调用
                    current_time = time.time()
                    if current_time - wda_last_restart_time >= Config.WDA_RESTART_INTERVAL:
                        device_logger.info(f"WDA 已运行 {(current_time - wda_last_restart_time) / 3600:.2f} 小时，执行定期重启...")
                        
                        # 停止 WDA 进程
                        wda_service_manager.stop_wda_process(device_logger, wda_port, device_udid)
                        time.sleep(Config.WDA_STOP_WAIT_TIME)
                        
                        # 重新启动 WDA
                        success_wda = wda_service_manager.start_wda_for_device(device["udid"], wda_port, device_logger, max_retries=5, device_name=device["name"])
                        
                        if success_wda:
                            device_logger.info(f"WDA 定期重启成功，设备: {device['name']} (UDID: {device_udid})")
                            wda_last_restart_time = time.time()
                        else:
                            device_logger.error(f"WDA 定期重启失败，设备: {device['name']} (UDID: {device_udid})")
                            update_device_status(device_udid, {
                                'status': 'wda_restart_failed', 
                                'last_update': time.time(),
                                'test_issue': True
                            })
                            # 等待一段时间后再次尝试
                            time.sleep(Config.WDA_RESTART_RETRY_WAIT)
                            
                            # 再次尝试启动 WDA
                            success_wda = wda_service_manager.start_wda_for_device(device["udid"], wda_port, device_logger, max_retries=3, device_name=device["name"])
                            if success_wda:
                                device_logger.info(f"WDA 第二次尝试重启成功，设备: {device['name']} (UDID: {device_udid})")
                                wda_last_restart_time = time.time()
                            else:
                                device_logger.error(f"WDA 第二次尝试重启也失败，设备: {device['name']} (UDID: {device_udid})")
                    
                    # 检查是否需要进行App更新检查
                    current_time = time.time()
                    # 检查上次更新时间和现在的差值
                    last_app_update_check_time = get_device_status(device_udid).get('last_app_version_check_time')
                    # 计算当前时间与上次更新时间的差值
                    time_diff = current_time - last_app_update_check_time
                    # 如果差值大于 24 小时，则开始检查App更新
                    if time_diff >= Config.APP_UPDATE_CHECK_INTERVAL:    
                        device_logger.info(f"距离上次App更新检查已经超过 {Config.APP_UPDATE_CHECK_INTERVAL/60/60} 小时，开始检查设备 {device['name']} (UDID: {device_udid}) 的美团App更新检查流程")
                        update_device_status(device_udid, {
                            'last_app_version_check_time': current_time
                        })
                        
                        try:
                            # 检查App更新状态
                            app_status = check_ios_app_updates_by_safari(
                                udid=device_udid,
                                device_name=device["name"],
                                appium_port=appium_port,
                                wda_port=wda_port,
                                app_id=Config.MEITUAN_APP_ID,
                                logger=device_logger,
                                ios_version=device["version"]
                            )
                            
                            # 更新设备状态中的App更新信息
                            update_device_status(device_udid, {
                                'last_app_version_check_time': current_time
                            })
                            device_logger.info(f"设备 {device['name']} (UDID: {device_udid}) 的美团App更新状态: {app_status}")
                            
                            # 如果需要更新并且已点击更新按钮，等待一段时间让App更新完成
                            if app_status == "已点击更新":
                                device_logger.info(f"设备 {device['name']} (UDID: {device_udid}) 的美团App正在更新，等待 {Config.APP_UPDATE_WAIT_TIME/60} 分钟...")
                                time.sleep(Config.APP_UPDATE_WAIT_TIME)
                                device_logger.info(f"设备 {device['name']} (UDID: {device_udid}) 的美团App更新等待完成，继续测试")
                        
                        except Exception as e:
                            device_logger.error(f"检查App更新状态时发生错误: {str(e)}")
                            device_logger.info(f"设备 {device['name']} (UDID: {device_udid}) 的美团App更新状态: {app_status}")
                
                # Android平台的App更新检查
                if platform == 'android':

                    # 检查是否需要进行App更新检查
                    current_time = time.time()
                    # 检查上次更新时间和现在的差值
                    last_app_update_check_time = get_device_status(device_udid).get('last_app_version_check_time')
                    # 计算当前时间与上次更新时间的差值
                    time_diff = current_time - last_app_update_check_time
                    # 如果差值大于 24 小时，则开始检查App更新
                    if time_diff >= Config.APP_UPDATE_CHECK_INTERVAL:    
                        device_logger.info(f"距离上次App更新检查已经超过 {Config.APP_UPDATE_CHECK_INTERVAL/60/60} 小时，开始检查设备 {device['name']} (UDID: {device_udid}) 的美团App更新检查流程")
                        update_device_status(device_udid, {
                            'last_app_version_check_time': current_time
                        })
                        
                        try:
                            # 检查App更新状态
                            app_status = check_android_app_updates_by_sigma(
                                udid=device_udid,
                                device_name=device["name"],
                                download_apk=True,
                                install_apk=True,
                                package_name="com.sankuai.meituan",
                                logger=device_logger
                            )
                            
                            # 更新设备状态中的App更新信息
                            update_device_status(device_udid, {
                                'last_app_version_check_time': current_time,
                                'last_update': time.time()
                            })
                            device_logger.info(f"安卓设备 {device['name']} (UDID: {device_udid}) 的美团App更新状态: {app_status}")
                            
                            # 如果有新版本并且正在下载安装，等待一段时间让App更新完成
                            if app_status.get('needs_update', False) and (app_status.get('is_updated', False) or app_status.get('is_installed', False)):
                                # 检查安装是否已提前完成
                                if app_status.get('early_installation_completed', False):
                                    device_logger.info(f"安卓设备 {device['name']} (UDID: {device_udid}) 的美团App已提前完成安装，无需额外等待")
                                else:
                                    device_logger.info(f"安卓设备 {device['name']} (UDID: {device_udid}) 的美团App正在更新，等待 {Config.APP_UPDATE_WAIT_TIME/60} 分钟...")
                                    time.sleep(Config.APP_UPDATE_WAIT_TIME)
                                    device_logger.info(f"安卓设备 {device['name']} (UDID: {device_udid}) 的美团App更新等待完成，继续测试")
                        
                        except Exception as e:
                            device_logger.error(f"检查安卓App更新状态时发生错误: {str(e)}")
                            device_logger.info(f"设备 {device['name']} (UDID: {device_udid}) 的美团App更新状态: {app_status}")
                
                try:
                    # 记录本轮测试开始时间
                    start_time = time.time()
                    
                    # 在iOS设备测试前检查WDA和Appium状态（跳过第一轮测试，因为刚刚建立了环境）
                    if platform == 'ios' and session_round_num > 1:
                        device_logger.info(f"第 {session_round_num} 轮测试 (历史轮次: {current_round}) 前检查WDA和Appium服务状态")
                        
                        # 检查WDA状态
                        wda_status_ok = wda_service_manager.check_wda_status(wda_port, device_logger, device_name=device["name"], 
                                                        device_udid=device_udid)
                        
                        # 检查Appium状态
                        appium_status_ok = appium_service_manager.check_appium_status(appium_port, device_logger)
                        
                        # 如果任一服务状态异常，尝试重启
                        if not wda_status_ok or not appium_status_ok:
                            device_logger.warning(f"检测到服务异常，WDA状态: {wda_status_ok}, Appium状态: {appium_status_ok}，尝试重启服务")
                            
                            # 使用现有的错误处理函数来重启服务
                            wda_last_restart_time = self.handle_ios_service_error(
                                device, appium_port, wda_port, current_round, 
                                device_logger
                            )
                            
                            device_logger.info(f"服务重启完成，继续测试")
                    
                    # 在调用测试函数前更新进程PID信息
                    update_device_status(device_udid, {
                        'update_process': f"{get_device_status(device_udid)['device_name']}的测试进程",
                        'update_pid': os.getpid(),
                        'last_update': time.time()
                    })
                    
                    # 根据平台执行不同的测试
                    test_result = None
                    if platform == 'ios':
                        test_result = test_func(
                            device_udid,
                            f"http://127.0.0.1:{appium_port}/wd/hub",
                            wda_port,
                            device_logger
                        )
                    else:  # android
                        test_result = test_func(
                            device_udid,
                            device_logger
                        )
                    
                    # 使用测试函数返回的结果
                    if test_result:
                        # 获取测试耗时
                        test_duration = test_result.test_duration
                        status = test_result.status
                        app_issue = test_result.app_issue
                        test_issue = test_result.test_issue
                        completed_icons = test_result.completed_icons
                        total_icons = test_result.total_icons
                        
                        device_logger.info(f"第 {session_round_num} 轮测试完成 (历史轮次: {current_round})，耗时: {test_duration:.2f} 秒，状态: {status}，完成图标: {completed_icons}/{total_icons}")
                    else:
                        # 如果测试函数未返回结果对象，则计算基本的耗时
                        test_duration = time.time() - start_time
                        status = 'completed'  # 默认状态
                        app_issue = False
                        test_issue = False
                        completed_icons = 0
                        total_icons = 0
                        
                        device_logger.info(f"第 {session_round_num} 轮测试完成 (历史轮次: {current_round})，耗时: {test_duration:.2f} 秒，未返回详细结果")
                    
                    # 更新设备状态，标记测试轮次已完成
                    update_device_status(device_udid, {
                            'status': status,  # 使用test_result中的状态
                            'test_duration': test_duration,  # 保存真实测试耗时
                            'app_issue': app_issue,
                            'test_issue': test_issue,
                            'round_num': current_round,  # 保存当前历史轮次
                            'session_round_num': session_round_num,  # 保存会话轮次
                            'completed_icon_num': completed_icons,  # 保存完成的图标数量
                            'last_update': time.time()
                        })
                    device_logger.info(f"设备 {device['name']} (UDID: {device_udid}) 已标记为测试轮次完成，状态: {status}, 耗时: {test_duration:.2f}秒")
                    
                    # 检查是否需要发送设备轮次通知
                    should_send_notification = False
                    notification_reason = ""
                    
                    # 第一轮测试完成后发送通知
                    if session_round_num == 1:
                        should_send_notification = True
                        notification_reason = "第一轮测试完成"
                    # 后续每10轮发送一次通知
                    elif session_round_num % DEVICE_ROUND_THRESHOLD == 0:
                        should_send_notification = True
                        notification_reason = f"第{session_round_num}轮测试完成（每{DEVICE_ROUND_THRESHOLD}轮通知）"
                    
                    # 发送设备轮次通知
                    if should_send_notification:
                        device_logger.info(f"触发设备轮次通知条件: {notification_reason}")
                        try:
                            from python.heartbeat_monitor import send_device_round_notification
                            success = send_device_round_notification(
                                device_id=device_udid,
                                device_name=device['name'],
                                platform=platform,
                                round_num=current_round,
                                status=status,
                                test_duration=test_duration
                            )
                            if success:
                                device_logger.info(f"设备轮次通知发送成功: {notification_reason}")
                            else:
                                device_logger.warning(f"设备轮次通知发送失败: {notification_reason}")
                        except Exception as e:
                            device_logger.error(f"发送设备轮次通知时发生错误: {str(e)}")
                            device_logger.error(f"错误详情: {traceback.format_exc()}")
                    else:
                        device_logger.debug(f"不需要发送轮次通知: 当前会话轮次={session_round_num}, 阈值={DEVICE_ROUND_THRESHOLD}")
                    
                    # 等待一段时间后开始下一轮测试
                    device_logger.info("本轮测试完成，准备开始下一轮...")
                    time.sleep(Config.DEVICE_TEST_INTERVAL)
                    
                except Exception as e:
                    device_logger.error(f"本轮测试发生错误: {str(e)}")
                    test_duration = time.time() - start_time
                    device_logger.error(f"第 {session_round_num} 轮测试失败 (历史轮次: {current_round})，耗时: {test_duration:.2f} 秒")
                    
                    # 更新设备状态
                    update_device_status(device_udid, {
                        'status': 'test_round_failed', 
                        'last_update': time.time(), 
                        'test_issue': True, 
                        'test_duration': test_duration,
                        'round_num': current_round,  # 保存当前历史轮次
                        'session_round_num': session_round_num,  # 保存会话轮次
                        'last_update': time.time()
                    })
                    
                    # iOS平台特有的服务检查和重启逻辑
                    if platform == 'ios':
                        wda_last_restart_time = self.handle_ios_service_error(
                            device, appium_port, wda_port, current_round, 
                            device_logger
                        )
                    
                    # 等待较长时间再重试
                    time.sleep(Config.DEVICE_ERROR_WAIT_TIME)
            
        except Exception as e:
            device_logger.error(f"服务启动或测试过程发生致命错误: {str(e)}")
            
            # 更新设备状态
            update_device_status(device_udid, {
                'status': 'service_start_failed',
                'last_update': time.time(),
                'test_issue': True
            })
            
            # 清理iOS资源
            if platform == 'ios' and 'appium_port' in locals():
                appium_service_manager.stop_appium_server(appium_port, device_logger)
            
            raise
        
    def start_services_ios(self, device, appium_default_port, wda_default_port, idx):
        """iOS设备测试服务启动和测试循环"""
        return self.start_services_base(
            device=device,
            platform='ios',
            appium_port=appium_default_port,
            wda_port=wda_default_port,
            idx=idx,
        )

    def start_services_android(self, device, idx, device_logger=None):
        """Android设备测试服务启动和测试循环"""
        return self.start_services_base(
            device=device,
            platform='android',
            idx=idx,
            device_logger=device_logger,
        )

    def start_ios_device_when_allowed(self, device, appium_port, wda_port, result_queue, stop_event, device_start_queue):
        device_logger = self.log_manager.get_device_logger(device["name"])
        device_udid = device['udid']
        
        # 第一阶段：等待控制器允许安装WDA
        device_logger.info(f"等待控制器允许设备 {device['name']} (UDID: {device_udid}) 开始WDA安装...")
        install_allowed = False
        wait_start_time = time.time()
        max_wait_time = Config.MAX_WAIT_TIME_DEVICE_START  # 最多等待10分钟
        
        while not install_allowed and time.time() - wait_start_time < max_wait_time:
            try:
                # 非阻塞方式检查队列
                allowed_message = device_start_queue.get(block=False)
                expected_install_message = f"install_{device_udid}"
                
                if allowed_message == expected_install_message:
                    device_logger.info(f"控制器已允许设备 {device['name']} (UDID: {device_udid}) 开始WDA安装")
                    install_allowed = True
                else:
                    # 如果不是当前设备的安装消息，放回队列
                    device_logger.debug(f"收到非本设备的消息: {allowed_message}，放回队列")
                    device_start_queue.put(allowed_message)
                    time.sleep(2)
            except Exception:
                # 队列为空，等待一段时间再检查
                time.sleep(2)
        
        if not install_allowed:
            device_logger.error(f"等待控制器允许安装WDA超时，设备 {device['name']} (UDID: {device_udid}) 将不会开始测试")
            update_device_status(device_udid, {
                'status': 'timeout_waiting_for_install',
                'last_update': time.time(),
                'test_issue': True
            })
            device_logger.info(f"设备 {device['name']} (UDID: {device_udid}) 状态更新为: timeout_waiting_for_install")
            return
        
        # 开始准备设备专属 WDA 目录（修改：在安装WDA之前先准备专属目录）
        device_logger.info(f"开始为设备 {device['name']} (UDID: {device_udid}) 准备WDA专属目录")
        
        # 更新设备状态为"准备WDA环境"
        update_device_status(device_udid, {
            'status': 'preparing_wda',
            'last_update': time.time()
        })
        device_logger.info(f"设备 {device['name']} (UDID: {device_udid}) 状态更新为: preparing_wda")
        
        # 准备设备特定的 WDA 目录和配置
        if not wda_service_manager.prepare_device_wda(device_logger, device_udid):
            error_message = f"准备设备特定的 WDA 环境失败"
            device_logger.error(error_message)
            update_device_status(device_udid, {
                'status': 'wda_prepare_failed', 'last_update': time.time(),
                'test_issue': True
            })
            device_logger.info(f"设备 {device['name']} (UDID: {device_udid}) 状态更新为: wda_prepare_failed")
            return
        
        # 开始安装WDA
        device_logger.info(f"开始为设备 {device['name']} (UDID: {device_udid}) 安装WDA")
        
        # 更新设备状态为"安装WDA中"
        update_device_status(device_udid, {
            'status': 'installing_wda',
            'last_update': time.time()
        })
        device_logger.info(f"设备 {device['name']} (UDID: {device_udid}) 状态更新为: installing_wda")
        
        # 查找可用的 WDA 端口
        actual_wda_port = device_manager.find_available_port(
            wda_port,
            wda_port + Config.IOS_WDA_PORT_INTERVAL - 1,
            device_logger
        )
        if not actual_wda_port:
            device_logger.error(f"无法找到可用的 WDA 端口")
            update_device_status(device_udid, {
                'status': 'wda_install_failed', 'last_update': time.time(),
                'test_issue': True
            })
            device_logger.info(f"设备 {device['name']} (UDID: {device_udid}) 状态更新为: wda_install_failed")
            return
        
        # 安装WDA
        success_wda = wda_service_manager.start_wda_for_device(device_udid, actual_wda_port, device_logger, device_name=device['name'])
        
        # WDA安装完成后更新设备状态
        
        if success_wda:
            # WDA启动成功，更新设备状态中的WDA端口信息和状态
            update_device_status(device_udid, {
                'status': 'wda_installed',
                'last_update': time.time(),
                'test_issue': False,
                'wda_forward_port': actual_wda_port,
                'wda_last_restart_time': time.time()
            })
            device_logger.info(f"设备 {device['name']} (UDID: {device_udid}) 状态更新为: wda_installed")
            device_logger.info(f"WDA启动成功，已更新设备状态 - WDA转发端口: {actual_wda_port}")
            # 等待一小段时间，确保状态更新被控制器检测到
            time.sleep(2)
        else:
            update_device_status(device_udid, {
                'status': 'wda_install_failed', 'last_update': time.time(),
                'test_issue': True
            })
            device_logger.info(f"设备 {device['name']} (UDID: {device_udid}) 状态更新为: wda_install_failed")
            return  # 如果WDA安装失败，直接返回，不进行后续测试
        
        # 第二阶段：等待控制器允许开始测试
        device_logger.info(f"等待控制器允许设备 {device['name']} (UDID: {device_udid}) 开始测试...")
        test_allowed = False
        wait_start_time = time.time()
        max_wait_time = Config.MAX_WAIT_TIME_DEVICE_START  # 最多等待10分钟
        
        # 每30秒打印一次当前状态，帮助调试
        last_status_print = time.time()
        status_print_interval = Config.CONTROLLER_STATUS_PRINT_INTERVAL
        
        while not test_allowed and not stop_event.is_set() and time.time() - wait_start_time < max_wait_time:
            current_time = time.time()
            
            # 定期打印状态，帮助调试
            if current_time - last_status_print >= status_print_interval:
                device_logger.info(f"设备 {device['name']} (UDID: {device_udid}) 当前状态: {get_device_status(device_udid).get('status')}, 已等待测试许可: {current_time - wait_start_time:.1f}秒")
                last_status_print = current_time
            
            try:
                # 非阻塞方式检查队列
                allowed_message = device_start_queue.get(block=False)
                expected_test_message = f"test_{device_udid}"
                
                if allowed_message == expected_test_message:
                    device_logger.info(f"控制器已允许设备 {device['name']} (UDID: {device_udid}) 开始测试")
                    test_allowed = True
                else:
                    # 如果不是当前设备的测试消息，放回队列
                    device_logger.debug(f"收到非本设备的测试消息: {allowed_message}，放回队列")
                    device_start_queue.put(allowed_message)
                    time.sleep(2)
            except Exception:
                # 队列为空，等待一段时间再检查
                time.sleep(2)
        
        if stop_event.is_set():
            device_logger.info(f"收到停止信号，设备 {device['name']} (UDID: {device_udid}) 不会开始测试")
            return
        
        if not test_allowed:
            device_logger.error(f"等待控制器允许开始测试超时，设备 {device['name']} (UDID: {device_udid}) 将不会开始测试")
            update_device_status(device_udid, {
                'status': 'timeout_waiting_for_test', 'last_update': time.time(),
                'test_issue': True
            })
            device_logger.info(f"设备 {device['name']} (UDID: {device_udid}) 状态更新为: timeout_waiting_for_test")
            return
        
        # 控制器已允许开始测试，开始设备测试
        device_logger.info(f"开始设备 {device['name']} (UDID: {device_udid}) 的测试")
        
        # 更新设备状态为"运行中"
        update_device_status(device_udid, {
            'status': 'running', 'last_update': time.time(),
            'test_issue': False
        })
        device_logger.info(f"设备 {device['name']} (UDID: {device_udid}) 状态更新为: running")
        # 将实际使用的WDA端口传递给测试函数，同时传递已创建的日志器避免重复创建
        run_single_ios_device(device, appium_port, actual_wda_port, result_queue, stop_event, device_logger)
    
    def setup_ios_requirements(self, device, appium_port, wda_port, device_logger, device_base_round, current_round=1):
        """
        启动iOS特有服务
        
        Args:
            device: 设备信息字典
            appium_port: Appium端口
            wda_port: WDA端口
            device_logger: 设备日志记录器
            device_base_round: 设备历史轮次基数
            current_round: 当前测试轮次
        """
        device_udid = device['udid']
        
        # 设备专属的 WDA 目录现在已经在 start_ios_device_when_allowed 中准备完成
        device_logger.info(f"设备 {device_udid} 的 WDA 环境应该已经准备完成")
        
        # 查找可用的 Appium 端口
        appium_port = device_manager.start_service(
            "Appium",
            appium_port,
            Config.APPIUM_PORT_STEP,
            3,
            device_logger,
            lambda port, attempt, max_retries: appium_service_manager.start_appium_server(port, attempt, max_retries, device_logger),
            lambda port: appium_service_manager.check_appium_status(port, device_logger)
        )
        if not appium_port:
            error_message = f"Appium 服务启动失败"
            device_logger.error(error_message)
            update_device_status(device_udid, {
                'status': 'appium_start_failed', 'last_update': time.time(),
                'test_issue': True
            })
            raise Exception(error_message)
            
        device_logger.info(f"为设备 {device_udid} 分配端口：WDA={wda_port}，Appium={appium_port}")
        
        # 确认WDA服务是否正常运行
        device_logger.info(f"检查设备 {device_udid} 的 WDA 服务状态...")
        wda_check_attempts = 3
        wda_status_ok = False
        
        for attempt in range(wda_check_attempts):
            device_logger.info(f"WDA 状态检查尝试 {attempt + 1}/{wda_check_attempts}")
            if wda_service_manager.check_wda_status(wda_port, device_logger, device_name=device["name"], device_udid=device_udid):
                wda_status_ok = True
                device_logger.info(f"WDA 服务状态检查通过")
                break
            else:
                device_logger.warning(f"WDA 服务状态检查失败，等待 {Config.WDA_WAIT_TIME} 秒后重试...")
                time.sleep(Config.WDA_WAIT_TIME)
        
        if not wda_status_ok:
            error_message = f"WDA 服务未正常运行，测试无法继续"
            device_logger.error(error_message)
            update_device_status(device_udid, {
                'status': 'wda_check_failed', 'last_update': time.time(),
                'test_issue': True
            })
            
            # 记录 WDA 问题到心跳监控
            log_device_issue(
                device_id=device["udid"],
                device_name=device["name"],
                round_num=current_round,
                issue_type=ISSUE_TYPE_TEST,
                issue_details=error_message
            )
            
            raise Exception(error_message)
        
        # 启动 Appium 服务
        if not appium_service_manager.check_appium_status(appium_port, device_logger):
            # 使用通用服务启动函数启动Appium
            appium_port = device_manager.start_service(
                service_name="Appium",
                start_port=appium_port,
                port_step=Config.APPIUM_PORT_STEP,
                max_retries=3,
                device_logger=device_logger,
                start_service_func=lambda port, attempt, max_retries: appium_service_manager.start_appium_server(port, attempt, max_retries, device_logger),
                check_status_func=lambda port: appium_service_manager.check_appium_status(port, device_logger)
            )
            if not appium_port:
                error_message = f"Appium 服务启动失败"
                device_logger.error(error_message)
                update_device_status(device_udid, {
                    'status': 'appium_start_failed', 'last_update': time.time(),
                    'test_issue': True
                })
                
                # 记录 Appium 启动失败问题到心跳监控
                log_device_issue(
                    device_id=device["udid"],
                    device_name=device["name"],
                    round_num=current_round,
                    issue_type=ISSUE_TYPE_TEST,
                    issue_details=error_message
                )
                
                raise Exception(error_message)
        
        # 最终确认 Appium 服务状态
        device_logger.info(f"最终确认 Appium 服务状态...")
        appium_check_attempts = 3
        for attempt in range(appium_check_attempts):
            device_logger.info(f"Appium 状态检查尝试 {attempt + 1}/{appium_check_attempts}")
            if appium_service_manager.check_appium_status(appium_port, device_logger):
                device_logger.info(f"Appium 服务状态检查通过")
                break
            else:
                device_logger.warning(f"Appium 服务状态检查失败，等待 {Config.APPIUM_WAIT_TIME} 秒后重试...")
                time.sleep(Config.APPIUM_WAIT_TIME)
        else:
            error_message = f"Appium 服务最终状态检查失败"
            device_logger.error(error_message)
            raise Exception(error_message)
        
        # 等待服务稳定
        device_logger.info(f"WDA和Appium服务已启动，等待{Config.WDA_WAIT_TIME}秒确保服务稳定...")
        time.sleep(Config.WDA_WAIT_TIME)

        # 更新设备状态中的端口信息
        update_device_status(device_udid, {
            'appium_port': appium_port,
            'wda_forward_port': wda_port,
            'wda_last_restart_time': time.time()
        })
        device_logger.info(f"已更新设备状态 - Appium端口: {appium_port}, WDA转发端口: {wda_port}")

        return appium_port, wda_port

    def handle_ios_service_error(self, device, appium_port, wda_port, current_round, device_logger):
        """
        处理iOS服务错误，尝试重启服务
        
        Args:
            device: 设备信息字典
            appium_port: Appium端口
            wda_port: WDA端口
            current_round: 当前历史轮次
            device_logger: 设备日志记录器
            
        Returns:
            float: 最后一次WDA重启时间
        """
        device_udid = device['udid']
        
        # 检查服务状态并在必要时重启
        if not wda_service_manager.check_wda_status(wda_port, device_logger, device_name=device["name"], device_udid=device_udid) or not appium_service_manager.check_appium_status(appium_port, device_logger):
            device_logger.info("检测到服务异常，正在重启服务...")
            wda_service_manager.stop_wda_process(device_logger, wda_port, device_udid)
            appium_service_manager.stop_appium_server(appium_port, device_logger)
            time.sleep(Config.WDA_STOP_WAIT_TIME)
            
            # 重新启动服务
            success_wda = wda_service_manager.start_wda_for_device(device["udid"], wda_port, device_logger, max_retries=5, device_name=device["name"])
            # 使用通用服务启动函数启动Appium
            appium_port_new = device_manager.start_service(
                service_name="Appium",
                start_port=appium_port,
                port_step=Config.APPIUM_PORT_STEP,
                max_retries=3,
                device_logger=device_logger,
                start_service_func=lambda port, attempt, max_retries: appium_service_manager.start_appium_server(port, attempt, max_retries, device_logger),
                check_status_func=lambda port: appium_service_manager.check_appium_status(port, device_logger)
            )
            if not success_wda or not appium_port_new:
                update_device_status(device_udid, {
                    'status': 'service_restart_failed', 'last_update': time.time(),
                    'test_issue': True
                })
                raise Exception("服务重启失败")
            else:
                update_device_status(device_udid, {
                    'status': 'service_restarted', 'last_update': time.time(),
                    'test_issue': False
                })
            
            # 更新最后重启时间
            wda_last_restart_time = time.time()
        
        time.sleep(Config.WDA_WAIT_TIME)
        return time.time()  # 返回新的重启时间

    def device_start_controller(self, devices_ios, devices_android, device_start_queue):
        """设备启动控制器，确保iOS设备的WDA安装是串行的，但测试可以并行执行"""
        global_logger.info("设备启动控制器启动")
        
        # 第一阶段：串行安装WDA
        for idx, device in enumerate(devices_ios):
            device_udid = device['udid']
            global_logger.info(f"控制器: 允许设备 {device['name']} (UDID: {device_udid}) 开始WDA安装")
            
            # 将设备放入启动队列，但仅用于WDA安装
            device_start_queue.put(f"install_{device_udid}")
            
            # 等待设备WDA安装完成
            wait_start_time = time.time()
            max_wait_time = Config.MAX_WAIT_TIME_WDA_INSTALL  # 减少等待时间到5分钟
            wda_installed = False
            
            global_logger.info(f"控制器: 等待设备 {device['name']} (UDID: {device_udid}) 的WDA安装完成...")
            
            # 记录初始状态
            global_logger.info(f"控制器: 设备 {device['name']} (UDID: {device_udid}) 的初始状态: {get_device_status(device_udid).get('status')}")
            
            # 每10秒打印一次当前状态，帮助调试
            last_status_print = time.time()
            status_print_interval = Config.CONTROLLER_STATUS_PRINT_INTERVAL
            
            while time.time() - wait_start_time < max_wait_time:
                current_time = time.time()
                
                # 定期打印状态，帮助调试
                if current_time - last_status_print >= status_print_interval:
                    global_logger.info(f"控制器: 设备 {device['name']} (UDID: {device_udid}) 的当前状态: {get_device_status(device_udid).get('status')}, 已等待: {current_time - wait_start_time:.1f}秒")
                    last_status_print = current_time
                
                # 检查设备状态
                status = get_device_status(device_udid).get('status')
                global_logger.debug(f"控制器: 检查设备 {device['name']} (UDID: {device_udid}) 状态: {status}")
                    
                if status in ['wda_installed', 'running', 'success', 'failed', 'error']:
                    # WDA已安装完成或设备已经开始运行测试或已完成
                    global_logger.info(f"控制器: 设备 {device['name']} (UDID: {device_udid}) 的WDA已安装完成，状态: {status}")
                    wda_installed = True
                    
                    # 立即允许该设备开始测试，不等待其他设备
                    if status in ['wda_installed', 'running']:
                        global_logger.info(f"控制器: 立即允许设备 {device['name']} (UDID: {device_udid}) 开始测试")
                        device_start_queue.put(f"test_{device_udid}")
                    
                    break
                elif status == 'wda_install_failed':
                    global_logger.warning(f"控制器: 设备 {device['name']} (UDID: {device_udid}) 的WDA安装失败")
                    wda_installed = False
                    break
                
                time.sleep(2)  # 减少检查间隔到2秒
            
            # 检查最终状态
            global_logger.info(f"控制器: 设备 {device['name']} (UDID: {device_udid}) 的最终状态: {get_device_status(device_udid).get('status')}")
            
            if not wda_installed:
                global_logger.warning(f"控制器: 等待设备 {device['name']} (UDID: {device_udid}) 的WDA安装超时或失败")
                # 尝试再次检查状态，以防状态更新延迟
                status = get_device_status(device_udid).get('status')
                global_logger.info(f"控制器: 最后一次检查设备 {device['name']} (UDID: {device_udid}) 状态: {status}")
                    
                # 如果状态实际上是已安装，但我们之前没有检测到，现在允许开始测试
                if status in ['wda_installed', 'running']:
                    global_logger.info(f"控制器: 设备 {device['name']} (UDID: {device_udid}) 的WDA实际上已安装成功，允许开始测试")
                    device_start_queue.put(f"test_{device_udid}")
                    wda_installed = True
            
            # 无论是否成功，只等待10秒再允许下一个设备安装WDA，以确保资源释放
            time.sleep(10)  # 减少等待时间到10秒
        
        # 第二阶段：检查是否有任何iOS设备没有收到测试许可（可能是因为状态更新延迟）
        global_logger.info("控制器: 检查是否有任何iOS设备没有收到测试许可")
        
        # 再次检查所有iOS设备，确保所有安装成功的设备都收到了测试许可
        for device in devices_ios:
            device_udid = device['udid']
            # 检查设备状态，只有WDA安装成功的设备才允许开始测试
            status = get_device_status(device_udid).get('status')
            global_logger.info(f"控制器: 第二阶段检查设备 {device['name']} (UDID: {device_udid}) 状态: {status}")
            if status in ['wda_installed', 'running']:
                # 再次发送测试许可，如果设备已经收到过，它会忽略重复的消息
                global_logger.info(f"控制器: 确保设备 {device['name']} (UDID: {device_udid}) 已收到测试许可")
                device_start_queue.put(f"test_{device_udid}")
            elif status not in ['success', 'failed', 'error']:
                global_logger.warning(f"控制器: 设备 {device['name']} (UDID: {device_udid}) 状态为 {status}，不允许开始测试")
        
        # 不再处理Android设备，Android设备将直接启动
        
        global_logger.info("控制器: 所有iOS设备已加入测试队列")
    

# 创建全局设备管理器实例
device_manager = DeviceManager(global_logger)

# 添加全局变量跟踪初始检测到的设备
initial_devices_ios = []
initial_devices_android = []

def execute_command(cmd):
    """执行外部命令并捕获输出或错误"""
    # 如果是列表，就使用join将其连接，如果已经是字符串则直接使用
    if isinstance(cmd, list):
        # 确保每个命令组件是字符串类型
        cmd = [str(c) for c in cmd]
        cmd_str = ' '.join(cmd)
        global_logger.info(f"执行命令: {cmd_str}")
        # 保持列表形式执行命令，确保参数正确传递
        actual_cmd = cmd
    else:
        # 如果是字符串，直接使用但确保记录
        global_logger.info(f"执行命令: {cmd}")
        actual_cmd = cmd
    
    try:
        output = subprocess.check_output(
            actual_cmd,
            stderr=subprocess.STDOUT,
            text=True,
            encoding='utf-8',  # 指定编码格式
            errors='replace'   # 替换无法解码的字符
        )
        # 限制输出长度，避免日志过大
        log_output = output if len(output) < 500 else output[:500] + "... [输出已截断]"
        global_logger.info(f"命令输出: {log_output}")
        return output
    except subprocess.CalledProcessError as e:
        global_logger.error(f"命令执行失败: {e.cmd}")
        global_logger.error(f"错误输出: {e.output}")
        return None

def create_test_result(device, status, error_message=None, test_duration=0.0):
    """创建一个测试结果对象
    
    Args:
        device (dict): 设备信息字典
        status (str): 测试状态
        error_message (str, optional): 错误信息
        test_duration (float, optional): 测试耗时
        
    Returns:
        TestResult: 测试结果对象
    """
    return TestResult(
        device_id=device["udid"],
        device_name=device["name"],
        platform='ios' if device["udid"] in Config.TARGET_UDIDS_IOS else 'android',
        timestamp=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        status=status,
        error_message=error_message,
        test_duration=test_duration
    )

def run_single_ios_device(
    device: Dict,
    appium_port: int,
    wda_port: int,
    result_queue: Queue,
    stop_event: Event,
    device_logger=None,
):
    """单个 iOS 设备的测试进程"""
    if device_logger is None:
        device_logger = log_manager.get_device_logger(device["name"])
    start_time = time.time()
    device_udid = device['udid']
    
    try:
        # 设置进程名称以便监控
        current_process = multiprocessing.current_process()
        current_process.name = f"iOS_{device['name']}"
        
        # 添加进程ID和名称的日志记录
        process_id = os.getpid()
        device_logger.info(f"iOS设备测试进程启动: PID={process_id}, 名称={current_process.name}, 设备={device['name']} (UDID: {device_udid})")
        
        device_logger.info(f"开始 iOS 设备测试: {device['name']} (UDID: {device_udid}), WDA端口: {wda_port}")
        
        while not stop_event.is_set():
            try:
                # 运行单个设备的测试
                test_result = device_manager.start_services_ios(
                    device=device,
                    appium_default_port=appium_port,
                    wda_default_port=wda_port,  # 使用传入的WDA端口
                    idx=0,  # 因为是单设备，所以 idx 为 0
                )
                
                # 处理测试结果
                if test_result:
                    device_logger.info(f"收到测试结果: 状态={test_result.status}, 耗时={test_result.test_duration:.2f}秒, 图标完成={test_result.completed_icons}/{test_result.total_icons}")
                    
                    # 更新设备状态
                    update_device_status(device_udid, {
                        'status': test_result.status,
                        'test_duration': test_result.test_duration,
                        'app_issue': test_result.app_issue,
                        'test_issue': test_result.test_issue,
                        'last_update': time.time()
                    })
                    device_logger.info(f"设备 {device['name']} (UDID: {device_udid}) 状态更新为: {test_result.status}")
                    
                    # 发送测试结果
                    result_queue.put(test_result)
                else:
                    # 兼容旧的方式，创建基本的测试结果
                    device_logger.info("未收到测试结果，创建基本的成功结果")
                    
                    # 更新设备状态为"成功"
                    update_device_status(device_udid, {
                        'status': 'success',
                        'last_update': time.time()
                    })
                    device_logger.info(f"设备 {device['name']} (UDID: {device_udid}) 状态更新为: success")
                    
                    # 发送成功结果
                    result = create_test_result(
                        device=device,
                        status='success',
                        test_duration=time.time() - start_time
                    )
                    result_queue.put(result)
                
            except Exception as e:
                device_logger.error(f"设备测试发生错误: {str(e)}")
                
                # 更新设备状态为"失败"
                update_device_status(device_udid, {
                    'status': 'failed',
                    'last_update': time.time()
                })
                device_logger.info(f"设备 {device['name']} (UDID: {device_udid}) 状态更新为: failed")
                
                # 记录测试错误到心跳监控
                error_message = str(e)
                
                # 检查错误消息中是否包含 WDA 相关错误
                if "WDA" in error_message or "WebDriverAgent" in error_message or "xcodebuild" in error_message:
                    issue_details = f"WebDriverAgent 启动失败: {error_message[:200]}"
                else:
                    issue_details = f"设备测试发生错误: {error_message[:200]}"
                
                log_device_issue(
                    device_id=device["udid"],
                    device_name=device["name"],
                    round_num=1,  # 修改为1，确保轮次从1开始计数
                    issue_type=ISSUE_TYPE_TEST,
                    issue_details=issue_details
                )
                
                result = create_test_result(
                    device=device,
                    status='error',
                    error_message=str(e),
                    test_duration=time.time() - start_time
                )
                result_queue.put(result)
                
            # 检查是否需要继续测试
            if not stop_event.is_set():
                device_logger.info("等待下一轮测试...")
                time.sleep(10)
                
    except Exception as e:
        device_logger.error(f"进程发生致命错误: {str(e)}")
        
        # 更新设备状态为"错误"
        update_device_status(device_udid, {
            'status': 'error',
            'last_update': time.time()
        })
        device_logger.info(f"设备 {device['name']} (UDID: {device_udid}) 状态更新为: error")
        
        result = create_test_result(
            device=device,
            status='error',
            error_message=f"Fatal error: {str(e)}",
            test_duration=time.time() - start_time
        )
        result_queue.put(result)
    finally:
        device_logger.info("测试进程结束")

def run_single_android_device(
    device: Dict,
    result_queue: Queue,
    stop_event: Event,
):
    """单个 Android 设备的测试进程"""
    print(f"ANDROID_DEBUG: run_single_android_device 被调用，设备: {device['name']}")  # 立即打印，不依赖日志系统
    
    device_logger = log_manager.get_device_logger(device["name"])
    start_time = time.time()
    device_udid = device['udid']
    
    try:
        # 设置进程名称以便监控
        current_process = multiprocessing.current_process()
        current_process.name = f"Android_{device['name']}"
        
        # 添加进程ID和名称的日志记录
        process_id = os.getpid()
        device_logger.info(f"Android设备测试进程启动: PID={process_id}, 名称={current_process.name}, 设备={device['name']} (UDID: {device_udid})")
        print(f"ANDROID_DEBUG: 进程启动完成，PID={process_id}")  # 立即打印
        
        device_logger.info(f"开始 Android 设备测试: {device['name']} (UDID: {device_udid})")
        
        # 确保 stop_event 是可以检查 is_set 方法的对象
        if not hasattr(stop_event, 'is_set'):
            device_logger.warning(f"stop_event 不具有 is_set 方法，创建新的 Event")
            stop_event = Event()
        
        print(f"ANDROID_DEBUG: 开始进入测试循环")  # 立即打印
        
        while not stop_event.is_set():
            try:    
                # 运行单个设备的测试
                device_logger.info(f"准备调用 start_services_android 方法")
                test_result = device_manager.start_services_android(
                    device=device,
                    idx=0,  # 因为是单设备，所以 idx 为 0
                    device_logger=device_logger,  # 传递已创建的日志器避免重复创建
                )
            
                # 处理测试结果
                if test_result:
                    device_logger.info(f"收到测试结果: 状态={test_result.status}, 耗时={test_result.test_duration:.2f}秒, 图标完成={test_result.completed_icons}/{test_result.total_icons}")
                    
                    # 更新设备状态
                    update_device_status(device_udid, {
                        'status': test_result.status,
                        'test_duration': test_result.test_duration,
                        'app_issue': test_result.app_issue,
                        'test_issue': test_result.test_issue,
                        'last_update': time.time()
                    })
                    device_logger.info(f"设备 {device['name']} (UDID: {device_udid}) 状态更新为: {test_result.status}")
                    
                    # 发送测试结果
                    result_queue.put(test_result)
                else:
                    # 兼容旧的方式，创建基本的测试结果
                    device_logger.info("未收到测试结果，创建基本的成功结果")
                    
                    # 更新设备状态为"成功"
                    update_device_status(device_udid, {
                        'status': 'success',
                        'last_update': time.time()
                    })
                    device_logger.info(f"设备 {device['name']} (UDID: {device_udid}) 状态更新为: success")
                    
                    # 发送成功结果
                    result = create_test_result(
                        device=device,
                        status='success',
                        test_duration=time.time() - start_time
                    )
                    result_queue.put(result)
                
            except Exception as e:
                device_logger.error(f"设备测试发生错误: {e}")
                
                # 更新设备状态为"失败"
                update_device_status(device_udid, {
                    'status': 'failed',
                    'last_update': time.time()
                })
                device_logger.info(f"设备 {device['name']} (UDID: {device_udid}) 状态更新为: failed")
                
                result = create_test_result(
                    device=device,
                    status='error',
                    error_message=str(e),
                    test_duration=time.time() - start_time
                )
                result_queue.put(result)
                
            if not stop_event.is_set():
                device_logger.info("等待下一轮测试...")
                time.sleep(10)
                
    except Exception as e:
        device_logger.error(f"进程发生致命错误: {str(e)}")
        
        # 更新设备状态为"错误"
        update_device_status(device_udid, {
            'status': 'error',
            'last_update': time.time()
        })
        device_logger.info(f"设备 {device['name']} (UDID: {device_udid}) 状态更新为: error")
        
        result = create_test_result(
            device=device,
            status='error',
            error_message=f"Fatal error: {str(e)}",
            test_duration=time.time() - start_time
        )
        result_queue.put(result)
    finally:
        device_logger.info("测试进程结束")

def check_device_connection_status(logger=None):
    """
    检查所有设备的实际连接状态，特别是状态为"running"但实际已断开的设备
    
    两种判断设备断开的情况:
    1. 物理连接已断开: 通过adb或idevice命令检查设备不存在
    2. 逻辑连接已断开: 设备物理连接存在，但状态长时间(10分钟)未更新
    
    Args:
        logger: 日志记录器，默认为全局日志记录器
    
    Returns:
        list: 检测到的已断开但状态不是离线的设备列表
    """
    if logger is None:
        logger = global_logger
    
    # 设备状态更新超时时间（10分钟）
    STATUS_UPDATE_TIMEOUT = 10 * 60  # 秒
    
    disconnected_devices = []
    
    # 获取当前实际连接的设备
    ios_devices = []
    android_devices = []
    current_time = time.time()
    
    # 获取iOS设备列表
    try:
        output = subprocess.check_output(["idevice_id", "-l"], universal_newlines=True)
        ios_devices = output.strip().split('\n') if output and output.strip() else []
        logger.debug(f"当前连接的iOS设备: {ios_devices}")
    except Exception as e:
        logger.error(f"获取iOS设备列表时出错: {e}")
    
    # 获取Android设备列表 - 先尝试一次，如果找不到设备再重启ADB服务
    try:
        stdout = execute_command(["adb", "devices"])
        android_devices = []
        if stdout:
            android_devices = [line.split()[0] for line in stdout.strip().split('\n')[1:] if line.strip() and 'device' in line]
            logger.debug(f"当前连接的Android设备: {android_devices}")
        
        # 检查是否需要重启ADB服务
        if not android_devices:
            logger.warning("未检测到任何Android设备，尝试重启ADB服务...")
            if adb_service_manager.restart_adb_service():
                logger.info("ADB服务重启成功，重新检查设备连接状态")
                time.sleep(Config.ADB_DEVICE_WAIT_TIME)
                
                # 重新获取Android设备列表
                stdout = execute_command(["adb", "devices"])
                if stdout:
                    android_devices = [line.split()[0] for line in stdout.strip().split('\n')[1:] if line.strip() and 'device' in line]
                    logger.info(f"ADB重启后连接的Android设备: {android_devices}")
    except Exception as e:
        logger.error(f"获取Android设备列表时出错: {e}")
    
    # 检查每个设备的状态
    for device_id, status_info in list(get_device_status_dict().items()):  # 使用list创建副本，避免遍历时修改字典
        if not status_info or not isinstance(status_info, dict):
            continue
        
        status = status_info.get('status', 'unknown')
        platform = status_info.get('platform', 'unknown').lower()
        device_name = status_info.get('device_name', 'Unknown')
        last_update = status_info.get('last_update', 0)
        
        # 跳过已标记为离线或永久离线的设备
        if status in ['offline', 'permanent_offline']:
            continue
        
        disconnected = False
        disconnect_reason = ""
        
        # 情况1: 检查设备是否物理连接
        is_physically_connected = False
        if platform == 'ios' and ios_devices and device_id in ios_devices:
            is_physically_connected = True
        elif platform == 'android' and android_devices and device_id in android_devices:
            is_physically_connected = True
        
        if not is_physically_connected:
            disconnected = True
            disconnect_reason = "设备物理连接已断开"
        
        # 情况2: 检查设备状态是否长时间未更新
        elif current_time - last_update > STATUS_UPDATE_TIMEOUT:
            disconnected = True
            disconnect_reason = f"设备状态已超过{int((current_time - last_update) / 60)}分钟未更新"
        
        # 如果设备被判定为断开连接
        if disconnected:
            logger.warning(f"检测到设备 {device_name} (UDID: {device_id}, 平台: {platform}) 状态为 '{status}' 已断开连接，原因: {disconnect_reason}")
            disconnected_devices.append({
                'device_id': device_id,
                'device_name': device_name,
                'platform': platform,
                'current_status': status,
                'disconnect_reason': disconnect_reason
            })
            
            # 更新设备状态为离线
            update_device_status(device_id, {
                'status': 'offline',
                'last_update': time.time(),
            })
            logger.info(f"已将设备 {device_name} (UDID: {device_id}) 状态更新为离线")
    
    # 如果有断开的设备，打印详细信息
    if disconnected_devices:
        logger.warning(f"共检测到 {len(disconnected_devices)} 台设备已断开连接:")
        for i, device in enumerate(disconnected_devices, 1):
            logger.warning(f"{i}. {device['device_name']} (UDID: {device['device_id']}, 平台: {device['platform']}), 原因: {device.get('disconnect_reason', '未知')}")
    
    return disconnected_devices

def monitor_processes(processes, result_queue, stop_event, logger):
    """监控所有测试进程，并处理结果队列"""
    logger.info("开始监控测试进程...")
    
    # 记录开始时间
    start_time = time.time()
    last_status_print_time = start_time
    last_status_check_time = start_time
    last_heartbeat_check_time = start_time  # 心跳服务检查时间记录
    
    # 设备连接状态检查间隔 (秒)
    connection_check_interval = 60  # 与MONITOR_SLEEP_INTERVAL保持一致，每60秒检查一次
    heartbeat_check_interval = 5 * 60  # 每5分钟检查一次心跳服务
    
    try:
        while any(p.is_alive() for p in processes):
            # 检查ADB服务状态
            if adb_service_manager.check_and_restart_if_needed():
                logger.info("ADB服务已重启，等待所有设备重新连接...")
                time.sleep(Config.ADB_DEVICE_WAIT_TIME)
            
            # 定期检查设备实际连接状态，包括物理连接和状态更新时间
            current_time = time.time()
            if current_time - last_status_check_time >= connection_check_interval:
                logger.info("检查设备实际连接状态(物理连接和状态更新)...")
                disconnected_devices = check_device_connection_status(logger)
                
                # 如果发现有断开的设备，进行简单的重连状态检查
                if disconnected_devices and (initial_devices_ios or initial_devices_android):
                    logger.warning(f"检测到 {len(disconnected_devices)} 台设备已断开连接，检查是否有设备重新连接")
                    # 过滤出初始设备列表中的设备
                    disconnected_initial_devices = [
                        device['device_id'] for device in disconnected_devices 
                        if device['device_id'] in initial_devices_ios or device['device_id'] in initial_devices_android
                    ]
                    if disconnected_initial_devices:
                        logger.info(f"断开的设备中有 {len(disconnected_initial_devices)} 台是初始设备，检查重连状态")
                        # 使用简化的重连检查函数
                        check_and_reconnect_devices_simple(logger)
                    else:
                        logger.info("断开的设备不在初始设备列表中，跳过重连检查")
                    
                last_status_check_time = current_time
            
            # 新增：定期检查心跳服务状态
            if current_time - last_heartbeat_check_time >= heartbeat_check_interval:
                try:
                    from heartbeat_service import check_and_restart_heartbeat_service
                    if check_and_restart_heartbeat_service():
                        logger.info("心跳服务已重启")
                    last_heartbeat_check_time = current_time
                except Exception as e:
                    logger.error(f"检查心跳服务时发生错误: {str(e)}")
            
            # 处理结果队列
            try:
                while True:
                    result = result_queue.get(block=False)
                    logger.info(f"收到测试结果: {result}")
            except queue.Empty:
                pass
            
            # 定期打印设备状态
            current_time = time.time()
            if current_time - last_status_print_time >= Config.STATUS_PRINT_INTERVAL:
                print_device_status()
                last_status_print_time = current_time
            
            # 检查是否有进程异常退出
            for p in processes:
                if not p.is_alive() and p.exitcode != 0:
                    logger.warning(f"进程 {p.name} 异常退出，退出码: {p.exitcode}")
            
            # 避免CPU占用过高
            time.sleep(Config.MONITOR_SLEEP_INTERVAL)
            
    except KeyboardInterrupt:
        logger.info("收到中断信号，正在停止所有测试...")
        stop_event.set()
    finally:
        # 最后一次打印设备状态
        print_device_status()
        
        # 确保所有进程都已停止
        for p in processes:
            if p.is_alive():
                logger.info(f"等待进程 {p.name} 结束...")
                p.join(timeout=Config.PROCESS_JOIN_TIMEOUT)
                if p.is_alive():
                    logger.warning(f"强制终止进程 {p.name}")
                    p.terminate()
        
        logger.info("所有测试进程已停止")


def print_device_status():
    """打印所有设备的当前状态
    
    Args:
        shared_device_status: 已弃用，保留为兼容性参数
    """
    global_logger.info("=== 当前设备状态 ===")
    
    # 从所有初始设备获取状态
    if not (initial_devices_ios or initial_devices_android):
        global_logger.info("没有设备状态信息")
        return
    
    # 按平台分组设备
    ios_devices = []
    android_devices = []
    
    # 获取iOS设备状态
    for device_udid in initial_devices_ios:
        status_info = get_device_status(device_udid)
        if status_info:
            ios_devices.append((device_udid, status_info))
    
    # 获取Android设备状态
    for device_udid in initial_devices_android:
        status_info = get_device_status(device_udid)
        if status_info:
            android_devices.append((device_udid, status_info))
    
    # 打印iOS设备状态
    if ios_devices:
        global_logger.info("iOS设备状态:")
        for device_id, status_info in ios_devices:
            device_name = status_info.get('device_name', 'Unknown')
            status = status_info.get('status', 'unknown')
            last_update = status_info.get('last_update', 0)
            time_since_update = time.time() - last_update if last_update else 0
            
            # 添加App更新状态信息
            app_update_status = status_info.get('app_update_status', '未检查')
            last_app_update_check = status_info.get('last_app_update_check', 0)
            time_since_app_check = time.time() - last_app_update_check if last_app_update_check else 0
            
            # 格式化时间为小时:分钟:秒
            hours, remainder = divmod(int(time_since_app_check), 3600)
            minutes, seconds = divmod(remainder, 60)
            time_since_app_check_str = f"{hours:02}:{minutes:02}:{seconds:02}"
            
            global_logger.info(f"  - {device_name} (UDID: {device_id}): 状态={status}, 最后更新={time_since_update:.1f}秒前, "
                              f"App更新状态={app_update_status}, 上次检查={time_since_app_check_str}前")
    else:
        global_logger.info("没有iOS设备")
    
    # 打印Android设备状态
    if android_devices:
        global_logger.info("Android设备状态:")
        for device_id, status_info in android_devices:
            device_name = status_info.get('device_name', 'Unknown')
            status = status_info.get('status', 'unknown')
            last_update = status_info.get('last_update', 0)
            time_since_update = time.time() - last_update if last_update else 0
            global_logger.info(f"  - {device_name} (UDID: {device_id}): 状态={status}, 最后更新={time_since_update:.1f}秒前")
    else:
        global_logger.info("没有Android设备")
    
    global_logger.info("=====================")

def check_and_reconnect_devices_simple(logger=None, specific_device=None):
    """
    简单的设备重连检查函数，用于检查设备是否重新连接
    如果设备重连成功，更新状态为 "online"
    
    Args:
        logger: 日志记录器，默认为全局日志记录器
        specific_device: 指定检查的设备UDID，如果为None则检查所有设备
        
    Returns:
        tuple: (重连成功的设备列表, 仍然离线的设备列表)
    """
    global initial_devices_ios, initial_devices_android
    
    if logger is None:
        logger = global_logger
    
    reconnected_devices = {'ios': [], 'android': []}
    still_offline_devices = {'ios': [], 'android': []}
    
    # 获取当前连接的目标设备（使用白名单过滤）
    current_ios_devices = device_manager.get_connected_devices('ios', target_udids=Config.TARGET_UDIDS_IOS)
    current_android_devices = device_manager.get_connected_devices('android', target_udids=Config.TARGET_UDIDS_ANDROID)
    
    # 创建UDID到设备对象的映射
    current_ios_devices_map = {device['udid']: device for device in current_ios_devices}
    current_android_devices_map = {device['udid']: device for device in current_android_devices}
    
    logger.info(f"当前已连接的iOS设备: {list(current_ios_devices_map.keys())}")
    logger.info(f"当前已连接的Android设备: {list(current_android_devices_map.keys())}")
    
    # 处理iOS设备重连检查 - 只检查初始设备列表中的设备
    ios_devices_to_check = []
    if specific_device:
        if specific_device in initial_devices_ios:
            ios_devices_to_check = [specific_device]
    else:
        ios_devices_to_check = initial_devices_ios
    
    for udid in ios_devices_to_check:
        device_status = get_device_status(udid)
        current_status = device_status.get('status') if device_status else 'unknown'
        
        # 只检查离线状态的设备
        if current_status == 'offline':
            # 检查设备是否已重新连接
            if udid in current_ios_devices_map:
                logger.info(f"iOS设备 {udid} 已重新连接")
                device_name = device_status.get('device_name', 'Unknown')
                
                # 更新设备状态为在线
                update_device_status(udid, {
                    'status': 'online',
                    'last_update': time.time()
                })
                logger.info(f"iOS设备 {device_name} (UDID: {udid}) 状态已更新为在线")
                
                # 添加到重连设备列表
                reconnected_devices['ios'].append({
                    'name': device_name,
                    'udid': udid
                })
            else:
                # 设备仍然离线
                still_offline_devices['ios'].append({
                    'name': device_status.get('device_name', 'Unknown'),
                    'udid': udid
                })
    
    # 处理Android设备重连检查 - 只检查初始设备列表中的设备  
    android_devices_to_check = []
    if specific_device:
        if specific_device in initial_devices_android:
            android_devices_to_check = [specific_device]
    else:
        android_devices_to_check = initial_devices_android
    
    for udid in android_devices_to_check:
        device_status = get_device_status(udid)
        current_status = device_status.get('status') if device_status else 'unknown'
        
        # 只检查离线状态的设备
        if current_status == 'offline':
            # 检查设备是否已重新连接
            if udid in current_android_devices_map:
                logger.info(f"Android设备 {udid} 已重新连接")
                device_name = device_status.get('device_name', 'Unknown')
                
                # 更新设备状态为在线
                update_device_status(udid, {
                    'status': 'online',
                    'last_update': time.time()
                })
                logger.info(f"Android设备 {device_name} (UDID: {udid}) 状态已更新为在线")
                
                # 添加到重连设备列表
                reconnected_devices['android'].append({
                    'name': device_name,
                    'udid': udid
                })
            else:
                # 设备仍然离线
                still_offline_devices['android'].append({
                    'name': device_status.get('device_name', 'Unknown'),
                    'udid': udid
                })
    
    # 汇总重连结果
    total_reconnected = len(reconnected_devices['ios']) + len(reconnected_devices['android'])
    total_still_offline = len(still_offline_devices['ios']) + len(still_offline_devices['android'])
    
    if total_reconnected > 0:
        logger.info(f"设备重连检查结果: 共 {total_reconnected} 台设备已重连, {total_still_offline} 台设备仍然离线")
        logger.info(f"- iOS设备: {len(reconnected_devices['ios'])} 台重连成功, {len(still_offline_devices['ios'])} 台仍然离线")
        logger.info(f"- Android设备: {len(reconnected_devices['android'])} 台重连成功, {len(still_offline_devices['android'])} 台仍然离线")
    
    return reconnected_devices, still_offline_devices



def main():
    # 设置服务器标识符
    set_server_identifier_func = None
    
    
    host_id = get_host_id()
    if host_id:
        server_id = host_id
        global_logger.info(f"从环境配置获取服务器标识符: {server_id}")
    else:
        # 如果环境配置中没有设置主机ID，使用主机名
        hostname = "MBP-D95QHDYH5K-2250"
        if '.' in hostname:
            hostname = hostname.split('.')[0]  # 去掉域名后缀
        server_id = hostname
        global_logger.info(f"使用主机名作为服务器标识符: {server_id}")
    
    # 在开始测试前清理旧日志
    log_manager.cleanup_old_logs(days=1, keep_latest=10)
    
    # 设置多进程启动方法
    if sys.platform == 'darwin':  # macOS
        try:
            multiprocessing.set_start_method('spawn', force=True)
            global_logger.info("已设置多进程启动方法为 'spawn'")
        except RuntimeError as e:
            global_logger.warning(f"设置多进程启动方法失败: {e}")
    
    # 在获取设备列表前重启ADB服务
    global_logger.info("在检测设备前重启ADB服务...")
    if not adb_service_manager.restart_adb_service():
        global_logger.error("ADB服务重启失败，程序将退出")
        return
    
    # 创建Manager用于进程间通信
    global_logger.info("创建多进程Manager用于进程间通信")
    manager = Manager()
    
    # 创建设备启动队列，用于控制设备启动顺序
    device_start_queue = manager.Queue()
    
    # 获取初始设备列表，这是我们要追踪和测试的设备
    global initial_devices_ios, initial_devices_android
    # 使用Config中定义的白名单过滤设备，只获取目标设备列表中的设备
    devices_ios = device_manager.get_connected_devices('ios', target_udids=Config.TARGET_UDIDS_IOS)
    devices_android = device_manager.get_connected_devices('android', target_udids=Config.TARGET_UDIDS_ANDROID)
    
    # 保存初始设备UDID列表，后续只处理这些设备
    initial_devices_ios = [device['udid'] for device in devices_ios]
    initial_devices_android = [device['udid'] for device in devices_android]
    global_logger.info(f"初始iOS设备列表: {initial_devices_ios}")
    global_logger.info(f"初始Android设备列表: {initial_devices_android}")
    
    # 打印设备列表信息
    global_logger.info(f"检测到 {len(devices_ios)} 台iOS设备和 {len(devices_android)} 台Android设备")
    for device in devices_ios:
        global_logger.info(f"iOS设备: {device['name']} (UDID: {device['udid']})")
    for device in devices_android:
        global_logger.info(f"Android设备: {device['name']} (UDID: {device['udid']})")
    
    # 如果没有连接任何设备，直接返回
    if not (devices_ios or devices_android):
        global_logger.warning("没有连接的目标设备！程序将退出")
        return
    
    # 检查并更新现有设备状态文件中的系统版本信息
    global_logger.info("检查并更新设备系统版本信息...")
    
    # 更新iOS设备的系统版本
    for device in devices_ios:
        device_udid = device['udid']
        current_version = device.get('version', '')
        current_name = device.get('name', '')  # 当前获取的设备名称（已处理空格）
        existing_status = get_device_status(device_udid)
        
        if existing_status:
            stored_version = existing_status.get('system_version', '')
            stored_name = existing_status.get('device_name', '')
            
            # 检查系统版本是否需要更新
            if stored_version != current_version:
                global_logger.info(f"iOS设备 {device['name']} (UDID: {device_udid}) 系统版本从 '{stored_version}' 更新为 '{current_version}'")
                update_device_status(device_udid, {
                    'system_version': current_version,
                    'last_update': time.time()
                })
            else:
                global_logger.debug(f"iOS设备 {device['name']} (UDID: {device_udid}) 系统版本无变化: {current_version}")
                
            # 检查设备名称是否需要更新
            # 使用标准化函数处理两个名称，确保比较的一致性
            standardized_stored_name = device_manager.standardize_device_name(stored_name)
            standardized_current_name = device_manager.standardize_device_name(current_name)
            
            if standardized_stored_name != standardized_current_name:
                global_logger.info(f"iOS设备 (UDID: {device_udid}) 设备名称从 '{standardized_stored_name}' 更新为 '{standardized_current_name}'")
                update_device_status(device_udid, {
                    'device_name': standardized_current_name,
                    'last_update': time.time()
                })
            else:
                global_logger.debug(f"iOS设备 {device['name']} (UDID: {device_udid}) 设备名称无变化: {standardized_current_name}")
    
    # 更新Android设备的系统版本
    for device in devices_android:
        device_udid = device['udid']
        current_version = device.get('version', '')
        current_name = device.get('name', '')  # 当前获取的设备名称（已处理空格）
        existing_status = get_device_status(device_udid)
        
        if existing_status:
            stored_version = existing_status.get('system_version', '')
            stored_name = existing_status.get('device_name', '')
            
            # 检查系统版本是否需要更新
            if stored_version != current_version:
                global_logger.info(f"Android设备 {device['name']} (UDID: {device_udid}) 系统版本从 '{stored_version}' 更新为 '{current_version}'")
                update_device_status(device_udid, {
                    'system_version': current_version,
                    'last_update': time.time()
                })
            else:
                global_logger.debug(f"Android设备 {device['name']} (UDID: {device_udid}) 系统版本无变化: {current_version}")
                
            # 检查设备名称是否需要更新
            # 使用标准化函数处理两个名称，确保比较的一致性
            standardized_stored_name = device_manager.standardize_device_name(stored_name)
            standardized_current_name = device_manager.standardize_device_name(current_name)
            
            if standardized_stored_name != standardized_current_name:
                global_logger.info(f"Android设备 (UDID: {device_udid}) 设备名称从 '{standardized_stored_name}' 更新为 '{standardized_current_name}'")
                update_device_status(device_udid, {
                    'device_name': standardized_current_name,
                    'last_update': time.time()
                })
            else:
                global_logger.debug(f"Android设备 {device['name']} (UDID: {device_udid}) 设备名称无变化: {standardized_current_name}")
    
    global_logger.info("设备系统版本信息检查完成")
    
    # 启动心跳服务
    global_logger.info("启动心跳服务")
    try:
        # 启动新的心跳服务
        initialize_heartbeat_monitor()
        global_logger.info("心跳服务已启动，将每30分钟发送一次心跳通知")
    except Exception as e:
        global_logger.error(f"启动心跳服务时发生错误: {str(e)}")
        global_logger.error(traceback.format_exc())
    
    # 创建进程间通信对象
    result_queue = manager.Queue()
    stop_event = Event()
    processes = []
    
    try:
        # 重置日志服务处理记录，确保在开始测试前日志记录状态是最新的
        global_logger.info("重置日志服务处理记录，确保日志记录状态是最新的...")
        log_manager.reset_log_service_records()
        
        # 启动日志服务 - 保留处理记录，只处理新增日志
        global_logger.info("启动日志服务，将只处理新增日志...")
        log_manager.start_log_service()
        
        # 启动设备控制器进程 - 使用全局函数，但仅用于iOS设备
        controller_process = multiprocessing.Process(
            target=device_manager.device_start_controller,
            args=(devices_ios, [], device_start_queue) 
        )
        controller_process.name = "DeviceStartController"
        controller_process.start()
        global_logger.info("设备启动控制器进程已启动（仅用于iOS设备）")
        processes.append(controller_process)  # 将控制器进程添加到进程列表中
        
        # 修改iOS设备启动流程，等待控制器允许后再启动
        for idx, device in enumerate(devices_ios):
            # 初始化设备状态为"等待中" - 这里是设备物理文件创建的位置
            device_udid = device['udid']
            if not get_device_status(device_udid):
                global_logger.error(f"设备 {device['name']} (UDID: {device_udid}) 状态文件不存在，为首次进入测试，创建状态文件")
                # 使用device_status_manager创建设备状态文件
                create_device_status(
                    udid=device_udid,
                    device_name=device['name'],
                    platform='ios',
                    round_num=1,
                    session_round_num=1,
                    status='waiting',
                    last_update=time.time(),
                    system_version=device.get('version', ''),  # 添加系统版本信息
                )
                global_logger.info(f"已创建iOS设备状态文件: {device['name']} (UDID: {device_udid}), 状态: waiting, 系统版本: {device.get('version', '未知')}")
            else:
                global_logger.info(f"设备 {device['name']} (UDID: {device_udid}) 状态文件已存在，跳过创建状态文件，初始化对应的设备状态文件")
                initialize_device_status(device_udid)
            
            # 创建一个包装函数，等待控制器允许后再启动设备
            process = multiprocessing.Process(
                target=device_manager.start_ios_device_when_allowed,
                args=(
                    device,
                    Config.IOS_BASE_APPIUM_PORT + (idx * Config.IOS_APPIUM_PORT_INTERVAL),
                    Config.IOS_BASE_WDA_PORT + (idx * Config.IOS_WDA_PORT_INTERVAL),
                    result_queue,
                    stop_event,
                    device_start_queue,
                )
            )
            process.name = f"iOS_{device['name']}"
            processes.append(process)
            process.start()
        
        # 修改Android设备启动流程，直接启动测试进程
        for idx, device in enumerate(devices_android):
            global_logger.info(f"开始处理Android设备 {idx+1}/{len(devices_android)}: {device['name']} (UDID: {device['udid']})")
            
            # 初始化设备状态为"等待中" - 这里是设备物理文件创建的位置
            device_udid = device['udid']
            # 使用device_status_manager创建设备状态文件
            if not get_device_status(device_udid):
                global_logger.info(f"设备 {device['name']} (UDID: {device_udid}) 状态文件不存在，为首次进入测试，创建状态文件")
                device_status = create_device_status(
                    udid=device_udid,
                    device_name=device['name'],
                    platform='android',
                    round_num=1,
                    session_round_num=1,
                    status='waiting',
                    last_update=time.time(),
                    system_version=device.get('version', ''),  # 添加系统版本信息
                )
                global_logger.info(f"已创建Android设备状态文件: {device['name']} (UDID: {device_udid}), 状态: waiting, 系统版本: {device.get('version', '未知')}")
            else:
                global_logger.info(f"设备 {device['name']} (UDID: {device_udid}) 状态文件已存在，跳过创建状态文件，初始化对应的设备状态文件")
                initialize_device_status(device_udid)

            global_logger.info(f"正在创建Android设备进程: {device['name']} (UDID: {device_udid})")
            
            # 创建Android设备进程，直接启动测试
            try:
                process = multiprocessing.Process(
                    target=run_single_android_device,
                    args=(
                        device,
                        result_queue,
                        stop_event,
                    )
                )
                process.name = f"Android_{device['name']}"
                processes.append(process)
                global_logger.info(f"Android设备进程创建成功: {device['name']} (UDID: {device_udid})")
                
                global_logger.info(f"正在启动Android设备进程: {device['name']} (UDID: {device_udid})")
                
                # 启动进程
                process.start()
                
                # 检查进程是否真的启动了
                time.sleep(1)  # 等待1秒让进程有时间启动
                    
            except Exception as e:
                global_logger.error(f"创建或启动Android设备进程时发生错误: {device['name']} (UDID: {device_udid}), 错误: {str(e)}")
                global_logger.error(f"错误详情: {traceback.format_exc()}")
            
            # 只有多个设备时才需要等待间隔，第一个设备立即启动
            if idx < len(devices_android) - 1:  # 如果不是最后一个设备
                delay_time = Config.DEVICE_START_INTERVAL
                global_logger.info(f"等待 {delay_time} 秒后启动下一个Android设备...")
                time.sleep(delay_time)
        
        # 启动状态监控线程
        def monitor_device_status():
            global_logger.info("设备状态监控线程启动")
            status_check_interval = Config.STATUS_CHECK_INTERVAL  # 每300秒检查一次
            max_status_unchanged_time = Config.MAX_STATUS_UNCHANGED_TIME  # 状态最长不变时间（100分钟）
            
            while not stop_event.is_set():
                try:
                    # 打印当前设备状态
                    print_device_status()
                    
                    # 检查设备状态是否长时间未变化
                    current_time = time.time()
                    
                    # 检查所有初始设备的状态
                    all_device_udids = initial_devices_ios + initial_devices_android
                    for device_udid in all_device_udids:
                        status_info = get_device_status(device_udid)
                        if not status_info:
                            continue
                            
                        status = status_info.get('status', 'unknown')
                        last_update = status_info.get('last_update', 0)
                        
                        # 如果状态是"waiting"或"installing_wda"，并且长时间未变化
                        if status in ['waiting', 'installing_wda', 'wda_installed'] and current_time - last_update > max_status_unchanged_time:
                            # 检查设备进程是否还在运行
                            device_process_running = False
                            device_name = status_info.get('device_name', 'Unknown')
                            platform = status_info.get('platform', 'unknown')
                            process_name = f"{platform.capitalize()}_{device_name}"
                            
                            for p in processes:
                                if p.name == process_name and p.is_alive():
                                    device_process_running = True
                                    break
                            
                            if device_process_running:
                                global_logger.warning(f"设备 {device_name} (UDID: {device_udid}) 状态 '{status}' 已超过 {(current_time - last_update) / 60:.1f} 分钟未变化，但进程仍在运行")
                                
                                # 如果设备状态是"wda_installed"，但长时间未开始测试，尝试手动发送测试许可
                                if status == 'wda_installed':
                                    global_logger.info(f"尝试手动发送测试许可给设备 {device_name} (UDID: {device_udid})")
                                    device_start_queue.put(f"test_{device_udid}")
                            else:
                                global_logger.error(f"设备 {device_name} (UDID: {device_udid}) 状态 '{status}' 已超过 {(current_time - last_update) / 60:.1f} 分钟未变化，且进程已不在运行")
                    
                    # 等待下一次检查
                    time.sleep(status_check_interval)
                except Exception as e:
                    global_logger.error(f"设备状态监控线程发生错误: {str(e)}")
                    time.sleep(status_check_interval)
        
        # 启动状态监控线程
        status_monitor_thread = threading.Thread(target=monitor_device_status)
        status_monitor_thread.daemon = True
        status_monitor_thread.start()
        
        # 启动监控进程
        monitor_processes(processes, result_queue, stop_event, global_logger)
        
    except KeyboardInterrupt:
        global_logger.info("收到中断信号，正在停止所有测试...")
        stop_event.set()
    except Exception as e:
        global_logger.error(f"主进程发生错误: {str(e)}")
        stop_event.set()
    finally:
        # 停止所有进程
        stop_event.set()
        for process in processes:
            if process.is_alive():
                process.join(timeout=5)
                if process.is_alive():
                    process.terminate()
        
        # 停止日志服务
        log_manager.stop_log_service()
        
        # 停止心跳服务
        try:
            stop_heartbeat_monitor()
            global_logger.info("心跳服务已停止")
        except Exception as e:
            global_logger.error(f"停止心跳服务时发生错误: {str(e)}")
        
        global_logger.info("所有进程已停止，程序退出")

if __name__ == "__main__":
    # 确保主模块安全导入
    main()