import subprocess
import time

def get_device_info(device_id=None):
    """获取设备信息的关键命令"""
    # 基础命令 - 不需要设备ID
    base_commands = [
        ["adb", "devices"]
    ]
    
    # 最关键的设备信息命令
    device_specific_commands = [
        # 获取设备型号（这个通常是我们在设置里看到的型号名称）
        ["adb", "-s", device_id, "shell", "getprop", "ro.product.model"],
        # 获取设备品牌（如：HUAWEI/XIAOMI等）
        ["adb", "-s", device_id, "shell", "getprop", "ro.product.brand"],
        # 获取营销名称（如：Mate 30 Pro等）
        ["adb", "-s", device_id, "shell", "getprop", "ro.product.marketname"]
    ]
    
    device_info = {}
    
    # 获取设备列表
    try:
        result = subprocess.run(base_commands[0], capture_output=True, text=True)
        if result.stdout:
            # 解析已连接的设备
            lines = result.stdout.strip().split('\n')[1:]  # 跳过第一行
            devices = [line.split()[0] for line in lines if line.strip() and 'device' in line]
            device_info['connected_devices'] = devices
    except Exception as e:
        print(f"获取设备列表失败: {str(e)}")
        return None

    # 如果提供了设备ID，获取详细信息
    if device_id:
        for cmd in device_specific_commands:
            try:
                result = subprocess.run(cmd, capture_output=True, text=True)
                if result.stdout:
                    # 获取属性名（最后一个参数）
                    prop_name = cmd[-1].split('.')[-1]
                    device_info[prop_name] = result.stdout.strip()
            except Exception as e:
                print(f"获取设备信息失败: {str(e)}")
    
    return device_info

def print_device_info(device_info):
    """打印设备信息"""
    if not device_info:
        print("未能获取设备信息")
        return

    if 'connected_devices' in device_info:
        print("\n已连接的设备:")
        for device in device_info['connected_devices']:
            print(f"- {device}")
    
    if 'model' in device_info:
        print(f"\n设备型号: {device_info['model']}")
    if 'brand' in device_info:
        print(f"设备品牌: {device_info['brand']}")
    if 'marketname' in device_info:
        print(f"营销名称: {device_info['marketname']}")

# 截图并保存到设备内部存储
def take_screenshot():
    # 执行 adb 截图命令
    subprocess.run(["adb", "shell", "screencap", "-p", "/sdcard/screenshot.png"])

    # 将截图从设备拉取到本地
    subprocess.run(["adb", "pull", "/sdcard/screenshot.png", "screenshot.png"])

    print("截图已保存为 screenshot.png")

# 在指定坐标点击
def tap_screen(x, y):
    # 执行 adb 点击命令
    subprocess.run(["adb", "shell", "input", "tap", str(x), str(y)])
    print(f"在屏幕坐标 ({x}, {y}) 进行了点击")

# 示例：点击坐标 (500, 600)
# tap_screen(500, 600)
# take_screenshot()

def get_app_info(package_keyword=None):
    """获取已安装应用的包名和Activity信息
    Args:
        package_keyword: 应用包名关键字，如'meituan'
    """
    try:
        # 获取所有包含关键字的包名
        if package_keyword:
            cmd = f"adb shell pm list packages | grep {package_keyword}"
        else:
            cmd = "adb shell pm list packages"
        
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        packages = []
        
        if result.stdout:
            # 解析包名（去掉'package:'前缀）
            packages = [line.replace('package:', '').strip() 
                       for line in result.stdout.split('\n') 
                       if line.strip()]
            
        # 对每个包获取其当前活动的Activity
        for package in packages:
            # 先尝试启动应用
            subprocess.run(f"adb shell monkey -p {package} 1", shell=True)
            time.sleep(2)  # 等待应用启动
            
            # 获取当前活动的Activity
            cmd = "adb shell dumpsys window | grep mCurrentFocus"
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
            
            if result.stdout:
                print(f"\n包名: {package}")
                print(f"当前Activity: {result.stdout.strip()}")
            
            # 关闭应用
            subprocess.run(f"adb shell am force-stop {package}", shell=True)
            
    except Exception as e:
        print(f"获取应用信息失败: {str(e)}")

if __name__ == "__main__":
    # # 先获取所有设备列表
    # print("获取所有设备列表：")
    # all_devices = get_device_info()
    # print_device_info(all_devices)
    tap_screen(329, 759)
    
    # if all_devices and 'connected_devices' in all_devices:
    #     # 对每个连接的设备获取详细信息
    #     for device_id in all_devices['connected_devices']:
    #         print(f"\n获取设备 {device_id} 的详细信息：")
    #         device_info = get_device_info(device_id)
    #         print_device_info(device_info)

    # 获取美团应用信息
    # print("\n获取美团应用信息：")
    # get_app_info("m")

