#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
圆圈检测脚本
功能：检测图片中的所有圆圈，用红方框标记并输出中心点和半径信息
"""

import cv2
import numpy as np
import os


def find_best_circle_center(image_path):
    """
    查找图片中白色像素占比最高的圆的中心坐标
    
    默认筛选条件：
    - 圆度大于 0.85
    - 半径在 20-40 像素之间  
    - 霍夫变换参数: param1=50, param2=30
    - 返回白色像素占比最高的圆
    
    Args:
        image_path (str): 图片路径
    
    Returns:
        tuple or None: 
            - 如果找到符合条件的圆: (center_x, center_y)
            - 如果没有找到: None
    
    Example:
        center = find_best_circle_center("/path/to/image.jpg")
        if center:
            print(f"最佳圆的中心坐标: {center}")
        else:
            print("未找到符合条件的圆")
    """
    # 使用默认参数调用 detect_circles
    original_img, marked_img, circle_info = detect_circles(
        image_path=image_path,
        min_radius=20,           # 最小半径 20 像素
        max_radius=40,           # 最大半径 40 像素
        param1=50,               # 边缘检测阈值
        param2=30,               # 累加器阈值
        min_dist=30,             # 圆心最小距离
        circularity_threshold=0.85  # 圆度阈值 0.85
    )
    
    # 如果检测到圆圈，返回白色像素占比最高的圆的中心坐标
    if circle_info and len(circle_info) > 0:
        # circle_info 已经按白色像素占比排序，第一个就是最佳的
        best_circle = circle_info[0]
        center_x, center_y = best_circle['中心点']
        
        # 可选：打印一些基本信息
        print(f"找到最佳圆圈:")
        print(f"  中心坐标: ({center_x}, {center_y})")
        print(f"  半径: {best_circle['半径']} 像素")
        print(f"  圆度: {best_circle['圆度']}")
        print(f"  白色像素占比: {best_circle['白色像素占比']}%")
        
        return (center_x, center_y)
    else:
        print("未找到符合条件的圆圈")
        return None


def calculate_circularity(contour):
    """
    计算轮廓的圆度
    圆度 = 4π×面积 / 周长²
    完美圆形的圆度为1，值越接近1越圆
    
    Args:
        contour: OpenCV轮廓对象
    
    Returns:
        float: 圆度值，范围0-1
    """
    area = cv2.contourArea(contour)
    perimeter = cv2.arcLength(contour, True)
    
    if perimeter == 0:
        return 0
    
    circularity = 4 * np.pi * area / (perimeter * perimeter)
    return min(circularity, 1.0)  # 限制最大值为1


def calculate_white_pixel_ratio(image, center_x, center_y, radius, tolerance=5):
    """
    计算圆圈内白色像素的占比
    
    Args:
        image: 原始彩色图像
        center_x: 圆心x坐标
        center_y: 圆心y坐标  
        radius: 圆的半径
        tolerance: 白色的容差值，默认5（即250-255范围内都算白色）
    
    Returns:
        tuple: (白色像素占比, 总像素数, 白色像素数, 代表性颜色BGR)
    """
    # 创建圆形掩码
    mask = np.zeros(image.shape[:2], dtype=np.uint8)
    cv2.circle(mask, (center_x, center_y), radius, 255, -1)
    
    # 获取圆圈内的像素
    circle_pixels = image[mask == 255]
    
    if len(circle_pixels) == 0:
        return (0.0, 0, 0, (0, 0, 0))
    
    total_pixels = len(circle_pixels)
    
    # 定义白色范围：每个通道都在 (255-tolerance) 到 255 之间
    white_threshold = 255 - tolerance  # 默认是250
    
    # 统计白色像素 (OpenCV使用BGR格式)
    white_pixels = circle_pixels[
        (circle_pixels[:, 0] >= white_threshold) &  # B通道 >= 250
        (circle_pixels[:, 1] >= white_threshold) &  # G通道 >= 250  
        (circle_pixels[:, 2] >= white_threshold)    # R通道 >= 250
    ]
    
    white_count = len(white_pixels)
    white_ratio = white_count / total_pixels
    
    # 计算代表性颜色（所有像素的平均值，用于显示）
    avg_b = np.mean(circle_pixels[:, 0])
    avg_g = np.mean(circle_pixels[:, 1]) 
    avg_r = np.mean(circle_pixels[:, 2])
    representative_color = (int(avg_b), int(avg_g), int(avg_r))
    
    return (white_ratio, total_pixels, white_count, representative_color)


def calculate_dominant_color_ratio(image, center_x, center_y, radius, color_tolerance=13):
    """
    计算圆圈内占比最高的单一色值及其占比
    将相近的颜色合并为同一色值（RGB差值在tolerance范围内）
    
    Args:
        image: 原始彩色图像
        center_x: 圆心x坐标
        center_y: 圆心y坐标  
        radius: 圆的半径
        color_tolerance: 颜色容差，默认13 (约5%的RGB差值)
    
    Returns:
        tuple: (最大色值占比, 最大色值RGB, 总像素数, 该色值像素数)
    """
    # 创建圆形掩码
    mask = np.zeros(image.shape[:2], dtype=np.uint8)
    cv2.circle(mask, (center_x, center_y), radius, 255, -1)
    
    # 获取圆圈内的像素
    circle_pixels = image[mask == 255]
    
    if len(circle_pixels) == 0:
        return (0.0, (0, 0, 0), 0, 0)
    
    total_pixels = len(circle_pixels)
    
    # 颜色聚类：将相近的颜色合并
    color_clusters = {}
    
    for pixel in circle_pixels:
        b, g, r = pixel
        current_bgr = (int(b), int(g), int(r))
        
        # 查找是否有相近的颜色簇
        found_cluster = False
        for cluster_color in color_clusters.keys():
            # 计算颜色距离（欧几里得距离）
            distance = np.sqrt(
                (current_bgr[0] - cluster_color[0])**2 + 
                (current_bgr[1] - cluster_color[1])**2 + 
                (current_bgr[2] - cluster_color[2])**2
            )
            
            # 如果距离小于容差，归入该颜色簇
            if distance <= color_tolerance:
                color_clusters[cluster_color] += 1
                found_cluster = True
                break
        
        # 如果没有找到相近的颜色簇，创建新的颜色簇
        if not found_cluster:
            color_clusters[current_bgr] = 1
    
    # 找出占比最高的颜色
    if not color_clusters:
        return (0.0, (0, 0, 0), total_pixels, 0)
    
    # 找到像素数最多的颜色
    dominant_color = max(color_clusters.keys(), key=lambda x: color_clusters[x])
    dominant_count = color_clusters[dominant_color]
    dominant_ratio = dominant_count / total_pixels
    
    # 转换为RGB格式显示
    dominant_rgb = (dominant_color[2], dominant_color[1], dominant_color[0])
    
    return (dominant_ratio, dominant_rgb, total_pixels, dominant_count)


def detect_circles(image_path, min_radius=10, max_radius=200, param1=50, param2=30, min_dist=50, circularity_threshold=0.75):
    """
    检测图片中的圆圈，并使用圆度验证过滤结果
    
    Args:
        image_path (str): 图片路径
        min_radius (int): 最小半径
        max_radius (int): 最大半径  
        param1 (int): 霍夫变换参数1 (边缘检测阈值)
        param2 (int): 霍夫变换参数2 (累加器阈值)
        min_dist (int): 圆心之间的最小距离
        circularity_threshold (float): 圆度阈值，范围0-1，越接近1越严格
    
    Returns:
        tuple: (原图, 标记后图片, 圆圈信息列表)
    """
    # 检查文件是否存在
    if not os.path.exists(image_path):
        print(f"错误：图片文件不存在 - {image_path}")
        return None, None, []
    
    # 读取图片
    try:
        original_img = cv2.imread(image_path)
        if original_img is None:
            print(f"错误：无法读取图片 - {image_path}")
            return None, None, []
    except Exception as e:
        print(f"错误：读取图片时发生异常 - {e}")
        return None, None, []
    
    # 转换为灰度图
    gray = cv2.cvtColor(original_img, cv2.COLOR_BGR2GRAY)
    
    # 应用高斯模糊以减少噪声
    blurred = cv2.GaussianBlur(gray, (9, 9), 2)
    
    # 使用霍夫圆变换检测圆圈
    circles = cv2.HoughCircles(
        blurred,
        cv2.HOUGH_GRADIENT,
        dp=1,                    # 累加器分辨率与图像分辨率的反比
        minDist=min_dist,        # 圆心之间的最小距离
        param1=param1,           # 传递给Canny边缘检测的较高阈值
        param2=param2,           # 累加器阈值，较小的值会检测更多圆圈
        minRadius=min_radius,    # 最小半径
        maxRadius=max_radius     # 最大半径
    )
    
    # 复制原图用于标记
    marked_img = original_img.copy()
    circle_info = []
    
    if circles is not None:
        # 将圆圈参数转换为整数
        circles = np.round(circles[0, :]).astype("int")
        
        print(f"霍夫变换初步检测到 {len(circles)} 个候选圆圈")
        print("正在进行圆度验证...")
        print("-" * 50)
        
        # 创建掩码用于轮廓检测
        edges = cv2.Canny(blurred, param1//2, param1)
        
        valid_circles = []  # 存储通过圆度验证的圆圈
        
        for i, (x, y, r) in enumerate(circles):
            # 创建圆形区域掩码
            mask = np.zeros(gray.shape, dtype=np.uint8)
            cv2.circle(mask, (x, y), r, 255, -1)
            
            # 在圆形区域内查找轮廓
            masked_edges = cv2.bitwise_and(edges, mask)
            contours, _ = cv2.findContours(masked_edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            if contours:
                # 找到面积最大的轮廓
                largest_contour = max(contours, key=cv2.contourArea)
                
                # 计算圆度
                circularity = calculate_circularity(largest_contour)
                
                # 圆度验证
                if circularity >= circularity_threshold:
                    valid_circles.append((x, y, r, circularity))
        
        print(f"圆度验证完成，筛选出 {len(valid_circles)} 个有效圆圈 (阈值: {circularity_threshold})")
        print("正在计算白色像素占比并排序...")
        print("-" * 50)
        
        for i, (x, y, r, circularity) in enumerate(valid_circles):
            # 计算圆圈内白色像素占比
            white_ratio, total_pixels, white_count, avg_color = calculate_white_pixel_ratio(original_img, x, y, r, tolerance=5)
            
            # 计算圆圈内最大单一色值占比
            dominant_ratio, dominant_rgb, _, dominant_count = calculate_dominant_color_ratio(original_img, x, y, r, color_tolerance=13)
            
            # 收集圆圈信息
            circle_data = {
                '序号': i + 1,
                '中心点': (x, y),
                '半径': r,
                '直径': r * 2,
                '圆度': round(circularity, 3),
                '白色像素占比': round(white_ratio * 100, 2),  # 转换为百分比
                '总像素数': total_pixels,
                '白色像素数': white_count,
                '代表颜色BGR': avg_color,
                '代表颜色RGB': (avg_color[2], avg_color[1], avg_color[0]),  # 转换为RGB显示
                '最大单色占比': round(dominant_ratio * 100, 2),  # 转换为百分比
                '最大单色RGB': dominant_rgb,
                '最大单色像素数': dominant_count
            }
            circle_info.append(circle_data)
            
            # 暂时不在这里绘制标记，等排序后统一绘制
    else:
        print("未检测到任何圆圈")
    
    # 按照白色像素占比进行排序（占比越高排在前面）
    if circle_info:
        circle_info.sort(key=lambda x: x['白色像素占比'], reverse=True)
        
        # 只保留前10名
        top_10_circles = circle_info[:10]
        
        print("=" * 70)
        print(f"🔍 按白色像素占比排序的前{len(top_10_circles)}名 (占比从高到低):")
        print("=" * 70)
        
        # 绘制排序后的标记（只标注前10名）
        for i, circle in enumerate(top_10_circles):
            circle['排序序号'] = i + 1
            x, y = circle['中心点']
            r = circle['半径']
            
            # 在图片上画红色方框标记圆圈
            top_left = (x - r, y - r)
            bottom_right = (x + r, y + r)
            cv2.rectangle(marked_img, top_left, bottom_right, (0, 0, 255), 2)
            
            # 标记圆心
            cv2.circle(marked_img, (x, y), 3, (0, 255, 0), -1)
            
            # 添加排序序号标签（绿色表示按白色占比排序）
            cv2.putText(marked_img, f"#{i + 1}", (x - 15, y - r - 25), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
            # 添加白色占比信息
            cv2.putText(marked_img, f"{circle['白色像素占比']}%", (x - 25, y - r - 10), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 255, 0), 1)
            
            print(f"第 {i + 1} 名:")
            print(f"  原序号: {circle['序号']}")
            print(f"  中心点: {circle['中心点']}")
            print(f"  半径: {circle['半径']} 像素")
            print(f"  总像素数: {circle['总像素数']}")
            print(f"  白色像素数: {circle['白色像素数']} (阈值: RGB≥250)")
            print(f"  白色像素占比: {circle['白色像素占比']}% (排序依据)")
            print(f"  最大单色占比: {circle['最大单色占比']}% (RGB: {circle['最大单色RGB']})")
            print(f"  代表颜色RGB: {circle['代表颜色RGB']}")
            print()
        
        # 更新circle_info为只包含前10名
        circle_info = top_10_circles
    
    return original_img, marked_img, circle_info


def save_result(marked_img, output_path):
    """保存标记后的图片"""
    try:
        cv2.imwrite(output_path, marked_img)
        print(f"标记结果已保存到: {output_path}")
        return True
    except Exception as e:
        print(f"保存图片时发生错误: {e}")
        return False


def debug_circle_detection(image_path):
    """
    调试函数：详细输出圆圈检测的所有中间结果和排序信息
    Args:
        image_path (str): 图片路径
    """
    print("圆圈检测工具")
    print("=" * 30)
    print(f"分析图片: {image_path}")
    
    # 设置检测参数
    min_radius = 20          # 最小半径
    max_radius = 40         # 最大半径  
    param1 = 50              # 边缘检测阈值 (可调整范围: 30-100)
    param2 = 30              # 累加器阈值 (调低可检测更多圆，范围: 10-50)
    min_dist = 30            # 圆心最小距离 (调低可检测相邻圆)
    circularity_threshold = 0.88  # 圆度阈值，0.65更宽松，适合真实图片
    show_result = True       # 显示结果
    
    print(f"圆度阈值设置为: {circularity_threshold} (值越大越严格)")
    print()
    
    # 检测圆圈
    original_img, marked_img, circle_info = detect_circles(
        image_path, min_radius, max_radius, param1, param2, min_dist, circularity_threshold
    )
    
    # 同时按两种方式排序并显示
    if marked_img is not None and circle_info:
        print("\n" + "="*70)
        print("📊 按最大单一色值占比排序的前10名:")
        print("="*70)
        
        # 按最大单一色值占比排序
        circle_info_by_dominant = sorted(circle_info, key=lambda x: x['最大单色占比'], reverse=True)[:10]
        
        for i, circle in enumerate(circle_info_by_dominant):
            print(f"第 {i + 1} 名 (按单色占比):")
            print(f"  原序号: {circle['序号']}")
            print(f"  中心点: {circle['中心点']}")
            print(f"  半径: {circle['半径']} 像素")
            print(f"  最大单色占比: {circle['最大单色占比']}% (RGB: {circle['最大单色RGB']}) 🎯")
            print(f"  白色像素占比: {circle['白色像素占比']}%")
            print(f"  代表颜色RGB: {circle['代表颜色RGB']}")
            print()
    
    # 如果检测结果太少，尝试更宽松的参数
    if marked_img is not None and len(circle_info) < 3:
        print(f"\n当前参数检测到{len(circle_info)}个圆圈，尝试更宽松的参数...")
        print("=" * 50)
        
        # 更宽松的参数组合
        looser_param1 = 40
        looser_param2 = 15
        looser_min_dist = 20
        looser_circularity = 0.5
        
        original_img2, marked_img2, circle_info2 = detect_circles(
            image_path, min_radius, max_radius, looser_param1, looser_param2, 
            looser_min_dist, looser_circularity
        )
        
        # 如果新参数检测到更多圆圈，使用新结果
        if marked_img2 is not None and len(circle_info2) > len(circle_info):
            print(f"宽松参数检测到{len(circle_info2)}个圆圈，使用新结果")
            marked_img = marked_img2
            circle_info = circle_info2
        else:
            print("宽松参数没有改善结果，保持原始检测结果")
    
    if marked_img is None:
        return
    
    # 生成输出文件名
    base_name = os.path.splitext(image_path)[0]
    output_path = f"{base_name}_circles_detected.jpg"
    
    # 保存结果
    save_result(marked_img, output_path)
    
    # 显示结果
    if show_result:
        try:
            # 调整图片大小以适应屏幕
            height, width = marked_img.shape[:2]
            if width > 1200 or height > 800:
                scale = min(1200/width, 800/height)
                new_width = int(width * scale)
                new_height = int(height * scale)
                display_img = cv2.resize(marked_img, (new_width, new_height))
            else:
                display_img = marked_img
            
            cv2.imshow('圆圈检测结果', display_img)
            print("\n按任意键关闭图片窗口...")
            cv2.waitKey(0)
            cv2.destroyAllWindows()
        except Exception as e:
            print(f"显示图片时发生错误: {e}")
    
    # 输出总结
    print(f"\n" + "="*80)
    print("🎉 检测完成！")
    print("="*80)
    print(f"输入图片: {image_path}")
    print(f"输出图片: {output_path}")
    print(f"显示结果: 两种排序方式的前10名圆圈")
    if circle_info:
        # 获取两种排序的冠军
        white_champion = circle_info[0]  # 白色占比冠军
        dominant_champion = max(circle_info, key=lambda x: x['最大单色占比'])  # 单色占比冠军
        
        print(f"\n🏆 白色占比冠军: 中心点{white_champion['中心点']}，白色占比{white_champion['白色像素占比']}%")
        print(f"🎯 单色占比冠军: 中心点{dominant_champion['中心点']}，单色占比{dominant_champion['最大单色占比']}% (RGB:{dominant_champion['最大单色RGB']})")
        print(f"\n📝 说明:")
        print("  • 图片上标注的是按白色像素占比排序的前10名")
        print("  • 白色判定标准: RGB每个通道都≥250 (允许5像素偏差)")
        print("  • 单色统计时相近颜色会合并 (RGB差值≤13)")
        print("  • 绿色序号和百分比表示白色占比排序")


def main():
    """主函数"""
    # 直接指定图片路径
    pic_path = "/Users/<USER>/Desktop/work/platform_autotest_frame_python/photo/小米 10 天天现金.webp"
    ans = find_best_circle_center(pic_path)
    print(ans)


if __name__ == "__main__":
    main()
