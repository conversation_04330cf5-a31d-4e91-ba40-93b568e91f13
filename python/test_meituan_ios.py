import time
import logging
import subprocess
from appium import webdriver
from appium.options.ios import XCUITestOptions
import os
from PIL import Image
from python.heartbeat_monitor import log_device_issue, ISSUE_TYPE_APP, ISSUE_TYPE_TEST, initialize_issue_logger
from python.config import Config
import traceback
from python.device_common import device_operate_external
from python.swipe_homepage import swipe_homepage_recommend
from python.tap_main_icons import tap_main_icons
from python.tap_all_icons import tap_all_icons
from python.device_status_manager import get_device_status, update_device_status, get_device_wda_port

# 从Config类导入常量
TARGET_ICONS = Config.TARGET_ICONS
HOMEPAGE_TARGET_ICONS = Config.HOMEPAGE_TARGET_ICONS

# 问题类型常量
ISSUE_TYPE_APP = "app"
ISSUE_TYPE_TEST = "test"

# 美团App的bundleId常量
MEITUAN_BUNDLE_ID = 'com.meituan.imeituan'

DEFAULT_LOGGER = logging.getLogger(__name__)

# 添加全局字典来跟踪设备测试问题
device_issues = {}
    
def check_ios_device_online(udid, logger):
    """检查 iOS 设备是否在线"""
    try:
        output = subprocess.check_output(["idevice_id", "-l"], universal_newlines=True)
        if not output:
            logger.warning("没有检测到任何 iOS 设备")
            return False
        connected_devices = output.strip().split('\n')
        return udid in connected_devices
    except Exception as e:
        logger.error(f"检查 iOS 设备状态时发生错误: {e}")
        return False

def test_meituan_ios(udid, appium_url, wda_port, logger = None):
    """
    美团App测试函数
    """
    if logger is None:
        logger = DEFAULT_LOGGER

    # 记录测试开始时间
    start_time = time.time()
    device_id = udid # device_id需要在finally之外定义
    device_status = get_device_status(udid)
    device_name = device_status['device_name']
    system_version = device_status['system_version']

    # 从设备状态中获取实际的WDA转发端口，如果获取失败则使用传入的端口
    actual_wda_port = get_device_wda_port(udid)
    if actual_wda_port:
        wda_port = actual_wda_port
        logger.info(f"从设备状态获取WDA转发端口: {wda_port}")
    else:
        logger.warning(f"无法从设备状态获取WDA端口，使用传入端口: {wda_port}")

    # 记录当前使用的端口信息
    logger.info(f"测试使用端口 - Appium: {appium_url}, WDA转发端口: {wda_port}")

    # 初始化本轮测试的问题标记，确保在 try 块之前，以便 finally 中可用
    # 如果 try 块完全失败，device_issues 可能未初始化
    if device_id not in device_issues:
        device_issues[device_id] = {
            'app_issue': False,
            'test_issue': False
        }
    
    # 用于finally块统计的变量
    is_special_round_executed = False
    final_completed_actions_for_round = 0
    final_total_actions_for_round = 0
    # 常规流程的计数器，如果走了常规流程，其值会被赋给 final_completed_actions_for_round
    completed_icons_main_loop = 0
    
    try:
        
        # 首先确保问题日志记录器已初始化，在所有操作之前完成这一步
        script_dir = os.path.dirname(os.path.abspath(__file__))
        project_root = os.path.dirname(script_dir)
        initialize_issue_logger(project_root)
        
        # 从设备状态中获取当前轮次信息（现在由split_devices.py管理）
        status_info = get_device_status(device_id)
        session_round_num = status_info.get('session_round_num', 1)
        current_round = status_info.get('round_num', 1)
        
        # 记录轮次信息日志
        logger.info(f"开始第 {session_round_num} 轮测试 (历史轮次: {current_round})")
        
        # 问题日志记录器初始化完成日志
        logger.info(f"问题日志记录器初始化完成 - 进程ID: {os.getpid()}")
        
        # 生成当前测试的唯一执行ID - 每轮测试生成一次，整个函数内复用
        execution_id = f"{device_id}_{current_round}_{int(start_time)}"
        logger.info(f"生成测试执行ID: {execution_id}")
        
        total_icons_main_loop = Config.get_actual_icon_count()  # 使用Config类的方法获取实际图标数量
        
        update_device_status(device_id, {
            'status': 'running',
            'last_update': time.time(),
            'session_round_num': session_round_num
        })
        
        logger.info(f"开始测试设备：{device_name} (UDID: {udid}, iOS {system_version}) - 第 {session_round_num} 轮（历史轮次: {current_round}）")

        # 设备在线检测现在由split_devices.py统一处理，这里不再需要检测
        logger.info(f"设备 {device_name} (UDID: {udid}) 已通过预检查，开始测试流程")

        # 动态获取当前文件的上级目录，并创建设备专属的截图目录
        current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))  # 获取上级目录
        device_screenshot_dir = os.path.join(current_dir, 'screenshot', device_name)
        device_error_dir = os.path.join(device_screenshot_dir, 'error')
        os.makedirs(device_screenshot_dir, exist_ok=True)
        os.makedirs(device_error_dir, exist_ok=True)
        update_device_status(device_id, {
            'device_screenshot_dir': device_screenshot_dir,
            'device_error_dir': device_error_dir
        })

        # 配置 XCUITestOptions
        options = XCUITestOptions()
        options.device_name = device_name
        options.platform_version = system_version
        options.udid = udid
        options.automation_name = 'XCUITest'
        options.bundle_id = MEITUAN_BUNDLE_ID
        options.no_reset = True

        # 对于新的go-ios工具链，不再使用wda_local_port，而是通过webDriverAgentUrl指定
        # WDA服务已经通过ios forward在指定端口上运行
        options.web_driver_agent_url = f"http://127.0.0.1:{wda_port}"

        # 使用预构建的WDA，跳过构建过程
        options.use_prebuilt_wda = True
        
        # 防止Appium尝试启动WDA，因为我们已经通过ios工具启动了WDA
        options.use_new_wda = False
        options.wda_startup_retries = 0
        options.wda_startup_retry_interval = 0

        # 记录驱动创建时间
        driver_created_time = time.time()
        update_device_status(device_id, {'driver_created_time': driver_created_time})

        logger.info(f"连接 Appium 服务器：{appium_url}")
        logger.info(f"使用 WDA 服务地址：http://127.0.0.1:{wda_port}")

        driver = webdriver.Remote(appium_url, options = options)
        
        try:
            # 重启 app
            time.sleep(5)
            logger.info("重启美团应用...")
            device_operate_external.restart_app(
                driver=driver,
                udid=udid,
                logger=logger
            )
            time.sleep(10)
            
            # 获取屏幕尺寸和缩放比例（用于设备状态更新）
            screen_size = driver.get_window_size()
            logger.info(f"实际屏幕尺寸: {screen_size}")

            # 临时截图来计算缩放比例
            timestamp=time.strftime('%Y-%m-%d %H:%M:%S')
            temp_screenshot_path = os.path.join(device_screenshot_dir, f'临时_缩放比例计算_{device_name}_{timestamp}.png')
            screenshot_success = device_operate_external.take_screenshot(
                driver=driver,
                udid=udid,
                screenshot_path=temp_screenshot_path,
                logger=logger
            )
            if screenshot_success == "success":
                screenshot_image = Image.open(temp_screenshot_path)
                screenshot_size = screenshot_image.size
                logger.info(f"截图尺寸: {screenshot_size}")

                # 更新设备状态中的 scale_ratio 值
                width_ratio = screenshot_size[0] / screen_size['width']
                height_ratio = screenshot_size[1] / screen_size['height']
                scale_ratio = (width_ratio + height_ratio) / 2
                logger.info(f"计算得到新的缩放比例: {scale_ratio}")
                
                # 保存新计算的scale_ratio到设备状态
                update_device_status(device_id, {
                    'status': 'running',
                    'last_update': time.time(),
                    'scale_ratio': scale_ratio
                })
                
                # 删除临时截图
                os.remove(temp_screenshot_path)
            else:
                logger.warning("临时截图失败，使用默认缩放比例")
            
            logger.info("准备开始具体的测试流程...")
            
            # 特殊测试流程：第9轮执行推荐测试，第10轮执行全量图标测试
            if current_round % 10 == 9:
                is_special_round_executed = True
                logger.info(f"当前历史轮次 {current_round} 是第9轮，开始执行【额外首页测试】：点击「推荐」触发对应操作")

                # 执行推荐点击测试
                recommend_completed, recommend_total = swipe_homepage_recommend(
                    driver=driver,
                    udid=udid,
                    logger=logger,
                    execution_id=execution_id,
                    device_issues=device_issues
                )
                
                # 记录推荐测试结果
                final_total_actions_for_round = recommend_total
                final_completed_actions_for_round = recommend_completed
                
            elif current_round % 10 == 0:
                is_special_round_executed = True
                logger.info(f"当前历史轮次 {current_round} 是第10轮，开始执行【全量金刚区图标测试】：获取并测试所有频道图标")
                
                completed, total, screen_count, all_screen_icons_list = tap_all_icons(
                    driver=driver,
                    udid=udid,
                    logger=logger,
                    execution_id=execution_id,
                    device_issues=device_issues,
                    perform_ui_check=True
                )
                final_completed_actions_for_round = completed
                logger.info(f"全量金刚区图标测试完成，共测试 {total} 个图标，成功完成 {screen_count} 屏")

            if not is_special_round_executed:
                # 设置常规流程的总操作数
                final_total_actions_for_round = total_icons_main_loop
                
                # 使用新的tap_main_icons函数进行固定图标测试
                logger.info(f"开始执行【常规测试】：测试美团首页主要频道图标")
                completed_icons_main_loop, total_icons_main_loop = tap_main_icons(
                    driver=driver,
                    udid=udid,
                    logger=logger,
                    execution_id=execution_id,
                    device_issues=device_issues
                )
                
                # 将常规流程的完成数赋值给最终变量
                final_completed_actions_for_round = completed_icons_main_loop
                
        except Exception as e:
            logger.error(f"测试过程中发生全局错误: {e}")
            # 标记本轮测试有测试流程问题
            device_issues[device_id]['test_issue'] = True
            
            # 获取详细的错误信息和堆栈跟踪
            error_traceback = traceback.format_exc()
            logger.error(f"全局错误详情: {error_traceback}")
            
            # 提取更有用的错误信息
            error_type = type(e).__name__
            error_message = str(e)
            
            # 只保留到 stacktrace 之前的错误信息
            if "Stacktrace:" in error_message:
                error_message = error_message.split("Stacktrace:")[0].strip()
            
            # 根据错误类型提供更具体的错误描述
            error_description = f"{error_type}: {error_message}"
            if "timeout" in error_message.lower():
                error_description = f"操作超时: {error_message}"
            elif "no such element" in error_message.lower():
                error_description = f"未找到元素: {error_message}"
            elif "stale element reference" in error_message.lower():
                error_description = f"元素已过期: {error_message}"
            
            # 记录问题
            log_device_issue(
                device_id=device_id,
                device_name=device_name,
                round_num=current_round,
                issue_type=ISSUE_TYPE_TEST,
                issue_details=f"测试过程中发生全局错误: {error_description}",
                execution_id=execution_id
            )
            
    finally:
        # 计算测试耗时
        test_duration = time.time() - start_time
        logger.info(f"测试完成 - 第 {session_round_num} 轮（历史轮次: {current_round}），耗时: {test_duration:.2f} 秒")
        
        # 确保驱动退出
        try:
            driver.quit()
        except:
            pass
        
        # 获取当前轮次的问题状态
        app_issue = device_issues[device_id].get('app_issue', False)
        test_issue = device_issues[device_id].get('test_issue', False)

        status = ''
        # 根据测试问题状态设置测试状态
        if test_issue or app_issue or final_completed_actions_for_round < final_total_actions_for_round:
            status = 'partial_completed'
        else:
            status = 'completed'
        
        log_message_status_type = "额外首页测试轮次" if is_special_round_executed else "常规测试轮次"
        logger.info(f"更新设备状态({log_message_status_type})为 {status}: {device_name}, 第 {session_round_num} 轮（历史轮次: {current_round}）, 耗时: {test_duration:.2f}秒, 应用问题: {app_issue}, 测试问题: {test_issue}, 完成操作: {final_completed_actions_for_round}/{final_total_actions_for_round}")
        
        # 更新设备状态
        update_device_status(device_id, {
            'device_name': device_name,
            'round_num': current_round,
            'status': status,
            'test_duration': test_duration,
            'app_issue': app_issue,
            'test_issue': test_issue,
            'session_round_num': session_round_num,
            'last_update': time.time()
        })
        
        # 重置本轮测试的问题标记
        device_issues[device_id] = {
            'app_issue': False,
            'test_issue': False
        }
        
        # 简单检查设备是否仍在线（不进行重连，由split_devices.py统一处理）
        if not check_ios_device_online(udid, logger):
            logger.warning(f"测试完成后发现设备 {device_name} (UDID: {udid}) 已离线")
            update_device_status(device_id, {
                'status': 'offline_after_test',
                'last_update': time.time()
            })
        else:
            logger.info(f"测试完成后设备 {device_name} (UDID: {udid}) 仍在线")

        # 在所有测试完成后清理截图（不管是常规测试还是特殊测试）
        try:
            screenshot_count = 0
            for filename in os.listdir(device_screenshot_dir):
                file_path = os.path.join(device_screenshot_dir, filename)
                # 只删除文件（不删除目录），且不处理error目录
                if os.path.isfile(file_path) and 'error' not in file_path:
                    os.remove(file_path)
                    screenshot_count += 1
            logger.info(f"本轮测试清理完成：已删除设备 {device_name} 的 {screenshot_count} 张非error截图")
        except Exception as e:
            logger.error(f"清理截图时发生错误: {e}")

        # 创建并返回TestResult对象
        from python.split_devices import TestResult
        test_result = TestResult(
            device_id=device_id,
            device_name=device_name,
            platform='ios',
            timestamp=time.strftime('%Y-%m-%d %H:%M:%S'),
            status=status,
            error_message=None if status == 'completed' else "测试未完全完成",
            test_duration=test_duration,
            app_issue=app_issue,
            test_issue=test_issue,
            completed_icons=final_completed_actions_for_round, # 使用新的统计变量
            total_icons=final_total_actions_for_round # 使用新的统计变量
        )
        
        return test_result