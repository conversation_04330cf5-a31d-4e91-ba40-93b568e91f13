#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
iOS设备重启脚本
使用idevice_id获取所有连接的iOS设备，然后使用idevicediagnostics重启每个设备
"""

import subprocess
import sys
import time


def run_command(command):
    """
    执行shell命令并返回输出
    
    Args:
        command (str): 要执行的命令
    
    Returns:
        tuple: (是否成功, 输出内容)
    """
    try:
        result = subprocess.run(
            command,
            shell=True,
            capture_output=True,
            text=True,
            timeout=30
        )
        if result.returncode == 0:
            return True, result.stdout.strip()
        else:
            return False, result.stderr.strip()
    except subprocess.TimeoutExpired:
        return False, "命令执行超时"
    except Exception as e:
        return False, f"执行命令时出错: {e}"


def get_ios_devices():
    """
    获取所有连接的iOS设备ID
    
    Returns:
        list: iOS设备ID列表
    """
    print("正在查找连接的iOS设备...")
    success, output = run_command("idevice_id")
    
    if not success:
        print(f"❌ 获取设备列表失败: {output}")
        print("请确保已安装libimobiledevice工具包")
        return []
    
    if not output:
        print("❌ 未找到连接的iOS设备")
        return []
    
    # 清理设备ID，去掉括号及其内容（如 (USB)）
    devices = []
    raw_devices = [device.strip() for device in output.split('\n') if device.strip()]
    
    for device in raw_devices:
        # 如果设备ID包含括号，只取括号前的部分
        clean_device_id = device.split('(')[0].strip()
        if clean_device_id:  # 确保不是空字符串
            devices.append(clean_device_id)
    
    print(f"✅ 找到 {len(devices)} 个iOS设备:")
    for i, device in enumerate(devices, 1):
        # 显示原始信息和清理后的ID
        original_device = raw_devices[i-1] if i-1 < len(raw_devices) else device
        if device != original_device:
            print(f"  {i}. {device} (原始: {original_device})")
        else:
            print(f"  {i}. {device}")
    
    return devices


def restart_device(device_id):
    """
    重启指定的iOS设备
    
    Args:
        device_id (str): 设备ID
    
    Returns:
        bool: 是否重启成功
    """
    print(f"正在重启设备 {device_id}...")
    command = f"idevicediagnostics -u {device_id} restart"
    success, output = run_command(command)
    
    if success:
        print(f"✅ 设备 {device_id} 重启命令发送成功")
        return True
    else:
        print(f"❌ 设备 {device_id} 重启失败: {output}")
        return False


def main():
    """
    主函数
    """
    print("=" * 50)
    print("iOS设备批量重启工具")
    print("=" * 50)
    
    # 检查必要的工具是否安装
    print("检查libimobiledevice工具...")
    
    # 检查idevice_id是否可用
    success, _ = run_command("which idevice_id")
    if not success:
        print("❌ idevice_id命令未找到，请安装libimobiledevice")
        print("安装方法: brew install libimobiledevice")
        sys.exit(1)
    
    # 检查idevicediagnostics是否可用
    success, _ = run_command("which idevicediagnostics")
    if not success:
        print("❌ idevicediagnostics命令未找到，请安装libimobiledevice")
        print("安装方法: brew install libimobiledevice")
        sys.exit(1)
    
    print("✅ libimobiledevice工具检查通过")
    print()
    
    # 获取所有iOS设备
    devices = get_ios_devices()
    
    if not devices:
        sys.exit(1)
    
    print()
    
    # 确认是否继续
    try:
        confirm = input(f"确定要重启以上 {len(devices)} 个设备吗？(y/N): ").strip().lower()
        if confirm not in ['y', 'yes', '是']:
            print("操作已取消")
            sys.exit(0)
    except KeyboardInterrupt:
        print("\n操作已取消")
        sys.exit(0)
    
    print()
    print("开始重启设备...")
    print("-" * 30)
    
    # 重启每个设备
    success_count = 0
    failed_devices = []
    
    for i, device_id in enumerate(devices, 1):
        print(f"[{i}/{len(devices)}] ", end="")
        if restart_device(device_id):
            success_count += 1
            # 等待一下再继续下一个设备
            if i < len(devices):
                time.sleep(2)
        else:
            failed_devices.append(device_id)
        print()
    
    # 显示结果统计
    print("-" * 30)
    print("重启完成!")
    print(f"成功: {success_count}/{len(devices)}")
    
    if failed_devices:
        print(f"失败的设备:")
        for device in failed_devices:
            print(f"  - {device}")
    
    print()
    print("注意: 设备重启需要一些时间，请等待设备完全重启后再进行其他操作。")


if __name__ == "__main__":
    main() 