import time
import os
from python.device_common import (
    get_screenshot_test, device_operate_external,get_screenshot_ocr_elements, check_is_homepage
)
from python.heartbeat_monitor import log_device_issue
from python.device_status_manager import get_device_status, update_device_status, manager

def swipe_homepage_recommend(
    driver=None,
    udid=None,
    logger=None,
    execution_id=None,
    device_issues=None,
):
    """
    首页推荐点击测试：点击"推荐"位置两次，预期第一次下滑进入猜你喜欢模块，第二次返回首页初始状态
    
    Args:
        driver: Appium driver对象（iOS需要）
        udid: 设备UDID
        device_screenshot_dir: 截图保存目录
        device_error_dir: 错误截图保存目录
        logger: 日志对象
        execution_id: 执行ID
        device_issues: 设备问题跟踪字典
    
    Returns:
        tuple: (completed_actions, total_actions) - 完成的操作数和总操作数
    """
    if logger is None:
        import logging
        logger = logging.getLogger(__name__)
    if udid is None:
        raise ValueError("udid 参数不能为空")
    if device_issues is None:
        raise ValueError("device_issues 参数不能为空")
    
    # 推荐测试视为 2 个icon
    total_actions = 2  # 两次点击操作
    completed_actions = 0
    
    # 累加推荐测试操作总数到设备状态
    success = manager.add_icon_count(udid, total_add=total_actions, completed_add=0)
    if success:
        logger.info(f"推荐测试累加图标总数: {total_actions}，当前设备累计要测试图标数已更新")
    else:
        logger.warning(f"更新设备累计图标总数失败")
    current_round = get_device_status(udid).get('round_num')
    session_round_num = get_device_status(udid).get('session_round_num')
    scale_ratio = get_device_status(udid).get('scale_ratio')
    device_name = get_device_status(udid).get('device_name')
    device_screenshot_dir = get_device_status(udid).get('device_screenshot_dir')
    device_error_dir = get_device_status(udid).get('device_error_dir')
    
    logger.info(f"开始执行【额外首页测试】：点击「推荐」触发对应操作")
    
    # 首先检测是否到了首页
    from python.device_common import go_back_homepage
    homepage_success = go_back_homepage(
        driver=driver,
        udid=udid,
        logger=logger,
        execution_id=execution_id,
        device_issues=device_issues,
        test_context_name="推荐测试"
    )
    
    if not homepage_success:
        logger.error("推荐测试：无法确保在美团首页，终止测试")
        return 0, total_actions
    
    # 自己完成首页截图和OCR识别
    logger.info("开始获取首页截图并进行OCR识别以查找「推荐」文本...")
    timestamp = time.strftime('%Y%m%d_%H%M%S')
    homepage_screenshot_path = os.path.join(device_screenshot_dir, f'推荐测试首页_{device_name}_{timestamp}.png')
    
    try:
        page_elements = get_screenshot_ocr_elements(
            udid=udid,
            driver=driver,
            logger=logger,
            screenshot_path=homepage_screenshot_path
        )
        if not page_elements:
            logger.error("获取首页元素失败，无法继续推荐测试")
            device_issues[udid]['test_issue'] = True
            return 0, total_actions
        logger.info(f"成功获取首页元素，共 {len(page_elements)} 个")
    except Exception as e:
        logger.error(f"获取首页截图和OCR识别失败: {e}")
        device_issues[udid]['test_issue'] = True
        return 0, total_actions
    
    # 1. 查找"推荐"文本坐标
    recommend_coords_original = None
    if page_elements:
        for element_text, coords_tuple in page_elements:
            if element_text == "推荐":
                recommend_coords_original = coords_tuple
                logger.info(f"额外首页测试：在首页元素中找到「推荐」文本，原始坐标: {recommend_coords_original}")
                break
    
    if not recommend_coords_original:
        logger.warning("额外首页测试：未在首页元素中找到「推荐」文本，跳过操作测试。")
        # 记录测试问题
        log_device_issue(
            device_id=udid,
            device_name=device_name,
            round_num=current_round,
            issue_type='test',
            issue_details="额外首页测试：未找到'推荐'文本",
            execution_id=execution_id
        )
        device_issues[udid]['test_issue'] = True
        return 0, total_actions  # 应该返回完整的总操作数，保持统计一致性
    
    # 2. 计算缩放后坐标
    original_x, original_y = recommend_coords_original
    scaled_x = original_x / scale_ratio
    scaled_y = original_y / scale_ratio
    logger.info(f"额外首页测试：「推荐」文本的缩放后坐标: ({scaled_x}, {scaled_y})")
    
    # 3. 第一次点击"推荐"
    logger.info(f"额外首页测试：第一次点击「推荐」位置于 ({int(scaled_x)}, {int(scaled_y)})")
    tap_success = device_operate_external.tap(
        driver=driver,
        udid=udid,
        x=scaled_x,
        y=scaled_y,
        logger=logger
    )
    if not tap_success:
        logger.error("额外首页测试：第一次点击「推荐」失败")
        return 0, total_actions
    
    logger.info("额外首页测试：第一次点击「推荐」位置，预期页面下滑进入猜你喜欢模块。")
    time.sleep(10)  # 等待页面加载
    
    # 4. 第一次截图和检查
    timestamp1 = time.strftime('%Y%m%d_%H%M%S')
    safe_name = get_device_status(udid).get('device_name')
    screenshot_path1 = os.path.join(device_screenshot_dir, f'额外首页测试_下滑进入猜喜_{safe_name}_{timestamp1}.png')
    
    logger.info("额外首页测试：调用 get_screenshot_test 进行第一次截图和检查...")
    success1 = get_screenshot_test(
        driver=driver,
        udid=udid,
        screenshot_path=screenshot_path1,
        logger=logger,
        execution_id=execution_id,
        page_name="额外首页测试_下滑进入猜喜",
        device_error_dir=device_error_dir,
        perform_ui_check=True,
        device_issues=device_issues,
    )
    
    if not success1:
        logger.error("额外首页测试：第一次截图和检查失败")
        return 0, total_actions
    
    time.sleep(5)  # 等待5秒
    
    # 5. 第二次点击"推荐"
    logger.info(f"额外首页测试：第二次点击「推荐」位置于 ({int(scaled_x)}, {int(scaled_y)})")
    tap_success = device_operate_external.tap(
        driver=driver,
        udid=udid,
        x=scaled_x,
        y=scaled_y,
        logger=logger
    )
    if not tap_success:
        logger.error("额外首页测试：第二次点击「推荐」失败")
        return 1, total_actions  # 第一次成功，第二次失败
    
    logger.info("额外首页测试：第二次点击「推荐」位置，预期返回首页初始状态。")
    time.sleep(10)  # 等待页面加载
    
    # 6. 第二次截图和检查
    timestamp2 = time.strftime('%Y%m%d_%H%M%S')
    screenshot_path2 = os.path.join(device_screenshot_dir, f'额外首页测试_返回首页初始状态_{safe_name}_{timestamp2}.png')
    
    logger.info("额外首页测试：调用 get_screenshot_test 进行第二次截图和检查...")
    success2 = get_screenshot_test(
        driver=driver,
        udid=udid,
        screenshot_path=screenshot_path2,
        logger=logger,
        execution_id=execution_id,
        page_name="额外首页测试_返回首页初始状态",
        device_error_dir=device_error_dir,
        perform_ui_check=True,
        device_issues=device_issues,
    )
    
    if not success2:
        logger.error("额外首页测试：第二次截图和检查失败")
        return 1, total_actions  # 第一次成功，第二次失败
    
    # 7. 根据本轮测试中是否有问题来决定完成的操作数
    if not device_issues[udid]['app_issue'] and not device_issues[udid]['test_issue']:
        completed_actions = 2
    else:
        completed_actions = 0  # 如果有任何问题，则认为特殊任务未完全成功
    
    # 累加完成的操作数到设备状态
    if completed_actions > 0:
        success = manager.add_icon_count(udid, total_add=0, completed_add=completed_actions)
        if success:
            logger.info(f"推荐测试成功完成 {completed_actions} 个操作，累计完成图标数已更新")
        else:
            logger.warning(f"更新设备累计完成图标数失败")
    
    logger.info("额外首页测试：点击「推荐」位置的两次操作测试完成。")
    
    return completed_actions, total_actions 