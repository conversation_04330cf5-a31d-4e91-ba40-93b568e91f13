#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import re
import time
import json
import signal
import logging
import argparse
import requests
import subprocess
from pathlib import Path
from datetime import datetime, timezone, timedelta
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler
import sys
from logging.handlers import TimedRotatingFileHandler, RotatingFileHandler
import env_config  # 导入环境配置模块

# 获取当前脚本的绝对路径
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
# 获取项目根目录（当前脚本所在目录的上一级）
PROJECT_ROOT = os.path.dirname(SCRIPT_DIR)
# 设置日志根目录
LOG_ROOT = os.path.join(PROJECT_ROOT, "log")

# 确保日志目录存在
os.makedirs(os.path.join(LOG_ROOT, "python_logs"), exist_ok=True)
os.makedirs(os.path.join(LOG_ROOT, "compress_logs"), exist_ok=True)
# 创建已处理日志目录
PROCESSED_DIR = os.path.join(LOG_ROOT, "processed_logs")
os.makedirs(PROCESSED_DIR, exist_ok=True)

# 配置日志
LOG_FILE = os.path.join(LOG_ROOT, "python_logs", "log_service.log")
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        # 使用RotatingFileHandler基于大小进行轮转，而非基于时间
        RotatingFileHandler(
            LOG_FILE,
            maxBytes=100*1024*1024,  # 100MB
            backupCount=9,           # 保留9个备份文件
            encoding='utf-8',
            delay=False              # 立即创建文件
        )
    ]
)
logger = logging.getLogger('LogService')

# 记录日志轮转配置变更信息
logger.info("======================================")
logger.info("日志轮转配置: 基于大小的轮转机制")
logger.info("文件大小限制: 100MB")
logger.info("保留文件数量: 9个")
logger.info("======================================")

# 记录已处理的日志行数
processed_lines = {}

# 已处理行数的保存文件路径
PROCESSED_LINES_FILE = os.path.join(LOG_ROOT, "processed_lines.json")

# 服务运行标志
running = True

# 待上传队列 - 当后端服务不可用时，将日志存储在这里
pending_uploads = []
PENDING_UPLOADS_FILE = os.path.join(LOG_ROOT, "pending_uploads.json")

def save_processed_lines():
    """保存已处理的行数到文件"""
    try:
        with open(PROCESSED_LINES_FILE, 'w', encoding='utf-8') as f:
            json.dump(processed_lines, f)
        logger.debug(f"已保存处理记录到 {PROCESSED_LINES_FILE}")
    except Exception as e:
        logger.error(f"保存处理记录失败: {e}")

def load_processed_lines():
    """从文件加载已处理的行数"""
    global processed_lines
    try:
        if os.path.exists(PROCESSED_LINES_FILE):
            with open(PROCESSED_LINES_FILE, 'r', encoding='utf-8') as f:
                processed_lines = json.load(f)
            logger.info(f"已加载处理记录: {len(processed_lines)} 个文件")
        else:
            logger.info("没有找到处理记录文件，将创建新的记录")
            processed_lines = {}
    except Exception as e:
        logger.error(f"加载处理记录失败: {e}")
        processed_lines = {}

def reset_processed_lines(file_path=None):
    """
    重置已处理行数记录
    
    参数:
    - file_path: 指定文件路径，如果为None则重置所有文件
    
    返回:
    - 重置的文件数量
    """
    global processed_lines
    reset_count = 0
    
    try:
        if file_path:
            # 重置指定文件
            file_name = os.path.basename(file_path)
            if os.path.exists(file_path):
                with open(file_path, 'r', encoding='utf-8') as f:
                    current_line_count = len(f.readlines())
                processed_lines[file_name] = current_line_count
                logger.info(f"重置文件 {file_name} 已处理行数为: {current_line_count}")
                reset_count = 1
            elif file_name in processed_lines:
                # 如果文件不存在但在记录中，则删除记录
                del processed_lines[file_name]
                logger.info(f"文件 {file_name} 不存在，已从记录中删除")
                reset_count = 1
        else:
            # 重置所有文件
            old_count = len(processed_lines)
            processed_lines = {}
            
            # 扫描日志目录中的所有日志文件
            log_dir = os.path.join(LOG_ROOT, 'python_logs')
            for log_file in Path(log_dir).rglob('*.log'):
                if log_file.name == 'log_service.log':
                    continue
                
                try:
                    with open(log_file, 'r', encoding='utf-8') as f:
                        current_line_count = len(f.readlines())
                    processed_lines[log_file.name] = current_line_count
                    logger.info(f"重置文件 {log_file.name} 已处理行数为: {current_line_count}")
                except Exception as e:
                    logger.error(f"重置文件 {log_file.name} 失败: {e}")
                    processed_lines[log_file.name] = 0
            
            reset_count = old_count + len(processed_lines)
        
        # 保存更新后的处理记录
        save_processed_lines()
        logger.info(f"已重置 {reset_count} 个文件的处理记录")
        return reset_count
    
    except Exception as e:
        logger.error(f"重置处理记录失败: {e}")
        return 0

def save_pending_uploads():
    """保存待上传队列到文件"""
    try:
        with open(PENDING_UPLOADS_FILE, 'w', encoding='utf-8') as f:
            json.dump(pending_uploads, f)
        logger.debug(f"已保存待上传队列到 {PENDING_UPLOADS_FILE}")
    except Exception as e:
        logger.error(f"保存待上传队列失败: {e}")

def load_pending_uploads():
    """从文件加载待上传队列"""
    global pending_uploads
    try:
        if os.path.exists(PENDING_UPLOADS_FILE):
            with open(PENDING_UPLOADS_FILE, 'r', encoding='utf-8') as f:
                pending_uploads = json.load(f)
            logger.info(f"已加载待上传队列: {len(pending_uploads)} 条记录")
        else:
            logger.info("没有找到待上传队列文件，将创建新的队列")
            pending_uploads = []
    except Exception as e:
        logger.error(f"加载待上传队列失败: {e}")
        pending_uploads = []

def extract_image_logs(input_path=None, output_path=None, content=None, file_name=None):
    """
    从原始日志中提取包含图片 URL 的条目，生成精简版日志
    格式：[时间戳] [设备名] [日志等级] 图片类型 - URL
    
    可以从文件路径或直接从内容字符串中提取
    """
    if content is None and input_path:
        # 原来的从文件读取的方式
        logger.info(f"开始处理日志文件: {input_path}")
        try:
            with open(input_path, "r", encoding="utf-8") as f:
                logs = f.read()
        except Exception as e:
            logger.error(f"处理日志文件 {input_path} 时发生错误: {e}")
            return []
    elif content is not None and file_name:
        # 新增的直接从内容字符串处理的方式
        logger.info(f"开始处理日志内容: 来自 {file_name}")
        logs = content
    else:
        logger.error("提取图片日志时参数错误：需要提供input_path或同时提供content和file_name")
        return []

    # 更新正则表达式以匹配更多格式的日志，使用更精确的URL匹配模式，并支持可选的轮次
    pattern = re.compile(
        r"^(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d{3}) - (\w+) - \[(.*?)\] - (.*?截图.*?)(?:已保存|已上传).*?URL: (https?://[\w.-]+(?:/[\w./()-_%]+)*(?:\.\w+)?)(?: 轮次: (\d+))?",
        re.M
    )

    matches = pattern.findall(logs)
    
    if output_path:
        output = []
        for match in matches:
            timestamp = match[0]
            log_level = match[1]
            device = match[2]
            desc = match[3].replace("截图已保存", "").replace("截图已上传", "").strip()
            url = match[4]
            round_num = match[5] if len(match) > 5 and match[5] else None
            
            # 验证URL格式（确保只包含URL部分）
            if len(url) > 500 or not url.startswith(('http://', 'https://')):
                logger.warning(f"发现异常URL({len(url)}字符): {url[:50]}...，可能包含非URL内容")
                # 尝试从文本中提取正确的URL
                url_match = re.search(r'(https?://[\w.-]+(?:/[\w./()-_%]+)*(?:\.\w+)?)', url)
                if url_match:
                    url = url_match.group(1)
                    logger.info(f"已提取到正确的URL: {url[:50]}...")
                else:
                    logger.error("无法从文本中提取到有效URL")
            
            # 追加轮次信息到输出，便于人工检查
            if round_num:
                output.append(f"[{timestamp}] [{device}] [{log_level}] {desc} - {url} 轮次: {round_num}")
            else:
                output.append(f"[{timestamp}] [{device}] [{log_level}] {desc} - {url}")
            logger.debug(f"提取到日志: {timestamp} - {device} - {log_level} - {desc} - {url} - 轮次: {round_num}")

        Path(output_path).parent.mkdir(parents=True, exist_ok=True)
        
        # 如果输出文件已存在，则追加内容
        mode = "a" if os.path.exists(output_path) else "w"
        with open(output_path, mode, encoding="utf-8") as f:
            if output:
                f.write("\n".join(output) + "\n")
    
    logger.info(f"提取了 {len(matches)} 条图片日志记录")
    return matches

def upload_logs_to_backend(matches, api_url=None):
    """
    将提取的日志信息上传到Java后端
    
    参数:
    - matches: 从日志中提取的匹配项列表
    - api_url: Java后端API地址，如果为None则使用当前环境配置
    
    返回:
    - 成功上传的记录数
    """
    if api_url is None:
        api_url = env_config.get_log_service_api_url()
        
    if not matches:
        return 0
        
    logger.info(f"开始上传 {len(matches)} 条日志记录到后端: {api_url}")
    success_count = 0
    failed_uploads = []
    permanent_failed = 0
    
    for match in matches:
        timestamp_str = match[0]
        log_level = match[1]
        device_id = match[2]
        desc = match[3].replace("截图已保存", "").replace("截图已上传", "").strip()
        image_url = match[4]
        round_num = match[5] if len(match) > 5 and match[5] else None
        
        # 检查并处理过长的URL
        if len(image_url) > 500 or not image_url.startswith(('http://', 'https://')):
            logger.warning(f"图片URL异常({len(image_url)}字符): {image_url[:50]}...")
            # 尝试从文本中提取正确的URL
            url_match = re.search(r'(https?://[\w.-]+(?:/[\w./()-_%]+)*(?:\.\w+)?)', image_url)
            if url_match:
                image_url = url_match.group(1)
                logger.info(f"已提取到正确的图片URL: {image_url[:50]}...")
            else:
                logger.error("无法从文本中提取到有效的图片URL")
                # 如果URL仍然超长，这里就直接标记为永久失败，不添加到重试队列
                if len(image_url) > 500:
                    logger.error(f"图片URL长度({len(image_url)})超过500字符限制，放弃上传")
                    permanent_failed += 1
                    continue
        
        # 将时间字符串转换为Java后端接受的格式
        try:
            # 解析时间戳 "2025-02-21 16:48:41,858" 格式
            dt = datetime.strptime(timestamp_str, "%Y-%m-%d %H:%M:%S,%f")
            # 加入东八区（北京时间）时区信息
            dt = dt.replace(tzinfo=timezone(timedelta(hours=8)))
            # 转换为ISO格式 (后端接受此格式)
            formatted_time = dt.isoformat()
        except Exception as e:
            logger.error(f"时间格式转换错误: {e}")
            formatted_time = None
        
        # 构建请求数据，对应CategoryInspection实体
        inspection_data = {
            "deviceId": device_id,
            "imagePath": image_url,
            "remarks": desc,  # 不再包含日志级别，单独作为字段发送
            "logLevel": log_level,  # 新增字段：日志级别
            "inspectionTime": formatted_time
            # createdAt, addTime, updateTime 由后端自动设置
        }
        if round_num:
            inspection_data["roundNum"] = int(round_num)
        
        try:
            # 发送POST请求到后端API
            headers = {"Content-Type": "application/json"}
            response = requests.post(api_url, json=inspection_data, headers=headers, timeout=5)
            
            if response.status_code == 200:
                result = response.json()
                if result.get("success"):
                    success_count += 1
                    logger.info(f"成功上传记录: {device_id} - [{log_level}] {desc}")
                else:
                    logger.error(f"上传失败: {result.get('message')}")
                    # 判断是否为数据校验错误，如果是则不重试
                    if "数据校验失败" in result.get('message', ''):
                        logger.warning(f"检测到数据校验错误，不会重试上传: {result.get('message')}")
                        permanent_failed += 1
                    else:
                        failed_uploads.append(match)
            else:
                logger.error(f"API请求失败，状态码: {response.status_code}")
                logger.error(f"响应内容: {response.text}")
                
                # 分析响应内容，判断是否为数据校验错误
                try:
                    result = response.json()
                    if response.status_code == 400 and "数据校验失败" in result.get('message', ''):
                        logger.warning(f"检测到数据校验错误，不会重试上传: {result.get('message')}")
                        permanent_failed += 1
                    else:
                        # 服务器错误或其他错误，添加到待重试队列
                        failed_uploads.append(match)
                except:
                    # 无法解析响应为JSON，视为服务器错误，添加到待重试队列
                    failed_uploads.append(match)
                
        except requests.exceptions.RequestException as e:
            logger.error(f"上传过程中发生连接错误: {e}")
            # 连接错误，添加到待重试队列
            failed_uploads.append(match)
        except Exception as e:
            logger.error(f"上传过程中发生其他错误: {e}")
            failed_uploads.append(match)
    
    # 如果有失败的上传，添加到待上传队列
    if failed_uploads:
        global pending_uploads
        pending_uploads.extend([("IMAGE", match) for match in failed_uploads])
        save_pending_uploads()
        logger.info(f"已将 {len(failed_uploads)} 条记录添加到待上传队列")
    
    logger.info(f"上传完成，成功上传 {success_count}/{len(matches)} 条记录，永久失败 {permanent_failed} 条记录")
    return success_count

def try_upload_pending():
    """尝试上传待上传队列中的记录"""
    global pending_uploads
    
    if not pending_uploads:
        return
        
    logger.info(f"尝试上传待上传队列中的 {len(pending_uploads)} 条记录")
    
    # 复制待上传队列，避免在迭代过程中修改
    current_pending = pending_uploads.copy()
    pending_uploads = []
    
    # 区分不同类型的日志记录
    image_logs_to_retry = [] # 重命名以区分
    heart_logs_to_retry = [] # 重命名以区分
    issue_logs_to_retry = [] # 新增区分
    
    # 根据记录格式分类
    for record in current_pending:
        # 检查记录格式是否有效 (至少包含类型标识符)
        if not isinstance(record, (list, tuple)) or len(record) < 1:
            logger.warning(f"跳过格式无效的待上传记录: {record}")
            continue

        log_type_identifier = record[0] # 第一个元素作为类型标识符

        if log_type_identifier == "IMAGE":
            if len(record) > 1 and isinstance(record[1], (list, tuple)):
                 image_logs_to_retry.append(record[1]) # 提取原始 match 元组
            else:
                 logger.warning(f"跳过格式无效的 IMAGE 待上传记录: {record}")
        elif log_type_identifier == "HEART":
             if len(record) > 1 and isinstance(record[1], (list, tuple)):
                 heart_logs_to_retry.append(record[1]) # 提取原始 match 元组
             else:
                 logger.warning(f"跳过格式无效的 HEART 待上传记录: {record}")
        elif log_type_identifier == "ISSUE":
             if len(record) > 1 and isinstance(record[1], dict):
                 issue_logs_to_retry.append(record[1]) # 提取 issue_info 字典
             else:
                 logger.warning(f"跳过格式无效的 ISSUE 待上传记录: {record}")
        else:
            # 兼容旧格式或其他意外情况，尝试按旧逻辑处理（可能主要是图片日志）
            # 注意：旧格式没有类型标识符，直接是 match 元组
            if isinstance(record, (list, tuple)) and len(record) > 4: # 假设图片日志的 match 元组长度大于4
                logger.warning(f"检测到可能是旧格式的待上传记录，尝试作为图片日志处理: {record}")
                image_logs_to_retry.append(record)
            else:
                 logger.warning(f"跳过无法识别类型的待上传记录: {record}")

    logger.info(f"待重试队列分类: {len(heart_logs_to_retry)} 条HEART日志, {len(image_logs_to_retry)} 条图片日志, {len(issue_logs_to_retry)} 条Issue日志")
    
    # 分别上传不同类型的日志
    heart_success = 0
    image_success = 0
    issue_success = 0
    
    if heart_logs_to_retry:
        heart_success = upload_heart_logs_to_backend(heart_logs_to_retry) # 使用正确的函数
        logger.info(f"HEART日志重试上传完成，成功上传 {heart_success}/{len(heart_logs_to_retry)} 条记录")
        
    if image_logs_to_retry:
        image_success = upload_logs_to_backend(image_logs_to_retry) # 使用正确的函数
        logger.info(f"图片日志重试上传完成，成功上传 {image_success}/{len(image_logs_to_retry)} 条记录")
    
    if issue_logs_to_retry:
        issue_success = upload_issue_logs_to_backend(issue_logs_to_retry) # 使用正确的函数
        logger.info(f"Issue日志重试上传完成，成功上传 {issue_success}/{len(issue_logs_to_retry)} 条记录")
    
    # 保存更新后的待上传队列 (包含本次重试再次失败的记录)
    save_pending_uploads()
    
    total_success = heart_success + image_success + issue_success
    total_tried = len(heart_logs_to_retry) + len(image_logs_to_retry) + len(issue_logs_to_retry)
    logger.info(f"待上传队列处理完成，总共成功上传 {total_success}/{total_tried} 条记录 (未成功的已重新加入队列)")

def move_to_processed(file_path):
    """将处理过的日志文件移动到已处理目录"""
    try:
        file_name = os.path.basename(file_path)
        processed_path = os.path.join(PROCESSED_DIR, file_name)
        
        # 如果目标文件已存在，添加时间戳
        if os.path.exists(processed_path):
            timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
            name, ext = os.path.splitext(file_name)
            processed_path = os.path.join(PROCESSED_DIR, f"{name}_{timestamp}{ext}")
        
        # 移动文件
        os.rename(file_path, processed_path)
        logger.info(f"已将处理过的日志文件移动到: {processed_path}")
        
        # 从处理记录中删除
        if file_name in processed_lines:
            del processed_lines[file_name]
            save_processed_lines()
            
        return True
    except Exception as e:
        logger.error(f"移动文件 {file_path} 时发生错误: {e}")
        return False

def extract_heart_logs(input_path=None, output_path=None, content=None, file_name=None):
    """
    从原始日志中提取HEART类型的日志条目，生成精简版日志
    格式：[时间戳] [设备名] [日志等级] 通知类型 - 内容
    只提取带有设备标记（【xxx】）的心跳日志，避免重复上报
    
    可以从文件路径或直接从内容字符串中提取
    """
    if content is None and input_path:
        # 原来的从文件读取的方式
        logger.info(f"开始处理HEART日志文件: {input_path}")
        try:
            with open(input_path, "r", encoding="utf-8") as f:
                logs = f.read()
        except Exception as e:
            logger.error(f"处理HEART日志文件 {input_path} 时发生错误: {e}")
            return []
    elif content is not None and file_name:
        # 新增的直接从内容字符串处理的方式
        logger.info(f"开始处理HEART日志内容: 来自 {file_name}")
        logs = content
    else:
        logger.error("提取HEART日志时参数错误：需要提供input_path或同时提供content和file_name")
        return []

    # 匹配HEART类型日志的正则表达式 - 更新以匹配带有主机标识的日志格式
    # 示例: 2025-03-12 15:21:14,020 - HEART - [Xiaomi_24129PN74C] - 设备轮次通知: 【MBP-PVFCQ0LQ62-2250】 设备轮次通知 | 时间：2025-03-12 15:21:14 | ...
    pattern = re.compile(
        r"^(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d{3}) - (HEART) - \[(.*?)\] - ((?:.*?通知:|心跳通知:)) (.*)",
        re.M
    )

    all_matches = pattern.findall(logs)
    
    # 过滤出带有设备标记的日志
    matches = []
    for match in all_matches:
        content_text = match[4].strip()  # 通知内容
        
        # 检查内容中是否包含设备标识【xxx】
        device_identifier_match = re.search(r"【(.*?)】", content_text)
        if device_identifier_match:
            # 只保留带有设备标识的日志
            matches.append(match)
            logger.debug(f"匹配到带有设备标识的日志: {match[0]} - {match[2]} - {match[3]}")
        else:
            logger.debug(f"跳过不含设备标识的日志: {match[0]} - {match[2]} - {match[3]}")
            
    # 为日志追加特殊标记，表示这是过滤后的结果
    filtered_count = len(all_matches) - len(matches)
    if filtered_count > 0:
        logger.info(f"过滤掉 {filtered_count} 条不含设备标记的心跳日志")
    
    if output_path:
        output = []
        for match in matches:
            timestamp = match[0]  # 创建时间，例如：2025-03-12 15:21:14,020
            log_level = match[1]  # 日志等级，值为 "HEART"
            device = match[2]     # 设备ID，例如：global 或 Xiaomi_24129PN74C
            notify_type = match[3].strip()  # 通知类型，例如：设备轮次通知:
            content_text = match[4].strip()      # 通知内容
            
            output.append(f"[{timestamp}] [{device}] [{log_level}] {notify_type} - {content_text}")
            logger.debug(f"提取到HEART日志: {timestamp} - {device} - {log_level} - {notify_type} - {content_text}")

        Path(output_path).parent.mkdir(parents=True, exist_ok=True)
        
        # 如果输出文件已存在，则追加内容
        mode = "a" if os.path.exists(output_path) else "w"
        with open(output_path, mode, encoding="utf-8") as f:
            if output:
                f.write("\n".join(output) + "\n")
    
    logger.info(f"提取了 {len(matches)} 条HEART日志记录（仅带设备标记的）")
    return matches

def upload_heart_logs_to_backend(matches, api_url=None):
    """
    将提取的HEART日志信息上传到Java后端
    只上传带有设备标记（【xxx】）的心跳日志，避免重复上报
    
    参数:
    - matches: 从日志中提取的匹配项列表(已过滤只包含带设备标记的记录)
    - api_url: Java后端API地址，如果为None则使用当前环境配置
    
    返回:
    - 成功上传的记录数
    """
    if api_url is None:
        api_url = env_config.get_log_service_api_url()
        
    if not matches:
        return 0
        
    logger.info(f"开始上传 {len(matches)} 条带设备标记的HEART日志记录到后端: {api_url}")
    success_count = 0
    failed_uploads = []
    permanent_failed = 0
    
    for match in matches:
        timestamp_str = match[0]  # 创建时间，例如：2025-03-12 15:21:14,020
        log_level = match[1]      # 日志等级，值为 "HEART"
        device_id = match[2]      # 设备ID，例如：global 或 Xiaomi_24129PN74C
        notify_type = match[3]    # 通知类型，例如：设备轮次通知:
        content = match[4]        # 通知内容
        
        # 将时间字符串转换为Java后端接受的格式
        try:
            # 解析时间戳 "2025-02-21 16:48:41,858" 格式
            dt = datetime.strptime(timestamp_str, "%Y-%m-%d %H:%M:%S,%f")
            # 加入东八区（北京时间）时区信息
            dt = dt.replace(tzinfo=timezone(timedelta(hours=8)))
            # 转换为ISO格式 (后端接受此格式)
            formatted_time = dt.isoformat()
        except Exception as e:
            logger.error(f"时间格式转换错误: {e}")
            formatted_time = None
        
        # 构建请求数据，对应CategoryInspection实体
        # 保留完整的内容，包括设备标识【xxx】
        # 确保imagePath为空，remarks字段包含通知类型和内容
        inspection_data = {
            "deviceId": device_id,                # 设备ID
            "imagePath": "",                      # HEART日志没有图片URL，确保为空字符串
            "remarks": f"{notify_type} {content}", # 将通知类型和内容作为备注，保留设备标识
            "logLevel": log_level,                # 日志级别为HEART
            "inspectionTime": formatted_time,     # 创建时间
            "heartBeat": True                     # 标识这是心跳日志
        }
        
        # 打印即将上传的数据，便于调试
        logger.debug(f"准备上传HEART记录到后端: deviceId={device_id}, imagePath='', remarks='{notify_type} {content}'[:50]...")
        
        try:
            # 发送POST请求到后端API
            headers = {"Content-Type": "application/json"}
            response = requests.post(api_url, json=inspection_data, headers=headers, timeout=5)
            
            if response.status_code == 200:
                result = response.json()
                if result.get("success"):
                    success_count += 1
                    logger.info(f"成功上传带设备标记的HEART记录: {device_id} - [{log_level}] {notify_type}")
                else:
                    logger.error(f"上传带设备标记的HEART日志失败: {result.get('message')}")
                    # 判断是否为数据校验错误，如果是则不重试
                    if "数据校验失败" in result.get('message', ''):
                        logger.warning(f"检测到数据校验错误，不会重试上传: {result.get('message')}")
                        permanent_failed += 1
                    else:
                        failed_uploads.append(match)
            else:
                logger.error(f"带设备标记的HEART日志API请求失败，状态码: {response.status_code}")
                logger.error(f"响应内容: {response.text}")
                
                # 分析响应内容，判断是否为数据校验错误
                try:
                    result = response.json()
                    if response.status_code == 400 and "数据校验失败" in result.get('message', ''):
                        logger.warning(f"检测到数据校验错误，不会重试上传: {result.get('message')}")
                        permanent_failed += 1
                    else:
                        # 服务器错误或其他错误，添加到待重试队列
                        failed_uploads.append(match)
                except:
                    # 无法解析响应为JSON，视为服务器错误，添加到待重试队列
                    failed_uploads.append(match)
                
        except requests.exceptions.RequestException as e:
            logger.error(f"上传带设备标记的HEART日志过程中发生连接错误: {e}")
            # 连接错误，添加到待重试队列
            failed_uploads.append(match)
        except Exception as e:
            logger.error(f"上传带设备标记的HEART日志过程中发生其他错误: {e}")
            failed_uploads.append(match)
    
    # 如果有失败的上传，添加到待上传队列
    if failed_uploads:
        global pending_uploads
        pending_uploads.extend(failed_uploads)
        save_pending_uploads()
        logger.info(f"已将 {len(failed_uploads)} 条带设备标记的HEART记录添加到待上传队列")
    
    logger.info(f"带设备标记的HEART日志上传完成，成功上传 {success_count}/{len(matches)} 条记录，永久失败 {permanent_failed} 条记录")
    return success_count

def extract_issue_logs(content, file_name):
    """
    从 issues 日志内容中提取错误日志条目。
    忽略初始化日志，解析 JSON 格式的日志。

    参数:
    - content: 日志文件的新增内容字符串
    - file_name: 日志文件名

    返回:
    - 包含提取信息的字典列表
    """
    logger.info(f"开始处理 Issues 日志内容: 来自 {file_name}")
    matches = []
    lines = content.strip().split('\n')

    for line in lines:
        line = line.strip()
        # 跳过空行和初始化日志
        if not line or "问题日志记录器初始化完成" in line:
            continue

        # 尝试从 INFO 日志行中提取 JSON 部分
        try:
            # 找到第一个 '{' 的位置
            json_start_index = line.find('{')
            if json_start_index != -1:
                json_str = line[json_start_index:]
                # 解析 JSON
                log_data = json.loads(json_str)
                
                # 提取所需字段
                issue_info = {
                    "timestamp": log_data.get("timestamp"),
                    "device_name": log_data.get("device_name"),
                    "screenshot_url": log_data.get("screenshot_url", ""), # 默认为空字符串
                    "issue_details": log_data.get("issue_details"),
                    # 可以添加其他需要的字段，例如 execution_id, round_num 等
                    "execution_id": log_data.get("execution_id"),
                    "round_num": log_data.get("round_num"),
                    "page_name": log_data.get("page_name"),
                    "issue_type": log_data.get("issue_type"),
                }
                
                # 确保基本字段存在
                if all(issue_info.get(k) is not None for k in ["timestamp", "device_name", "issue_details"]):
                     matches.append(issue_info)
                     logger.debug(f"提取到 Issue 日志: {issue_info['timestamp']} - {issue_info['device_name']} - {issue_info['issue_details'][:50]}...")
                else:
                    logger.warning(f"跳过缺少必要字段的 Issue 日志行: {line}")

            else:
                logger.warning(f"无法在 Issue 日志行中找到 JSON 开头: {line}")
        except json.JSONDecodeError as e:
            logger.error(f"解析 Issue 日志行中的 JSON 失败: {e} - 行内容: {line}")
        except Exception as e:
            logger.error(f"处理 Issue 日志行时发生未知错误: {e} - 行内容: {line}")

    logger.info(f"从 {file_name} 提取了 {len(matches)} 条 Issue 日志记录")
    return matches

def upload_issue_logs_to_backend(matches, api_url=None):
    """
    将提取的 Issue 日志信息上传到Java后端。

    参数:
    - matches: 从日志中提取的匹配项列表 (字典列表)
    - api_url: Java后端API地址，如果为None则使用当前环境配置

    返回:
    - 成功上传的记录数
    """
    if api_url is None:
        api_url = env_config.get_log_service_api_url()
        
    if not matches:
        return 0
        
    # 注意：这里的 matches 是 issue_info 字典的列表
    logger.info(f"开始上传 {len(matches)} 条 Issue 日志记录到后端: {api_url}")
    success_count = 0
    failed_uploads = [] # 用于收集本次调用失败的记录
    permanent_failed = 0
    
    for issue_info in matches:
        # 检查 issue_info 是否为字典
        if not isinstance(issue_info, dict):
            logger.error(f"发现无效的 Issue 记录（非字典）: {issue_info}，跳过上传")
            permanent_failed += 1
            continue

        timestamp_str = issue_info.get("timestamp")
        device_name = issue_info.get("device_name")
        screenshot_url = issue_info.get("screenshot_url", "") # 确保有默认值
        issue_details = issue_info.get("issue_details")

        # 增加对必要字段缺失的检查
        if not all([timestamp_str, device_name, issue_details is not None]): # issue_details 可以为空字符串，但不应为 None
             logger.error(f"Issue 记录缺少必要字段 (timestamp, device_name, issue_details): {issue_info}，跳过上传")
             permanent_failed += 1
             continue
        
        # 将时间字符串转换为Java后端接受的格式 (ISO 8601)
        try:
            # 优先解析带毫秒的格式 "2025-03-27 16:53:13,123"
            try:
                dt = datetime.strptime(timestamp_str, "%Y-%m-%d %H:%M:%S,%f")
            except ValueError:
                # 兼容无毫秒的老格式 "2025-03-27 16:53:13"
                dt = datetime.strptime(timestamp_str, "%Y-%m-%d %H:%M:%S")
            # 加入东八区（北京时间）时区信息
            dt = dt.replace(tzinfo=timezone(timedelta(hours=8)))
            # 转换为ISO格式 (后端接受此格式)
            formatted_time = dt.isoformat() 
        except ValueError as e:
            logger.error(f"Issue 日志时间格式转换错误: {e} - 原始时间: {timestamp_str}，记录: {issue_info}，标记为永久失败")
            permanent_failed += 1
            continue
        except Exception as e:
            logger.error(f"Issue 日志时间格式转换时发生未知错误: {e}，记录: {issue_info}，添加到重试队列")
            # 其他未知错误，暂时加入重试队列
            failed_uploads.append(("ISSUE", issue_info))
            continue

        # 处理空的 screenshot_url
        image_path = screenshot_url if screenshot_url else ""
        
        # 检查 image_path 长度
        if len(image_path) > 500:
            logger.warning(f"Issue 日志的 screenshot_url 过长 ({len(image_path)}字符)，将截断: {image_path[:50]}...，记录: {issue_info}")
            image_path = image_path[:500] 
        
        # 构建请求数据，对应CategoryInspection实体
        inspection_data = {
            "deviceId": device_name,
            "imagePath": image_path,
            "remarks": issue_details,
            "logLevel": "ERROR",  # 固定为 ERROR
            "inspectionTime": formatted_time,
            "issueType": issue_info.get("issue_type"),
            "createdAt": formatted_time,
            # 包含其他字段
            "executionId": issue_info.get("execution_id"),
            "roundNum": issue_info.get("round_num"),
            "pageName": issue_info.get("page_name"),
        }
        
        try:
            # 发送POST请求到后端API
            headers = {"Content-Type": "application/json"}
            # 增加日志记录请求数据（截断长文本）
            log_data_preview = {k: (v[:100] + '...' if isinstance(v, str) and len(v) > 100 else v) for k, v in inspection_data.items()}
            logger.debug(f"准备上传 Issue 数据: {log_data_preview}")
            response = requests.post(api_url, json=inspection_data, headers=headers, timeout=10) # 增加超时时间
            
            # 增强日志：记录响应状态码和内容（无论成功失败）
            response_text = response.text[:500] + '...' if len(response.text) > 500 else response.text # 限制日志长度
            logger.debug(f"Issue API 响应: Status={response.status_code}, Body={response_text}")

            if response.status_code == 200:
                try:
                    result = response.json()
                    if result.get("success"):
                        success_count += 1
                        logger.info(f"成功上传 Issue 记录: {device_name} - [ERROR] {str(issue_details)[:50]}...")
                    else:
                        # success 为 false
                        logger.error(f"上传 Issue 日志失败 (success=false): {result.get('message')} - 数据: {log_data_preview}")
                        if "数据校验失败" in result.get('message', '') or "无效的参数" in result.get('message', ''):
                            logger.warning(f"检测到 Issue 数据校验错误，不会重试上传。")
                            permanent_failed += 1
                        else:
                            failed_uploads.append(("ISSUE", issue_info)) # 其他业务失败，重试
                except json.JSONDecodeError:
                     logger.error(f"解析 Issue API 成功响应 (200) 的 JSON 失败: {response_text} - 数据: {log_data_preview}")
                     failed_uploads.append(("ISSUE", issue_info)) # 无法解析成功响应，可能API行为异常，重试
            elif response.status_code == 400:
                 # Bad Request，通常是校验失败
                 logger.error(f"Issue 日志 API 请求失败 (400 Bad Request): {response_text} - 数据: {log_data_preview}")
                 is_validation_error = False
                 try:
                     result = response.json()
                     if "数据校验失败" in result.get('message', '') or "无效的参数" in result.get('message', ''):
                        is_validation_error = True
                 except json.JSONDecodeError:
                     pass # 无法解析 JSON，可能不是标准的校验错误响应
                 
                 if is_validation_error:
                     logger.warning(f"检测到 Issue 数据校验错误 (状态码 400)，不会重试上传。")
                     permanent_failed += 1
                 else:
                     failed_uploads.append(("ISSUE", issue_info)) # 其他 400 错误，可能需要重试
            else:
                # 其他错误状态码 (e.g., 5xx 服务器错误)
                logger.error(f"Issue 日志 API 请求失败，状态码: {response.status_code} - 响应: {response_text} - 数据: {log_data_preview}")
                failed_uploads.append(("ISSUE", issue_info)) # 服务器错误或其他错误，添加到待重试队列
                
        except requests.exceptions.Timeout:
            logger.error(f"上传 Issue 日志过程中发生超时错误 - 数据: {log_data_preview}，添加到重试队列")
            failed_uploads.append(("ISSUE", issue_info))
        except requests.exceptions.RequestException as e:
            logger.error(f"上传 Issue 日志过程中发生连接错误: {e} - 数据: {log_data_preview}，添加到重试队列")
            failed_uploads.append(("ISSUE", issue_info))
        except Exception as e:
            logger.error(f"上传 Issue 日志过程中发生未知错误: {e} - 数据: {log_data_preview}，添加到重试队列")
            failed_uploads.append(("ISSUE", issue_info))
    
    # 如果有失败的上传，添加到全局待上传队列
    if failed_uploads:
        global pending_uploads
        # 注意：这里应该是 extend 而不是直接赋值给 failed_uploads
        # 修正：upload_issue_logs_to_backend 内部已经处理了失败记录的回填
        # pending_uploads.extend([("ISSUE", issue_info) for issue_info in failed_uploads]) # 这里不再需要，因为 upload 函数内部处理
        # save_pending_uploads() # 这里不再需要，因为 upload 函数内部处理
        # logger.info(f"已将 {len(failed_uploads)} 条记录添加到待上传队列") # 日志也在 upload 函数内部
        pass # 这段逻辑已移入 upload_issue_logs_to_backend 内部处理失败记录

    logger.info(f"Issue 日志上传完成，成功上传 {success_count}/{len(matches)} 条记录，永久失败 {permanent_failed} 条记录")
    return success_count

class LogFileHandler(FileSystemEventHandler):
    """处理日志文件变化的事件处理器"""
    
    def __init__(self, log_dir, output_dir, api_url, delete_after_process=False):
        """
        初始化处理器
        
        参数:
        - log_dir: 日志文件目录
        - output_dir: 输出目录
        - api_url: API地址
        - delete_after_process: 处理后是否删除原始日志
        """
        self.log_dir = Path(log_dir)
        self.output_dir = Path(output_dir)
        self.api_url = api_url
        self.delete_after_process = delete_after_process
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 加载已处理行数记录
        load_processed_lines()
        
        # 加载待上传队列
        load_pending_uploads()
        
        # 初始化已处理行数记录
        self._init_processed_lines()
        
    def _init_processed_lines(self):
        """初始化已处理行数记录，并检查文件行数是否减少"""
        for log_file in self.log_dir.rglob('*.log'):
            # 跳过监控自身的日志文件，但不再跳过global.log
            if log_file.name == 'log_service.log':
                continue
            
            # 如果文件不在处理记录中，则添加
            try:
                with open(log_file, 'r', encoding='utf-8') as f:
                    current_line_count = len(f.readlines())
                
                # 获取记录中的行数
                previous_line_count = processed_lines.get(log_file.name, 0)
                
                # 如果文件不在处理记录中，或者行数减少了，则重置记录
                if log_file.name not in processed_lines or current_line_count < previous_line_count:
                    logger.info(f"初始化时检测到文件 {log_file.name} 需要重置: {previous_line_count} -> {current_line_count}")
                    processed_lines[log_file.name] = current_line_count
                else:
                    logger.info(f"初始化文件 {log_file.name} 已处理行数: {current_line_count}")
            except Exception as e:
                logger.error(f"初始化文件 {log_file.name} 失败: {e}")
                processed_lines[log_file.name] = 0
            
            # 保存初始化后的处理记录
            save_processed_lines()
    
    def on_modified(self, event):
        """
        当文件被修改时触发
        
        参数:
        - event: 文件系统事件
        """
        if not event.is_directory and event.src_path.endswith('.log'):
            file_path = Path(event.src_path)
            file_name = file_path.name
            
            # 跳过监控自身的日志文件和临时文件，但不再跳过global.log
            if file_name == 'log_service.log' or file_name.startswith('temp_'):
                return
                
            logger.info(f"检测到文件变化: {file_name}")
            self._process_new_content(file_path)
    
    def on_created(self, event):
        """
        当新文件创建时触发
        
        参数:
        - event: 文件系统事件
        """
        if not event.is_directory and event.src_path.endswith('.log'):
            file_path = Path(event.src_path)
            file_name = file_path.name
            
            # 跳过监控自身的日志文件和临时文件，但不再跳过global.log
            if file_name == 'log_service.log' or file_name.startswith('temp_'):
                return
                
            logger.info(f"检测到新文件: {file_name}")
            processed_lines[file_name] = 0
            save_processed_lines()  # 保存更新后的处理记录
            self._process_new_content(file_path)
    
    def _process_new_content(self, file_path):
        """
        处理文件中的新内容，使用内存处理替代临时文件
        
        参数:
        - file_path: 文件路径
        """
        file_name = file_path.name
        
        try:
            # 读取文件所有内容
            with open(file_path, 'r', encoding='utf-8') as f:
                all_lines = f.readlines()
            
            # 获取新增内容
            current_line_count = len(all_lines)
            previous_line_count = processed_lines.get(file_name, 0)
            
            # 检查文件行数是否减少
            if current_line_count < previous_line_count:
                logger.warning(f"文件 {file_name} 行数减少: {previous_line_count} -> {current_line_count}，可能是日志被清理，正在重置处理记录")
                # 重置该文件的处理记录
                reset_processed_lines(file_path)
                # 更新已处理行数
                previous_line_count = processed_lines.get(file_name, 0)
            
            if current_line_count <= previous_line_count:
                logger.debug(f"文件 {file_name} 没有新内容")
                return
            
            # 提取新增内容
            new_lines = all_lines[previous_line_count:]
            new_content = ''.join(new_lines)

           # 如果是 issues 子目录下的文件，只做 Issue 日志处理
            if file_path.parent.name == 'issues':
                issue_matches = extract_issue_logs(new_content, file_name)
                if issue_matches:
                    cnt = upload_issue_logs_to_backend(issue_matches, self.api_url)
                    logger.info(f"文件 {file_name}（issues）提取 {len(issue_matches)} 条Issue日志，上传 {cnt} 条")
                else:
                    logger.info(f"文件 {file_name}（issues）未检测到Issue日志")
                # 更新处理行数并保存，然后不继续处理图片/心跳
                processed_lines[file_name] = current_line_count
                save_processed_lines()
                return
            
            logger.info(f"文件 {file_name} 有新增内容: {previous_line_count} -> {current_line_count} 行，新增 {current_line_count - previous_line_count} 行")
            
            # 处理输出路径
            output_path = self.output_dir / file_name
            
            # 直接从内存中处理新增内容 - 图片日志
            image_matches = extract_image_logs(content=new_content, file_name=file_name, output_path=output_path)
            
            # 如果有匹配项，上传到后端
            if image_matches:
                success_count = upload_logs_to_backend(image_matches, self.api_url)
                logger.info(f"文件 {file_name} 新增内容中提取了 {len(image_matches)} 条图片日志记录，成功上传 {success_count} 条")
            else:
                logger.info(f"文件 {file_name} 新增内容中没有找到图片日志记录")
                
            # 直接从内存中处理新增内容 - HEART日志
            heart_matches = extract_heart_logs(content=new_content, file_name=file_name, output_path=output_path)
            
            # 如果有匹配项，上传到后端
            if heart_matches:
                success_count = upload_heart_logs_to_backend(heart_matches, self.api_url)
                logger.info(f"文件 {file_name} 新增内容中提取了 {len(heart_matches)} 条HEART日志记录，成功上传 {success_count} 条")
            else:
                logger.info(f"文件 {file_name} 新增内容中没有找到HEART日志记录")
            
            # 更新已处理行数
            processed_lines[file_name] = current_line_count
            save_processed_lines()  # 保存更新后的处理记录
            
            # 如果配置了处理后删除，则移动到已处理目录
            if self.delete_after_process:
                move_to_processed(file_path)
            
        except Exception as e:
            logger.error(f"处理文件 {file_name} 时发生错误: {e}")
            # 出错时不更新处理行数，下次仍会尝试处理

def signal_handler(sig, frame):
    """处理信号"""
    global running
    logger.info("接收到终止信号，正在停止服务...")
    running = False

def start_service(log_dir, output_dir, api_url, delete_after_process=False):
    """
    启动日志监控服务
    
    参数:
    - log_dir: 日志目录
    - output_dir: 输出目录
    - api_url: API地址
    - delete_after_process: 处理后是否删除原始日志
    """
    global running
    
    # 记录服务启动信息
    logger.info("=" * 50)
    logger.info("日志监控服务启动")
    logger.info(f"日志目录: {log_dir}")
    logger.info(f"输出目录: {output_dir}")
    logger.info(f"API地址: {api_url}")
    logger.info(f"处理后删除: {delete_after_process}")
    logger.info(f"PID: {os.getpid()}")
    logger.info(f"日志轮转配置: 基于大小（200MB）保留9个备份文件")
    logger.info("=" * 50)
    
    # 保存PID到文件
    pid_file = os.path.join(LOG_ROOT, "log_service_pid.txt")
    with open(pid_file, 'w') as f:
        f.write(str(os.getpid()))
    
    # 确保目录存在
    Path(log_dir).mkdir(parents=True, exist_ok=True)
    Path(output_dir).mkdir(parents=True, exist_ok=True)
    
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # 创建事件处理器
    event_handler = LogFileHandler(log_dir, output_dir, api_url, delete_after_process)
    
    # 创建观察者
    observer = Observer()
    observer.schedule(event_handler, log_dir, recursive=True)
    observer.start()
    
    # 定期检查的时间间隔（秒）
    check_interval = 20  # 20秒
    last_check_time = time.time()
    
    try:
        # 主循环
        while running:
            # 每隔一段时间尝试上传待上传队列中的记录
            try_upload_pending()
            
            # 定期检查所有日志文件
            current_time = time.time()
            if current_time - last_check_time > check_interval:
                logger.info("定期检查所有日志文件...")
                for log_file in Path(log_dir).rglob('*.log'):
                    if log_file.name != 'log_service.log':
                        try:
                            # 检查文件行数
                            with open(log_file, 'r', encoding='utf-8') as f:
                                current_line_count = len(f.readlines())
                            
                            previous_line_count = processed_lines.get(log_file.name, 0)
                            
                            # 如果行数减少，重置处理记录
                            if current_line_count < previous_line_count:
                                logger.warning(f"定期检查发现文件 {log_file.name} 行数减少: {previous_line_count} -> {current_line_count}，正在重置处理记录")
                                reset_processed_lines(log_file)
                            elif current_line_count > previous_line_count:
                                # 如果有新内容，处理新内容
                                logger.info(f"定期检查发现文件 {log_file.name} 有新增内容，触发处理")
                                event_handler._process_new_content(log_file)
                        except Exception as e:
                            logger.error(f"定期检查文件 {log_file.name} 时发生错误: {e}")
                
                last_check_time = current_time
            
            time.sleep(5)  # 每5秒尝试一次
    except Exception as e:
        logger.error(f"服务运行时发生错误: {e}")
    finally:
        # 停止观察者
        observer.stop()
        observer.join()
        
        # 删除PID文件
        if os.path.exists(pid_file):
            os.remove(pid_file)
            
        logger.info("日志监控服务已停止")

def process_existing_logs(log_dir, output_dir, api_url, delete_after_process=False, reset_records=False, reset_on_start=False):
    """
    处理现有的日志文件
    
    参数:
    - log_dir: 日志目录
    - output_dir: 输出目录
    - api_url: API地址
    - delete_after_process: 处理后是否删除原始日志
    - reset_records: 是否重置处理记录
    - reset_on_start: 在启动时重置所有处理记录
    """
    logger.info("开始处理现有日志文件...")
    
    # 如果需要重置处理记录
    if reset_records:
        logger.info("正在重置处理记录...")
        reset_processed_lines()
    elif reset_on_start:
        logger.info("正在重置所有处理记录...")
        reset_processed_lines()
    
    # 获取所有日志文件
    log_files = list(Path(log_dir).rglob('*.log'))
    
    # 跳过服务自身的日志文件，但不再跳过global.log
    log_files = [f for f in log_files if f.name != 'log_service.log']
    
    logger.info(f"找到 {len(log_files)} 个日志文件")
    
    # 处理每个日志文件
    for log_file in log_files:
        try:
            # 获取当前行数
            with open(log_file, 'r', encoding='utf-8') as f:
                all_lines = f.readlines()
                current_line_count = len(all_lines)
            
            # 获取已处理行数
            previous_line_count = processed_lines.get(log_file.name, 0)
            
            # 检查文件行数是否减少
            if previous_line_count > 0 and current_line_count < previous_line_count:
                logger.warning(f"文件 {log_file.name} 行数减少: {previous_line_count} -> {current_line_count}，可能是日志被清理，正在重置处理记录")
                processed_lines[log_file.name] = current_line_count
                continue
            
            # 如果文件不在处理记录中，则添加
            if log_file.name not in processed_lines:
                logger.info(f"文件 {log_file.name} 首次处理，记录当前行数: {current_line_count}")
                processed_lines[log_file.name] = current_line_count
                continue
            
            # 如果有新内容，则处理
            if current_line_count > previous_line_count:
                logger.info(f"处理文件 {log_file.name}: {previous_line_count} -> {current_line_count} 行")
                
                # 读取新增内容
                new_lines = all_lines[previous_line_count:]
                new_content = ''.join(new_lines)
                
                # 如果是 issues 子目录下的文件，只做 Issue 日志处理
                if log_file.parent.name == 'issues':
                    matches = extract_issue_logs(new_content, log_file.name)
                    if matches:
                        cnt = upload_issue_logs_to_backend(matches, api_url)
                        logger.info(f"批量模式：文件 {log_file.name}（issues）提取 {len(matches)} 条Issue日志，上传 {cnt} 条")
                    else:
                        logger.info(f"批量模式：文件 {log_file.name}（issues）无 Issue 日志")
                    processed_lines[log_file.name] = current_line_count
                    continue  # 跳过下游图片/HEART 处理
                
                # 处理新增内容 - 图片日志
                output_path = Path(output_dir) / log_file.name
                image_matches = extract_image_logs(content=new_content, file_name=log_file.name, output_path=output_path)
                
                # 上传到后端
                if image_matches:
                    success_count = upload_logs_to_backend(image_matches, api_url)
                    logger.info(f"文件 {log_file.name} 提取了 {len(image_matches)} 条图片日志记录，成功上传 {success_count} 条")
                
                # 处理新增内容 - HEART日志
                heart_matches = extract_heart_logs(content=new_content, file_name=log_file.name, output_path=output_path)
                
                # 上传到后端
                if heart_matches:
                    success_count = upload_heart_logs_to_backend(heart_matches, api_url)
                    logger.info(f"文件 {log_file.name} 提取了 {len(heart_matches)} 条HEART日志记录，成功上传 {success_count} 条")
                
                # 更新已处理行数
                processed_lines[log_file.name] = current_line_count
                
                # 如果配置了处理后删除，则移动到已处理目录
                if delete_after_process:
                    move_to_processed(log_file)
            else:
                logger.info(f"文件 {log_file.name} 没有新内容")
        except Exception as e:
            logger.error(f"处理文件 {log_file.name} 时发生错误: {e}")
    
    # 保存处理记录
    save_processed_lines()
    logger.info("现有日志文件处理完成")

if __name__ == "__main__":
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='日志监控服务')
    parser.add_argument('--log-dir', type=str, default=os.path.join(LOG_ROOT, 'python_logs'),
                        help='日志文件目录路径')
    parser.add_argument('--output-dir', type=str, default=os.path.join(LOG_ROOT, 'compress_logs'),
                        help='压缩日志输出目录路径')
    parser.add_argument('--api-url', type=str, 
                        default=None,
                        help='后端API地址，如果指定则覆盖环境配置')
    parser.add_argument('--env', type=str, choices=['prod', 'test'], default=env_config.get_env(),
                        help='运行环境: prod(生产环境)或test(测试环境)')
    parser.add_argument('--process-existing', action='store_true',
                        help='是否处理现有的日志文件')
    parser.add_argument('--delete-after-process', action='store_true',
                        help='处理后是否删除原始日志')
    parser.add_argument('--reset', action='store_true',
                        help='重置所有日志文件的处理记录')
    parser.add_argument('--reset-file', type=str, default=None,
                        help='重置指定日志文件的处理记录')
    parser.add_argument('--reset-on-start', action='store_true',
                        help='在启动时重置所有处理记录')
    
    args = parser.parse_args()
    
    # 设置环境
    env_config.set_env(args.env)
    logger.info(f"当前运行环境: {env_config.get_env()}")
    
    # 如果命令行指定了API URL，则使用命令行参数
    api_url = args.api_url if args.api_url else env_config.get_log_service_api_url()
    logger.info(f"使用API地址: {api_url}")
    
    # 处理重置命令
    if args.reset:
        # 重置所有文件的处理记录
        load_processed_lines()
        reset_count = reset_processed_lines()
        logger.info(f"已重置 {reset_count} 个文件的处理记录")
        sys.exit(0)
    elif args.reset_file:
        # 重置指定文件的处理记录
        load_processed_lines()
        reset_count = reset_processed_lines(args.reset_file)
        logger.info(f"已重置 {reset_count} 个文件的处理记录")
        sys.exit(0)
    
    # 如果需要处理现有日志文件
    if args.process_existing:
        # 加载已处理行数记录
        load_processed_lines()
        # 加载待上传队列
        load_pending_uploads()
        # 处理现有日志文件，如果指定了重置参数，则重置处理记录
        process_existing_logs(args.log_dir, args.output_dir, api_url, args.delete_after_process, False, args.reset_on_start)
    
    # 启动服务
    start_service(args.log_dir, args.output_dir, api_url, args.delete_after_process) 