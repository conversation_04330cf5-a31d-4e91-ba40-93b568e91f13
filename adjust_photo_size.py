from PIL import Image
import os

def resize_image(input_path, output_path, size=(400, 400)):
    """
    调整图片尺寸并保持宽高比
    :param input_path: 输入图片路径
    :param output_path: 输出图片路径
    :param size: 目标尺寸，默认 400x400
    """
    try:
        # 打开图片
        with Image.open(input_path) as img:
            # 计算宽高比
            ratio = min(size[0]/img.size[0], size[1]/img.size[1])
            new_size = tuple([int(x*ratio) for x in img.size])
            
            # 调整图片尺寸
            resized_img = img.resize(new_size, Image.Resampling.LANCZOS)
            
            # 创建新的白色背景图片
            new_img = Image.new("RGB", size, (255, 255, 255))
            
            # 将调整后的图片粘贴到中心位置
            paste_x = (size[0] - new_size[0]) // 2
            paste_y = (size[1] - new_size[1]) // 2
            new_img.paste(resized_img, (paste_x, paste_y))
            
            # 保存图片
            new_img.save(output_path)
            print(f"图片已保存至: {output_path}")
            
    except Exception as e:
        print(f"处理图片时出错: {str(e)}")

def main():
    # 示例使用
    input_folder = "/Users/<USER>/Desktop/work/金刚区巡检项目/photo/"  # 输入文件夹
    output_folder = "/Users/<USER>/Desktop/work/金刚区巡检项目/photo/调整后"  # 输出文件夹
    
    # 创建输出文件夹（如果不存在）
    if not os.path.exists(output_folder):
        os.makedirs(output_folder)
    
    # 支持的图片格式
    supported_formats = ('.jpg', '.jpeg', '.png', '.bmp')
    
    # 处理文件夹中的所有图片
    for filename in os.listdir(input_folder):
        if filename.lower().endswith(supported_formats):
            input_path = os.path.join(input_folder, filename)
            output_path = os.path.join(output_folder, filename)
            resize_image(input_path, output_path)

if __name__ == "__main__":
    main()
