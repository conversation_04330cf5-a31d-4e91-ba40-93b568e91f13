#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Android设备内存监控测试脚本
用于检测Android设备的内存、CPU、存储等系统资源使用情况
"""

import os
import sys
import time
import json
import subprocess
import re
from datetime import datetime
from typing import Dict, List, Optional, Tuple

class AndroidDeviceMonitor:
    def __init__(self, device_udid=None, appium_port=4824):
        """
        初始化Android设备监控
        
        Args:
            device_udid: 设备UDID，如果为None则使用第一个连接的设备
            appium_port: Appium端口
        """
        self.device_udid = device_udid
        self.appium_port = appium_port
        
        # 如果没有指定UDID，尝试获取第一个连接的设备
        if not self.device_udid:
            self.device_udid = self.get_first_connected_device()
            
        if not self.device_udid:
            raise Exception("没有找到连接的Android设备")
            
        print(f"监控设备: {self.device_udid}")
        
        # 检查ADB连接
        if not self.check_adb_connection():
            raise Exception("ADB连接失败")
    
    def get_first_connected_device(self) -> Optional[str]:
        """获取第一个连接的Android设备UDID"""
        try:
            result = subprocess.run(['adb', 'devices'], 
                                 capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')[1:]  # 跳过第一行
                for line in lines:
                    if 'device' in line and 'offline' not in line:
                        return line.split()[0]
        except Exception as e:
            print(f"获取设备列表失败: {e}")
        return None
    
    def check_adb_connection(self) -> bool:
        """检查ADB连接状态"""
        try:
            result = subprocess.run(['adb', '-s', self.device_udid, 'shell', 'echo', 'test'], 
                                 capture_output=True, text=True, timeout=5)
            return result.returncode == 0 and 'test' in result.stdout
        except Exception as e:
            print(f"ADB连接检查失败: {e}")
            return False
    
    def execute_adb_command(self, command: str, timeout: int = 10) -> Tuple[str, str]:
        """执行ADB命令"""
        try:
            full_command = ['adb', '-s', self.device_udid, 'shell'] + command.split()
            result = subprocess.run(full_command, capture_output=True, text=True, timeout=timeout)
            return result.stdout, result.stderr
        except Exception as e:
            return "", str(e)
    
    def get_device_info(self) -> Dict:
        """获取设备基本信息"""
        device_info = {}
        
        # 获取设备属性
        properties = {
            'model': 'ro.product.model',
            'brand': 'ro.product.brand', 
            'manufacturer': 'ro.product.manufacturer',
            'device': 'ro.product.device',
            'android_version': 'ro.build.version.release',
            'sdk_version': 'ro.build.version.sdk',
            'build_id': 'ro.build.id',
            'fingerprint': 'ro.build.fingerprint',
            'cpu_abi': 'ro.product.cpu.abi',
            'cpu_abi2': 'ro.product.cpu.abi2',
        }
        
        for key, prop in properties.items():
            stdout, stderr = self.execute_adb_command(f'getprop {prop}')
            if stdout and not stderr:
                device_info[key] = stdout.strip()
            else:
                device_info[key] = "Unknown"
        
        # 获取屏幕信息
        stdout, stderr = self.execute_adb_command('wm size')
        if 'Physical size:' in stdout:
            size_match = re.search(r'Physical size: (\\d+)x(\\d+)', stdout)
            if size_match:
                device_info['screen_width'] = int(size_match.group(1))
                device_info['screen_height'] = int(size_match.group(2))
        
        # 获取密度
        stdout, stderr = self.execute_adb_command('wm density')
        if 'Physical density:' in stdout:
            density_match = re.search(r'Physical density: (\\d+)', stdout)
            if density_match:
                device_info['screen_density'] = int(density_match.group(1))
        
        return device_info
    
    def get_memory_info(self) -> Dict:
        """获取系统内存信息"""
        memory_info = {}
        
        # 获取系统内存信息
        stdout, stderr = self.execute_adb_command('cat /proc/meminfo')
        if stdout and not stderr:
            for line in stdout.split('\\n'):
                if ':' in line:
                    parts = line.split(':')
                    if len(parts) >= 2:
                        key = parts[0].strip()
                        value = parts[1].strip()
                        # 提取数字值（KB）
                        value_match = re.search(r'(\\d+)', value)
                        if value_match:
                            memory_info[key] = {
                                'kb': int(value_match.group(1)),
                                'mb': round(int(value_match.group(1)) / 1024, 2),
                                'gb': round(int(value_match.group(1)) / 1024 / 1024, 2)
                            }
        
        # 计算内存使用率
        if 'MemTotal' in memory_info and 'MemAvailable' in memory_info:
            total = memory_info['MemTotal']['mb']
            available = memory_info['MemAvailable']['mb']
            used = total - available
            memory_info['usage_summary'] = {
                'total_mb': total,
                'used_mb': round(used, 2),
                'available_mb': available,
                'usage_percent': round((used / total) * 100, 2)
            }
        
        return memory_info
    
    def get_storage_info(self) -> Dict:
        """获取存储空间信息"""
        storage_info = {}
        
        # 获取存储空间信息
        stdout, stderr = self.execute_adb_command('df -h')
        if stdout and not stderr:
            lines = stdout.strip().split('\\n')
            storage_info['partitions'] = []
            
            for line in lines[1:]:  # 跳过标题行
                parts = line.split()
                if len(parts) >= 6:
                    partition = {
                        'filesystem': parts[0],
                        'size': parts[1],
                        'used': parts[2],
                        'available': parts[3],
                        'use_percent': parts[4],
                        'mount_point': parts[5]
                    }
                    storage_info['partitions'].append(partition)
        
        # 获取内部存储信息
        stdout, stderr = self.execute_adb_command('df /data')
        if stdout and not stderr:
            lines = stdout.strip().split('\\n')
            if len(lines) >= 2:
                parts = lines[1].split()
                if len(parts) >= 4:
                    total_kb = int(parts[1])
                    used_kb = int(parts[2])
                    available_kb = int(parts[3])
                    
                    storage_info['internal_storage'] = {
                        'total_gb': round(total_kb / 1024 / 1024, 2),
                        'used_gb': round(used_kb / 1024 / 1024, 2),
                        'available_gb': round(available_kb / 1024 / 1024, 2),
                        'usage_percent': round((used_kb / total_kb) * 100, 2)
                    }
        
        return storage_info
    
    def get_cpu_info(self) -> Dict:
        """获取CPU信息"""
        cpu_info = {}
        
        # 获取CPU信息
        stdout, stderr = self.execute_adb_command('cat /proc/cpuinfo')
        if stdout and not stderr:
            cpu_info['cores'] = []
            current_core = {}
            
            for line in stdout.split('\\n'):
                if ':' in line:
                    key, value = line.split(':', 1)
                    key = key.strip()
                    value = value.strip()
                    
                    if key == 'processor':
                        if current_core:
                            cpu_info['cores'].append(current_core)
                        current_core = {'processor': value}
                    else:
                        current_core[key] = value
                elif line.strip() == '' and current_core:
                    cpu_info['cores'].append(current_core)
                    current_core = {}
            
            if current_core:
                cpu_info['cores'].append(current_core)
        
        # 获取CPU使用率
        stdout, stderr = self.execute_adb_command('top -n 1 -d 1')
        if stdout and not stderr:
            lines = stdout.split('\\n')
            for line in lines:
                if 'CPU:' in line:
                    # 解析CPU使用率
                    cpu_match = re.search(r'(\\d+)%', line)
                    if cpu_match:
                        cpu_info['usage_percent'] = int(cpu_match.group(1))
                    break
        
        return cpu_info
    
    def get_app_memory_info(self, package_name: str = 'com.meituan.android.kzn') -> Dict:
        """获取指定应用的内存信息"""
        app_memory = {}
        
        # 获取应用内存信息
        stdout, stderr = self.execute_adb_command(f'dumpsys meminfo {package_name}')
        if stdout and not stderr:
            lines = stdout.split('\\n')
            
            # 解析内存信息
            for i, line in enumerate(lines):
                if 'Total PSS:' in line:
                    pss_match = re.search(r'Total PSS:\\s*(\\d+)', line)
                    if pss_match:
                        app_memory['total_pss_kb'] = int(pss_match.group(1))
                        app_memory['total_pss_mb'] = round(int(pss_match.group(1)) / 1024, 2)
                
                if 'Native Heap:' in line:
                    # 解析Native Heap信息
                    heap_match = re.search(r'Native Heap:\\s*(\\d+)', line)
                    if heap_match:
                        app_memory['native_heap_kb'] = int(heap_match.group(1))
                        app_memory['native_heap_mb'] = round(int(heap_match.group(1)) / 1024, 2)
                
                if 'Dalvik Heap:' in line:
                    # 解析Dalvik Heap信息
                    dalvik_match = re.search(r'Dalvik Heap:\\s*(\\d+)', line)
                    if dalvik_match:
                        app_memory['dalvik_heap_kb'] = int(dalvik_match.group(1))
                        app_memory['dalvik_heap_mb'] = round(int(dalvik_match.group(1)) / 1024, 2)
        
        return app_memory
    
    def get_running_processes(self) -> List[Dict]:
        """获取正在运行的进程信息"""
        processes = []
        
        # 获取进程列表
        stdout, stderr = self.execute_adb_command('ps')
        if stdout and not stderr:
            lines = stdout.strip().split('\\n')
            headers = lines[0].split() if lines else []
            
            for line in lines[1:]:
                parts = line.split()
                if len(parts) >= len(headers):
                    process = {}
                    for i, header in enumerate(headers):
                        if i < len(parts):
                            process[header.lower()] = parts[i]
                    processes.append(process)
        
        # 按内存使用排序（如果有相关信息）
        processes.sort(key=lambda x: x.get('vsz', '0'), reverse=True)
        
        return processes[:20]  # 返回前20个进程
    
    def get_battery_info(self) -> Dict:
        """获取电池信息"""
        battery_info = {}
        
        # 获取电池信息
        stdout, stderr = self.execute_adb_command('dumpsys battery')
        if stdout and not stderr:
            for line in stdout.split('\\n'):
                if ':' in line:
                    parts = line.split(':', 1)
                    if len(parts) >= 2:
                        key = parts[0].strip()
                        value = parts[1].strip()
                        
                        # 转换数值
                        if key in ['level', 'scale', 'voltage', 'temperature']:
                            try:
                                battery_info[key] = int(value)
                            except ValueError:
                                battery_info[key] = value
                        else:
                            battery_info[key] = value
        
        return battery_info
    
    def get_network_info(self) -> Dict:
        """获取网络信息"""
        network_info = {}
        
        # 获取网络接口信息
        stdout, stderr = self.execute_adb_command('ip addr show')
        if stdout and not stderr:
            network_info['interfaces'] = []
            current_interface = {}
            
            for line in stdout.split('\\n'):
                if re.match(r'^\\d+:', line):
                    if current_interface:
                        network_info['interfaces'].append(current_interface)
                    
                    parts = line.split(':')
                    if len(parts) >= 2:
                        current_interface = {
                            'name': parts[1].strip().split()[0],
                            'status': 'UP' if 'UP' in line else 'DOWN'
                        }
                elif 'inet ' in line and current_interface:
                    ip_match = re.search(r'inet (\\S+)', line)
                    if ip_match:
                        current_interface['ip'] = ip_match.group(1)
            
            if current_interface:
                network_info['interfaces'].append(current_interface)
        
        return network_info
    
    def check_app_status(self, package_name: str = 'com.meituan.android.kzn') -> Dict:
        """检查应用运行状态"""
        app_status = {'package_name': package_name}
        
        # 检查应用是否安装
        stdout, stderr = self.execute_adb_command(f'pm list packages | grep {package_name}')
        app_status['installed'] = package_name in stdout
        
        if app_status['installed']:
            # 检查应用是否在运行
            stdout, stderr = self.execute_adb_command(f'ps | grep {package_name}')
            app_status['running'] = package_name in stdout
            
            # 获取应用版本
            stdout, stderr = self.execute_adb_command(f'dumpsys package {package_name}')
            if stdout:
                version_match = re.search(r'versionName=(\\S+)', stdout)
                if version_match:
                    app_status['version'] = version_match.group(1)
        
        return app_status
    
    def cleanup_temp_files(self) -> Dict:
        """清理设备上的临时文件"""
        cleanup_result = {'cleaned_files': [], 'errors': []}
        
        # 清理常见的临时文件
        temp_paths = [
            '/sdcard/temp_screenshot*.png',
            '/sdcard/screenshot*.png',
            '/sdcard/Download/*.tmp',
            '/data/local/tmp/*screenshot*',
        ]
        
        for path in temp_paths:
            try:
                stdout, stderr = self.execute_adb_command(f'rm {path}')
                if not stderr:
                    cleanup_result['cleaned_files'].append(path)
                elif 'No such file' not in stderr:
                    cleanup_result['errors'].append(f"{path}: {stderr}")
            except Exception as e:
                cleanup_result['errors'].append(f"{path}: {str(e)}")
        
        return cleanup_result
    
    def monitor_device_full_report(self) -> Dict:
        """生成完整的设备监控报告"""
        print("=" * 60)
        print(f"Android设备监控报告 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 60)
        print(f"设备UDID: {self.device_udid}")
        print(f"Appium端口: {self.appium_port}")
        print()
        
        report = {
            'timestamp': datetime.now().isoformat(),
            'device_udid': self.device_udid,
            'appium_port': self.appium_port
        }
        
        # 1. 基本设备信息
        print("📱 基本设备信息:")
        print("-" * 40)
        device_info = self.get_device_info()
        report['device_info'] = device_info
        for key, value in device_info.items():
            print(f"  {key}: {value}")
        print()
        
        # 2. 内存信息
        print("🧠 内存使用情况:")
        print("-" * 40)
        memory_info = self.get_memory_info()
        report['memory_info'] = memory_info
        if 'usage_summary' in memory_info:
            summary = memory_info['usage_summary']
            print(f"  总内存: {summary['total_mb']} MB")
            print(f"  已使用: {summary['used_mb']} MB")
            print(f"  可用: {summary['available_mb']} MB")
            print(f"  使用率: {summary['usage_percent']}%")
        else:
            print("  ❌ 无法获取内存使用摘要")
        print()
        
        # 3. 存储信息
        print("💾 存储空间信息:")
        print("-" * 40)
        storage_info = self.get_storage_info()
        report['storage_info'] = storage_info
        if 'internal_storage' in storage_info:
            storage = storage_info['internal_storage']
            print(f"  总容量: {storage['total_gb']} GB")
            print(f"  已使用: {storage['used_gb']} GB")
            print(f"  可用: {storage['available_gb']} GB")
            print(f"  使用率: {storage['usage_percent']}%")
        else:
            print("  ❌ 无法获取存储信息")
        print()
        
        # 4. CPU信息
        print("⚙️ CPU信息:")
        print("-" * 40)
        cpu_info = self.get_cpu_info()
        report['cpu_info'] = cpu_info
        if 'cores' in cpu_info and cpu_info['cores']:
            print(f"  CPU核心数: {len(cpu_info['cores'])}")
            if cpu_info['cores']:
                first_core = cpu_info['cores'][0]
                print(f"  处理器: {first_core.get('model name', 'Unknown')}")
                print(f"  架构: {first_core.get('cpu family', 'Unknown')}")
        if 'usage_percent' in cpu_info:
            print(f"  当前使用率: {cpu_info['usage_percent']}%")
        print()
        
        # 5. 美团应用信息
        print("🍔 美团应用状态:")
        print("-" * 40)
        app_status = self.check_app_status()
        report['app_status'] = app_status
        print(f"  应用包名: {app_status['package_name']}")
        print(f"  是否安装: {'✅' if app_status['installed'] else '❌'}")
        if app_status['installed']:
            print(f"  是否运行: {'✅' if app_status['running'] else '❌'}")
            if 'version' in app_status:
                print(f"  版本: {app_status['version']}")
        print()
        
        # 6. 美团应用内存使用
        if app_status['installed']:
            print("🧠 美团应用内存使用:")
            print("-" * 40)
            app_memory = self.get_app_memory_info()
            report['app_memory'] = app_memory
            if 'total_pss_mb' in app_memory:
                print(f"  总内存(PSS): {app_memory['total_pss_mb']} MB")
            if 'native_heap_mb' in app_memory:
                print(f"  Native堆: {app_memory['native_heap_mb']} MB")
            if 'dalvik_heap_mb' in app_memory:
                print(f"  Dalvik堆: {app_memory['dalvik_heap_mb']} MB")
            print()
        
        # 7. 电池信息
        print("🔋 电池信息:")
        print("-" * 40)
        battery_info = self.get_battery_info()
        report['battery_info'] = battery_info
        if 'level' in battery_info:
            print(f"  电量: {battery_info['level']}%")
        if 'status' in battery_info:
            print(f"  状态: {battery_info['status']}")
        if 'temperature' in battery_info:
            print(f"  温度: {battery_info['temperature']/10}°C")
        print()
        
        # 8. 网络信息
        print("🌐 网络信息:")
        print("-" * 40)
        network_info = self.get_network_info()
        report['network_info'] = network_info
        if 'interfaces' in network_info:
            for interface in network_info['interfaces']:
                print(f"  {interface['name']}: {interface['status']}")
                if 'ip' in interface:
                    print(f"    IP: {interface['ip']}")
        print()
        
        # 9. 临时文件清理
        print("🧹 临时文件清理:")
        print("-" * 40)
        cleanup_result = self.cleanup_temp_files()
        report['cleanup_result'] = cleanup_result
        if cleanup_result['cleaned_files']:
            print(f"  已清理: {len(cleanup_result['cleaned_files'])} 个文件/目录")
        if cleanup_result['errors']:
            print(f"  清理错误: {len(cleanup_result['errors'])} 个")
        print()
        
        # 10. 监控建议
        print("💡 监控建议:")
        print("-" * 40)
        suggestions = []
        
        # 内存建议
        if 'usage_summary' in memory_info:
            usage = memory_info['usage_summary']['usage_percent']
            if usage > 90:
                suggestions.append(f"⚠️  内存使用率过高 ({usage}%)，建议重启设备")
            elif usage > 80:
                suggestions.append(f"⚠️  内存使用率较高 ({usage}%)，需要关注")
            elif usage > 70:
                suggestions.append(f"ℹ️  内存使用率中等 ({usage}%)，建议监控")
            else:
                suggestions.append(f"✅ 内存使用率正常 ({usage}%)")
        
        # 存储建议
        if 'internal_storage' in storage_info:
            usage = storage_info['internal_storage']['usage_percent']
            if usage > 90:
                suggestions.append(f"⚠️  存储空间严重不足 ({usage}%)，建议清理")
            elif usage > 80:
                suggestions.append(f"⚠️  存储空间不足 ({usage}%)，需要关注")
            elif usage > 70:
                suggestions.append(f"ℹ️  存储空间使用较高 ({usage}%)，建议监控")
            else:
                suggestions.append(f"✅ 存储空间正常 ({usage}%)")
        
        # 应用建议
        if not app_status['installed']:
            suggestions.append("❌ 美团应用未安装，请先安装")
        elif not app_status['running']:
            suggestions.append("⚠️  美团应用未运行，可能需要启动")
        else:
            suggestions.append("✅ 美团应用运行正常")
        
        # 电池建议
        if 'level' in battery_info:
            level = battery_info['level']
            if level < 20:
                suggestions.append(f"⚠️  电量过低 ({level}%)，建议充电")
            elif level < 50:
                suggestions.append(f"ℹ️  电量中等 ({level}%)，建议关注")
            else:
                suggestions.append(f"✅ 电量充足 ({level}%)")
        
        for suggestion in suggestions:
            print(f"  {suggestion}")
        
        report['suggestions'] = suggestions
        
        return report

def main():
    """主函数"""
    print("🔍 Android设备监控工具")
    print("=" * 60)
    
    try:
        # 如果有命令行参数，使用第一个参数作为UDID
        device_udid = sys.argv[1] if len(sys.argv) > 1 else None
        appium_port = int(sys.argv[2]) if len(sys.argv) > 2 else 4824
        
        # 创建监控实例
        monitor = AndroidDeviceMonitor(device_udid, appium_port)
        
        # 生成完整报告
        report = monitor.monitor_device_full_report()
        
        # 可以选择将报告保存到文件
        save_report = input("\\n是否保存报告到文件？(y/n): ").lower().strip()
        if save_report == 'y':
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"android_device_monitor_report_{timestamp}.json"
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            
            print(f"✅ 报告已保存到: {filename}")
        
    except KeyboardInterrupt:
        print("\\n\\n⏹️  监控已停止")
    except Exception as e:
        print(f"\\n❌ 监控出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()