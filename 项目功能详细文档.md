# 美团App自动化测试平台 - 业务功能详细文档

## 项目概述

美团App自动化测试平台是一个专门用于美团App首页功能巡检的自动化测试系统。该平台支持iOS和Android双平台，提供24小时不间断的测试服务，确保美团App首页功能的稳定性和可用性。

## 核心测试业务

### 测试类型与覆盖范围

#### 1. 主要金刚区icon测试
**测试对象**: 美团App首页6个核心业务入口
- 外卖
- 团购  
- 美食
- 酒店民宿
- 休闲玩乐
- 看病买药

**测试频率**: 每10轮测试中的前8轮
**单轮耗时**: 约5分钟（每个icon约50秒）

#### 2. 全部金刚区icon测试
**测试对象**: 美团App首页所有可见的业务入口icon
**测试频率**: 每10轮测试中的第10轮
**单轮耗时**: 约50分钟

#### 3. 首页推荐按钮测试
**测试对象**: 首页左下角"推荐"按钮及相关页面
**测试频率**: 每10轮测试中的第9轮

### 测试流程详解

#### 主要测试流程（图标测试）

1. **点击icon**: 自动识别并点击目标icon
2. **异常检测**: 
   - 2.1 检测是否有闪退（应用崩溃或退到后台）
   - 2.2 检测是否有白屏（页面加载异常）
   - 2.3 检测是否有对应特征文本（验证页面内容正确性）
3. **返回操作**: 点击返回按钮回到首页
4. **首页验证**: 检测是否成功回到首页

#### 推荐按钮测试流程

1. **点击推荐按钮**: 点击首页左下角推荐按钮
2. **页面滑动**: 首页向下滑动
3. **白屏检测**: 检测滑动后是否出现白屏
4. **恢复操作**: 再次点击推荐按钮回到首页默认状态

### 巡检执行策略

#### 24小时不间断运行模式

**轮次分配**:
- 第1-8轮: 主要金刚区icon测试 (80%)
- 第9轮: 首页推荐按钮测试 (10%)  
- 第10轮: 全部金刚区icon测试 (10%)

**时间分配**:
- 主要金刚区icon测试: 每轮5分钟
- 推荐按钮测试: 每轮约2分钟
- 全部icon测试: 每轮50分钟
- 平均每10轮周期: 约92分钟

#### 多设备并发测试

**设备管理**: 
- 支持iOS和Android设备同时测试
- 每台设备独立运行测试轮次
- 自动设备状态监控和恢复

**服务保障**:
- 自动重启异常服务
- 设备离线自动检测
- 测试失败自动重试

## 技术架构与核心模块

### 核心控制器 (`split_devices.py`)

**主要功能**:
- 多设备并发测试管理
- 测试轮次控制和调度
- 设备状态监控和恢复
- 测试结果汇总统计

**关键类**:
- `DeviceManager`: 设备管理器，负责设备检测、连接和状态检查
- `TestResult`: 测试结果数据类，记录测试详情和统计信息

**核心实现**:
- **多进程架构**: 采用multiprocessing模块实现设备并发测试
- **状态同步**: 通过Manager().dict()实现进程间设备状态共享
- **测试轮次管理**: 自动追踪和管理每个设备的测试轮次
- **异常恢复**: 设备异常时自动重启服务和恢复测试

### 测试执行模块

#### iOS测试模块 (`test_meituan_ios.py`)
**核心类**: `test_meituan_ios`

**主要功能**:
- **test_meituan_ios()**: iOS设备美团App测试主控制器
- **check_ios_device_online()**: 检查iOS设备是否在线

**技术实现**:
- **WebDriverAgent集成**: 基于Facebook WebDriverAgent实现iOS设备控制
- **Appium服务**: 通过Appium Server与WDA通信
- **设备连接检查**: 通过libimobiledevice工具检查设备连接状态
- **应用状态检测**: 检查美团App是否正常运行，处理应用更新

**测试流程**:
1. 检查设备在线状态
2. 检查App更新
3. 启动美团App
4. 根据轮次执行不同测试类型
5. 异常处理和恢复

#### Android测试模块 (`test_meituan_android.py`)
**核心类**: `test_meituan_android`

**主要功能**:
- **test_meituan_android()**: Android设备美团App测试主控制器
- **check_android_device_online()**: 检查Android设备是否在线
- **check_adb_environment()**: 检查ADB环境
- **get_crash_logs()**: 获取应用崩溃日志

**技术实现**:
- **ADB原生命令**: 直接使用ADB命令进行设备操作
- **Appium集成**: 支持Appium和原生ADB双模式
- **设备型号适配**: 支持多种Android设备型号和版本
- **系统弹窗处理**: 自动处理各种Android系统弹窗

**测试特点**:
- **高兼容性**: 支持Android 5.0+版本
- **原生性能**: 直接ADB操作，响应速度快
- **异常处理**: 自动处理ANR、崩溃等异常情况

#### 图标测试核心模块

##### 主要图标测试 (`tap_main_icons.py`)
**核心函数**: `tap_main_icons()`

**技术实现**:
- **目标图标配置**: 通过Config.TARGET_ICONS配置6个核心业务图标
- **图标识别**: 结合OCR和图像识别技术定位图标
- **点击测试**: 自动点击图标并检测页面响应
- **异常检测**: 多维度检测闪退、白屏、内容异常
- **返回验证**: 确保能正确返回首页

**测试流程**:
```python
for icon in Config.TARGET_ICONS:
    1. 识别图标位置
    2. 点击图标
    3. 检测页面加载状态
    4. 验证特征文本
    5. 点击返回按钮
    6. 验证回到首页
```

##### 全部图标测试 (`tap_all_icons.py`)
**核心函数**: `tap_all_icons()`

**技术实现**:
- **图标发现**: 自动扫描首页所有可见图标
- **分类识别**: 区分业务图标和功能按钮
- **坐标计算**: 动态计算图标位置坐标
- **批量测试**: 遍历所有图标进行测试

**关键技术**:
- **连通域分析**: 使用OpenCV进行图标区域检测
- **OCR文字识别**: 识别图标名称和页面文本
- **相似度匹配**: 避免重复测试相同图标

##### 推荐页面测试 (`swipe_homepage.py`)
**核心函数**: `swipe_homepage_recommend()`

**技术实现**:
- **推荐按钮定位**: 精确定位首页左下角推荐按钮
- **滑动操作**: 模拟用户滑动行为
- **白屏检测**: 基于像素分析检测页面白屏
- **状态恢复**: 确保测试后恢复到初始状态

### 设备与服务管理

#### 设备状态管理 (`device_status_manager.py`)
**核心类**: `DeviceStatusManager`

**主要功能**:
- **get_status()** / **update_status()**: 设备状态CRUD操作  
- **create_status()**: 设备初始化
- **get_all_device_status()**: 批量状态检索
- **cleanup_offline_devices()**: 离线设备清理
- **get_devices_by_platform()**: 按平台获取设备
- **get_devices_by_status()**: 按状态获取设备
- **check_device_connectivity()**: 检查设备连接状态
- **add_icon_count()**: 累加图标计数

**技术实现**:
- **JSON持久化**: 使用JSON文件存储设备状态
- **文件锁机制**: 防止并发访问冲突
- **状态同步**: 定期同步设备状态到文件
- **异常恢复**: 状态文件损坏时自动恢复

#### 服务管理模块

##### ADB服务管理 (`adb_manager.py`)
**核心类**: `AdbServiceManager`

**主要功能**:
- **restart_adb_service()**: 重启ADB服务
- **check_and_restart_if_needed()**: 检查并在需要时重启ADB服务
- **is_adb_service_healthy()**: 检查ADB服务是否正常运行

**技术实现**:
- **服务监控**: 定期检查ADB服务状态
- **自动重启**: 服务异常时自动重启
- **健康检查**: 4小时间隔的健康检查机制
- **线程安全**: 支持多线程安全操作

##### WDA服务管理 (`wda_manager.py`)
**核心类**: `WdaServiceManager`

**主要功能**:
- **start_wda_for_device()**: 为iOS设备启动WebDriverAgent服务
- **check_wda_status()**: 通过HTTP请求和iproxy检查WDA状态
- **stop_wda_process()**: 优雅停止WDA进程
- **prepare_device_wda()**: 准备设备专用的WDA目录和配置
- **check_and_restart_wda_if_needed()**: 自动维护WDA服务
- **acquire_wda_lock()** / **release_wda_lock()**: WDA安装锁管理
- **get_latest_wda_version()**: 获取最新WDA版本

**技术实现**:
- **多设备支持**: 为每个设备独立启动WDA服务
- **端口管理**: 自动分配和管理WDA端口
- **进程管理**: 完整的WDA进程生命周期管理
- **定期重启**: 6小时间隔的定期重启机制
- **版本管理**: 自动检测和更新WDA版本

##### Appium服务管理 (`appium_manager.py`)
**核心类**: `AppiumServiceManager`

**主要功能**:
- **start_appium_server()**: 启动Appium服务器并管理端口
- **stop_appium_server()**: 优雅关闭Appium服务
- **check_appium_status()**: 服务健康状态监控

**技术实现**:
- **端口分配**: iOS设备从4724起，Android设备从4824起
- **服务监控**: 实时监控Appium服务状态
- **并发支持**: 支持多个Appium实例并发运行
- **自动重启**: 服务异常时自动重启

### 监控与报警系统

#### 实时监控 (`heartbeat_monitor.py`)
**主要功能**:
- **log_device_issue()**: 设备问题跟踪和报警
- **initialize_heartbeat_monitor()**: 系统监控初始化
- **heartbeat_loop()**: 心跳循环
- **check_device_status_changes()**: 检查设备状态变化
- **send_offline_notification()**: 发送离线通知
- **send_heartbeat()**: 发送心跳通知
- **get_recent_issues()**: 获取最近问题记录
- **calc_device_metrics()**: 计算设备指标

**技术实现**:
- **问题分类**: 自动分类测试问题和应用问题
- **通知策略**: 智能通知策略，避免重复报警
- **历史跟踪**: 记录和分析历史问题
- **指标计算**: 实时计算设备健康指标

#### 通知系统 (`notify_user.py`)
**主要功能**:
- **send_individual_kingkong_message()**: 个人DX消息推送
- **send_group_kingkong_message()**: 群组通知
- **send_individual_message()**: 发送个人消息
- **send_group_message()**: 发送群组消息
- **send_email()**: 邮件备用系统
- **add_host_info_to_message()**: 添加主机信息到消息

**技术实现**:
- **多渠道支持**: 支持DX、邮件等多种通知渠道
- **消息模板**: 预定义消息模板，支持变量替换
- **失败重试**: 通知失败时自动重试和降级
- **消息优先级**: 根据问题严重程度调整通知优先级

### 辅助支持功能

#### 计算机视觉与OCR

##### EasyOCR集成 (`easyocr_text.py`)
**主要功能**:
- **recognize_text_in_image()**: 图像文本识别
- **format_ocr_results()**: 格式化OCR识别结果
- **main()**: 主函数入口

**技术实现**:
- **多语言支持**: 支持中英文混合识别
- **高精度定位**: 精确的文本坐标定位
- **弹窗集成**: 与弹窗检测系统集成
- **文本过滤**: 区分弹窗内外的文本

##### Horus服务集成 (`get_message_from_horus.py`)
**主要功能**:
- **get_ui_page_parse()**: 高级UI元素分析
- **find_back_button_location()**: 导航按钮检测
- **get_message_from_horus()**: 外部OCR服务调用
- **analyze_ui_bug_result()**: 分析UI缺陷检测结果
- **get_ui_bug_from_ocr()**: 调用OCR API获取UI缺陷信息
- **extract_fields_and_centers()**: 解析字符串并提取字段和中心点坐标
- **find_back_button_location_especial()**: 特殊业务返回按钮检测

**技术实现**:
- **AI服务集成**: 集成外部AI服务提高识别准确性
- **API调用**: RESTful API调用外部服务
- **结果解析**: 智能解析AI服务返回结果
- **缓存机制**: 缓存常用识别结果

##### 弹窗检测 (`pop_up_detector.py`)
**主要功能**:
- **detect_popup_by_brightness()**: 基于亮度的弹窗检测
- **quick_detect_popup()**: 快速弹窗分析
- **handle_popup()**: 弹窗处理函数
- **detect_new_version_popup()**: 新版本弹窗检测
- **detect_popup_with_connected_components()**: 连通组件弹窗检测
- **detect_popup_with_contour()**: 轮廓弹窗检测
- **detect_popup_with_all_methods()**: 综合弹窗检测

**技术实现**:
- **多算法融合**: 结合多种计算机视觉算法
- **OpenCV集成**: 使用OpenCV进行图像处理
- **阈值优化**: 动态调整检测阈值
- **实时处理**: 实时弹窗检测和处理

#### 应用更新管理 (`check_app_updates.py`)

**主要功能**:
- **check_ios_app_updates_by_safari()**: 基于Safari的更新检测
- **check_android_app_updates_by_sigma()**: 基于API的更新管理
- **check_ios_device_online()**: 检查iOS设备在线状态
- **install_apk_with_manual_confirmation()**: 手动确认安装APK
- **compare_versions()**: 比较版本号
- **is_device_in_whitelist()**: 检查设备白名单
- **navigate_to_url_safely()**: 安全导航到URL

**技术实现**:
- **Safari自动化**: 通过Safari自动检测iOS应用更新
- **API集成**: 集成Sigma等外部API获取更新信息
- **版本比较**: 语义化版本号比较算法
- **自动安装**: 支持自动下载和安装更新

#### 设备操作 (`device_common.py`)
**核心类**: `device_operate_external`, `device_operate_internal`

**主要功能**:
- **take_screenshot()**: 跨平台截图功能
- **tap()**: iOS/Android通用点击操作
- **swipe()** / **swipe_by_coordinate()**: 手势自动化操作
- **restart_app()**: 重启应用
- **check_is_homepage()**: 首页状态验证
- **get_screenshot_ocr_elements()**: 截图与OCR集成
- **check_and_handle_popup_after_screenshot()**: 截图后弹窗处理
- **check_and_handle_app_crash()**: 应用崩溃检查处理

**技术实现**:
- **跨平台抽象**: 统一的iOS和Android操作接口
- **坐标适配**: 支持不同屏幕尺寸的坐标转换
- **异常处理**: 完善的异常捕获和恢复机制
- **性能优化**: 操作响应时间优化

### 日志与运维

#### 日志管理器 (`log_manager.py`)
**核心类**: `LogManager`

**主要功能**:
- **get_device_logger()**: 设备专用日志器创建
- **cleanup_old_logs()**: 自动日志轮转
- **create_wda_log_file()**: WDA专用日志
- **get_appium_log_file()**: Appium日志文件路径
- **cleanup_old_wda_logs()**: 清理旧WDA日志
- **start_log_service()** / **stop_log_service()**: 日志服务控制

**技术实现**:
- **分布式日志**: 按设备分离的日志系统
- **日志轮转**: 自动压缩和清理旧日志
- **多级过滤**: 支持不同级别的日志过滤
- **实时监控**: 实时日志文件监控

#### 日志服务 (`log_service.py`)
**核心类**: `LogFileHandler`

**主要功能**:
- **upload_logs_to_backend()**: 远程日志聚合
- **extract_image_logs()**: 提取图片日志
- **extract_heart_logs()**: 提取HEART类型日志
- **extract_issue_logs()**: 提取问题日志
- **start_service()**: 启动日志监控服务
- **try_upload_pending()**: 重试上传待上传队列

**技术实现**:
- **云端聚合**: 将日志上传到云端存储
- **智能分析**: 自动分析日志内容
- **队列管理**: 上传失败时的队列管理
- **存储优化**: 日志压缩和存储空间优化

### 配置管理

#### 配置管理 (`config.py`)
**核心类**: `Config`

**主要常量**:
- **TARGET_ICONS**: 目标测试图标列表
- **TARGET_UDIDS_IOS/ANDROID**: 设备白名单
- **服务端口配置**: Appium、WDA端口范围
- **超时和重试配置**: 各种操作的超时设置
- **get_actual_icon_count()**: 计算实际的图标数量

**技术实现**:
- **集中管理**: 所有配置参数集中管理
- **环境隔离**: 不同环境的配置隔离
- **动态更新**: 支持运行时配置更新
- **类型检查**: 配置参数类型验证

#### 环境配置 (`env_config.py`)
**主要功能**:
- **get_env()** / **set_env()**: 环境切换管理
- **get_api_url()**: 动态API URL生成
- **set_custom_service_url()** / **get_custom_service_url()**: 服务端点自定义
- **get_base_domain()**: 获取基础域名
- **get_horus_api_url()**: 获取Horus服务API URL
- **set_host_id()** / **get_host_id()**: 主机ID管理

**技术实现**:
- **多环境支持**: 支持dev/test/prod环境切换
- **配置文件**: 基于文件的配置存储
- **动态发现**: 支持服务自动发现
- **加密支持**: 敏感配置加密存储

## 使用场景与价值

### 1. 业务稳定性保障
- **全天候监控**: 24小时不间断测试，及时发现问题
- **核心功能覆盖**: 重点关注用户最常用的6个业务入口
- **全面性检测**: 定期全量测试所有业务入口

### 2. 问题快速发现
- **多维度检测**: 闪退、白屏、内容异常的全方位检测
- **实时报警**: 问题发现后立即通知相关人员
- **自动恢复**: 临时性问题自动重试和恢复

### 3. 数据驱动决策
- **详细统计**: 每轮测试的执行情况和结果统计
- **趋势分析**: 历史数据分析，识别问题规律
- **设备健康度**: 设备状态监控，优化资源配置

### 4. 开发效率提升
- **自动化测试**: 减少人工测试成本
- **持续集成**: 支持CI/CD流程集成
- **多设备并发**: 提高测试效率和覆盖面

## 关键技术特性

### 多平台统一架构
- **iOS支持**: 基于WebDriverAgent (WDA) + Appium集成
- **Android支持**: ADB + Appium原生命令执行
- **统一接口**: 通过device_common.py提供跨平台操作基础功能
- **协议适配**: 自动适配不同平台的通信协议

### 高并发测试框架
- **多进程支持**: 基于multiprocessing实现真正的并行测试
- **端口管理**: 智能端口分配和冲突避免
  - iOS设备: WDA端口从8100起，Appium端口从4724起
  - Android设备: Appium端口从4824起
- **线程安全**: 设备状态管理的线程安全实现
- **资源隔离**: 每个设备独立的服务实例和日志系统

### AI驱动的智能测试
- **多AI服务集成**: 
  - Friday智能代理: 图像分析和坐标提取
  - Horus智能代理: 高级UI元素识别
  - EasyOCR: 本地文本识别
- **高级弹窗检测**: 基于亮度、连通组件、轮廓等多种算法
- **智能UI元素识别**: 自动识别按钮、文本、图标等UI元素
- **自适应测试策略**: 根据设备特性和历史数据调整测试方法

### 健壮的错误处理与恢复
- **分层异常处理**: 从底层设备操作到顶层业务逻辑的全面异常处理
- **自动服务恢复**: 
  - ADB服务4小时间隔健康检查
  - WDA服务6小时间隔定期重启
  - Appium服务异常自动重启
- **状态一致性**: 设备状态的强一致性保证
- **故障隔离**: 单设备故障不影响其他设备测试

### 企业级监控与运维
- **实时监控系统**: 
  - 设备状态实时监控
  - 测试进度跟踪
  - 问题自动分类和报警
- **多渠道通知**: 
  - DX消息推送
  - 邮件通知
  - 群组消息
- **完整日志体系**: 
  - 按设备分离的日志系统
  - 日志自动轮转和压缩
  - 云端日志聚合和分析
- **资源管理**: 
  - 内存使用监控
  - 磁盘空间管理
  - 进程生命周期管理

### 高可用性设计
- **服务自动恢复**: 
  - 进程崩溃自动重启
  - 网络断连自动重连
  - 服务异常自动修复
- **优雅降级**: 
  - 部分设备失效不影响整体测试
  - 服务降级时的备用方案
  - 测试优先级动态调整
- **状态持久化**: 
  - 设备状态持久化存储
  - 测试进度断点续传
  - 配置热更新支持

### 性能优化
- **并发优化**: 
  - 多进程并发测试
  - 异步I/O操作
  - 资源池管理
- **内存管理**: 
  - 图像处理后及时释放内存
  - 日志缓冲区管理
  - 对象生命周期优化
- **网络优化**: 
  - 连接复用
  - 请求合并
  - 超时控制

### 可扩展架构
- **模块化设计**: 松耦合的模块设计，便于扩展
- **插件化支持**: 支持新的测试类型和设备类型
- **配置驱动**: 通过配置文件灵活调整测试策略
- **API接口**: 提供RESTful API支持外部集成

## 部署与配置

### 环境要求
- **Python 3.8+**
- **iOS支持**: Xcode, libimobiledevice, WebDriverAgent
- **Android支持**: Android SDK, ADB
- **依赖服务**: Appium, EasyOCR, 外部AI服务

### 配置步骤
1. 环境变量配置 (`.env`)
2. 设备列表配置 (`config.py`)
3. 坐标文件配置 (`location/`)
4. 启动后台服务
5. 执行测试进程

### 启动命令
```bash
python python/split_devices.py
```

## 项目定位

该平台专注于**美团App首页功能的自动化巡检**，是一个生产就绪的移动测试解决方案。通过24小时不间断的测试，确保美团App核心功能的稳定性，为用户提供可靠的服务体验。

**核心价值**:
1. **业务保障**: 确保美团App首页核心功能始终可用
2. **问题预警**: 在用户发现问题前提前识别和解决
3. **数据支撑**: 为产品和技术决策提供数据支持
4. **效率提升**: 自动化替代人工测试，提高测试效率和覆盖面