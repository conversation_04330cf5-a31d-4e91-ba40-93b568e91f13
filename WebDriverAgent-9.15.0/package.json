{"name": "appium-webdriveragent", "version": "9.15.0", "description": "Package bundling WebDriverAgent", "main": "./build/index.js", "types": "./build/index.d.ts", "scripts": {"build": "tsc -b", "dev": "npm run build -- --watch", "clean": "npm run build -- --clean", "lint": "eslint .", "format": "prettier -w ./lib", "lint:fix": "npm run lint -- --fix", "prepare": "npm run build", "version": "npm run sync-wda-version", "test": "mocha --exit --timeout 1m \"./test/unit/**/*-specs.js\"", "e2e-test": "mocha --exit --timeout 10m \"./test/functional/**/*-specs.js\"", "bundle": "npm run bundle:ios && npm run bundle:tv", "bundle:ios": "TARGET=runner SDK=sim node ./Scripts/build-webdriveragent.js", "bundle:tv": "TARGET=tv_runner SDK=tv_sim node ./Scripts/build-webdriveragent.js", "fetch-prebuilt-wda": "node ./Scripts/fetch-prebuilt-wda.js", "sync-wda-version": "node ./scripts/update-wda-version.js --package-version=${npm_package_version} && git add WebDriverAgentLib/Info.plist"}, "engines": {"node": ">=14", "npm": ">=8"}, "prettier": {"bracketSpacing": false, "printWidth": 100, "singleQuote": true}, "repository": {"type": "git", "url": "git+https://github.com/appium/WebDriverAgent.git"}, "keywords": ["Appium", "iOS", "WebDriver", "Selenium", "WebDriverAgent"], "author": "Appium Contributors", "license": "Apache-2.0", "bugs": {"url": "https://github.com/appium/WebDriverAgent/issues"}, "homepage": "https://github.com/appium/WebDriverAgent#readme", "devDependencies": {"@appium/eslint-config-appium-ts": "^1.0.0", "@appium/test-support": "^3.0.0", "@appium/tsconfig": "^0.x", "@appium/types": "^0.x", "@semantic-release/changelog": "^6.0.1", "@semantic-release/git": "^10.0.1", "@types/bluebird": "^3.5.38", "@types/lodash": "^4.14.196", "@types/mocha": "^10.0.1", "@types/node": "^24.0.0", "@types/teen_process": "^2.0.1", "appium-xcode": "^5.0.0", "chai": "^5.1.1", "chai-as-promised": "^8.0.0", "conventional-changelog-conventionalcommits": "^9.0.0", "node-simctl": "^7.0.1", "mocha": "^11.0.1", "prettier": "^3.0.0", "semantic-release": "^24.0.0", "semver": "^7.3.7", "sinon": "^21.0.0", "ts-node": "^10.9.1", "typescript": "^5.4.2"}, "dependencies": {"@appium/base-driver": "^9.0.0", "@appium/strongbox": "^0.x", "@appium/support": "^6.0.0", "appium-ios-device": "^2.7.25", "appium-ios-simulator": "^6.2.2", "async-lock": "^1.0.0", "asyncbox": "^3.0.0", "axios": "^1.4.0", "bluebird": "^3.5.5", "lodash": "^4.17.11", "source-map-support": "^0.x", "teen_process": "^2.2.0"}, "files": ["index.ts", "lib", "build/index.*", "build/lib", "Scripts/build.sh", "Scripts/fetch-prebuilt-wda.js", "Scripts/build-webdriveragent.js", "Configurations", "PrivateHeaders", "WebDriverAgent.xcodeproj", "WebDriverAgentLib", "WebDriverAgentRunner", "WebDriverAgentTests", "XCTWebDriverAgentLib", "CHANGELOG.md"]}