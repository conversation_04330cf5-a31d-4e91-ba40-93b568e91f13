parameters:
  vmImage: 'macOS-11'
  name: macOS_11
  excludeXcode: $(excludeXcode)
jobs:
  - job: ${{ parameters.name }}
    variables:
      EXCLUDE_XCODE: ${{ parameters.excludeXcode }}
    pool:
      vmImage: ${{ parameters.vmImage }}
    dependsOn: create_github_release
    steps:
    - script: node ./ci-jobs/scripts/azure-print-tag-name
      displayName: Print Tag Name
    - script: ls /Applications/
      displayName: List Installed Applications
    - name: Use Node.js
      uses: actions/setup-node@v3
      with:
        node-version: lts/*
    - script: npm install
      displayName: Install Node Modules
    - script: mkdir -p Resources/WebDriverAgent.bundle
      displayName: Make Resources Folder
    - script: node ./Scripts/build-webdriveragent.js
      displayName: Build WebDriverAgents
    - script: ls ./bundles
      displayName: List WDA Bundles
    - task: PublishPipelineArtifact@0
      inputs:
        targetPath: bundles/
        artifactName: ${{ parameters.name }}
    - script: |
        brew install ghr
        ghr $(node ./ci-jobs/scripts/azure-print-tag-name) bundles/
      env:
        GITHUB_TOKEN: $(GITHUB_TOKEN)
      displayName: Upload to GitHub Releases
