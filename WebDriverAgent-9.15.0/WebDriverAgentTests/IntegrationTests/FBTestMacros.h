/**
 * Copyright (c) 2015-present, Facebook, Inc.
 * All rights reserved.
 *
 * This source code is licensed under the BSD-style license found in the
 * LICENSE file in the root directory of this source tree. An additional grant
 * of patent rights can be found in the PATENTS file in the same directory.
 */

#import <WebDriverAgentLib/FBRunLoopSpinner.h>

/**
 <PERSON><PERSON> used to wait till certain condition is true.
 If condition will not become true within default timeout (1m) it will fail running test
 */
#define FBAssertWaitTillBecomesTrue(condition) \
  ({ \
    NSError *__error; \
    XCTAssertTrue([[[FBRunLoopSpinner new] \
      interval:1.0] \
    spinUntilTrue:^BOOL{ \
      return (condition); \
    }]); \
    XCTAssertNil(__error); \
  })

#define FBWaitExact(timeoutSeconds) \
  ({ \
    [[NSRunLoop currentRunLoop] runUntilDate:[NSDate dateWithTimeIntervalSinceNow:(timeoutSeconds)]]; \
  })

#define FBCellElementWithLabel(label) ([self.testedApplication descendantsMatchingType:XCUIElementTypeAny][label])
#define FBAssertVisibleCell(label) FBAssertWaitTillBecomesTrue(FBCellElementWithLabel(label).fb_isVisible)
#define FBAssertInvisibleCell(label) FBAssertWaitTillBecomesTrue(!FBCellElementWithLabel(label).fb_isVisible)
