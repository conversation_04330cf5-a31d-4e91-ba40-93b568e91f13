/**
 * Copyright (c) 2015-present, Facebook, Inc.
 * All rights reserved.
 *
 * This source code is licensed under the BSD-style license found in the
 * LICENSE file in the root directory of this source tree. An additional grant
 * of patent rights can be found in the PATENTS file in the same directory.
 */

#import "FBAlertViewController.h"

#import <Photos/Photos.h>
#import <CoreLocation/CoreLocation.h>

@interface FBAlertViewController ()
@property (nonatomic, strong) CLLocationManager *locationManager;
@end

@implementation FBAlertViewController

- (IBAction)createAppAlert:(UIButton *)sender
{
  [self presentAlertController];
}

- (IBAction)createAppSheet:(UIButton *)sender
{
    UIAlertController *alerController =
    [UIAlertController alertControllerWithTitle:@"Magic Sheet"
                                        message:@"Should read"
                                 preferredStyle:UIAlertControllerStyleActionSheet];
    UIPopoverPresentationController *popPresenter = [alerController popoverPresentationController];
    popPresenter.sourceView = sender;
    [self presentViewController:alerController animated:YES completion:nil];
    
}

- (IBAction)createNotificationAlert:(UIButton *)sender
{
  UNUserNotificationCenter *center = [UNUserNotificationCenter currentNotificationCenter];
  [center requestAuthorizationWithOptions:(UNAuthorizationOptionSound|UNAuthorizationOptionAlert|UNAuthorizationOptionBadge) 
                        completionHandler:^(BOOL granted, NSError * _Nullable error)
   {
    dispatch_async(dispatch_get_main_queue(), ^{
      [[UIApplication sharedApplication] registerForRemoteNotifications];
    });
  }];
}

- (IBAction)createCameraRollAccessAlert:(UIButton *)sender
{
  [PHPhotoLibrary requestAuthorization:^(PHAuthorizationStatus status) {
  }];
}

- (IBAction)createGPSAccessAlert:(UIButton *)sender
{
  self.locationManager = [CLLocationManager new];
  [self.locationManager requestAlwaysAuthorization];
}

- (void)touchesMoved:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event
{
  [super touchesMoved:touches withEvent:event];
  for (UITouch *touch in touches) {
    if (fabs(touch.maximumPossibleForce - touch.force) < 0.0001) {
      [self presentAlertController];
      return;
    }
  }
}

- (void)presentAlertController
{
  UIAlertController *alerController =
  [UIAlertController alertControllerWithTitle:@"Magic"
                                      message:@"Should read"
                               preferredStyle:UIAlertControllerStyleAlert];
  [alerController addAction:[UIAlertAction actionWithTitle:@"Will do" style:UIAlertActionStyleDefault handler:nil]];
  [self presentViewController:alerController animated:YES completion:nil];
}

@end
