/**
 * Copyright (c) 2015-present, Facebook, Inc.
 * All rights reserved.
 *
 * This source code is licensed under the BSD-style license found in the
 * LICENSE file in the root directory of this source tree. An additional grant
 * of patent rights can be found in the PATENTS file in the same directory.
 */

#import <WebDriverAgentLib/FBXPath.h>

NS_ASSUME_NONNULL_BEGIN

@interface FBXPath ()

/**
 Gets xmllib2-compatible XML representation of n XCElementSnapshot instance
 
 @param root the root element to execute XPath query for
 @param writer the correspondig libxml2 writer object
 @param elementStore an empty dictionary to store indexes mapping or nil if no mappings should be stored
 @param query Optional XPath query value. By analyzing this query we may optimize the lookup speed.
 @param excludedAttributes The list of XML attribute names to be excluded from the generated XML representation.
 Setting nil to this argument means that none of the known attributes must be excluded.
 If `query` argument is assigned then `excludedAttributes` argument is effectively ignored.
 @return zero if the method has completed successfully
 */
+ (int)xmlRepresentationWithRootElement:(id<FBXCElementSnapshot>)root
                                 writer:(xmlTextWriterPtr)writer
                           elementStore:(nullable NSMutableDictionary *)elementStore
                                  query:(nullable NSString*)query
                    excludingAttributes:(nullable NSArray<NSString *> *)excludedAttributes;

/**
 Gets the list of matched snapshots from xmllib2-compatible xmlNodeSetPtr structure
 
 @param nodeSet set of nodes returned after successful XPath evaluation
 @param elementStore dictionary containing index->snapshot mapping
 @return array of filtered elements or nil in case of failure. Can be empty array as well
 */
+ (NSArray *)collectMatchingSnapshots:(xmlNodeSetPtr)nodeSet elementStore:(NSMutableDictionary *)elementStore;

/**
 Gets the list of matched XPath nodes from xmllib2-compatible XML document
 
 @param xpathQuery actual query. Should be valid XPath 1.0-compatible expression
 @param document libxml2-compatible document pointer
 @param contextNode Optonal context node instance
 @return pointer to a libxml2-compatible structure with set of matched nodes or NULL in case of failure
 */
+ (xmlXPathObjectPtr)evaluate:(NSString *)xpathQuery
                     document:(xmlDocPtr)doc
                  contextNode:(nullable xmlNodePtr)contextNode;

@end

NS_ASSUME_NONNULL_END
