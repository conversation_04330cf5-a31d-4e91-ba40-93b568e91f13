/**
 * Copyright (c) 2015-present, Facebook, Inc.
 * All rights reserved.
 *
 * This source code is licensed under the BSD-style license found in the
 * LICENSE file in the root directory of this source tree. An additional grant
 * of patent rights can be found in the PATENTS file in the same directory.
 */

#import "FBCapabilities.h"

NSString* const FB_CAP_USE_TEST_MANAGER_FOR_VISIBLITY_DETECTION = @"shouldUseTestManagerForVisibilityDetection";
NSString* const FB_CAP_MAX_TYPING_FREQUENCY = @"maxTypingFrequency";
NSString* const FB_CAP_USE_SINGLETON_TEST_MANAGER = @"shouldUseSingletonTestManager";
NSString* const FB_CAP_DISABLE_AUTOMATIC_SCREENSHOTS = @"disableAutomaticScreenshots";
NSString* const FB_CAP_SHOULD_TERMINATE_APP = @"shouldTerminateApp";
NSString* const FB_CAP_EVENT_LOOP_IDLE_DELAY_SEC = @"eventloopIdleDelaySec";
NSString* const FB_CAP_BUNDLE_ID = @"bundleId";
NSString* const FB_CAP_INITIAL_URL = @"initialUrl";
NSString* const FB_CAP_FORCE_APP_LAUNCH = @"forceAppLaunch";
NSString* const FB_CAP_SHOULD_WAIT_FOR_QUIESCENCE = @"shouldWaitForQuiescence";
NSString* const FB_CAP_ARGUMENTS = @"arguments";
NSString* const FB_CAP_ENVIRNOMENT = @"environment";
NSString* const FB_CAP_USE_NATIVE_CACHING_STRATEGY = @"useNativeCachingStrategy";
NSString* const FB_CAP_FORCE_SIMULATOR_SOFTWARE_KEYBOARD_PRESENCE = @"forceSimulatorSoftwareKeyboardPresence";
NSString* const FB_CAP_APP_LAUNCH_STATE_TIMEOUT_SEC = @"appLaunchStateTimeoutSec";
