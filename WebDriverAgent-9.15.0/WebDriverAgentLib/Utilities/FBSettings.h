/**
 * Copyright (c) 2015-present, Facebook, Inc.
 * All rights reserved.
 *
 * This source code is licensed under the BSD-style license found in the
 * LICENSE file in the root directory of this source tree. An additional grant
 * of patent rights can be found in the PATENTS file in the same directory.
 */

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

// See FBConfiguration.h for more details on the meaning of each setting

extern NSString* const FB_SETTING_USE_COMPACT_RESPONSES;
extern NSString* const FB_SETTING_ELEMENT_RESPONSE_ATTRIBUTES;
extern NSString* const FB_SETTING_MJPEG_SERVER_SCREENSHOT_QUALITY;
extern NSString* const FB_SETTING_MJPEG_SERVER_FRAMERATE;
extern NSString* const FB_SETTING_MJPEG_FIX_ORIENTATION;
extern NSString* const FB_SETTING_MJPEG_SCALING_FACTOR;
extern NSString* const FB_SETTING_SCREENSHOT_QUALITY;
extern NSString* const FB_SETTING_KEYBOARD_AUTOCORRECTION;
extern NSString* const FB_SETTING_KEYBOARD_PREDICTION;
extern NSString* const FB_SETTING_SNAPSHOT_MAX_DEPTH;
extern NSString* const FB_SETTING_USE_FIRST_MATCH;
extern NSString* const FB_SETTING_BOUND_ELEMENTS_BY_INDEX;
extern NSString* const FB_SETTING_REDUCE_MOTION;
extern NSString* const FB_SETTING_DEFAULT_ACTIVE_APPLICATION;
extern NSString* const FB_SETTING_ACTIVE_APP_DETECTION_POINT;
extern NSString* const FB_SETTING_INCLUDE_NON_MODAL_ELEMENTS;
extern NSString* const FB_SETTING_DEFAULT_ALERT_ACTION;
extern NSString* const FB_SETTING_ACCEPT_ALERT_BUTTON_SELECTOR;
extern NSString* const FB_SETTING_DISMISS_ALERT_BUTTON_SELECTOR;
extern NSString* const FB_SETTING_SCREENSHOT_ORIENTATION;
extern NSString* const FB_SETTING_WAIT_FOR_IDLE_TIMEOUT;
extern NSString* const FB_SETTING_ANIMATION_COOL_OFF_TIMEOUT;
extern NSString* const FB_SETTING_MAX_TYPING_FREQUENCY;
extern NSString* const FB_SETTING_RESPECT_SYSTEM_ALERTS;
extern NSString* const FB_SETTING_USE_CLEAR_TEXT_SHORTCUT;
extern NSString* const FB_SETTING_LIMIT_XPATH_CONTEXT_SCOPE;
extern NSString* const FB_SETTING_AUTO_CLICK_ALERT_SELECTOR;
extern NSString *const FB_SETTING_INCLUDE_HITTABLE_IN_PAGE_SOURCE;
extern NSString *const FB_SETTING_INCLUDE_NATIVE_FRAME_IN_PAGE_SOURCE;
extern NSString *const FB_SETTING_INCLUDE_MIN_MAX_VALUE_IN_PAGE_SOURCE;

NS_ASSUME_NONNULL_END
