/**
 * Copyright (c) 2015-present, Facebook, Inc.
 * All rights reserved.
 *
 * This source code is licensed under the BSD-style license found in the
 * LICENSE file in the root directory of this source tree. An additional grant
 * of patent rights can be found in the PATENTS file in the same directory.
 */

#import "FBExceptions.h"

NSString *const FBInvalidArgumentException = @"FBInvalidArgumentException";
NSString *const FBSessionCreationException = @"FBSessionCreationException";
NSString *const FBSessionDoesNotExistException = @"FBSessionDoesNotExistException";
NSString *const FBApplicationDeadlockDetectedException = @"FBApplicationDeadlockDetectedException";
NSString *const FBElementAttributeUnknownException = @"FBElementAttributeUnknownException";
NSString *const FBElementNotVisibleException = @"FBElementNotVisibleException";
NSString *const FBTimeoutException = @"FBTimeoutException";
NSString *const FBStaleElementException = @"FBStaleElementException";
NSString *const FBInvalidXPathException = @"FBInvalidXPathException";
NSString *const FBXPathQueryEvaluationException = @"FBXPathQueryEvaluationException";
NSString *const FBClassChainQueryParseException = @"FBClassChainQueryParseException";
NSString *const FBApplicationCrashedException = @"FBApplicationCrashedException";
NSString *const FBApplicationMissingException = @"FBApplicationMissingException";
NSString *const FBIncompatibleWdaException = @"FBIncompatibleWdaException";
