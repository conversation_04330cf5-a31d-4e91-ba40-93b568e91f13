# OS X
.DS_Store

# Xcode
#
# gitignore contributors: remember to update Global/Xcode.gitignore, Objective-C.gitignore & Swift.gitignore

## Build generated
build/
clang/
DerivedData

## Various settings
*.pbxuser
!default.pbxuser
*.mode1v3
!default.mode1v3
*.mode2v3
!default.mode2v3
*.perspectivev3
!default.perspectivev3
xcuserdata

## Other
*.xccheckout
*.moved-aside
*.xcuserstate
*.xcscmblueprint

## Obj-C/Swift specific
*.hmap
*.ipa

# AppCode
.idea/

# Modules map recreated on each build
Modules/module.modulemap

# test run
*.trace

# node stuff
node_modules

# webdriveragent zip bundles
bundles/
webdriveragent-*.tar.gz
uncompressed/
prebuilt-agents/
WebDriverAgentRunner-Runner.app.zip
WebDriverAgentRunner-Runner.app/

# Ruby
Gemfile.lock
