# https://docs.microsoft.com/en-us/azure/devops/pipelines/agents/hosted?view=azure-devops&tabs=yaml
variables:
  MIN_VM_IMAGE: macOS-13
  MIN_XCODE_VERSION: 14.3.1
  MIN_PLATFORM_VERSION: 16.4
  MIN_TV_PLATFORM_VERSION: 16.4
  MIN_TV_DEVICE_NAME: Apple TV 4K (3rd generation)
  MIN_IPHONE_DEVICE_NAME: iPhone 14 Plus
  MIN_IPAD_DEVICE_NAME: iPad Pro (11-inch) (4th generation)
  MAX_VM_IMAGE: macOS-14
  MAX_XCODE_VERSION: 15.4
  MAX_PLATFORM_VERSION: 17.5
  MAX_PLATFORM_VERSION_TV: 17.5
  MAX_IPHONE_DEVICE_NAME: iPhone 15 Plus
  MAX_TV_DEVICE_NAME: Apple TV 4K (3rd generation)
  MAX_IPAD_DEVICE_NAME: iPad Air 11-inch (M2)
  DEFAULT_NODE_VERSION: "18.x"

trigger:
  batch: true
  branches:
    include: [master]

pr:
  autoCancel: true
  branches:
    include: [master]

pool:
  vmImage: "$(MAX_VM_IMAGE)"


parameters:
- name: integrationJobs
  type: object
  default:
  - action: int_test_1
    dest: iphone
  - action: int_test_2
    dest: iphone
  - action: int_test_3
    dest: iphone
  - action: int_test_1
    dest: ipad
  - action: int_test_2
    dest: ipad
  - action: int_test_3
    dest: ipad


stages:
- stage: Unit_Tests_And_Linters
  jobs:
  # region Build
  - template: ./azure-templates/base_job.yml
    parameters:
      name: Generic_iOS_Build_Max_Xcode
      action: build
      target: runner
      sdk: sim
      dest: generic
      codeSign: no
      xcodeVersion: $(MAX_XCODE_VERSION)
      vmImage: $(MAX_VM_IMAGE)
  - template: ./azure-templates/base_job.yml
    parameters:
      name: Generic_tvOS_Build_Max_Xcode
      action: build
      target: tv_runner
      sdk: tv_sim
      dest: tv_generic
      codeSign: no
      xcodeVersion: $(MAX_XCODE_VERSION)
      vmImage: $(MAX_VM_IMAGE)
  - template: ./azure-templates/base_job.yml
    parameters:
      name: iOS_Build_Max_Xcode
      action: build
      target: runner
      sdk: sim
      iphoneModel: $(MAX_IPHONE_DEVICE_NAME)
      ipadModel: $(MAX_IPAD_DEVICE_NAME)
      iosVersion: $(MAX_PLATFORM_VERSION)
      xcodeVersion: $(MAX_XCODE_VERSION)
      vmImage: $(MAX_VM_IMAGE)
  - template: ./azure-templates/base_job.yml
    parameters:
      name: tvOS_Build_Max_Xcode
      action: build
      target: tv_runner
      sdk: tv_sim
      tvModel: $(MAX_TV_DEVICE_NAME)
      tvVersion: $(MAX_PLATFORM_VERSION_TV)
      xcodeVersion: $(MAX_XCODE_VERSION)
      vmImage: $(MAX_VM_IMAGE)
  - template: ./azure-templates/base_job.yml
    parameters:
      name: Generic_iOS_Build_Min_Xcode
      action: build
      target: runner
      sdk: sim
      dest: generic
      codeSign: no
      xcodeVersion: $(MIN_XCODE_VERSION)
      vmImage: $(MIN_VM_IMAGE)
      extraXcArgs: IPHONEOS_DEPLOYMENT_TARGET=$(MIN_PLATFORM_VERSION)
  - template: ./azure-templates/base_job.yml
    parameters:
      name: Generic_tvOS_Build_Min_Xcode
      action: build
      target: tv_runner
      dest: tv_generic
      sdk: tv_sim
      codeSign: no
      xcodeVersion: $(MIN_XCODE_VERSION)
      vmImage: $(MIN_VM_IMAGE)
  - template: ./azure-templates/base_job.yml
    parameters:
      name: iOS_Build_Min_Xcode
      action: build
      target: runner
      sdk: sim
      iphoneModel: $(MIN_IPHONE_DEVICE_NAME)
      ipadModel: $(MIN_IPAD_DEVICE_NAME)
      iosVersion: $(MIN_PLATFORM_VERSION)
      xcodeVersion: $(MIN_XCODE_VERSION)
      vmImage: $(MIN_VM_IMAGE)
  - template: ./azure-templates/base_job.yml
    parameters:
      name: tvOS_Build_Min_Xcode
      action: build
      target: tv_runner
      sdk: tv_sim
      dest: tv
      tvModel: $(MIN_TV_DEVICE_NAME)
      tvVersion: $(MIN_TV_PLATFORM_VERSION)
      xcodeVersion: $(MIN_XCODE_VERSION)
      vmImage: $(MIN_VM_IMAGE)
  # endregion

  # region Analyze
  - template: ./azure-templates/base_job.yml
    parameters:
      name: iOS_Lib_Analyze_Max_Xcode
      action: analyze
      sdk: sim
      target: lib
      iphoneModel: $(MAX_IPHONE_DEVICE_NAME)
      ipadModel: $(MAX_IPAD_DEVICE_NAME)
      iosVersion: $(MAX_PLATFORM_VERSION)
      xcodeVersion: $(MAX_XCODE_VERSION)
      vmImage: $(MAX_VM_IMAGE)
  - template: ./azure-templates/base_job.yml
    parameters:
      name: iOS_Runner_Analyze_Max_Xcode
      action: analyze
      sdk: sim
      target: runner
      iphoneModel: $(MAX_IPHONE_DEVICE_NAME)
      ipadModel: $(MAX_IPAD_DEVICE_NAME)
      iosVersion: $(MAX_PLATFORM_VERSION)
      xcodeVersion: $(MAX_XCODE_VERSION)
      vmImage: $(MAX_VM_IMAGE)
  - template: ./azure-templates/base_job.yml
    parameters:
      name: tvOS_Lib_Analyze_Max_Xcode
      action: analyze
      target: tv_lib
      sdk: tv_sim
      tvModel: $(MAX_TV_DEVICE_NAME)
      tvVersion: $(MAX_PLATFORM_VERSION_TV)
      xcodeVersion: $(MAX_XCODE_VERSION)
      vmImage: $(MAX_VM_IMAGE)
  - template: ./azure-templates/base_job.yml
    parameters:
      name: tvOS_Runner_Analyze_Max_Xcode
      action: analyze
      target: tv_runner
      sdk: tv_sim
      tvModel: $(MAX_TV_DEVICE_NAME)
      tvVersion: $(MAX_PLATFORM_VERSION_TV)
      xcodeVersion: $(MAX_XCODE_VERSION)
      vmImage: $(MAX_VM_IMAGE)
  - template: ./azure-templates/base_job.yml
    parameters:
      name: iOS_Lib_Analyze_Min_Xcode
      action: analyze
      target: lib
      sdk: sim
      iphoneModel: $(MIN_IPHONE_DEVICE_NAME)
      ipadModel: $(MIN_IPAD_DEVICE_NAME)
      iosVersion: $(MIN_PLATFORM_VERSION)
      xcodeVersion: $(MIN_XCODE_VERSION)
      vmImage: $(MIN_VM_IMAGE)
  - template: ./azure-templates/base_job.yml
    parameters:
      name: iOS_Runner_Analyze_Min_Xcode
      action: analyze
      target: runner
      sdk: sim
      iphoneModel: $(MIN_IPHONE_DEVICE_NAME)
      ipadModel: $(MIN_IPAD_DEVICE_NAME)
      iosVersion: $(MIN_PLATFORM_VERSION)
      xcodeVersion: $(MIN_XCODE_VERSION)
      vmImage: $(MIN_VM_IMAGE)
  - template: ./azure-templates/base_job.yml
    parameters:
      name: tvOS_Lib_Analyze_Min_Xcode
      action: analyze
      target: tv_lib
      sdk: tv_sim
      tvModel: $(MIN_TV_DEVICE_NAME)
      tvVersion: $(MIN_TV_PLATFORM_VERSION)
      xcodeVersion: $(MIN_XCODE_VERSION)
      vmImage: $(MIN_VM_IMAGE)
  - template: ./azure-templates/base_job.yml
    parameters:
      name: tvOS_Runner_Analyze_Min_Xcode
      action: analyze
      target: tv_runner
      sdk: tv_sim
      tvModel: $(MIN_TV_DEVICE_NAME)
      tvVersion: $(MIN_TV_PLATFORM_VERSION)
      xcodeVersion: $(MIN_XCODE_VERSION)
      vmImage: $(MIN_VM_IMAGE)
  # endregion

  # region Unit Tests
  - template: ./azure-templates/base_job.yml
    parameters:
      name: iPhone_Unit_Test_Max_Xcode
      action: unit_test
      dest: iphone
      target: lib
      sdk: sim
      iphoneModel: $(MAX_IPHONE_DEVICE_NAME)
      ipadModel: $(MAX_IPAD_DEVICE_NAME)
      iosVersion: $(MAX_PLATFORM_VERSION)
      xcodeVersion: $(MAX_XCODE_VERSION)
      vmImage: $(MAX_VM_IMAGE)
  - template: ./azure-templates/base_job.yml
    parameters:
      name: iPad_Unit_Test_Max_Xcode
      action: unit_test
      dest: ipad
      target: lib
      sdk: sim
      iphoneModel: $(MAX_IPHONE_DEVICE_NAME)
      ipadModel: $(MAX_IPAD_DEVICE_NAME)
      iosVersion: $(MAX_PLATFORM_VERSION)
      xcodeVersion: $(MAX_XCODE_VERSION)
      vmImage: $(MAX_VM_IMAGE)
  - template: ./azure-templates/base_job.yml
    parameters:
      name: tvOS_Unit_Test_Max_Xcode
      action: tv_unit_test
      dest: tv
      target: tv_lib
      sdk: tv_sim
      tvModel: $(MAX_TV_DEVICE_NAME)
      tvVersion: $(MAX_PLATFORM_VERSION_TV)
      xcodeVersion: $(MAX_XCODE_VERSION)
      vmImage: $(MAX_VM_IMAGE)
  - template: ./azure-templates/base_job.yml
    parameters:
      name: iPhone_Unit_Test_Min_Xcode
      action: unit_test
      dest: iphone
      target: lib
      sdk: sim
      iphoneModel: $(MIN_IPHONE_DEVICE_NAME)
      ipadModel: $(MIN_IPAD_DEVICE_NAME)
      iosVersion: $(MIN_PLATFORM_VERSION)
      xcodeVersion: $(MIN_XCODE_VERSION)
      vmImage: $(MIN_VM_IMAGE)
  - template: ./azure-templates/base_job.yml
    parameters:
      name: iPad_Unit_Test_Min_Xcode
      action: unit_test
      dest: ipad
      target: lib
      sdk: sim
      iphoneModel: $(MIN_IPHONE_DEVICE_NAME)
      ipadModel: $(MIN_IPAD_DEVICE_NAME)
      iosVersion: $(MIN_PLATFORM_VERSION)
      xcodeVersion: $(MIN_XCODE_VERSION)
      vmImage: $(MIN_VM_IMAGE)
  - template: ./azure-templates/base_job.yml
    parameters:
      name: tvOS_Unit_Test_Min_Xcode
      action: tv_unit_test
      dest: tv
      target: tv_lib
      sdk: tv_sim
      tvModel: $(MIN_TV_DEVICE_NAME)
      tvVersion: $(MIN_TV_PLATFORM_VERSION)
      xcodeVersion: $(MIN_XCODE_VERSION)
      vmImage: $(MIN_VM_IMAGE)
  # endregion

- stage: Integration_Tests
  jobs:

  # region Integration Tests Max Xcode
  - ${{ each job in parameters.integrationJobs }}:
    - template: ./azure-templates/base_job.yml
      parameters:
        name: ${{ job.dest }}_${{ job.action }}_Max_Xcode
        action: ${{ job.action }}
        dest: ${{ job.dest }}
        target: lib
        sdk: sim
        iphoneModel: $(MAX_IPHONE_DEVICE_NAME)
        ipadModel: $(MAX_IPAD_DEVICE_NAME)
        iosVersion: $(MAX_PLATFORM_VERSION)
        xcodeVersion: $(MAX_XCODE_VERSION)
        vmImage: $(MAX_VM_IMAGE)
  # endregion

  # region Integration Tests Min Xcode
  - ${{ each job in parameters.integrationJobs }}:
    - template: ./azure-templates/base_job.yml
      parameters:
        name: ${{ job.dest }}_${{ job.action }}_Min_Xcode
        action: ${{ job.action }}
        dest: ${{ job.dest }}
        target: lib
        sdk: sim
        iphoneModel: $(MIN_IPHONE_DEVICE_NAME)
        ipadModel: $(MIN_IPAD_DEVICE_NAME)
        iosVersion: $(MIN_PLATFORM_VERSION)
        xcodeVersion: $(MIN_XCODE_VERSION)
        vmImage: $(MIN_VM_IMAGE)
  # endregion
