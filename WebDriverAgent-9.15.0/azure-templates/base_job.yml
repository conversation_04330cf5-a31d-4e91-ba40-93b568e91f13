parameters:
    name: ''
    action: ''
    target: ''
    dest: ''
    sdk: ''
    iphoneModel: ''
    ipadModel: ''
    tvModel: ''
    iosVersion: ''
    xcodeVersion: ''
    tvVersion: ''
    vmImage: ''
    extraXcArgs: ''


jobs:
  - job: ${{ parameters.name }}
    pool:
      vmImage: ${{ parameters.vmImage }}
    variables:
      ACTION: ${{ parameters.action }}
      TARGET: ${{ parameters.target }}
      DEST: ${{ parameters.dest }}
      SDK: ${{ parameters.sdk }}
      CODE_SIGN: ${{ parameters.codeSign }}
      IPHONE_MODEL: ${{ parameters.iphoneModel }}
      TV_MODEL: ${{ parameters.tvModel }}
      IPAD_MODEL: ${{ parameters.ipadModel }}
      IOS_VERSION: ${{ parameters.iosVersion }}
      XCODE_VERSION: ${{ parameters.xcodeVersion }}
      TV_VERSION: ${{ parameters.tvVersion }}
      EXTRA_XC_ARGS: ${{ parameters.extraXcArgs }}
    steps:
    - template: bootstrap_steps.yml
    - script: ./Scripts/build.sh
