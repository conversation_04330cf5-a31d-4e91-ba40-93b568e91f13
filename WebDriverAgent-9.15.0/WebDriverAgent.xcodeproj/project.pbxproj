// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 54;
	objects = {

/* Begin PBXBuildFile section */
		0E0413382DF1E15100AF007C /* XCUIElement+FBMinMax.m in Sources */ = {isa = PBXBuildFile; fileRef = 0E0413372DF1E15100AF007C /* XCUIElement+FBMinMax.m */; };
		0E0413392DF1E15100AF007C /* XCUIElement+FBMinMax.m in Sources */ = {isa = PBXBuildFile; fileRef = 0E0413372DF1E15100AF007C /* XCUIElement+FBMinMax.m */; };
		0E04133B2DF1E15900AF007C /* XCUIElement+FBMinMax.h in Headers */ = {isa = PBXBuildFile; fileRef = 0E04133A2DF1E15900AF007C /* XCUIElement+FBMinMax.h */; };
		0E04133C2DF1E15900AF007C /* XCUIElement+FBMinMax.h in Headers */ = {isa = PBXBuildFile; fileRef = 0E04133A2DF1E15900AF007C /* XCUIElement+FBMinMax.h */; };
		1357E296233D05240054BDB8 /* XCUIHitPointResult.h in Headers */ = {isa = PBXBuildFile; fileRef = 1357E295233D05240054BDB8 /* XCUIHitPointResult.h */; };
		1357E297233D05240054BDB8 /* XCUIHitPointResult.h in Headers */ = {isa = PBXBuildFile; fileRef = 1357E295233D05240054BDB8 /* XCUIHitPointResult.h */; };
		13815F6F2328D20400CDAB61 /* FBActiveAppDetectionPoint.h in Headers */ = {isa = PBXBuildFile; fileRef = 13815F6D2328D20400CDAB61 /* FBActiveAppDetectionPoint.h */; };
		13815F702328D20400CDAB61 /* FBActiveAppDetectionPoint.h in Headers */ = {isa = PBXBuildFile; fileRef = 13815F6D2328D20400CDAB61 /* FBActiveAppDetectionPoint.h */; };
		13815F712328D20400CDAB61 /* FBActiveAppDetectionPoint.m in Sources */ = {isa = PBXBuildFile; fileRef = 13815F6E2328D20400CDAB61 /* FBActiveAppDetectionPoint.m */; };
		13815F722328D20400CDAB61 /* FBActiveAppDetectionPoint.m in Sources */ = {isa = PBXBuildFile; fileRef = 13815F6E2328D20400CDAB61 /* FBActiveAppDetectionPoint.m */; };
		13DE7A43287C2A8D003243C6 /* FBXCAccessibilityElement.h in Headers */ = {isa = PBXBuildFile; fileRef = 13DE7A41287C2A8D003243C6 /* FBXCAccessibilityElement.h */; };
		13DE7A44287C2A8D003243C6 /* FBXCAccessibilityElement.h in Headers */ = {isa = PBXBuildFile; fileRef = 13DE7A41287C2A8D003243C6 /* FBXCAccessibilityElement.h */; };
		13DE7A45287C2A8D003243C6 /* FBXCAccessibilityElement.m in Sources */ = {isa = PBXBuildFile; fileRef = 13DE7A42287C2A8D003243C6 /* FBXCAccessibilityElement.m */; };
		13DE7A46287C2A8D003243C6 /* FBXCAccessibilityElement.m in Sources */ = {isa = PBXBuildFile; fileRef = 13DE7A42287C2A8D003243C6 /* FBXCAccessibilityElement.m */; };
		13DE7A49287C4005003243C6 /* FBXCDeviceEvent.h in Headers */ = {isa = PBXBuildFile; fileRef = 13DE7A47287C4005003243C6 /* FBXCDeviceEvent.h */; };
		13DE7A4A287C4005003243C6 /* FBXCDeviceEvent.h in Headers */ = {isa = PBXBuildFile; fileRef = 13DE7A47287C4005003243C6 /* FBXCDeviceEvent.h */; };
		13DE7A4B287C4005003243C6 /* FBXCDeviceEvent.m in Sources */ = {isa = PBXBuildFile; fileRef = 13DE7A48287C4005003243C6 /* FBXCDeviceEvent.m */; };
		13DE7A4C287C4005003243C6 /* FBXCDeviceEvent.m in Sources */ = {isa = PBXBuildFile; fileRef = 13DE7A48287C4005003243C6 /* FBXCDeviceEvent.m */; };
		13DE7A4F287C46BB003243C6 /* FBXCElementSnapshot.h in Headers */ = {isa = PBXBuildFile; fileRef = 13DE7A4D287C46BB003243C6 /* FBXCElementSnapshot.h */; settings = {ATTRIBUTES = (Public, ); }; };
		13DE7A50287C46BB003243C6 /* FBXCElementSnapshot.h in Headers */ = {isa = PBXBuildFile; fileRef = 13DE7A4D287C46BB003243C6 /* FBXCElementSnapshot.h */; settings = {ATTRIBUTES = (Public, ); }; };
		13DE7A51287C46BB003243C6 /* FBXCElementSnapshot.m in Sources */ = {isa = PBXBuildFile; fileRef = 13DE7A4E287C46BB003243C6 /* FBXCElementSnapshot.m */; };
		13DE7A52287C46BB003243C6 /* FBXCElementSnapshot.m in Sources */ = {isa = PBXBuildFile; fileRef = 13DE7A4E287C46BB003243C6 /* FBXCElementSnapshot.m */; };
		13DE7A55287CA1EC003243C6 /* FBXCElementSnapshotWrapper.h in Headers */ = {isa = PBXBuildFile; fileRef = 13DE7A53287CA1EC003243C6 /* FBXCElementSnapshotWrapper.h */; settings = {ATTRIBUTES = (Public, ); }; };
		13DE7A56287CA1EC003243C6 /* FBXCElementSnapshotWrapper.h in Headers */ = {isa = PBXBuildFile; fileRef = 13DE7A53287CA1EC003243C6 /* FBXCElementSnapshotWrapper.h */; settings = {ATTRIBUTES = (Public, ); }; };
		13DE7A57287CA1EC003243C6 /* FBXCElementSnapshotWrapper.m in Sources */ = {isa = PBXBuildFile; fileRef = 13DE7A54287CA1EC003243C6 /* FBXCElementSnapshotWrapper.m */; };
		13DE7A58287CA1EC003243C6 /* FBXCElementSnapshotWrapper.m in Sources */ = {isa = PBXBuildFile; fileRef = 13DE7A54287CA1EC003243C6 /* FBXCElementSnapshotWrapper.m */; };
		13DE7A5B287CA444003243C6 /* FBXCElementSnapshotWrapper+Helpers.h in Headers */ = {isa = PBXBuildFile; fileRef = 13DE7A59287CA444003243C6 /* FBXCElementSnapshotWrapper+Helpers.h */; };
		13DE7A5C287CA444003243C6 /* FBXCElementSnapshotWrapper+Helpers.h in Headers */ = {isa = PBXBuildFile; fileRef = 13DE7A59287CA444003243C6 /* FBXCElementSnapshotWrapper+Helpers.h */; };
		13DE7A5D287CA444003243C6 /* FBXCElementSnapshotWrapper+Helpers.m in Sources */ = {isa = PBXBuildFile; fileRef = 13DE7A5A287CA444003243C6 /* FBXCElementSnapshotWrapper+Helpers.m */; };
		13DE7A5E287CA444003243C6 /* FBXCElementSnapshotWrapper+Helpers.m in Sources */ = {isa = PBXBuildFile; fileRef = 13DE7A5A287CA444003243C6 /* FBXCElementSnapshotWrapper+Helpers.m */; };
		13FFF2F2287DBEE600E561E4 /* XCElementSnapshotDouble.m in Sources */ = {isa = PBXBuildFile; fileRef = 13FFF2F1287DBEE600E561E4 /* XCElementSnapshotDouble.m */; };
		315A15012518CB8700A3A064 /* TouchableView.m in Sources */ = {isa = PBXBuildFile; fileRef = 315A15002518CB8700A3A064 /* TouchableView.m */; };
		315A15072518CC2800A3A064 /* TouchSpotView.m in Sources */ = {isa = PBXBuildFile; fileRef = 315A15062518CC2800A3A064 /* TouchSpotView.m */; };
		315A150A2518D6F400A3A064 /* TouchViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 315A15092518D6F400A3A064 /* TouchViewController.m */; };
		6385F4A7220A40760095BBDB /* XCUIApplicationProcessDelay.m in Sources */ = {isa = PBXBuildFile; fileRef = 6385F4A5220A40760095BBDB /* XCUIApplicationProcessDelay.m */; };
		63CCF91221ECE4C700E94ABD /* FBImageProcessor.h in Headers */ = {isa = PBXBuildFile; fileRef = 63CCF91021ECE4C700E94ABD /* FBImageProcessor.h */; };
		63CCF91321ECE4C700E94ABD /* FBImageProcessor.m in Sources */ = {isa = PBXBuildFile; fileRef = 63CCF91121ECE4C700E94ABD /* FBImageProcessor.m */; };
		63FD950221F9D06100A3E356 /* FBImageProcessorTests.m in Sources */ = {isa = PBXBuildFile; fileRef = 631B523421F6174300625362 /* FBImageProcessorTests.m */; };
		63FD950321F9D06100A3E356 /* FBImageProcessorTests.m in Sources */ = {isa = PBXBuildFile; fileRef = 631B523421F6174300625362 /* FBImageProcessorTests.m */; };
		63FD950421F9D06200A3E356 /* FBImageProcessorTests.m in Sources */ = {isa = PBXBuildFile; fileRef = 631B523421F6174300625362 /* FBImageProcessorTests.m */; };
		641EE3452240C1C800173FCB /* UITestingUITests.m in Sources */ = {isa = PBXBuildFile; fileRef = EE9AB7FD1CAEE048008C271F /* UITestingUITests.m */; };
		641EE5D72240C5CA00173FCB /* FBScreenshotCommands.m in Sources */ = {isa = PBXBuildFile; fileRef = EE9AB75F1CAEDF0C008C271F /* FBScreenshotCommands.m */; };
		641EE5D92240C5CA00173FCB /* XCUIElement+FBPickerWheel.m in Sources */ = {isa = PBXBuildFile; fileRef = 7136A4781E8918E60024FC3D /* XCUIElement+FBPickerWheel.m */; };
		641EE5DA2240C5CA00173FCB /* XCUIApplicationProcessDelay.m in Sources */ = {isa = PBXBuildFile; fileRef = 6385F4A5220A40760095BBDB /* XCUIApplicationProcessDelay.m */; };
		641EE5DB2240C5CA00173FCB /* FBXPath.m in Sources */ = {isa = PBXBuildFile; fileRef = 711084431DA3AA7500F913D6 /* FBXPath.m */; };
		641EE5DC2240C5CA00173FCB /* XCUIApplication+FBAlert.m in Sources */ = {isa = PBXBuildFile; fileRef = 719CD8FB2126C88B00C7D0C2 /* XCUIApplication+FBAlert.m */; };
		641EE5DE2240C5CA00173FCB /* XCUIApplication+FBTouchAction.m in Sources */ = {isa = PBXBuildFile; fileRef = 71BD20721F86116100B36EC2 /* XCUIApplication+FBTouchAction.m */; };
		641EE5DF2240C5CA00173FCB /* FBWebServer.m in Sources */ = {isa = PBXBuildFile; fileRef = EE9AB78D1CAEDF0C008C271F /* FBWebServer.m */; };
		641EE5E02240C5CA00173FCB /* FBTCPSocket.m in Sources */ = {isa = PBXBuildFile; fileRef = 715557D2211DBCE700613B26 /* FBTCPSocket.m */; };
		641EE5E12240C5CA00173FCB /* FBErrorBuilder.m in Sources */ = {isa = PBXBuildFile; fileRef = EE3A18611CDE618F00DE4205 /* FBErrorBuilder.m */; };
		641EE5E22240C5CA00173FCB /* XCUIElement+FBClassChain.m in Sources */ = {isa = PBXBuildFile; fileRef = 71A7EAF41E20516B001DA4F2 /* XCUIElement+FBClassChain.m */; };
		641EE5E32240C5CA00173FCB /* NSExpression+FBFormat.m in Sources */ = {isa = PBXBuildFile; fileRef = 71555A3C1DEC460A007D4A8B /* NSExpression+FBFormat.m */; };
		641EE5E42240C5CA00173FCB /* XCUIApplication+FBHelpers.m in Sources */ = {isa = PBXBuildFile; fileRef = AD6C269B1CF2494200F8B5FF /* XCUIApplication+FBHelpers.m */; };
		641EE5E52240C5CA00173FCB /* FBKeyboard.m in Sources */ = {isa = PBXBuildFile; fileRef = EE3A18651CDE734B00DE4205 /* FBKeyboard.m */; };
		641EE5E62240C5CA00173FCB /* FBElementUtils.m in Sources */ = {isa = PBXBuildFile; fileRef = 713C6DCE1DDC772A00285B92 /* FBElementUtils.m */; };
		641EE5E72240C5CA00173FCB /* FBW3CActionsSynthesizer.m in Sources */ = {isa = PBXBuildFile; fileRef = 7140974A1FAE1B51008FB2C5 /* FBW3CActionsSynthesizer.m */; };
		641EE5E92240C5CA00173FCB /* FBFailureProofTestCase.m in Sources */ = {isa = PBXBuildFile; fileRef = EE6A89391D0B38640083E92B /* FBFailureProofTestCase.m */; };
		641EE5EA2240C5CA00173FCB /* XCUIElement+FBIsVisible.m in Sources */ = {isa = PBXBuildFile; fileRef = EE9AB7481CAEDF0C008C271F /* XCUIElement+FBIsVisible.m */; };
		641EE5EB2240C5CA00173FCB /* XCUIElement+FBFind.m in Sources */ = {isa = PBXBuildFile; fileRef = EEBBD48A1D47746D00656A81 /* XCUIElement+FBFind.m */; };
		641EE5EC2240C5CA00173FCB /* FBResponsePayload.m in Sources */ = {isa = PBXBuildFile; fileRef = EE9AB7831CAEDF0C008C271F /* FBResponsePayload.m */; };
		641EE5ED2240C5CA00173FCB /* FBRoute.m in Sources */ = {isa = PBXBuildFile; fileRef = EE9AB7851CAEDF0C008C271F /* FBRoute.m */; };
		641EE5EE2240C5CA00173FCB /* NSString+FBVisualLength.m in Sources */ = {isa = PBXBuildFile; fileRef = EE0D1F601EBCDCF7006A3123 /* NSString+FBVisualLength.m */; };
		641EE5EF2240C5CA00173FCB /* FBRunLoopSpinner.m in Sources */ = {isa = PBXBuildFile; fileRef = EEE9B4711CD02B88009D2030 /* FBRunLoopSpinner.m */; };
		641EE5F02240C5CA00173FCB /* FBAlertsMonitor.m in Sources */ = {isa = PBXBuildFile; fileRef = 719CD8F72126C78F00C7D0C2 /* FBAlertsMonitor.m */; };
		641EE5F12240C5CA00173FCB /* FBClassChainQueryParser.m in Sources */ = {isa = PBXBuildFile; fileRef = 71A7EAF81E224648001DA4F2 /* FBClassChainQueryParser.m */; };
		641EE5F22240C5CA00173FCB /* NSPredicate+FBFormat.m in Sources */ = {isa = PBXBuildFile; fileRef = 71A224E41DE2F56600844D55 /* NSPredicate+FBFormat.m */; };
		641EE5F42240C5CA00173FCB /* XCUIDevice+FBRotation.m in Sources */ = {isa = PBXBuildFile; fileRef = EEE3763E1D59F81400ED88DD /* XCUIDevice+FBRotation.m */; };
		641EE5F52240C5CA00173FCB /* XCUIElement+FBUID.m in Sources */ = {isa = PBXBuildFile; fileRef = 71B49EC61ED1A58100D51AD6 /* XCUIElement+FBUID.m */; };
		641EE5F62240C5CA00173FCB /* FBRouteRequest.m in Sources */ = {isa = PBXBuildFile; fileRef = EE9AB7881CAEDF0C008C271F /* FBRouteRequest.m */; };
		641EE5F72240C5CA00173FCB /* FBResponseJSONPayload.m in Sources */ = {isa = PBXBuildFile; fileRef = EE9AB7811CAEDF0C008C271F /* FBResponseJSONPayload.m */; };
		641EE5F92240C5CA00173FCB /* FBMjpegServer.m in Sources */ = {isa = PBXBuildFile; fileRef = 7155D702211DCEF400166C20 /* FBMjpegServer.m */; };
		641EE5FA2240C5CA00173FCB /* XCUIDevice+FBHealthCheck.m in Sources */ = {isa = PBXBuildFile; fileRef = EEDFE1201D9C06F800E6FFE5 /* XCUIDevice+FBHealthCheck.m */; };
		641EE5FD2240C5CA00173FCB /* FBBaseActionsSynthesizer.m in Sources */ = {isa = PBXBuildFile; fileRef = 7140974D1FAE20EE008FB2C5 /* FBBaseActionsSynthesizer.m */; };
		641EE5FE2240C5CA00173FCB /* XCUIElement+FBWebDriverAttributes.m in Sources */ = {isa = PBXBuildFile; fileRef = EEE376481D59FAE900ED88DD /* XCUIElement+FBWebDriverAttributes.m */; };
		641EE5FF2240C5CA00173FCB /* XCUIElement+FBForceTouch.m in Sources */ = {isa = PBXBuildFile; fileRef = EE8DDD7C20C5733B004D4925 /* XCUIElement+FBForceTouch.m */; };
		641EE6002240C5CA00173FCB /* FBTouchActionCommands.m in Sources */ = {isa = PBXBuildFile; fileRef = 71241D7A1FAE3D2500B9559F /* FBTouchActionCommands.m */; };
		641EE6012240C5CA00173FCB /* FBImageProcessor.m in Sources */ = {isa = PBXBuildFile; fileRef = 63CCF91121ECE4C700E94ABD /* FBImageProcessor.m */; };
		641EE6022240C5CA00173FCB /* FBTouchIDCommands.m in Sources */ = {isa = PBXBuildFile; fileRef = EE9AB7631CAEDF0C008C271F /* FBTouchIDCommands.m */; };
		641EE6032240C5CA00173FCB /* FBDebugCommands.m in Sources */ = {isa = PBXBuildFile; fileRef = EE9AB7551CAEDF0C008C271F /* FBDebugCommands.m */; };
		641EE6042240C5CA00173FCB /* NSString+FBXMLSafeString.m in Sources */ = {isa = PBXBuildFile; fileRef = 716E0BCD1E917E810087A825 /* NSString+FBXMLSafeString.m */; };
		641EE6052240C5CA00173FCB /* FBUnknownCommands.m in Sources */ = {isa = PBXBuildFile; fileRef = EE9AB7651CAEDF0C008C271F /* FBUnknownCommands.m */; };
		641EE6062240C5CA00173FCB /* FBOrientationCommands.m in Sources */ = {isa = PBXBuildFile; fileRef = EE9AB75D1CAEDF0C008C271F /* FBOrientationCommands.m */; };
		641EE6082240C5CA00173FCB /* FBRuntimeUtils.m in Sources */ = {isa = PBXBuildFile; fileRef = EE9AB7921CAEDF0C008C271F /* FBRuntimeUtils.m */; };
		641EE6092240C5CA00173FCB /* XCUIElement+FBUtilities.m in Sources */ = {isa = PBXBuildFile; fileRef = EEE376401D59F81400ED88DD /* XCUIElement+FBUtilities.m */; };
		641EE60A2240C5CA00173FCB /* FBLogger.m in Sources */ = {isa = PBXBuildFile; fileRef = EE9B76A41CF7A43900275851 /* FBLogger.m */; };
		641EE60B2240C5CA00173FCB /* FBCustomCommands.m in Sources */ = {isa = PBXBuildFile; fileRef = EE9AB7531CAEDF0C008C271F /* FBCustomCommands.m */; };
		641EE60C2240C5CA00173FCB /* XCUIDevice+FBHelpers.m in Sources */ = {isa = PBXBuildFile; fileRef = AD6C26971CF2481700F8B5FF /* XCUIDevice+FBHelpers.m */; };
		641EE60D2240C5CA00173FCB /* XCTestPrivateSymbols.m in Sources */ = {isa = PBXBuildFile; fileRef = EE6B64FC1D0F86EF00E85F5D /* XCTestPrivateSymbols.m */; };
		641EE60E2240C5CA00173FCB /* XCUIElement+FBTyping.m in Sources */ = {isa = PBXBuildFile; fileRef = AD76723C1D6B7CC000610457 /* XCUIElement+FBTyping.m */; };
		641EE60F2240C5CA00173FCB /* XCUIElement+FBAccessibility.m in Sources */ = {isa = PBXBuildFile; fileRef = EE9AB7461CAEDF0C008C271F /* XCUIElement+FBAccessibility.m */; };
		641EE6102240C5CA00173FCB /* FBImageUtils.m in Sources */ = {isa = PBXBuildFile; fileRef = 7150348621A6DAD600A0F4BA /* FBImageUtils.m */; };
		641EE6112240C5CA00173FCB /* FBSession.m in Sources */ = {isa = PBXBuildFile; fileRef = EE9AB78B1CAEDF0C008C271F /* FBSession.m */; };
		641EE6122240C5CA00173FCB /* FBFindElementCommands.m in Sources */ = {isa = PBXBuildFile; fileRef = EE9AB7591CAEDF0C008C271F /* FBFindElementCommands.m */; };
		641EE6132240C5CA00173FCB /* FBDebugLogDelegateDecorator.m in Sources */ = {isa = PBXBuildFile; fileRef = EE7E27191D06C69F001BEC7B /* FBDebugLogDelegateDecorator.m */; };
		641EE6142240C5CA00173FCB /* FBAlertViewCommands.m in Sources */ = {isa = PBXBuildFile; fileRef = EE9AB7511CAEDF0C008C271F /* FBAlertViewCommands.m */; };
		641EE6152240C5CA00173FCB /* XCUIElement+FBScrolling.m in Sources */ = {isa = PBXBuildFile; fileRef = EE9AB74A1CAEDF0C008C271F /* XCUIElement+FBScrolling.m */; };
		641EE6162240C5CA00173FCB /* FBSessionCommands.m in Sources */ = {isa = PBXBuildFile; fileRef = EE9AB7611CAEDF0C008C271F /* FBSessionCommands.m */; };
		641EE6192240C5CA00173FCB /* FBConfiguration.m in Sources */ = {isa = PBXBuildFile; fileRef = EE9B76A21CF7A43900275851 /* FBConfiguration.m */; };
		641EE61A2240C5CA00173FCB /* FBElementCache.m in Sources */ = {isa = PBXBuildFile; fileRef = EEC088E41CB56AC000B65968 /* FBElementCache.m */; };
		641EE61B2240C5CA00173FCB /* FBPasteboard.m in Sources */ = {isa = PBXBuildFile; fileRef = 71930C4120662E1F00D3AFEC /* FBPasteboard.m */; };
		641EE61C2240C5CA00173FCB /* FBAlert.m in Sources */ = {isa = PBXBuildFile; fileRef = AD6C26931CF2379700F8B5FF /* FBAlert.m */; };
		641EE61D2240C5CA00173FCB /* FBElementCommands.m in Sources */ = {isa = PBXBuildFile; fileRef = EE9AB7571CAEDF0C008C271F /* FBElementCommands.m */; };
		641EE61E2240C5CA00173FCB /* FBExceptionHandler.m in Sources */ = {isa = PBXBuildFile; fileRef = EEC088E71CB56DA400B65968 /* FBExceptionHandler.m */; };
		641EE61F2240C5CA00173FCB /* FBXCodeCompatibility.m in Sources */ = {isa = PBXBuildFile; fileRef = EE5A24411F136C8D0078B1D9 /* FBXCodeCompatibility.m */; };
		641EE6212240C5CA00173FCB /* FBElementTypeTransformer.m in Sources */ = {isa = PBXBuildFile; fileRef = EE9AB7901CAEDF0C008C271F /* FBElementTypeTransformer.m */; };
		641EE6232240C5CA00173FCB /* FBScreen.m in Sources */ = {isa = PBXBuildFile; fileRef = 715AFAC01FFA29180053896D /* FBScreen.m */; };
		641EE6242240C5CA00173FCB /* FBXCTestDaemonsProxy.m in Sources */ = {isa = PBXBuildFile; fileRef = EE35AD7A1E3B80C000A02D78 /* FBXCTestDaemonsProxy.m */; };
		641EE6262240C5CA00173FCB /* FBMathUtils.m in Sources */ = {isa = PBXBuildFile; fileRef = EE1888391DA661C400307AA8 /* FBMathUtils.m */; };
		641EE6272240C5CA00173FCB /* FBXCAXClientProxy.m in Sources */ = {isa = PBXBuildFile; fileRef = 7157B290221DADD2001C348C /* FBXCAXClientProxy.m */; };
		641EE6312240C5CA00173FCB /* XCUIElement+FBWebDriverAttributes.h in Headers */ = {isa = PBXBuildFile; fileRef = EEE376471D59FAE900ED88DD /* XCUIElement+FBWebDriverAttributes.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6322240C5CA00173FCB /* FBScreen.h in Headers */ = {isa = PBXBuildFile; fileRef = 715AFABF1FFA29180053896D /* FBScreen.h */; };
		641EE6332240C5CA00173FCB /* XCTestPrivateSymbols.h in Headers */ = {isa = PBXBuildFile; fileRef = EE6B64FB1D0F86EF00E85F5D /* XCTestPrivateSymbols.h */; };
		641EE6342240C5CA00173FCB /* XCUIElement+FBTyping.h in Headers */ = {isa = PBXBuildFile; fileRef = AD76723B1D6B7CC000610457 /* XCUIElement+FBTyping.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6352240C5CA00173FCB /* XCUIElement+FBUtilities.h in Headers */ = {isa = PBXBuildFile; fileRef = EEE3763F1D59F81400ED88DD /* XCUIElement+FBUtilities.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6362240C5CA00173FCB /* XCUIElement+FBScrolling.h in Headers */ = {isa = PBXBuildFile; fileRef = EE9AB7491CAEDF0C008C271F /* XCUIElement+FBScrolling.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6372240C5CA00173FCB /* XCSourceCodeTreeNode.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACC51E3B77D600A02D78 /* XCSourceCodeTreeNode.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6382240C5CA00173FCB /* XCPointerEventPath.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACC31E3B77D600A02D78 /* XCPointerEventPath.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6392240C5CA00173FCB /* FBRouteRequest.h in Headers */ = {isa = PBXBuildFile; fileRef = EE9AB7871CAEDF0C008C271F /* FBRouteRequest.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE63A2240C5CA00173FCB /* XCTest.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACCF1E3B77D600A02D78 /* XCTest.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE63B2240C5CA00173FCB /* FBAlertsMonitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 719CD8F62126C78F00C7D0C2 /* FBAlertsMonitor.h */; };
		641EE63D2240C5CA00173FCB /* FBSession.h in Headers */ = {isa = PBXBuildFile; fileRef = EE9AB78A1CAEDF0C008C271F /* FBSession.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE63E2240C5CA00173FCB /* _XCTestImplementation.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35AC9E1E3B77D600A02D78 /* _XCTestImplementation.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE63F2240C5CA00173FCB /* FBTouchActionCommands.h in Headers */ = {isa = PBXBuildFile; fileRef = 71241D791FAE3D2500B9559F /* FBTouchActionCommands.h */; };
		641EE6402240C5CA00173FCB /* FBTouchIDCommands.h in Headers */ = {isa = PBXBuildFile; fileRef = EE9AB7621CAEDF0C008C271F /* FBTouchIDCommands.h */; };
		641EE6412240C5CA00173FCB /* XCUIApplication.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACF91E3B77D600A02D78 /* XCUIApplication.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6422240C5CA00173FCB /* FBCustomCommands.h in Headers */ = {isa = PBXBuildFile; fileRef = EE9AB7521CAEDF0C008C271F /* FBCustomCommands.h */; };
		641EE6432240C5CA00173FCB /* _XCTestCaseInterruptionException.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35AC9C1E3B77D600A02D78 /* _XCTestCaseInterruptionException.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6442240C5CA00173FCB /* FBOrientationCommands.h in Headers */ = {isa = PBXBuildFile; fileRef = EE9AB75C1CAEDF0C008C271F /* FBOrientationCommands.h */; };
		641EE6452240C5CA00173FCB /* XCUIScreen.h in Headers */ = {isa = PBXBuildFile; fileRef = 7119097B2152580600BA3C7E /* XCUIScreen.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6462240C5CA00173FCB /* XCTRunnerIDESession.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACF01E3B77D600A02D78 /* XCTRunnerIDESession.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6472240C5CA00173FCB /* FBRouteRequest-Private.h in Headers */ = {isa = PBXBuildFile; fileRef = EE9AB7861CAEDF0C008C271F /* FBRouteRequest-Private.h */; };
		641EE6482240C5CA00173FCB /* XCTTestRunSession.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACF11E3B77D600A02D78 /* XCTTestRunSession.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6492240C5CA00173FCB /* XCTestProbe.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACE31E3B77D600A02D78 /* XCTestProbe.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE64A2240C5CA00173FCB /* XCApplicationQuery.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACB71E3B77D600A02D78 /* XCApplicationQuery.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE64B2240C5CA00173FCB /* XCTAsyncActivity.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACCB1E3B77D600A02D78 /* XCTAsyncActivity.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE64C2240C5CA00173FCB /* XCTestMisuseObserver.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACDF1E3B77D600A02D78 /* XCTestMisuseObserver.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE64D2240C5CA00173FCB /* XCTRunnerDaemonSession.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACEF1E3B77D600A02D78 /* XCTRunnerDaemonSession.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE64F2240C5CA00173FCB /* XCTestExpectationWaiter.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACDA1E3B77D600A02D78 /* XCTestExpectationWaiter.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6502240C5CA00173FCB /* UIGestureRecognizer-RecordingAdditions.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACAD1E3B77D600A02D78 /* UIGestureRecognizer-RecordingAdditions.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6512240C5CA00173FCB /* XCKeyboardKeyMap.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACBF1E3B77D600A02D78 /* XCKeyboardKeyMap.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6522240C5CA00173FCB /* XCTNSPredicateExpectationObject-Protocol.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACEC1E3B77D600A02D78 /* XCTNSPredicateExpectationObject-Protocol.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6532240C5CA00173FCB /* WebDriverAgentLib.h in Headers */ = {isa = PBXBuildFile; fileRef = EE158B5E1CBD47A000A3E3F0 /* WebDriverAgentLib.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6542240C5CA00173FCB /* FBFindElementCommands.h in Headers */ = {isa = PBXBuildFile; fileRef = EE9AB7581CAEDF0C008C271F /* FBFindElementCommands.h */; };
		641EE6552240C5CA00173FCB /* XCTestRun.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACE41E3B77D600A02D78 /* XCTestRun.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6562240C5CA00173FCB /* FBWebServer.h in Headers */ = {isa = PBXBuildFile; fileRef = EE9AB78C1CAEDF0C008C271F /* FBWebServer.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6572240C5CA00173FCB /* FBScreenshotCommands.h in Headers */ = {isa = PBXBuildFile; fileRef = EE9AB75E1CAEDF0C008C271F /* FBScreenshotCommands.h */; };
		641EE6582240C5CA00173FCB /* _XCKVOExpectationImplementation.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35AC991E3B77D600A02D78 /* _XCKVOExpectationImplementation.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6592240C5CA00173FCB /* NSString+FBVisualLength.h in Headers */ = {isa = PBXBuildFile; fileRef = EE0D1F5F1EBCDCF7006A3123 /* NSString+FBVisualLength.h */; };
		641EE65A2240C5CA00173FCB /* FBXCTestDaemonsProxy.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35AD791E3B80C000A02D78 /* FBXCTestDaemonsProxy.h */; };
		641EE65B2240C5CA00173FCB /* XCUIElementHitPointCoordinate.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35AD011E3B77D600A02D78 /* XCUIElementHitPointCoordinate.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE65C2240C5CA00173FCB /* XCTDarwinNotificationExpectation.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACCE1E3B77D600A02D78 /* XCTDarwinNotificationExpectation.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE65D2240C5CA00173FCB /* XCTRunnerAutomationSession.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACEE1E3B77D600A02D78 /* XCTRunnerAutomationSession.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE65F2240C5CA00173FCB /* XCSourceCodeTreeNodeEnumerator.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACC61E3B77D600A02D78 /* XCSourceCodeTreeNodeEnumerator.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6602240C5CA00173FCB /* XCUIElement+FBIsVisible.h in Headers */ = {isa = PBXBuildFile; fileRef = EE9AB7471CAEDF0C008C271F /* XCUIElement+FBIsVisible.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6622240C5CA00173FCB /* FBResponsePayload.h in Headers */ = {isa = PBXBuildFile; fileRef = EE9AB7821CAEDF0C008C271F /* FBResponsePayload.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6632240C5CA00173FCB /* FBUnknownCommands.h in Headers */ = {isa = PBXBuildFile; fileRef = EE9AB7641CAEDF0C008C271F /* FBUnknownCommands.h */; };
		641EE6642240C5CA00173FCB /* NSPredicate+FBFormat.h in Headers */ = {isa = PBXBuildFile; fileRef = 71A224E31DE2F56600844D55 /* NSPredicate+FBFormat.h */; };
		641EE6652240C5CA00173FCB /* UILongPressGestureRecognizer-RecordingAdditions.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACAE1E3B77D600A02D78 /* UILongPressGestureRecognizer-RecordingAdditions.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6662240C5CA00173FCB /* XCTestCase.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACD01E3B77D600A02D78 /* XCTestCase.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6672240C5CA00173FCB /* XCSymbolicatorHolder.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACC81E3B77D600A02D78 /* XCSymbolicatorHolder.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6682240C5CA00173FCB /* XCUIApplicationImpl.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACFA1E3B77D600A02D78 /* XCUIApplicationImpl.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6692240C5CA00173FCB /* UIPanGestureRecognizer-RecordingAdditions.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACAF1E3B77D600A02D78 /* UIPanGestureRecognizer-RecordingAdditions.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE66A2240C5CA00173FCB /* NSExpression+FBFormat.h in Headers */ = {isa = PBXBuildFile; fileRef = 71555A3B1DEC460A007D4A8B /* NSExpression+FBFormat.h */; };
		641EE66B2240C5CA00173FCB /* _XCTestCaseImplementation.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35AC9B1E3B77D600A02D78 /* _XCTestCaseImplementation.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE66C2240C5CA00173FCB /* UIPinchGestureRecognizer-RecordingAdditions.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACB01E3B77D600A02D78 /* UIPinchGestureRecognizer-RecordingAdditions.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE66D2240C5CA00173FCB /* XCTestManager_TestsInterface-Protocol.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACDE1E3B77D600A02D78 /* XCTestManager_TestsInterface-Protocol.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE66E2240C5CA00173FCB /* XCUIApplication+FBAlert.h in Headers */ = {isa = PBXBuildFile; fileRef = 719CD8FA2126C88B00C7D0C2 /* XCUIApplication+FBAlert.h */; };
		641EE6702240C5CA00173FCB /* FBMathUtils.h in Headers */ = {isa = PBXBuildFile; fileRef = EE1888381DA661C400307AA8 /* FBMathUtils.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6712240C5CA00173FCB /* UISwipeGestureRecognizer-RecordingAdditions.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACB11E3B77D600A02D78 /* UISwipeGestureRecognizer-RecordingAdditions.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6722240C5CA00173FCB /* FBElementUtils.h in Headers */ = {isa = PBXBuildFile; fileRef = 713C6DCD1DDC772A00285B92 /* FBElementUtils.h */; };
		641EE6732240C5CA00173FCB /* FBDebugCommands.h in Headers */ = {isa = PBXBuildFile; fileRef = EE9AB7541CAEDF0C008C271F /* FBDebugCommands.h */; };
		641EE6742240C5CA00173FCB /* XCTestSuite.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACE51E3B77D600A02D78 /* XCTestSuite.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6752240C5CA00173FCB /* XCUICoordinate.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACFC1E3B77D600A02D78 /* XCUICoordinate.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6762240C5CA00173FCB /* XCTNSPredicateExpectation.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACEB1E3B77D600A02D78 /* XCTNSPredicateExpectation.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6772240C5CA00173FCB /* XCTestObservationCenter.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACE11E3B77D600A02D78 /* XCTestObservationCenter.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6782240C5CA00173FCB /* XCTNSNotificationExpectation.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACEA1E3B77D600A02D78 /* XCTNSNotificationExpectation.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6792240C5CA00173FCB /* XCUIRecorderNodeFinder.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35AD041E3B77D600A02D78 /* XCUIRecorderNodeFinder.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE67A2240C5CA00173FCB /* XCUIElement+FBAccessibility.h in Headers */ = {isa = PBXBuildFile; fileRef = EE9AB7451CAEDF0C008C271F /* XCUIElement+FBAccessibility.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE67B2240C5CA00173FCB /* XCUIRecorderUtilities.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35AD071E3B77D600A02D78 /* XCUIRecorderUtilities.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE67C2240C5CA00173FCB /* XCTestCaseRun.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACD11E3B77D600A02D78 /* XCTestCaseRun.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE67D2240C5CA00173FCB /* XCTestConfiguration.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACD31E3B77D600A02D78 /* XCTestConfiguration.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE67E2240C5CA00173FCB /* _XCTDarwinNotificationExpectationImplementation.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35AC9A1E3B77D600A02D78 /* _XCTDarwinNotificationExpectationImplementation.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE67F2240C5CA00173FCB /* XCTestExpectation.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACD81E3B77D600A02D78 /* XCTestExpectation.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6802240C5CA00173FCB /* FBElementTypeTransformer.h in Headers */ = {isa = PBXBuildFile; fileRef = EE9AB78F1CAEDF0C008C271F /* FBElementTypeTransformer.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6812240C5CA00173FCB /* FBXCAXClientProxy.h in Headers */ = {isa = PBXBuildFile; fileRef = 7157B28F221DADD2001C348C /* FBXCAXClientProxy.h */; };
		641EE6822240C5CA00173FCB /* FBElementCache.h in Headers */ = {isa = PBXBuildFile; fileRef = EE9AB77B1CAEDF0C008C271F /* FBElementCache.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6832240C5CA00173FCB /* XCTMetric.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACE91E3B77D600A02D78 /* XCTMetric.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6842240C5CA00173FCB /* XCTestContextScope.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACD51E3B77D600A02D78 /* XCTestContextScope.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6852240C5CA00173FCB /* XCUIElement+FBClassChain.h in Headers */ = {isa = PBXBuildFile; fileRef = 71A7EAF31E20516B001DA4F2 /* XCUIElement+FBClassChain.h */; };
		641EE6862240C5CA00173FCB /* FBResponseJSONPayload.h in Headers */ = {isa = PBXBuildFile; fileRef = EE9AB7801CAEDF0C008C271F /* FBResponseJSONPayload.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6872240C5CA00173FCB /* XCTAutomationTarget-Protocol.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACCC1E3B77D600A02D78 /* XCTAutomationTarget-Protocol.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6882240C5CA00173FCB /* FBElement.h in Headers */ = {isa = PBXBuildFile; fileRef = EE9AB7791CAEDF0C008C271F /* FBElement.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6892240C5CA00173FCB /* XCTAXClient-Protocol.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACCD1E3B77D600A02D78 /* XCTAXClient-Protocol.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE68B2240C5CA00173FCB /* FBExceptionHandler.h in Headers */ = {isa = PBXBuildFile; fileRef = EEC088E61CB56DA400B65968 /* FBExceptionHandler.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE68C2240C5CA00173FCB /* FBRoute.h in Headers */ = {isa = PBXBuildFile; fileRef = EE9AB7841CAEDF0C008C271F /* FBRoute.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE68D2240C5CA00173FCB /* XCTestDriver.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACD61E3B77D600A02D78 /* XCTestDriver.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE68E2240C5CA00173FCB /* _XCTNSNotificationExpectationImplementation.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACA11E3B77D600A02D78 /* _XCTNSNotificationExpectationImplementation.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE68F2240C5CA00173FCB /* XCSynthesizedEventRecord.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACC91E3B77D600A02D78 /* XCSynthesizedEventRecord.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6922240C5CA00173FCB /* XCTWaiterDelegatePrivate-Protocol.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACF61E3B77D600A02D78 /* XCTWaiterDelegatePrivate-Protocol.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6932240C5CA00173FCB /* XCTestManager_IDEInterface-Protocol.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACDC1E3B77D600A02D78 /* XCTestManager_IDEInterface-Protocol.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6942240C5CA00173FCB /* FBXPath.h in Headers */ = {isa = PBXBuildFile; fileRef = 711084421DA3AA7500F913D6 /* FBXPath.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6952240C5CA00173FCB /* XCUIRecorderTimingMessage.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35AD061E3B77D600A02D78 /* XCUIRecorderTimingMessage.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6962240C5CA00173FCB /* XCApplicationMonitor.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACB61E3B77D600A02D78 /* XCApplicationMonitor.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6972240C5CA00173FCB /* XCUIElement+FBForceTouch.h in Headers */ = {isa = PBXBuildFile; fileRef = EE8DDD7D20C5733C004D4925 /* XCUIElement+FBForceTouch.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6982240C5CA00173FCB /* FBRuntimeUtils.h in Headers */ = {isa = PBXBuildFile; fileRef = EE9AB7911CAEDF0C008C271F /* FBRuntimeUtils.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6992240C5CA00173FCB /* XCUIElement+FBPickerWheel.h in Headers */ = {isa = PBXBuildFile; fileRef = 7136A4771E8918E60024FC3D /* XCUIElement+FBPickerWheel.h */; };
		641EE69A2240C5CA00173FCB /* XCTestObservation-Protocol.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACE01E3B77D600A02D78 /* XCTestObservation-Protocol.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE69B2240C5CA00173FCB /* _XCTNSPredicateExpectationImplementation.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACA21E3B77D600A02D78 /* _XCTNSPredicateExpectationImplementation.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE69C2240C5CA00173FCB /* FBElementCommands.h in Headers */ = {isa = PBXBuildFile; fileRef = EE9AB7561CAEDF0C008C271F /* FBElementCommands.h */; };
		641EE69F2240C5CA00173FCB /* FBTCPSocket.h in Headers */ = {isa = PBXBuildFile; fileRef = 715557D1211DBCE700613B26 /* FBTCPSocket.h */; };
		641EE6A02240C5CA00173FCB /* XCUIElement+FBUID.h in Headers */ = {isa = PBXBuildFile; fileRef = 71B49EC51ED1A58100D51AD6 /* XCUIElement+FBUID.h */; };
		641EE6A12240C5CA00173FCB /* XCSymbolicationRecord.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACC71E3B77D600A02D78 /* XCSymbolicationRecord.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6A22240C5CA00173FCB /* XCUIDevice.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACFD1E3B77D600A02D78 /* XCUIDevice.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6A32240C5CA00173FCB /* XCUIApplication+FBTouchAction.h in Headers */ = {isa = PBXBuildFile; fileRef = 71BD20711F86116100B36EC2 /* XCUIApplication+FBTouchAction.h */; };
		641EE6A42240C5CA00173FCB /* FBCommandHandler.h in Headers */ = {isa = PBXBuildFile; fileRef = EE9AB7751CAEDF0C008C271F /* FBCommandHandler.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6A52240C5CA00173FCB /* FBSessionCommands.h in Headers */ = {isa = PBXBuildFile; fileRef = EE9AB7601CAEDF0C008C271F /* FBSessionCommands.h */; };
		641EE6A62240C5CA00173FCB /* FBImageProcessor.h in Headers */ = {isa = PBXBuildFile; fileRef = 63CCF91021ECE4C700E94ABD /* FBImageProcessor.h */; };
		641EE6A72240C5CA00173FCB /* FBSession-Private.h in Headers */ = {isa = PBXBuildFile; fileRef = EE9AB7891CAEDF0C008C271F /* FBSession-Private.h */; };
		641EE6A82240C5CA00173FCB /* NSString+FBXMLSafeString.h in Headers */ = {isa = PBXBuildFile; fileRef = 716E0BCC1E917E810087A825 /* NSString+FBXMLSafeString.h */; };
		641EE6A92240C5CA00173FCB /* FBCommandStatus.h in Headers */ = {isa = PBXBuildFile; fileRef = EE9AB7761CAEDF0C008C271F /* FBCommandStatus.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6AB2240C5CA00173FCB /* FBAlertViewCommands.h in Headers */ = {isa = PBXBuildFile; fileRef = EE9AB7501CAEDF0C008C271F /* FBAlertViewCommands.h */; };
		641EE6AC2240C5CA00173FCB /* XCTWaiter.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACF41E3B77D600A02D78 /* XCTWaiter.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6AD2240C5CA00173FCB /* XCTWaiterManagement-Protocol.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACF71E3B77D600A02D78 /* XCTWaiterManagement-Protocol.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6AF2240C5CA00173FCB /* XCTestContext.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACD41E3B77D600A02D78 /* XCTestContext.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6B12240C5CA00173FCB /* XCTWaiterDelegate-Protocol.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACF51E3B77D600A02D78 /* XCTWaiterDelegate-Protocol.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6B22240C5CA00173FCB /* _XCTestExpectationImplementation.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35AC9D1E3B77D600A02D78 /* _XCTestExpectationImplementation.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6B32240C5CA00173FCB /* XCAXClient_iOS.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACB81E3B77D600A02D78 /* XCAXClient_iOS.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6B42240C5CA00173FCB /* XCTWaiterManager.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACF81E3B77D600A02D78 /* XCTWaiterManager.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6B52240C5CA00173FCB /* XCTestDriverInterface-Protocol.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACD71E3B77D600A02D78 /* XCTestDriverInterface-Protocol.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6B62240C5CA00173FCB /* _XCTestSuiteImplementation.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACA01E3B77D600A02D78 /* _XCTestSuiteImplementation.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6B72240C5CA00173FCB /* FBBaseActionsSynthesizer.h in Headers */ = {isa = PBXBuildFile; fileRef = 714097411FAE1B0B008FB2C5 /* FBBaseActionsSynthesizer.h */; };
		641EE6B82240C5CA00173FCB /* FBAlert.h in Headers */ = {isa = PBXBuildFile; fileRef = AD6C26921CF2379700F8B5FF /* FBAlert.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6B92240C5CA00173FCB /* XCUIElementQuery.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35AD021E3B77D600A02D78 /* XCUIElementQuery.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6BA2240C5CA00173FCB /* XCPointerEvent.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACC21E3B77D600A02D78 /* XCPointerEvent.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6BB2240C5CA00173FCB /* XCSourceCodeRecording.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACC41E3B77D600A02D78 /* XCSourceCodeRecording.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6BC2240C5CA00173FCB /* FBRunLoopSpinner.h in Headers */ = {isa = PBXBuildFile; fileRef = EEE9B4701CD02B88009D2030 /* FBRunLoopSpinner.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6BD2240C5CA00173FCB /* FBErrorBuilder.h in Headers */ = {isa = PBXBuildFile; fileRef = EE3A18601CDE618F00DE4205 /* FBErrorBuilder.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6BE2240C5CA00173FCB /* XCApplicationMonitor_iOS.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACB51E3B77D600A02D78 /* XCApplicationMonitor_iOS.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6BF2240C5CA00173FCB /* FBKeyboard.h in Headers */ = {isa = PBXBuildFile; fileRef = EE3A18641CDE734B00DE4205 /* FBKeyboard.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6C02240C5CA00173FCB /* XCUIApplication+FBHelpers.h in Headers */ = {isa = PBXBuildFile; fileRef = AD6C269A1CF2494200F8B5FF /* XCUIApplication+FBHelpers.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6C12240C5CA00173FCB /* _XCTestObservationCenterImplementation.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35AC9F1E3B77D600A02D78 /* _XCTestObservationCenterImplementation.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6C22240C5CA00173FCB /* XCUIDevice+FBHelpers.h in Headers */ = {isa = PBXBuildFile; fileRef = AD6C26961CF2481700F8B5FF /* XCUIDevice+FBHelpers.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6C32240C5CA00173FCB /* FBClassChainQueryParser.h in Headers */ = {isa = PBXBuildFile; fileRef = 71A7EAF71E224648001DA4F2 /* FBClassChainQueryParser.h */; };
		641EE6C42240C5CA00173FCB /* FBMacros.h in Headers */ = {isa = PBXBuildFile; fileRef = EE9B76A51CF7A43900275851 /* FBMacros.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6C52240C5CA00173FCB /* XCTestExpectationDelegate-Protocol.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACD91E3B77D600A02D78 /* XCTestExpectationDelegate-Protocol.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6C62240C5CA00173FCB /* XCTUIApplicationMonitor-Protocol.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACF31E3B77D600A02D78 /* XCTUIApplicationMonitor-Protocol.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6C82240C5CA00173FCB /* XCTKVOExpectation.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACE81E3B77D600A02D78 /* XCTKVOExpectation.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6C92240C5CA00173FCB /* XCUIDevice+FBRotation.h in Headers */ = {isa = PBXBuildFile; fileRef = EEE3763D1D59F81400ED88DD /* XCUIDevice+FBRotation.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6CA2240C5CA00173FCB /* XCEventGenerator.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACBD1E3B77D600A02D78 /* XCEventGenerator.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6CB2240C5CA00173FCB /* FBConfiguration.h in Headers */ = {isa = PBXBuildFile; fileRef = EE9B76A11CF7A43900275851 /* FBConfiguration.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6CC2240C5CA00173FCB /* XCTestSuiteRun.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACE61E3B77D600A02D78 /* XCTestSuiteRun.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6CD2240C5CA00173FCB /* XCUIElementAsynchronousHandlerWrapper.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACFF1E3B77D600A02D78 /* XCUIElementAsynchronousHandlerWrapper.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6CE2240C5CA00173FCB /* XCTestLog.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACDB1E3B77D600A02D78 /* XCTestLog.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6CF2240C5CA00173FCB /* UITapGestureRecognizer-RecordingAdditions.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACB21E3B77D600A02D78 /* UITapGestureRecognizer-RecordingAdditions.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6D02240C5CA00173FCB /* XCDebugLogDelegate-Protocol.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACB91E3B77D600A02D78 /* XCDebugLogDelegate-Protocol.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6D12240C5CA00173FCB /* NSString-XCTAdditions.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACAB1E3B77D600A02D78 /* NSString-XCTAdditions.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6D22240C5CA00173FCB /* XCTestWaiter.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACE71E3B77D600A02D78 /* XCTestWaiter.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6D32240C5CA00173FCB /* FBImageUtils.h in Headers */ = {isa = PBXBuildFile; fileRef = 7150348521A6DAD600A0F4BA /* FBImageUtils.h */; };
		641EE6D42240C5CA00173FCB /* NSValue-XCTestAdditions.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACAC1E3B77D600A02D78 /* NSValue-XCTestAdditions.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6D52240C5CA00173FCB /* _XCTWaiterImpl.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACA31E3B77D600A02D78 /* _XCTWaiterImpl.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6D62240C5CA00173FCB /* FBLogger.h in Headers */ = {isa = PBXBuildFile; fileRef = EE9B76A31CF7A43900275851 /* FBLogger.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6D72240C5CA00173FCB /* XCTestObserver.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACE21E3B77D600A02D78 /* XCTestObserver.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6D82240C5CA00173FCB /* XCUIElement.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACFE1E3B77D600A02D78 /* XCUIElement.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6D92240C5CA00173FCB /* XCKeyboardInputSolver.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACBE1E3B77D600A02D78 /* XCKeyboardInputSolver.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6DB2240C5CA00173FCB /* FBPasteboard.h in Headers */ = {isa = PBXBuildFile; fileRef = 71930C4020662E1F00D3AFEC /* FBPasteboard.h */; };
		641EE6DD2240C5CA00173FCB /* FBDebugLogDelegateDecorator.h in Headers */ = {isa = PBXBuildFile; fileRef = EE7E27181D06C69F001BEC7B /* FBDebugLogDelegateDecorator.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6DE2240C5CA00173FCB /* XCUIDevice+FBHealthCheck.h in Headers */ = {isa = PBXBuildFile; fileRef = EEDFE11F1D9C06F800E6FFE5 /* XCUIDevice+FBHealthCheck.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6DF2240C5CA00173FCB /* FBMjpegServer.h in Headers */ = {isa = PBXBuildFile; fileRef = 7155D701211DCEF400166C20 /* FBMjpegServer.h */; };
		641EE6E02240C5CA00173FCB /* XCUIRecorderNodeFinderMatch.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35AD051E3B77D600A02D78 /* XCUIRecorderNodeFinderMatch.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6E12240C5CA00173FCB /* XCUIApplicationProcess.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACFB1E3B77D600A02D78 /* XCUIApplicationProcess.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6E22240C5CA00173FCB /* FBW3CActionsSynthesizer.h in Headers */ = {isa = PBXBuildFile; fileRef = 714097491FAE1B51008FB2C5 /* FBW3CActionsSynthesizer.h */; };
		641EE6E32240C5CA00173FCB /* CDStructures.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACA41E3B77D600A02D78 /* CDStructures.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6E42240C5CA00173FCB /* XCKeyboardLayout.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACC01E3B77D600A02D78 /* XCKeyboardLayout.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6E52240C5CA00173FCB /* XCTAsyncActivity-Protocol.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACCA1E3B77D600A02D78 /* XCTAsyncActivity-Protocol.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6E62240C5CA00173FCB /* XCActivityRecord.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACB41E3B77D600A02D78 /* XCActivityRecord.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6E72240C5CA00173FCB /* XCUIElement+FBFind.h in Headers */ = {isa = PBXBuildFile; fileRef = EEBBD4891D47746D00656A81 /* XCUIElement+FBFind.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6E82240C5CA00173FCB /* XCTestManager_ManagerInterface-Protocol.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACDD1E3B77D600A02D78 /* XCTestManager_ManagerInterface-Protocol.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6E92240C5CA00173FCB /* FBFailureProofTestCase.h in Headers */ = {isa = PBXBuildFile; fileRef = EE6A89381D0B38640083E92B /* FBFailureProofTestCase.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6EA2240C5CA00173FCB /* XCTTestRunSessionDelegate-Protocol.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACF21E3B77D600A02D78 /* XCTTestRunSessionDelegate-Protocol.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6EB2240C5CA00173FCB /* XCTestCaseSuite.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACD21E3B77D600A02D78 /* XCTestCaseSuite.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6EC2240C5CA00173FCB /* _XCInternalTestRun.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35AC981E3B77D600A02D78 /* _XCInternalTestRun.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6ED2240C5CA00173FCB /* FBXPath-Private.h in Headers */ = {isa = PBXBuildFile; fileRef = 712A0C861DA3E55D007D02E5 /* FBXPath-Private.h */; };
		641EE6EE2240C5CA00173FCB /* XCKeyMappingPath.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACC11E3B77D600A02D78 /* XCKeyMappingPath.h */; settings = {ATTRIBUTES = (Public, ); }; };
		641EE6FC2240C5FD00173FCB /* WebDriverAgentLib_tvOS.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 641EE6F82240C5CA00173FCB /* WebDriverAgentLib_tvOS.framework */; };
		641EE6FD2240C61D00173FCB /* WebDriverAgentLib_tvOS.framework in Copy frameworks */ = {isa = PBXBuildFile; fileRef = 641EE6F82240C5CA00173FCB /* WebDriverAgentLib_tvOS.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		641EE7052240CDCF00173FCB /* XCUIElement+FBTVFocuse.h in Headers */ = {isa = PBXBuildFile; fileRef = 641EE7042240CDCF00173FCB /* XCUIElement+FBTVFocuse.h */; };
		641EE7062240CDCF00173FCB /* XCUIElement+FBTVFocuse.h in Headers */ = {isa = PBXBuildFile; fileRef = 641EE7042240CDCF00173FCB /* XCUIElement+FBTVFocuse.h */; };
		641EE7082240CDEB00173FCB /* XCUIElement+FBTVFocuse.m in Sources */ = {isa = PBXBuildFile; fileRef = 641EE7072240CDEB00173FCB /* XCUIElement+FBTVFocuse.m */; };
		641EE7092240CDEB00173FCB /* XCUIElement+FBTVFocuse.m in Sources */ = {isa = PBXBuildFile; fileRef = 641EE7072240CDEB00173FCB /* XCUIElement+FBTVFocuse.m */; };
		641EE70B2240CE2D00173FCB /* FBTVNavigationTracker.h in Headers */ = {isa = PBXBuildFile; fileRef = 641EE70A2240CE2D00173FCB /* FBTVNavigationTracker.h */; };
		641EE70C2240CE2D00173FCB /* FBTVNavigationTracker.h in Headers */ = {isa = PBXBuildFile; fileRef = 641EE70A2240CE2D00173FCB /* FBTVNavigationTracker.h */; };
		641EE70E2240CE4800173FCB /* FBTVNavigationTracker.m in Sources */ = {isa = PBXBuildFile; fileRef = 641EE70D2240CE4800173FCB /* FBTVNavigationTracker.m */; };
		641EE70F2240CE4800173FCB /* FBTVNavigationTracker.m in Sources */ = {isa = PBXBuildFile; fileRef = 641EE70D2240CE4800173FCB /* FBTVNavigationTracker.m */; };
		644D9CCE230E1F1A00C90459 /* FBConfigurationTests.m in Sources */ = {isa = PBXBuildFile; fileRef = 644D9CCD230E1F1A00C90459 /* FBConfigurationTests.m */; };
		648C10AB22AAAD9C00B81B9A /* UIKeyboardImpl.h in Headers */ = {isa = PBXBuildFile; fileRef = 648C10AA22AAAD9C00B81B9A /* UIKeyboardImpl.h */; };
		648C10AC22AAAD9C00B81B9A /* UIKeyboardImpl.h in Headers */ = {isa = PBXBuildFile; fileRef = 648C10AA22AAAD9C00B81B9A /* UIKeyboardImpl.h */; };
		648C10AF22AAAE4000B81B9A /* TIPreferencesController.h in Headers */ = {isa = PBXBuildFile; fileRef = 648C10AE22AAAE4000B81B9A /* TIPreferencesController.h */; };
		648C10B022AAAE4000B81B9A /* TIPreferencesController.h in Headers */ = {isa = PBXBuildFile; fileRef = 648C10AE22AAAE4000B81B9A /* TIPreferencesController.h */; };
		6496A5D9230D6EB30087F8CB /* AXSettings.h in Headers */ = {isa = PBXBuildFile; fileRef = 6496A5D8230D6EB30087F8CB /* AXSettings.h */; };
		6496A5DA230D6EB30087F8CB /* AXSettings.h in Headers */ = {isa = PBXBuildFile; fileRef = 6496A5D8230D6EB30087F8CB /* AXSettings.h */; };
		64B264FE228C50E0002A5025 /* WebDriverAgentLib_tvOS.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 641EE6F82240C5CA00173FCB /* WebDriverAgentLib_tvOS.framework */; };
		64B26504228C5299002A5025 /* FBTVNavigationTrackerTests.m in Sources */ = {isa = PBXBuildFile; fileRef = 64B264F3228C5098002A5025 /* FBTVNavigationTrackerTests.m */; };
		64B26508228C5514002A5025 /* XCUIElementDouble.m in Sources */ = {isa = PBXBuildFile; fileRef = 64B26507228C5514002A5025 /* XCUIElementDouble.m */; };
		64B2650A228CE4FF002A5025 /* FBTVNavigationTracker-Private.h in Headers */ = {isa = PBXBuildFile; fileRef = 64B26509228CE4FF002A5025 /* FBTVNavigationTracker-Private.h */; };
		64B2650B228CE4FF002A5025 /* FBTVNavigationTracker-Private.h in Headers */ = {isa = PBXBuildFile; fileRef = 64B26509228CE4FF002A5025 /* FBTVNavigationTracker-Private.h */; };
		64E3502E2AC0B6EB005F3ACB /* NSDictionary+FBUtf8SafeDictionary.m in Sources */ = {isa = PBXBuildFile; fileRef = 716F0DA02A16CA1000CDD977 /* NSDictionary+FBUtf8SafeDictionary.m */; };
		64E3502F2AC0B6FE005F3ACB /* NSDictionary+FBUtf8SafeDictionary.h in Headers */ = {isa = PBXBuildFile; fileRef = 716F0D9F2A16CA1000CDD977 /* NSDictionary+FBUtf8SafeDictionary.h */; };
		711084441DA3AA7500F913D6 /* FBXPath.h in Headers */ = {isa = PBXBuildFile; fileRef = 711084421DA3AA7500F913D6 /* FBXPath.h */; settings = {ATTRIBUTES = (Public, ); }; };
		711084451DA3AA7500F913D6 /* FBXPath.m in Sources */ = {isa = PBXBuildFile; fileRef = 711084431DA3AA7500F913D6 /* FBXPath.m */; };
		7119097C2152580600BA3C7E /* XCUIScreen.h in Headers */ = {isa = PBXBuildFile; fileRef = 7119097B2152580600BA3C7E /* XCUIScreen.h */; settings = {ATTRIBUTES = (Public, ); }; };
		7119E1EC1E891F8600D0B125 /* FBPickerWheelSelectTests.m in Sources */ = {isa = PBXBuildFile; fileRef = 7119E1EB1E891F8600D0B125 /* FBPickerWheelSelectTests.m */; };
		711CD03425ED1106001C01D2 /* XCUIScreenDataSource-Protocol.h in Headers */ = {isa = PBXBuildFile; fileRef = 711CD03325ED1106001C01D2 /* XCUIScreenDataSource-Protocol.h */; };
		711CD03525ED1106001C01D2 /* XCUIScreenDataSource-Protocol.h in Headers */ = {isa = PBXBuildFile; fileRef = 711CD03325ED1106001C01D2 /* XCUIScreenDataSource-Protocol.h */; };
		71241D7B1FAE3D2500B9559F /* FBTouchActionCommands.h in Headers */ = {isa = PBXBuildFile; fileRef = 71241D791FAE3D2500B9559F /* FBTouchActionCommands.h */; };
		71241D7C1FAE3D2500B9559F /* FBTouchActionCommands.m in Sources */ = {isa = PBXBuildFile; fileRef = 71241D7A1FAE3D2500B9559F /* FBTouchActionCommands.m */; };
		71241D7E1FAF084E00B9559F /* FBW3CTouchActionsIntegrationTests.m in Sources */ = {isa = PBXBuildFile; fileRef = 71241D7D1FAF084E00B9559F /* FBW3CTouchActionsIntegrationTests.m */; };
		71241D801FAF087500B9559F /* FBW3CMultiTouchActionsIntegrationTests.m in Sources */ = {isa = PBXBuildFile; fileRef = 71241D7F1FAF087500B9559F /* FBW3CMultiTouchActionsIntegrationTests.m */; };
		712A0C851DA3E459007D02E5 /* FBXPathTests.m in Sources */ = {isa = PBXBuildFile; fileRef = 712A0C841DA3E459007D02E5 /* FBXPathTests.m */; };
		712A0C871DA3E55D007D02E5 /* FBXPath-Private.h in Headers */ = {isa = PBXBuildFile; fileRef = 712A0C861DA3E55D007D02E5 /* FBXPath-Private.h */; };
		713352FD26CEF31D00523CBC /* FBLRUCacheTests.m in Sources */ = {isa = PBXBuildFile; fileRef = 713352FC26CEF31D00523CBC /* FBLRUCacheTests.m */; };
		7136A4791E8918E60024FC3D /* XCUIElement+FBPickerWheel.h in Headers */ = {isa = PBXBuildFile; fileRef = 7136A4771E8918E60024FC3D /* XCUIElement+FBPickerWheel.h */; };
		7136A47A1E8918E60024FC3D /* XCUIElement+FBPickerWheel.m in Sources */ = {isa = PBXBuildFile; fileRef = 7136A4781E8918E60024FC3D /* XCUIElement+FBPickerWheel.m */; };
		7136C0F9243A182400921C76 /* FBW3CTypeActionsTests.m in Sources */ = {isa = PBXBuildFile; fileRef = 7136C0F8243A182400921C76 /* FBW3CTypeActionsTests.m */; };
		7139145A1DF01989005896C2 /* XCUIElementHelpersTests.m in Sources */ = {isa = PBXBuildFile; fileRef = 713914591DF01989005896C2 /* XCUIElementHelpersTests.m */; };
		7139145C1DF01A12005896C2 /* NSExpressionFBFormatTests.m in Sources */ = {isa = PBXBuildFile; fileRef = 7139145B1DF01A12005896C2 /* NSExpressionFBFormatTests.m */; };
		713AE575243A53BE0000D657 /* FBW3CActionsHelpers.h in Headers */ = {isa = PBXBuildFile; fileRef = 713AE573243A53BE0000D657 /* FBW3CActionsHelpers.h */; };
		713AE576243A53BE0000D657 /* FBW3CActionsHelpers.m in Sources */ = {isa = PBXBuildFile; fileRef = 713AE574243A53BE0000D657 /* FBW3CActionsHelpers.m */; };
		713C6DCF1DDC772A00285B92 /* FBElementUtils.h in Headers */ = {isa = PBXBuildFile; fileRef = 713C6DCD1DDC772A00285B92 /* FBElementUtils.h */; };
		713C6DD01DDC772A00285B92 /* FBElementUtils.m in Sources */ = {isa = PBXBuildFile; fileRef = 713C6DCE1DDC772A00285B92 /* FBElementUtils.m */; };
		714097431FAE1B0B008FB2C5 /* FBBaseActionsSynthesizer.h in Headers */ = {isa = PBXBuildFile; fileRef = 714097411FAE1B0B008FB2C5 /* FBBaseActionsSynthesizer.h */; };
		7140974B1FAE1B51008FB2C5 /* FBW3CActionsSynthesizer.h in Headers */ = {isa = PBXBuildFile; fileRef = 714097491FAE1B51008FB2C5 /* FBW3CActionsSynthesizer.h */; };
		7140974C1FAE1B51008FB2C5 /* FBW3CActionsSynthesizer.m in Sources */ = {isa = PBXBuildFile; fileRef = 7140974A1FAE1B51008FB2C5 /* FBW3CActionsSynthesizer.m */; };
		7140974E1FAE20EE008FB2C5 /* FBBaseActionsSynthesizer.m in Sources */ = {isa = PBXBuildFile; fileRef = 7140974D1FAE20EE008FB2C5 /* FBBaseActionsSynthesizer.m */; };
		71414ED42670A1EE003A8C5D /* LRUCache.h in Headers */ = {isa = PBXBuildFile; fileRef = 71414ED02670A1ED003A8C5D /* LRUCache.h */; };
		71414ED52670A1EE003A8C5D /* LRUCache.h in Headers */ = {isa = PBXBuildFile; fileRef = 71414ED02670A1ED003A8C5D /* LRUCache.h */; };
		71414ED62670A1EE003A8C5D /* LRUCacheNode.h in Headers */ = {isa = PBXBuildFile; fileRef = 71414ED12670A1ED003A8C5D /* LRUCacheNode.h */; };
		71414ED72670A1EE003A8C5D /* LRUCacheNode.h in Headers */ = {isa = PBXBuildFile; fileRef = 71414ED12670A1ED003A8C5D /* LRUCacheNode.h */; };
		71414ED82670A1EE003A8C5D /* LRUCache.m in Sources */ = {isa = PBXBuildFile; fileRef = 71414ED22670A1ED003A8C5D /* LRUCache.m */; };
		71414ED92670A1EE003A8C5D /* LRUCache.m in Sources */ = {isa = PBXBuildFile; fileRef = 71414ED22670A1ED003A8C5D /* LRUCache.m */; };
		71414EDA2670A1EE003A8C5D /* LRUCacheNode.m in Sources */ = {isa = PBXBuildFile; fileRef = 71414ED32670A1ED003A8C5D /* LRUCacheNode.m */; };
		71414EDB2670A1EE003A8C5D /* LRUCacheNode.m in Sources */ = {isa = PBXBuildFile; fileRef = 71414ED32670A1ED003A8C5D /* LRUCacheNode.m */; };
		714801D11FA9D9FA00DC5997 /* FBSDKVersionTests.m in Sources */ = {isa = PBXBuildFile; fileRef = 714801D01FA9D9FA00DC5997 /* FBSDKVersionTests.m */; };
		714D88CC2733FB970074A925 /* FBXMLGenerationOptions.h in Headers */ = {isa = PBXBuildFile; fileRef = 714D88CA2733FB970074A925 /* FBXMLGenerationOptions.h */; };
		714D88CD2733FB970074A925 /* FBXMLGenerationOptions.h in Headers */ = {isa = PBXBuildFile; fileRef = 714D88CA2733FB970074A925 /* FBXMLGenerationOptions.h */; };
		714D88CE2733FB970074A925 /* FBXMLGenerationOptions.m in Sources */ = {isa = PBXBuildFile; fileRef = 714D88CB2733FB970074A925 /* FBXMLGenerationOptions.m */; };
		714D88CF2733FB970074A925 /* FBXMLGenerationOptions.m in Sources */ = {isa = PBXBuildFile; fileRef = 714D88CB2733FB970074A925 /* FBXMLGenerationOptions.m */; };
		714E14B829805CAE00375DD7 /* XCAXClient_iOS+FBSnapshotReqParams.h in Headers */ = {isa = PBXBuildFile; fileRef = 714E14B629805CAE00375DD7 /* XCAXClient_iOS+FBSnapshotReqParams.h */; };
		714E14B929805CAE00375DD7 /* XCAXClient_iOS+FBSnapshotReqParams.h in Headers */ = {isa = PBXBuildFile; fileRef = 714E14B629805CAE00375DD7 /* XCAXClient_iOS+FBSnapshotReqParams.h */; };
		714E14BA29805CAE00375DD7 /* XCAXClient_iOS+FBSnapshotReqParams.m in Sources */ = {isa = PBXBuildFile; fileRef = 714E14B729805CAE00375DD7 /* XCAXClient_iOS+FBSnapshotReqParams.m */; };
		714E14BB29805CAE00375DD7 /* XCAXClient_iOS+FBSnapshotReqParams.m in Sources */ = {isa = PBXBuildFile; fileRef = 714E14B729805CAE00375DD7 /* XCAXClient_iOS+FBSnapshotReqParams.m */; };
		714EAA0D2673FDFE005C5B47 /* FBCapabilities.h in Headers */ = {isa = PBXBuildFile; fileRef = 714EAA0B2673FDFE005C5B47 /* FBCapabilities.h */; };
		714EAA0E2673FDFE005C5B47 /* FBCapabilities.h in Headers */ = {isa = PBXBuildFile; fileRef = 714EAA0B2673FDFE005C5B47 /* FBCapabilities.h */; };
		714EAA0F2673FDFE005C5B47 /* FBCapabilities.m in Sources */ = {isa = PBXBuildFile; fileRef = 714EAA0C2673FDFE005C5B47 /* FBCapabilities.m */; };
		714EAA102673FDFE005C5B47 /* FBCapabilities.m in Sources */ = {isa = PBXBuildFile; fileRef = 714EAA0C2673FDFE005C5B47 /* FBCapabilities.m */; };
		7150348721A6DAD600A0F4BA /* FBImageUtils.h in Headers */ = {isa = PBXBuildFile; fileRef = 7150348521A6DAD600A0F4BA /* FBImageUtils.h */; };
		7150348821A6DAD600A0F4BA /* FBImageUtils.m in Sources */ = {isa = PBXBuildFile; fileRef = 7150348621A6DAD600A0F4BA /* FBImageUtils.m */; };
		7150FFF722476B3A00B2EE28 /* FBForceTouchTests.m in Sources */ = {isa = PBXBuildFile; fileRef = EE8DDD7A20C57320004D4925 /* FBForceTouchTests.m */; };
		7152EB301F41F9960047EEFF /* FBSessionIntegrationTests.m in Sources */ = {isa = PBXBuildFile; fileRef = 7152EB2F1F41F9960047EEFF /* FBSessionIntegrationTests.m */; };
		715557D3211DBCE700613B26 /* FBTCPSocket.h in Headers */ = {isa = PBXBuildFile; fileRef = 715557D1211DBCE700613B26 /* FBTCPSocket.h */; };
		715557D4211DBCE700613B26 /* FBTCPSocket.m in Sources */ = {isa = PBXBuildFile; fileRef = 715557D2211DBCE700613B26 /* FBTCPSocket.m */; };
		71555A3D1DEC460A007D4A8B /* NSExpression+FBFormat.h in Headers */ = {isa = PBXBuildFile; fileRef = 71555A3B1DEC460A007D4A8B /* NSExpression+FBFormat.h */; };
		71555A3E1DEC460A007D4A8B /* NSExpression+FBFormat.m in Sources */ = {isa = PBXBuildFile; fileRef = 71555A3C1DEC460A007D4A8B /* NSExpression+FBFormat.m */; };
		7155B40E224D5A850042A993 /* libAccessibility.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 716C9343224D53DF004B8542 /* libAccessibility.tbd */; };
		7155B414224D5B170042A993 /* XCTest.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 716C9342224D53A1004B8542 /* XCTest.framework */; };
		7155B41B224D5B5A0042A993 /* libAccessibility.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 7155B41A224D5B480042A993 /* libAccessibility.tbd */; };
		7155B41C224D5B5D0042A993 /* libxml2.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 7155B419224D5B460042A993 /* libxml2.tbd */; };
		7155B424224D5BA10042A993 /* XCTest.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 7155B423224D5B980042A993 /* XCTest.framework */; };
		7155D703211DCEF400166C20 /* FBMjpegServer.h in Headers */ = {isa = PBXBuildFile; fileRef = 7155D701211DCEF400166C20 /* FBMjpegServer.h */; };
		7155D704211DCEF400166C20 /* FBMjpegServer.m in Sources */ = {isa = PBXBuildFile; fileRef = 7155D702211DCEF400166C20 /* FBMjpegServer.m */; };
		7157B291221DADD2001C348C /* FBXCAXClientProxy.h in Headers */ = {isa = PBXBuildFile; fileRef = 7157B28F221DADD2001C348C /* FBXCAXClientProxy.h */; };
		7157B292221DADD2001C348C /* FBXCAXClientProxy.m in Sources */ = {isa = PBXBuildFile; fileRef = 7157B290221DADD2001C348C /* FBXCAXClientProxy.m */; };
		715A84CF2DD92AD3007134CC /* FBElementHelpers.m in Sources */ = {isa = PBXBuildFile; fileRef = 715A84CE2DD92AD3007134CC /* FBElementHelpers.m */; };
		715A84D02DD92AD3007134CC /* FBElementHelpers.h in Headers */ = {isa = PBXBuildFile; fileRef = 715A84CD2DD92AD3007134CC /* FBElementHelpers.h */; };
		715A84D12DD92AD3007134CC /* FBElementHelpers.m in Sources */ = {isa = PBXBuildFile; fileRef = 715A84CE2DD92AD3007134CC /* FBElementHelpers.m */; };
		715A84D22DD92AD3007134CC /* FBElementHelpers.h in Headers */ = {isa = PBXBuildFile; fileRef = 715A84CD2DD92AD3007134CC /* FBElementHelpers.h */; };
		715AFAC11FFA29180053896D /* FBScreen.h in Headers */ = {isa = PBXBuildFile; fileRef = 715AFABF1FFA29180053896D /* FBScreen.h */; };
		715AFAC21FFA29180053896D /* FBScreen.m in Sources */ = {isa = PBXBuildFile; fileRef = 715AFAC01FFA29180053896D /* FBScreen.m */; };
		715AFAC41FFA2AAF0053896D /* FBScreenTests.m in Sources */ = {isa = PBXBuildFile; fileRef = 715AFAC31FFA2AAF0053896D /* FBScreenTests.m */; };
		715D554B2229891B00524509 /* FBExceptionHandlerTests.m in Sources */ = {isa = PBXBuildFile; fileRef = 715D554A2229891B00524509 /* FBExceptionHandlerTests.m */; };
		715D5773224DE02E00DA2D99 /* libxml2.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 716C9345224D540C004B8542 /* libxml2.tbd */; };
		715D5774224DE05400DA2D99 /* libxml2.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 716C9345224D540C004B8542 /* libxml2.tbd */; };
		715D5775224DE05C00DA2D99 /* libxml2.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 716C9345224D540C004B8542 /* libxml2.tbd */; };
		715D5776224DE06500DA2D99 /* libxml2.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 716C9345224D540C004B8542 /* libxml2.tbd */; };
		716C9346224D540C004B8542 /* libxml2.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 716C9345224D540C004B8542 /* libxml2.tbd */; };
		716C9347224D540C004B8542 /* libxml2.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 716C9345224D540C004B8542 /* libxml2.tbd */; };
		716C9DFA27315D21005AD475 /* FBReflectionUtils.h in Headers */ = {isa = PBXBuildFile; fileRef = 716C9DF827315D21005AD475 /* FBReflectionUtils.h */; };
		716C9DFB27315D21005AD475 /* FBReflectionUtils.h in Headers */ = {isa = PBXBuildFile; fileRef = 716C9DF827315D21005AD475 /* FBReflectionUtils.h */; };
		716C9DFC27315D21005AD475 /* FBReflectionUtils.m in Sources */ = {isa = PBXBuildFile; fileRef = 716C9DF927315D21005AD475 /* FBReflectionUtils.m */; };
		716C9DFD27315D21005AD475 /* FBReflectionUtils.m in Sources */ = {isa = PBXBuildFile; fileRef = 716C9DF927315D21005AD475 /* FBReflectionUtils.m */; };
		716C9E0027315EFF005AD475 /* XCUIApplication+FBUIInterruptions.h in Headers */ = {isa = PBXBuildFile; fileRef = 716C9DFE27315EFF005AD475 /* XCUIApplication+FBUIInterruptions.h */; };
		716C9E0127315EFF005AD475 /* XCUIApplication+FBUIInterruptions.h in Headers */ = {isa = PBXBuildFile; fileRef = 716C9DFE27315EFF005AD475 /* XCUIApplication+FBUIInterruptions.h */; };
		716C9E0227315EFF005AD475 /* XCUIApplication+FBUIInterruptions.m in Sources */ = {isa = PBXBuildFile; fileRef = 716C9DFF27315EFF005AD475 /* XCUIApplication+FBUIInterruptions.m */; };
		716C9E0327315EFF005AD475 /* XCUIApplication+FBUIInterruptions.m in Sources */ = {isa = PBXBuildFile; fileRef = 716C9DFF27315EFF005AD475 /* XCUIApplication+FBUIInterruptions.m */; };
		716E0BCE1E917E810087A825 /* NSString+FBXMLSafeString.h in Headers */ = {isa = PBXBuildFile; fileRef = 716E0BCC1E917E810087A825 /* NSString+FBXMLSafeString.h */; };
		716E0BCF1E917E810087A825 /* NSString+FBXMLSafeString.m in Sources */ = {isa = PBXBuildFile; fileRef = 716E0BCD1E917E810087A825 /* NSString+FBXMLSafeString.m */; };
		716E0BD11E917F260087A825 /* FBXMLSafeStringTests.m in Sources */ = {isa = PBXBuildFile; fileRef = 716E0BD01E917F260087A825 /* FBXMLSafeStringTests.m */; };
		716F0DA12A16CA1000CDD977 /* NSDictionary+FBUtf8SafeDictionary.h in Headers */ = {isa = PBXBuildFile; fileRef = 716F0D9F2A16CA1000CDD977 /* NSDictionary+FBUtf8SafeDictionary.h */; };
		716F0DA32A16CA1000CDD977 /* NSDictionary+FBUtf8SafeDictionary.m in Sources */ = {isa = PBXBuildFile; fileRef = 716F0DA02A16CA1000CDD977 /* NSDictionary+FBUtf8SafeDictionary.m */; };
		716F0DA62A17323300CDD977 /* NSDictionaryFBUtf8SafeTests.m in Sources */ = {isa = PBXBuildFile; fileRef = 716F0DA52A17323300CDD977 /* NSDictionaryFBUtf8SafeTests.m */; };
		718226CA2587443700661B83 /* GCDAsyncUdpSocket.h in Headers */ = {isa = PBXBuildFile; fileRef = 718226C62587443600661B83 /* GCDAsyncUdpSocket.h */; };
		718226CB2587443700661B83 /* GCDAsyncUdpSocket.h in Headers */ = {isa = PBXBuildFile; fileRef = 718226C62587443600661B83 /* GCDAsyncUdpSocket.h */; };
		718226CC2587443700661B83 /* GCDAsyncSocket.h in Headers */ = {isa = PBXBuildFile; fileRef = 718226C72587443600661B83 /* GCDAsyncSocket.h */; };
		718226CD2587443700661B83 /* GCDAsyncSocket.h in Headers */ = {isa = PBXBuildFile; fileRef = 718226C72587443600661B83 /* GCDAsyncSocket.h */; };
		718226CE2587443700661B83 /* GCDAsyncSocket.m in Sources */ = {isa = PBXBuildFile; fileRef = 718226C82587443600661B83 /* GCDAsyncSocket.m */; };
		718226CF2587443700661B83 /* GCDAsyncSocket.m in Sources */ = {isa = PBXBuildFile; fileRef = 718226C82587443600661B83 /* GCDAsyncSocket.m */; };
		718226D02587443700661B83 /* GCDAsyncUdpSocket.m in Sources */ = {isa = PBXBuildFile; fileRef = 718226C92587443600661B83 /* GCDAsyncUdpSocket.m */; };
		718226D12587443700661B83 /* GCDAsyncUdpSocket.m in Sources */ = {isa = PBXBuildFile; fileRef = 718226C92587443600661B83 /* GCDAsyncUdpSocket.m */; };
		71822702258744A400661B83 /* HTTPResponseProxy.h in Headers */ = {isa = PBXBuildFile; fileRef = E444DCA224913C210060D7EB /* HTTPResponseProxy.h */; };
		7182270B258744A700661B83 /* Route.h in Headers */ = {isa = PBXBuildFile; fileRef = E444DCA324913C210060D7EB /* Route.h */; };
		71822714258744A900661B83 /* RouteRequest.h in Headers */ = {isa = PBXBuildFile; fileRef = E444DCAA24913C220060D7EB /* RouteRequest.h */; };
		7182271D258744AB00661B83 /* RouteResponse.h in Headers */ = {isa = PBXBuildFile; fileRef = E444DCA124913C210060D7EB /* RouteResponse.h */; };
		71822726258744AE00661B83 /* RoutingConnection.h in Headers */ = {isa = PBXBuildFile; fileRef = E444DCA524913C210060D7EB /* RoutingConnection.h */; };
		7182272F258744B000661B83 /* RoutingHTTPServer.h in Headers */ = {isa = PBXBuildFile; fileRef = E444DCA724913C210060D7EB /* RoutingHTTPServer.h */; };
		71822738258744B800661B83 /* HTTPConnection.h in Headers */ = {isa = PBXBuildFile; fileRef = E444DC89249131D30060D7EB /* HTTPConnection.h */; };
		71822741258744BB00661B83 /* HTTPLogging.h in Headers */ = {isa = PBXBuildFile; fileRef = E444DC8D249131D30060D7EB /* HTTPLogging.h */; };
		7182274A258744BE00661B83 /* HTTPMessage.h in Headers */ = {isa = PBXBuildFile; fileRef = E444DC87249131D30060D7EB /* HTTPMessage.h */; };
		71822753258744C100661B83 /* HTTPResponse.h in Headers */ = {isa = PBXBuildFile; fileRef = E444DC8F249131D40060D7EB /* HTTPResponse.h */; };
		7182275C258744C300661B83 /* HTTPServer.h in Headers */ = {isa = PBXBuildFile; fileRef = E444DC8B249131D30060D7EB /* HTTPServer.h */; };
		71822765258744C700661B83 /* HTTPDataResponse.h in Headers */ = {isa = PBXBuildFile; fileRef = E444DC60249131890060D7EB /* HTTPDataResponse.h */; };
		7182276E258744C900661B83 /* HTTPErrorResponse.h in Headers */ = {isa = PBXBuildFile; fileRef = E444DC59249131880060D7EB /* HTTPErrorResponse.h */; };
		71822777258744CE00661B83 /* DDNumber.h in Headers */ = {isa = PBXBuildFile; fileRef = E444DC7D249131B00060D7EB /* DDNumber.h */; };
		71822780258744D000661B83 /* DDRange.h in Headers */ = {isa = PBXBuildFile; fileRef = E444DC7B249131B00060D7EB /* DDRange.h */; };
		718F49C8230844330045FE8B /* FBProtocolHelpersTests.m in Sources */ = {isa = PBXBuildFile; fileRef = 718F49C7230844330045FE8B /* FBProtocolHelpersTests.m */; };
		718F49C923087ACF0045FE8B /* FBProtocolHelpers.h in Headers */ = {isa = PBXBuildFile; fileRef = 71B155DD23080CA600646AFB /* FBProtocolHelpers.h */; };
		718F49CA23087AD30045FE8B /* FBProtocolHelpers.m in Sources */ = {isa = PBXBuildFile; fileRef = 71B155DE23080CA600646AFB /* FBProtocolHelpers.m */; };
		718F49CB23087B040045FE8B /* FBCommandStatus.m in Sources */ = {isa = PBXBuildFile; fileRef = 71B155DB230711E900646AFB /* FBCommandStatus.m */; };
		71930C4220662E1F00D3AFEC /* FBPasteboard.h in Headers */ = {isa = PBXBuildFile; fileRef = 71930C4020662E1F00D3AFEC /* FBPasteboard.h */; };
		71930C4320662E1F00D3AFEC /* FBPasteboard.m in Sources */ = {isa = PBXBuildFile; fileRef = 71930C4120662E1F00D3AFEC /* FBPasteboard.m */; };
		71930C472066434000D3AFEC /* FBPasteboardTests.m in Sources */ = {isa = PBXBuildFile; fileRef = 71930C462066434000D3AFEC /* FBPasteboardTests.m */; };
		719CD8F82126C78F00C7D0C2 /* FBAlertsMonitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 719CD8F62126C78F00C7D0C2 /* FBAlertsMonitor.h */; };
		719CD8F92126C78F00C7D0C2 /* FBAlertsMonitor.m in Sources */ = {isa = PBXBuildFile; fileRef = 719CD8F72126C78F00C7D0C2 /* FBAlertsMonitor.m */; };
		719CD8FC2126C88B00C7D0C2 /* XCUIApplication+FBAlert.h in Headers */ = {isa = PBXBuildFile; fileRef = 719CD8FA2126C88B00C7D0C2 /* XCUIApplication+FBAlert.h */; };
		719CD8FD2126C88B00C7D0C2 /* XCUIApplication+FBAlert.m in Sources */ = {isa = PBXBuildFile; fileRef = 719CD8FB2126C88B00C7D0C2 /* XCUIApplication+FBAlert.m */; };
		719CD8FF2126C90200C7D0C2 /* FBAutoAlertsHandlerTests.m in Sources */ = {isa = PBXBuildFile; fileRef = 719CD8FE2126C90200C7D0C2 /* FBAutoAlertsHandlerTests.m */; };
		719DCF152601EAFB000E765F /* FBNotificationsHelper.h in Headers */ = {isa = PBXBuildFile; fileRef = 719DCF132601EAFB000E765F /* FBNotificationsHelper.h */; };
		719DCF162601EAFB000E765F /* FBNotificationsHelper.h in Headers */ = {isa = PBXBuildFile; fileRef = 719DCF132601EAFB000E765F /* FBNotificationsHelper.h */; };
		719DCF172601EAFB000E765F /* FBNotificationsHelper.m in Sources */ = {isa = PBXBuildFile; fileRef = 719DCF142601EAFB000E765F /* FBNotificationsHelper.m */; };
		719DCF182601EAFB000E765F /* FBNotificationsHelper.m in Sources */ = {isa = PBXBuildFile; fileRef = 719DCF142601EAFB000E765F /* FBNotificationsHelper.m */; };
		719FF5B91DAD21F5008E0099 /* FBElementUtilitiesTests.m in Sources */ = {isa = PBXBuildFile; fileRef = 719FF5B81DAD21F5008E0099 /* FBElementUtilitiesTests.m */; };
		71A224E51DE2F56600844D55 /* NSPredicate+FBFormat.h in Headers */ = {isa = PBXBuildFile; fileRef = 71A224E31DE2F56600844D55 /* NSPredicate+FBFormat.h */; };
		71A224E61DE2F56600844D55 /* NSPredicate+FBFormat.m in Sources */ = {isa = PBXBuildFile; fileRef = 71A224E41DE2F56600844D55 /* NSPredicate+FBFormat.m */; };
		71A224E81DE326C500844D55 /* NSPredicateFBFormatTests.m in Sources */ = {isa = PBXBuildFile; fileRef = 71A224E71DE326C500844D55 /* NSPredicateFBFormatTests.m */; };
		71A5C67329A4F39600421C37 /* XCTIssue+FBPatcher.h in Headers */ = {isa = PBXBuildFile; fileRef = 71A5C67129A4F39600421C37 /* XCTIssue+FBPatcher.h */; settings = {ATTRIBUTES = (Public, ); }; };
		71A5C67429A4F39600421C37 /* XCTIssue+FBPatcher.h in Headers */ = {isa = PBXBuildFile; fileRef = 71A5C67129A4F39600421C37 /* XCTIssue+FBPatcher.h */; settings = {ATTRIBUTES = (Public, ); }; };
		71A5C67529A4F39600421C37 /* XCTIssue+FBPatcher.m in Sources */ = {isa = PBXBuildFile; fileRef = 71A5C67229A4F39600421C37 /* XCTIssue+FBPatcher.m */; };
		71A5C67629A4F39600421C37 /* XCTIssue+FBPatcher.m in Sources */ = {isa = PBXBuildFile; fileRef = 71A5C67229A4F39600421C37 /* XCTIssue+FBPatcher.m */; };
		71A7EAF51E20516B001DA4F2 /* XCUIElement+FBClassChain.h in Headers */ = {isa = PBXBuildFile; fileRef = 71A7EAF31E20516B001DA4F2 /* XCUIElement+FBClassChain.h */; };
		71A7EAF61E20516B001DA4F2 /* XCUIElement+FBClassChain.m in Sources */ = {isa = PBXBuildFile; fileRef = 71A7EAF41E20516B001DA4F2 /* XCUIElement+FBClassChain.m */; };
		71A7EAF91E224648001DA4F2 /* FBClassChainQueryParser.h in Headers */ = {isa = PBXBuildFile; fileRef = 71A7EAF71E224648001DA4F2 /* FBClassChainQueryParser.h */; };
		71A7EAFA1E224648001DA4F2 /* FBClassChainQueryParser.m in Sources */ = {isa = PBXBuildFile; fileRef = 71A7EAF81E224648001DA4F2 /* FBClassChainQueryParser.m */; };
		71A7EAFC1E229302001DA4F2 /* FBClassChainTests.m in Sources */ = {isa = PBXBuildFile; fileRef = 71A7EAFB1E229302001DA4F2 /* FBClassChainTests.m */; };
		71ACF5B8242F2FDC00F0AAD4 /* FBSafariAlertTests.m in Sources */ = {isa = PBXBuildFile; fileRef = 71ACF5B7242F2FDC00F0AAD4 /* FBSafariAlertTests.m */; };
		71AE3CF72D38EE8E0039FC36 /* XCUIElement+FBVisibleFrame.h in Headers */ = {isa = PBXBuildFile; fileRef = 71AE3CF52D38EE8E0039FC36 /* XCUIElement+FBVisibleFrame.h */; };
		71AE3CF82D38EE8E0039FC36 /* XCUIElement+FBVisibleFrame.m in Sources */ = {isa = PBXBuildFile; fileRef = 71AE3CF62D38EE8E0039FC36 /* XCUIElement+FBVisibleFrame.m */; };
		71AE3CF92D38EE8E0039FC36 /* XCUIElement+FBVisibleFrame.h in Headers */ = {isa = PBXBuildFile; fileRef = 71AE3CF52D38EE8E0039FC36 /* XCUIElement+FBVisibleFrame.h */; };
		71AE3CFA2D38EE8E0039FC36 /* XCUIElement+FBVisibleFrame.m in Sources */ = {isa = PBXBuildFile; fileRef = 71AE3CF62D38EE8E0039FC36 /* XCUIElement+FBVisibleFrame.m */; };
		71B155DA23070ECF00646AFB /* FBHTTPStatusCodes.h in Headers */ = {isa = PBXBuildFile; fileRef = 71B155D923070ECF00646AFB /* FBHTTPStatusCodes.h */; settings = {ATTRIBUTES = (Public, ); }; };
		71B155DC230711E900646AFB /* FBCommandStatus.m in Sources */ = {isa = PBXBuildFile; fileRef = 71B155DB230711E900646AFB /* FBCommandStatus.m */; };
		71B155DF23080CA600646AFB /* FBProtocolHelpers.h in Headers */ = {isa = PBXBuildFile; fileRef = 71B155DD23080CA600646AFB /* FBProtocolHelpers.h */; };
		71B155E123080CA600646AFB /* FBProtocolHelpers.m in Sources */ = {isa = PBXBuildFile; fileRef = 71B155DE23080CA600646AFB /* FBProtocolHelpers.m */; };
		71B49EC71ED1A58100D51AD6 /* XCUIElement+FBUID.h in Headers */ = {isa = PBXBuildFile; fileRef = 71B49EC51ED1A58100D51AD6 /* XCUIElement+FBUID.h */; };
		71B49EC81ED1A58100D51AD6 /* XCUIElement+FBUID.m in Sources */ = {isa = PBXBuildFile; fileRef = 71B49EC61ED1A58100D51AD6 /* XCUIElement+FBUID.m */; };
		71BB58DE2B9631B700CB9BFE /* FBVideoRecordingTests.m in Sources */ = {isa = PBXBuildFile; fileRef = 71BB58DD2B9631B700CB9BFE /* FBVideoRecordingTests.m */; };
		71BB58E12B9631F100CB9BFE /* FBScreenRecordingPromise.h in Headers */ = {isa = PBXBuildFile; fileRef = 71BB58DF2B9631F100CB9BFE /* FBScreenRecordingPromise.h */; };
		71BB58E22B9631F100CB9BFE /* FBScreenRecordingPromise.h in Headers */ = {isa = PBXBuildFile; fileRef = 71BB58DF2B9631F100CB9BFE /* FBScreenRecordingPromise.h */; };
		71BB58E32B9631F100CB9BFE /* FBScreenRecordingPromise.m in Sources */ = {isa = PBXBuildFile; fileRef = 71BB58E02B9631F100CB9BFE /* FBScreenRecordingPromise.m */; };
		71BB58E42B9631F100CB9BFE /* FBScreenRecordingPromise.m in Sources */ = {isa = PBXBuildFile; fileRef = 71BB58E02B9631F100CB9BFE /* FBScreenRecordingPromise.m */; };
		71BB58E52B9631F100CB9BFE /* FBScreenRecordingPromise.m in Sources */ = {isa = PBXBuildFile; fileRef = 71BB58E02B9631F100CB9BFE /* FBScreenRecordingPromise.m */; };
		71BB58E82B96328700CB9BFE /* FBScreenRecordingRequest.h in Headers */ = {isa = PBXBuildFile; fileRef = 71BB58E62B96328700CB9BFE /* FBScreenRecordingRequest.h */; };
		71BB58E92B96328700CB9BFE /* FBScreenRecordingRequest.h in Headers */ = {isa = PBXBuildFile; fileRef = 71BB58E62B96328700CB9BFE /* FBScreenRecordingRequest.h */; };
		71BB58EA2B96328700CB9BFE /* FBScreenRecordingRequest.m in Sources */ = {isa = PBXBuildFile; fileRef = 71BB58E72B96328700CB9BFE /* FBScreenRecordingRequest.m */; };
		71BB58EB2B96328700CB9BFE /* FBScreenRecordingRequest.m in Sources */ = {isa = PBXBuildFile; fileRef = 71BB58E72B96328700CB9BFE /* FBScreenRecordingRequest.m */; };
		71BB58EC2B96328700CB9BFE /* FBScreenRecordingRequest.m in Sources */ = {isa = PBXBuildFile; fileRef = 71BB58E72B96328700CB9BFE /* FBScreenRecordingRequest.m */; };
		71BB58EF2B96511800CB9BFE /* FBVideoCommands.h in Headers */ = {isa = PBXBuildFile; fileRef = 71BB58ED2B96511800CB9BFE /* FBVideoCommands.h */; };
		71BB58F02B96511800CB9BFE /* FBVideoCommands.h in Headers */ = {isa = PBXBuildFile; fileRef = 71BB58ED2B96511800CB9BFE /* FBVideoCommands.h */; };
		71BB58F12B96511800CB9BFE /* FBVideoCommands.m in Sources */ = {isa = PBXBuildFile; fileRef = 71BB58EE2B96511800CB9BFE /* FBVideoCommands.m */; };
		71BB58F22B96511800CB9BFE /* FBVideoCommands.m in Sources */ = {isa = PBXBuildFile; fileRef = 71BB58EE2B96511800CB9BFE /* FBVideoCommands.m */; };
		71BB58F32B96511800CB9BFE /* FBVideoCommands.m in Sources */ = {isa = PBXBuildFile; fileRef = 71BB58EE2B96511800CB9BFE /* FBVideoCommands.m */; };
		71BB58F62B96531900CB9BFE /* FBScreenRecordingContainer.h in Headers */ = {isa = PBXBuildFile; fileRef = 71BB58F42B96531900CB9BFE /* FBScreenRecordingContainer.h */; };
		71BB58F72B96531900CB9BFE /* FBScreenRecordingContainer.h in Headers */ = {isa = PBXBuildFile; fileRef = 71BB58F42B96531900CB9BFE /* FBScreenRecordingContainer.h */; };
		71BB58F82B96531900CB9BFE /* FBScreenRecordingContainer.m in Sources */ = {isa = PBXBuildFile; fileRef = 71BB58F52B96531900CB9BFE /* FBScreenRecordingContainer.m */; };
		71BB58F92B96531900CB9BFE /* FBScreenRecordingContainer.m in Sources */ = {isa = PBXBuildFile; fileRef = 71BB58F52B96531900CB9BFE /* FBScreenRecordingContainer.m */; };
		71BB58FA2B96531900CB9BFE /* FBScreenRecordingContainer.m in Sources */ = {isa = PBXBuildFile; fileRef = 71BB58F52B96531900CB9BFE /* FBScreenRecordingContainer.m */; };
		71BD20731F86116100B36EC2 /* XCUIApplication+FBTouchAction.h in Headers */ = {isa = PBXBuildFile; fileRef = 71BD20711F86116100B36EC2 /* XCUIApplication+FBTouchAction.h */; };
		71BD20741F86116100B36EC2 /* XCUIApplication+FBTouchAction.m in Sources */ = {isa = PBXBuildFile; fileRef = 71BD20721F86116100B36EC2 /* XCUIApplication+FBTouchAction.m */; };
		71C8E55125399A6B008572C1 /* XCUIApplication+FBQuiescence.h in Headers */ = {isa = PBXBuildFile; fileRef = 71C8E54F25399A6B008572C1 /* XCUIApplication+FBQuiescence.h */; };
		71C8E55225399A6B008572C1 /* XCUIApplication+FBQuiescence.h in Headers */ = {isa = PBXBuildFile; fileRef = 71C8E54F25399A6B008572C1 /* XCUIApplication+FBQuiescence.h */; };
		71C8E55325399A6B008572C1 /* XCUIApplication+FBQuiescence.m in Sources */ = {isa = PBXBuildFile; fileRef = 71C8E55025399A6B008572C1 /* XCUIApplication+FBQuiescence.m */; };
		71C8E55425399A6B008572C1 /* XCUIApplication+FBQuiescence.m in Sources */ = {isa = PBXBuildFile; fileRef = 71C8E55025399A6B008572C1 /* XCUIApplication+FBQuiescence.m */; };
		71C9EAAC25E8415A00470CD8 /* FBScreenshot.h in Headers */ = {isa = PBXBuildFile; fileRef = 71C9EAAA25E8415A00470CD8 /* FBScreenshot.h */; };
		71C9EAAD25E8415A00470CD8 /* FBScreenshot.h in Headers */ = {isa = PBXBuildFile; fileRef = 71C9EAAA25E8415A00470CD8 /* FBScreenshot.h */; };
		71C9EAAE25E8415A00470CD8 /* FBScreenshot.m in Sources */ = {isa = PBXBuildFile; fileRef = 71C9EAAB25E8415A00470CD8 /* FBScreenshot.m */; };
		71C9EAAF25E8415A00470CD8 /* FBScreenshot.m in Sources */ = {isa = PBXBuildFile; fileRef = 71C9EAAB25E8415A00470CD8 /* FBScreenshot.m */; };
		71D04DC825356C43008A052C /* XCUIElement+FBCaching.h in Headers */ = {isa = PBXBuildFile; fileRef = 71D04DC625356C43008A052C /* XCUIElement+FBCaching.h */; settings = {ATTRIBUTES = (Public, ); }; };
		71D04DC925356C43008A052C /* XCUIElement+FBCaching.h in Headers */ = {isa = PBXBuildFile; fileRef = 71D04DC625356C43008A052C /* XCUIElement+FBCaching.h */; settings = {ATTRIBUTES = (Public, ); }; };
		71D04DCA25356C43008A052C /* XCUIElement+FBCaching.m in Sources */ = {isa = PBXBuildFile; fileRef = 71D04DC725356C43008A052C /* XCUIElement+FBCaching.m */; };
		71D04DCB25356C43008A052C /* XCUIElement+FBCaching.m in Sources */ = {isa = PBXBuildFile; fileRef = 71D04DC725356C43008A052C /* XCUIElement+FBCaching.m */; };
		71D3B3D5267FC7260076473D /* XCUIElement+FBResolve.h in Headers */ = {isa = PBXBuildFile; fileRef = 71D3B3D3267FC7260076473D /* XCUIElement+FBResolve.h */; };
		71D3B3D6267FC7260076473D /* XCUIElement+FBResolve.h in Headers */ = {isa = PBXBuildFile; fileRef = 71D3B3D3267FC7260076473D /* XCUIElement+FBResolve.h */; };
		71D3B3D7267FC7260076473D /* XCUIElement+FBResolve.m in Sources */ = {isa = PBXBuildFile; fileRef = 71D3B3D4267FC7260076473D /* XCUIElement+FBResolve.m */; };
		71D3B3D8267FC7260076473D /* XCUIElement+FBResolve.m in Sources */ = {isa = PBXBuildFile; fileRef = 71D3B3D4267FC7260076473D /* XCUIElement+FBResolve.m */; };
		71D475C22538F5A8008D9401 /* XCUIApplicationProcess+FBQuiescence.h in Headers */ = {isa = PBXBuildFile; fileRef = 71D475C02538F5A8008D9401 /* XCUIApplicationProcess+FBQuiescence.h */; };
		71D475C32538F5A8008D9401 /* XCUIApplicationProcess+FBQuiescence.h in Headers */ = {isa = PBXBuildFile; fileRef = 71D475C02538F5A8008D9401 /* XCUIApplicationProcess+FBQuiescence.h */; };
		71D475C42538F5A8008D9401 /* XCUIApplicationProcess+FBQuiescence.m in Sources */ = {isa = PBXBuildFile; fileRef = 71D475C12538F5A8008D9401 /* XCUIApplicationProcess+FBQuiescence.m */; };
		71D475C52538F5A8008D9401 /* XCUIApplicationProcess+FBQuiescence.m in Sources */ = {isa = PBXBuildFile; fileRef = 71D475C12538F5A8008D9401 /* XCUIApplicationProcess+FBQuiescence.m */; };
		71E75E6D254824230099FC87 /* XCUIElementQuery+FBHelpers.h in Headers */ = {isa = PBXBuildFile; fileRef = 71E75E6B254824230099FC87 /* XCUIElementQuery+FBHelpers.h */; };
		71E75E6E254824230099FC87 /* XCUIElementQuery+FBHelpers.h in Headers */ = {isa = PBXBuildFile; fileRef = 71E75E6B254824230099FC87 /* XCUIElementQuery+FBHelpers.h */; };
		71E75E6F254824230099FC87 /* XCUIElementQuery+FBHelpers.m in Sources */ = {isa = PBXBuildFile; fileRef = 71E75E6C254824230099FC87 /* XCUIElementQuery+FBHelpers.m */; };
		71E75E70254824230099FC87 /* XCUIElementQuery+FBHelpers.m in Sources */ = {isa = PBXBuildFile; fileRef = 71E75E6C254824230099FC87 /* XCUIElementQuery+FBHelpers.m */; };
		71F3E7D425417FF400E0C22B /* FBSettings.h in Headers */ = {isa = PBXBuildFile; fileRef = 71F3E7D225417FF400E0C22B /* FBSettings.h */; };
		71F3E7D525417FF400E0C22B /* FBSettings.h in Headers */ = {isa = PBXBuildFile; fileRef = 71F3E7D225417FF400E0C22B /* FBSettings.h */; };
		71F3E7D625417FF400E0C22B /* FBSettings.m in Sources */ = {isa = PBXBuildFile; fileRef = 71F3E7D325417FF400E0C22B /* FBSettings.m */; };
		71F3E7D725417FF400E0C22B /* FBSettings.m in Sources */ = {isa = PBXBuildFile; fileRef = 71F3E7D325417FF400E0C22B /* FBSettings.m */; };
		71F5BE23252E576C00EE9EBA /* XCUIElement+FBSwiping.h in Headers */ = {isa = PBXBuildFile; fileRef = 71F5BE21252E576C00EE9EBA /* XCUIElement+FBSwiping.h */; };
		71F5BE24252E576C00EE9EBA /* XCUIElement+FBSwiping.h in Headers */ = {isa = PBXBuildFile; fileRef = 71F5BE21252E576C00EE9EBA /* XCUIElement+FBSwiping.h */; };
		71F5BE25252E576C00EE9EBA /* XCUIElement+FBSwiping.m in Sources */ = {isa = PBXBuildFile; fileRef = 71F5BE22252E576C00EE9EBA /* XCUIElement+FBSwiping.m */; };
		71F5BE26252E576C00EE9EBA /* XCUIElement+FBSwiping.m in Sources */ = {isa = PBXBuildFile; fileRef = 71F5BE22252E576C00EE9EBA /* XCUIElement+FBSwiping.m */; };
		71F5BE34252E5B2200EE9EBA /* FBElementSwipingTests.m in Sources */ = {isa = PBXBuildFile; fileRef = 71F5BE33252E5B2200EE9EBA /* FBElementSwipingTests.m */; };
		71F5BE4F252F14EB00EE9EBA /* FBExceptions.h in Headers */ = {isa = PBXBuildFile; fileRef = 71F5BE4D252F14EB00EE9EBA /* FBExceptions.h */; };
		71F5BE50252F14EB00EE9EBA /* FBExceptions.h in Headers */ = {isa = PBXBuildFile; fileRef = 71F5BE4D252F14EB00EE9EBA /* FBExceptions.h */; };
		71F5BE51252F14EB00EE9EBA /* FBExceptions.m in Sources */ = {isa = PBXBuildFile; fileRef = 71F5BE4E252F14EB00EE9EBA /* FBExceptions.m */; };
		71F5BE52252F14EB00EE9EBA /* FBExceptions.m in Sources */ = {isa = PBXBuildFile; fileRef = 71F5BE4E252F14EB00EE9EBA /* FBExceptions.m */; };
		AD35D06C1CF1C35500870A75 /* WebDriverAgentLib.framework in Copy frameworks */ = {isa = PBXBuildFile; fileRef = EE158A991CBD452B00A3E3F0 /* WebDriverAgentLib.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		AD6C26941CF2379700F8B5FF /* FBAlert.h in Headers */ = {isa = PBXBuildFile; fileRef = AD6C26921CF2379700F8B5FF /* FBAlert.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD6C26951CF2379700F8B5FF /* FBAlert.m in Sources */ = {isa = PBXBuildFile; fileRef = AD6C26931CF2379700F8B5FF /* FBAlert.m */; };
		AD6C26981CF2481700F8B5FF /* XCUIDevice+FBHelpers.h in Headers */ = {isa = PBXBuildFile; fileRef = AD6C26961CF2481700F8B5FF /* XCUIDevice+FBHelpers.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD6C26991CF2481700F8B5FF /* XCUIDevice+FBHelpers.m in Sources */ = {isa = PBXBuildFile; fileRef = AD6C26971CF2481700F8B5FF /* XCUIDevice+FBHelpers.m */; };
		AD6C269C1CF2494200F8B5FF /* XCUIApplication+FBHelpers.h in Headers */ = {isa = PBXBuildFile; fileRef = AD6C269A1CF2494200F8B5FF /* XCUIApplication+FBHelpers.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD6C269D1CF2494200F8B5FF /* XCUIApplication+FBHelpers.m in Sources */ = {isa = PBXBuildFile; fileRef = AD6C269B1CF2494200F8B5FF /* XCUIApplication+FBHelpers.m */; };
		AD76723D1D6B7CC000610457 /* XCUIElement+FBTyping.h in Headers */ = {isa = PBXBuildFile; fileRef = AD76723B1D6B7CC000610457 /* XCUIElement+FBTyping.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD76723E1D6B7CC000610457 /* XCUIElement+FBTyping.m in Sources */ = {isa = PBXBuildFile; fileRef = AD76723C1D6B7CC000610457 /* XCUIElement+FBTyping.m */; };
		AD8D96F21D3C12990061268E /* WebDriverAgentLib.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = EE158A991CBD452B00A3E3F0 /* WebDriverAgentLib.framework */; };
		ADBC39941D0782CD00327304 /* FBElementCacheTests.m in Sources */ = {isa = PBXBuildFile; fileRef = ADBC39931D0782CD00327304 /* FBElementCacheTests.m */; };
		ADBC39981D07842800327304 /* XCUIElementDouble.m in Sources */ = {isa = PBXBuildFile; fileRef = ADBC39971D07842800327304 /* XCUIElementDouble.m */; };
		ADDA07241D6BB2BF001700AC /* FBScrollViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = ADDA07231D6BB2BF001700AC /* FBScrollViewController.m */; };
		ADEF63AF1D09DEBE0070A7E3 /* FBRuntimeUtilsTests.m in Sources */ = {isa = PBXBuildFile; fileRef = ADEF63AE1D09DEBE0070A7E3 /* FBRuntimeUtilsTests.m */; };
		B316351C2DDF0CF5007D9317 /* FBAccessibilityTraits.m in Sources */ = {isa = PBXBuildFile; fileRef = B316351B2DDF0CF5007D9317 /* FBAccessibilityTraits.m */; };
		B316351D2DDF0CF5007D9317 /* FBAccessibilityTraits.m in Sources */ = {isa = PBXBuildFile; fileRef = B316351B2DDF0CF5007D9317 /* FBAccessibilityTraits.m */; };
		B316351F2DDF0D0B007D9317 /* FBAccessibilityTraits.h in Headers */ = {isa = PBXBuildFile; fileRef = B316351E2DDF0D0B007D9317 /* FBAccessibilityTraits.h */; };
		B31635202DDF0D0B007D9317 /* FBAccessibilityTraits.h in Headers */ = {isa = PBXBuildFile; fileRef = B316351E2DDF0D0B007D9317 /* FBAccessibilityTraits.h */; };
		C845206222D5E79400EA68CB /* FBUnattachedAppLauncher.h in Headers */ = {isa = PBXBuildFile; fileRef = C8FB547722D4C1FC00B69954 /* FBUnattachedAppLauncher.h */; };
		C845206322D5E79700EA68CB /* FBUnattachedAppLauncher.m in Sources */ = {isa = PBXBuildFile; fileRef = C8FB547822D4C1FC00B69954 /* FBUnattachedAppLauncher.m */; };
		C8FB547422D3949C00B69954 /* LSApplicationWorkspace.h in Headers */ = {isa = PBXBuildFile; fileRef = C8FB547322D3949C00B69954 /* LSApplicationWorkspace.h */; };
		C8FB547922D4C1FC00B69954 /* FBUnattachedAppLauncher.h in Headers */ = {isa = PBXBuildFile; fileRef = C8FB547722D4C1FC00B69954 /* FBUnattachedAppLauncher.h */; };
		C8FB547A22D4C1FC00B69954 /* FBUnattachedAppLauncher.m in Sources */ = {isa = PBXBuildFile; fileRef = C8FB547822D4C1FC00B69954 /* FBUnattachedAppLauncher.m */; };
		E444DC65249131890060D7EB /* HTTPErrorResponse.h in Headers */ = {isa = PBXBuildFile; fileRef = E444DC59249131880060D7EB /* HTTPErrorResponse.h */; };
		E444DC67249131890060D7EB /* HTTPDataResponse.m in Sources */ = {isa = PBXBuildFile; fileRef = E444DC5B249131880060D7EB /* HTTPDataResponse.m */; };
		E444DC6C249131890060D7EB /* HTTPDataResponse.h in Headers */ = {isa = PBXBuildFile; fileRef = E444DC60249131890060D7EB /* HTTPDataResponse.h */; };
		E444DC6D249131890060D7EB /* HTTPErrorResponse.m in Sources */ = {isa = PBXBuildFile; fileRef = E444DC61249131890060D7EB /* HTTPErrorResponse.m */; };
		E444DC81249131B10060D7EB /* DDRange.h in Headers */ = {isa = PBXBuildFile; fileRef = E444DC7B249131B00060D7EB /* DDRange.h */; };
		E444DC83249131B10060D7EB /* DDNumber.h in Headers */ = {isa = PBXBuildFile; fileRef = E444DC7D249131B00060D7EB /* DDNumber.h */; };
		E444DC84249131B10060D7EB /* DDRange.m in Sources */ = {isa = PBXBuildFile; fileRef = E444DC7E249131B00060D7EB /* DDRange.m */; };
		E444DC85249131B10060D7EB /* DDNumber.m in Sources */ = {isa = PBXBuildFile; fileRef = E444DC7F249131B00060D7EB /* DDNumber.m */; };
		E444DC93249131D40060D7EB /* HTTPMessage.h in Headers */ = {isa = PBXBuildFile; fileRef = E444DC87249131D30060D7EB /* HTTPMessage.h */; };
		E444DC95249131D40060D7EB /* HTTPConnection.h in Headers */ = {isa = PBXBuildFile; fileRef = E444DC89249131D30060D7EB /* HTTPConnection.h */; };
		E444DC97249131D40060D7EB /* HTTPServer.h in Headers */ = {isa = PBXBuildFile; fileRef = E444DC8B249131D30060D7EB /* HTTPServer.h */; };
		E444DC98249131D40060D7EB /* HTTPConnection.m in Sources */ = {isa = PBXBuildFile; fileRef = E444DC8C249131D30060D7EB /* HTTPConnection.m */; };
		E444DC99249131D40060D7EB /* HTTPLogging.h in Headers */ = {isa = PBXBuildFile; fileRef = E444DC8D249131D30060D7EB /* HTTPLogging.h */; };
		E444DC9B249131D40060D7EB /* HTTPResponse.h in Headers */ = {isa = PBXBuildFile; fileRef = E444DC8F249131D40060D7EB /* HTTPResponse.h */; };
		E444DC9C249131D40060D7EB /* HTTPServer.m in Sources */ = {isa = PBXBuildFile; fileRef = E444DC90249131D40060D7EB /* HTTPServer.m */; };
		E444DC9D249131D40060D7EB /* HTTPMessage.m in Sources */ = {isa = PBXBuildFile; fileRef = E444DC91249131D40060D7EB /* HTTPMessage.m */; };
		E444DCAB24913C220060D7EB /* HTTPResponseProxy.m in Sources */ = {isa = PBXBuildFile; fileRef = E444DC9F24913C210060D7EB /* HTTPResponseProxy.m */; };
		E444DCAC24913C220060D7EB /* Route.m in Sources */ = {isa = PBXBuildFile; fileRef = E444DCA024913C210060D7EB /* Route.m */; };
		E444DCAD24913C220060D7EB /* RouteResponse.h in Headers */ = {isa = PBXBuildFile; fileRef = E444DCA124913C210060D7EB /* RouteResponse.h */; };
		E444DCAE24913C220060D7EB /* HTTPResponseProxy.h in Headers */ = {isa = PBXBuildFile; fileRef = E444DCA224913C210060D7EB /* HTTPResponseProxy.h */; };
		E444DCAF24913C220060D7EB /* Route.h in Headers */ = {isa = PBXBuildFile; fileRef = E444DCA324913C210060D7EB /* Route.h */; };
		E444DCB024913C220060D7EB /* RouteResponse.m in Sources */ = {isa = PBXBuildFile; fileRef = E444DCA424913C210060D7EB /* RouteResponse.m */; };
		E444DCB124913C220060D7EB /* RoutingConnection.h in Headers */ = {isa = PBXBuildFile; fileRef = E444DCA524913C210060D7EB /* RoutingConnection.h */; };
		E444DCB224913C220060D7EB /* RoutingConnection.m in Sources */ = {isa = PBXBuildFile; fileRef = E444DCA624913C210060D7EB /* RoutingConnection.m */; };
		E444DCB324913C220060D7EB /* RoutingHTTPServer.h in Headers */ = {isa = PBXBuildFile; fileRef = E444DCA724913C210060D7EB /* RoutingHTTPServer.h */; };
		E444DCB424913C220060D7EB /* RoutingHTTPServer.m in Sources */ = {isa = PBXBuildFile; fileRef = E444DCA824913C220060D7EB /* RoutingHTTPServer.m */; };
		E444DCB524913C220060D7EB /* RouteRequest.m in Sources */ = {isa = PBXBuildFile; fileRef = E444DCA924913C220060D7EB /* RouteRequest.m */; };
		E444DCB624913C220060D7EB /* RouteRequest.h in Headers */ = {isa = PBXBuildFile; fileRef = E444DCAA24913C220060D7EB /* RouteRequest.h */; };
		E444DCBC24917A5E0060D7EB /* HTTPResponseProxy.m in Sources */ = {isa = PBXBuildFile; fileRef = E444DC9F24913C210060D7EB /* HTTPResponseProxy.m */; };
		E444DCBE24917A5E0060D7EB /* Route.m in Sources */ = {isa = PBXBuildFile; fileRef = E444DCA024913C210060D7EB /* Route.m */; };
		E444DCC024917A5E0060D7EB /* RouteRequest.m in Sources */ = {isa = PBXBuildFile; fileRef = E444DCA924913C220060D7EB /* RouteRequest.m */; };
		E444DCC224917A5E0060D7EB /* RouteResponse.m in Sources */ = {isa = PBXBuildFile; fileRef = E444DCA424913C210060D7EB /* RouteResponse.m */; };
		E444DCC424917A5E0060D7EB /* RoutingConnection.m in Sources */ = {isa = PBXBuildFile; fileRef = E444DCA624913C210060D7EB /* RoutingConnection.m */; };
		E444DCC624917A5E0060D7EB /* RoutingHTTPServer.m in Sources */ = {isa = PBXBuildFile; fileRef = E444DCA824913C220060D7EB /* RoutingHTTPServer.m */; };
		E444DCC824917A5E0060D7EB /* HTTPConnection.m in Sources */ = {isa = PBXBuildFile; fileRef = E444DC8C249131D30060D7EB /* HTTPConnection.m */; };
		E444DCCB24917A5E0060D7EB /* HTTPMessage.m in Sources */ = {isa = PBXBuildFile; fileRef = E444DC91249131D40060D7EB /* HTTPMessage.m */; };
		E444DCCE24917A5E0060D7EB /* HTTPServer.m in Sources */ = {isa = PBXBuildFile; fileRef = E444DC90249131D40060D7EB /* HTTPServer.m */; };
		E444DCD024917A5E0060D7EB /* HTTPDataResponse.m in Sources */ = {isa = PBXBuildFile; fileRef = E444DC5B249131880060D7EB /* HTTPDataResponse.m */; };
		E444DCD224917A5E0060D7EB /* HTTPErrorResponse.m in Sources */ = {isa = PBXBuildFile; fileRef = E444DC61249131890060D7EB /* HTTPErrorResponse.m */; };
		E444DCD424917A5E0060D7EB /* DDNumber.m in Sources */ = {isa = PBXBuildFile; fileRef = E444DC7F249131B00060D7EB /* DDNumber.m */; };
		E444DCD624917A5E0060D7EB /* DDRange.m in Sources */ = {isa = PBXBuildFile; fileRef = E444DC7E249131B00060D7EB /* DDRange.m */; };
		EE006EAD1EB99B15006900A4 /* FBElementVisibilityTests.m in Sources */ = {isa = PBXBuildFile; fileRef = EE006EAC1EB99B15006900A4 /* FBElementVisibilityTests.m */; };
		EE05BAFA1D13003C00A3EB00 /* FBKeyboardTests.m in Sources */ = {isa = PBXBuildFile; fileRef = EE05BAF91D13003C00A3EB00 /* FBKeyboardTests.m */; };
		EE0D1F611EBCDCF7006A3123 /* NSString+FBVisualLength.h in Headers */ = {isa = PBXBuildFile; fileRef = EE0D1F5F1EBCDCF7006A3123 /* NSString+FBVisualLength.h */; };
		EE0D1F621EBCDCF7006A3123 /* NSString+FBVisualLength.m in Sources */ = {isa = PBXBuildFile; fileRef = EE0D1F601EBCDCF7006A3123 /* NSString+FBVisualLength.m */; };
		EE158AAE1CBD456F00A3E3F0 /* XCUIElement+FBAccessibility.h in Headers */ = {isa = PBXBuildFile; fileRef = EE9AB7451CAEDF0C008C271F /* XCUIElement+FBAccessibility.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE158AAF1CBD456F00A3E3F0 /* XCUIElement+FBAccessibility.m in Sources */ = {isa = PBXBuildFile; fileRef = EE9AB7461CAEDF0C008C271F /* XCUIElement+FBAccessibility.m */; };
		EE158AB01CBD456F00A3E3F0 /* XCUIElement+FBIsVisible.h in Headers */ = {isa = PBXBuildFile; fileRef = EE9AB7471CAEDF0C008C271F /* XCUIElement+FBIsVisible.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE158AB11CBD456F00A3E3F0 /* XCUIElement+FBIsVisible.m in Sources */ = {isa = PBXBuildFile; fileRef = EE9AB7481CAEDF0C008C271F /* XCUIElement+FBIsVisible.m */; };
		EE158AB21CBD456F00A3E3F0 /* XCUIElement+FBScrolling.h in Headers */ = {isa = PBXBuildFile; fileRef = EE9AB7491CAEDF0C008C271F /* XCUIElement+FBScrolling.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE158AB31CBD456F00A3E3F0 /* XCUIElement+FBScrolling.m in Sources */ = {isa = PBXBuildFile; fileRef = EE9AB74A1CAEDF0C008C271F /* XCUIElement+FBScrolling.m */; };
		EE158AB81CBD456F00A3E3F0 /* FBAlertViewCommands.h in Headers */ = {isa = PBXBuildFile; fileRef = EE9AB7501CAEDF0C008C271F /* FBAlertViewCommands.h */; };
		EE158AB91CBD456F00A3E3F0 /* FBAlertViewCommands.m in Sources */ = {isa = PBXBuildFile; fileRef = EE9AB7511CAEDF0C008C271F /* FBAlertViewCommands.m */; };
		EE158ABA1CBD456F00A3E3F0 /* FBCustomCommands.h in Headers */ = {isa = PBXBuildFile; fileRef = EE9AB7521CAEDF0C008C271F /* FBCustomCommands.h */; };
		EE158ABB1CBD456F00A3E3F0 /* FBCustomCommands.m in Sources */ = {isa = PBXBuildFile; fileRef = EE9AB7531CAEDF0C008C271F /* FBCustomCommands.m */; };
		EE158ABC1CBD456F00A3E3F0 /* FBDebugCommands.h in Headers */ = {isa = PBXBuildFile; fileRef = EE9AB7541CAEDF0C008C271F /* FBDebugCommands.h */; };
		EE158ABD1CBD456F00A3E3F0 /* FBDebugCommands.m in Sources */ = {isa = PBXBuildFile; fileRef = EE9AB7551CAEDF0C008C271F /* FBDebugCommands.m */; };
		EE158ABE1CBD456F00A3E3F0 /* FBElementCommands.h in Headers */ = {isa = PBXBuildFile; fileRef = EE9AB7561CAEDF0C008C271F /* FBElementCommands.h */; };
		EE158ABF1CBD456F00A3E3F0 /* FBElementCommands.m in Sources */ = {isa = PBXBuildFile; fileRef = EE9AB7571CAEDF0C008C271F /* FBElementCommands.m */; };
		EE158AC01CBD456F00A3E3F0 /* FBFindElementCommands.h in Headers */ = {isa = PBXBuildFile; fileRef = EE9AB7581CAEDF0C008C271F /* FBFindElementCommands.h */; };
		EE158AC11CBD456F00A3E3F0 /* FBFindElementCommands.m in Sources */ = {isa = PBXBuildFile; fileRef = EE9AB7591CAEDF0C008C271F /* FBFindElementCommands.m */; };
		EE158AC41CBD456F00A3E3F0 /* FBOrientationCommands.h in Headers */ = {isa = PBXBuildFile; fileRef = EE9AB75C1CAEDF0C008C271F /* FBOrientationCommands.h */; };
		EE158AC51CBD456F00A3E3F0 /* FBOrientationCommands.m in Sources */ = {isa = PBXBuildFile; fileRef = EE9AB75D1CAEDF0C008C271F /* FBOrientationCommands.m */; };
		EE158AC61CBD456F00A3E3F0 /* FBScreenshotCommands.h in Headers */ = {isa = PBXBuildFile; fileRef = EE9AB75E1CAEDF0C008C271F /* FBScreenshotCommands.h */; };
		EE158AC71CBD456F00A3E3F0 /* FBScreenshotCommands.m in Sources */ = {isa = PBXBuildFile; fileRef = EE9AB75F1CAEDF0C008C271F /* FBScreenshotCommands.m */; };
		EE158AC81CBD456F00A3E3F0 /* FBSessionCommands.h in Headers */ = {isa = PBXBuildFile; fileRef = EE9AB7601CAEDF0C008C271F /* FBSessionCommands.h */; };
		EE158AC91CBD456F00A3E3F0 /* FBSessionCommands.m in Sources */ = {isa = PBXBuildFile; fileRef = EE9AB7611CAEDF0C008C271F /* FBSessionCommands.m */; };
		EE158ACA1CBD456F00A3E3F0 /* FBTouchIDCommands.h in Headers */ = {isa = PBXBuildFile; fileRef = EE9AB7621CAEDF0C008C271F /* FBTouchIDCommands.h */; };
		EE158ACB1CBD456F00A3E3F0 /* FBTouchIDCommands.m in Sources */ = {isa = PBXBuildFile; fileRef = EE9AB7631CAEDF0C008C271F /* FBTouchIDCommands.m */; };
		EE158ACC1CBD456F00A3E3F0 /* FBUnknownCommands.h in Headers */ = {isa = PBXBuildFile; fileRef = EE9AB7641CAEDF0C008C271F /* FBUnknownCommands.h */; };
		EE158ACD1CBD456F00A3E3F0 /* FBUnknownCommands.m in Sources */ = {isa = PBXBuildFile; fileRef = EE9AB7651CAEDF0C008C271F /* FBUnknownCommands.m */; };
		EE158ACE1CBD456F00A3E3F0 /* FBCommandHandler.h in Headers */ = {isa = PBXBuildFile; fileRef = EE9AB7751CAEDF0C008C271F /* FBCommandHandler.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE158ACF1CBD456F00A3E3F0 /* FBCommandStatus.h in Headers */ = {isa = PBXBuildFile; fileRef = EE9AB7761CAEDF0C008C271F /* FBCommandStatus.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE158AD01CBD456F00A3E3F0 /* FBElement.h in Headers */ = {isa = PBXBuildFile; fileRef = EE9AB7791CAEDF0C008C271F /* FBElement.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE158AD21CBD456F00A3E3F0 /* FBElementCache.h in Headers */ = {isa = PBXBuildFile; fileRef = EE9AB77B1CAEDF0C008C271F /* FBElementCache.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE158AD31CBD456F00A3E3F0 /* FBElementCache.m in Sources */ = {isa = PBXBuildFile; fileRef = EEC088E41CB56AC000B65968 /* FBElementCache.m */; };
		EE158AD41CBD456F00A3E3F0 /* FBExceptionHandler.h in Headers */ = {isa = PBXBuildFile; fileRef = EEC088E61CB56DA400B65968 /* FBExceptionHandler.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE158AD51CBD456F00A3E3F0 /* FBExceptionHandler.m in Sources */ = {isa = PBXBuildFile; fileRef = EEC088E71CB56DA400B65968 /* FBExceptionHandler.m */; };
		EE158ADA1CBD456F00A3E3F0 /* FBResponseJSONPayload.h in Headers */ = {isa = PBXBuildFile; fileRef = EE9AB7801CAEDF0C008C271F /* FBResponseJSONPayload.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE158ADB1CBD456F00A3E3F0 /* FBResponseJSONPayload.m in Sources */ = {isa = PBXBuildFile; fileRef = EE9AB7811CAEDF0C008C271F /* FBResponseJSONPayload.m */; };
		EE158ADC1CBD456F00A3E3F0 /* FBResponsePayload.h in Headers */ = {isa = PBXBuildFile; fileRef = EE9AB7821CAEDF0C008C271F /* FBResponsePayload.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE158ADD1CBD456F00A3E3F0 /* FBResponsePayload.m in Sources */ = {isa = PBXBuildFile; fileRef = EE9AB7831CAEDF0C008C271F /* FBResponsePayload.m */; };
		EE158ADE1CBD456F00A3E3F0 /* FBRoute.h in Headers */ = {isa = PBXBuildFile; fileRef = EE9AB7841CAEDF0C008C271F /* FBRoute.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE158ADF1CBD456F00A3E3F0 /* FBRoute.m in Sources */ = {isa = PBXBuildFile; fileRef = EE9AB7851CAEDF0C008C271F /* FBRoute.m */; };
		EE158AE01CBD456F00A3E3F0 /* FBRouteRequest-Private.h in Headers */ = {isa = PBXBuildFile; fileRef = EE9AB7861CAEDF0C008C271F /* FBRouteRequest-Private.h */; };
		EE158AE11CBD456F00A3E3F0 /* FBRouteRequest.h in Headers */ = {isa = PBXBuildFile; fileRef = EE9AB7871CAEDF0C008C271F /* FBRouteRequest.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE158AE21CBD456F00A3E3F0 /* FBRouteRequest.m in Sources */ = {isa = PBXBuildFile; fileRef = EE9AB7881CAEDF0C008C271F /* FBRouteRequest.m */; };
		EE158AE31CBD456F00A3E3F0 /* FBSession-Private.h in Headers */ = {isa = PBXBuildFile; fileRef = EE9AB7891CAEDF0C008C271F /* FBSession-Private.h */; };
		EE158AE41CBD456F00A3E3F0 /* FBSession.h in Headers */ = {isa = PBXBuildFile; fileRef = EE9AB78A1CAEDF0C008C271F /* FBSession.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE158AE51CBD456F00A3E3F0 /* FBSession.m in Sources */ = {isa = PBXBuildFile; fileRef = EE9AB78B1CAEDF0C008C271F /* FBSession.m */; };
		EE158AE61CBD456F00A3E3F0 /* FBWebServer.h in Headers */ = {isa = PBXBuildFile; fileRef = EE9AB78C1CAEDF0C008C271F /* FBWebServer.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE158AE71CBD456F00A3E3F0 /* FBWebServer.m in Sources */ = {isa = PBXBuildFile; fileRef = EE9AB78D1CAEDF0C008C271F /* FBWebServer.m */; };
		EE158AE81CBD456F00A3E3F0 /* FBElementTypeTransformer.h in Headers */ = {isa = PBXBuildFile; fileRef = EE9AB78F1CAEDF0C008C271F /* FBElementTypeTransformer.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE158AE91CBD456F00A3E3F0 /* FBElementTypeTransformer.m in Sources */ = {isa = PBXBuildFile; fileRef = EE9AB7901CAEDF0C008C271F /* FBElementTypeTransformer.m */; };
		EE158AEA1CBD456F00A3E3F0 /* FBRuntimeUtils.h in Headers */ = {isa = PBXBuildFile; fileRef = EE9AB7911CAEDF0C008C271F /* FBRuntimeUtils.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE158AEB1CBD456F00A3E3F0 /* FBRuntimeUtils.m in Sources */ = {isa = PBXBuildFile; fileRef = EE9AB7921CAEDF0C008C271F /* FBRuntimeUtils.m */; };
		EE158B5A1CBD462100A3E3F0 /* WebDriverAgentLib.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = EE158A991CBD452B00A3E3F0 /* WebDriverAgentLib.framework */; };
		EE158B5F1CBD47A000A3E3F0 /* WebDriverAgentLib.h in Headers */ = {isa = PBXBuildFile; fileRef = EE158B5E1CBD47A000A3E3F0 /* WebDriverAgentLib.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE18883A1DA661C400307AA8 /* FBMathUtils.h in Headers */ = {isa = PBXBuildFile; fileRef = EE1888381DA661C400307AA8 /* FBMathUtils.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE18883B1DA661C400307AA8 /* FBMathUtils.m in Sources */ = {isa = PBXBuildFile; fileRef = EE1888391DA661C400307AA8 /* FBMathUtils.m */; };
		EE18883D1DA663EB00307AA8 /* FBMathUtilsTests.m in Sources */ = {isa = PBXBuildFile; fileRef = EE18883C1DA663EB00307AA8 /* FBMathUtilsTests.m */; };
		EE1E06DA1D1808C2007CF043 /* FBIntegrationTestCase.m in Sources */ = {isa = PBXBuildFile; fileRef = EE1E06D91D1808C2007CF043 /* FBIntegrationTestCase.m */; };
		EE1E06E71D182E95007CF043 /* FBAlertViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = EE1E06E61D182E95007CF043 /* FBAlertViewController.m */; };
		EE2202131ECC612200A29571 /* FBIntegrationTestCase.m in Sources */ = {isa = PBXBuildFile; fileRef = EE1E06D91D1808C2007CF043 /* FBIntegrationTestCase.m */; };
		EE2202171ECC612200A29571 /* WebDriverAgentLib.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = EE158A991CBD452B00A3E3F0 /* WebDriverAgentLib.framework */; };
		EE22021E1ECC618900A29571 /* FBTapTest.m in Sources */ = {isa = PBXBuildFile; fileRef = EE26409A1D0EB5E8009BE6B0 /* FBTapTest.m */; };
		EE26409D1D0EBA25009BE6B0 /* FBElementAttributeTests.m in Sources */ = {isa = PBXBuildFile; fileRef = EE26409C1D0EBA25009BE6B0 /* FBElementAttributeTests.m */; };
		EE35AD091E3B77D600A02D78 /* _XCInternalTestRun.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35AC981E3B77D600A02D78 /* _XCInternalTestRun.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE35AD0A1E3B77D600A02D78 /* _XCKVOExpectationImplementation.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35AC991E3B77D600A02D78 /* _XCKVOExpectationImplementation.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE35AD0B1E3B77D600A02D78 /* _XCTDarwinNotificationExpectationImplementation.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35AC9A1E3B77D600A02D78 /* _XCTDarwinNotificationExpectationImplementation.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE35AD0C1E3B77D600A02D78 /* _XCTestCaseImplementation.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35AC9B1E3B77D600A02D78 /* _XCTestCaseImplementation.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE35AD0D1E3B77D600A02D78 /* _XCTestCaseInterruptionException.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35AC9C1E3B77D600A02D78 /* _XCTestCaseInterruptionException.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE35AD0E1E3B77D600A02D78 /* _XCTestExpectationImplementation.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35AC9D1E3B77D600A02D78 /* _XCTestExpectationImplementation.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE35AD0F1E3B77D600A02D78 /* _XCTestImplementation.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35AC9E1E3B77D600A02D78 /* _XCTestImplementation.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE35AD101E3B77D600A02D78 /* _XCTestObservationCenterImplementation.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35AC9F1E3B77D600A02D78 /* _XCTestObservationCenterImplementation.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE35AD111E3B77D600A02D78 /* _XCTestSuiteImplementation.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACA01E3B77D600A02D78 /* _XCTestSuiteImplementation.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE35AD121E3B77D600A02D78 /* _XCTNSNotificationExpectationImplementation.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACA11E3B77D600A02D78 /* _XCTNSNotificationExpectationImplementation.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE35AD131E3B77D600A02D78 /* _XCTNSPredicateExpectationImplementation.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACA21E3B77D600A02D78 /* _XCTNSPredicateExpectationImplementation.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE35AD141E3B77D600A02D78 /* _XCTWaiterImpl.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACA31E3B77D600A02D78 /* _XCTWaiterImpl.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE35AD151E3B77D600A02D78 /* CDStructures.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACA41E3B77D600A02D78 /* CDStructures.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE35AD1C1E3B77D600A02D78 /* NSString-XCTAdditions.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACAB1E3B77D600A02D78 /* NSString-XCTAdditions.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE35AD1D1E3B77D600A02D78 /* NSValue-XCTestAdditions.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACAC1E3B77D600A02D78 /* NSValue-XCTestAdditions.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE35AD1E1E3B77D600A02D78 /* UIGestureRecognizer-RecordingAdditions.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACAD1E3B77D600A02D78 /* UIGestureRecognizer-RecordingAdditions.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE35AD1F1E3B77D600A02D78 /* UILongPressGestureRecognizer-RecordingAdditions.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACAE1E3B77D600A02D78 /* UILongPressGestureRecognizer-RecordingAdditions.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE35AD201E3B77D600A02D78 /* UIPanGestureRecognizer-RecordingAdditions.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACAF1E3B77D600A02D78 /* UIPanGestureRecognizer-RecordingAdditions.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE35AD211E3B77D600A02D78 /* UIPinchGestureRecognizer-RecordingAdditions.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACB01E3B77D600A02D78 /* UIPinchGestureRecognizer-RecordingAdditions.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE35AD221E3B77D600A02D78 /* UISwipeGestureRecognizer-RecordingAdditions.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACB11E3B77D600A02D78 /* UISwipeGestureRecognizer-RecordingAdditions.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE35AD231E3B77D600A02D78 /* UITapGestureRecognizer-RecordingAdditions.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACB21E3B77D600A02D78 /* UITapGestureRecognizer-RecordingAdditions.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE35AD251E3B77D600A02D78 /* XCActivityRecord.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACB41E3B77D600A02D78 /* XCActivityRecord.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE35AD261E3B77D600A02D78 /* XCApplicationMonitor_iOS.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACB51E3B77D600A02D78 /* XCApplicationMonitor_iOS.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE35AD271E3B77D600A02D78 /* XCApplicationMonitor.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACB61E3B77D600A02D78 /* XCApplicationMonitor.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE35AD281E3B77D600A02D78 /* XCApplicationQuery.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACB71E3B77D600A02D78 /* XCApplicationQuery.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE35AD291E3B77D600A02D78 /* XCAXClient_iOS.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACB81E3B77D600A02D78 /* XCAXClient_iOS.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE35AD2A1E3B77D600A02D78 /* XCDebugLogDelegate-Protocol.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACB91E3B77D600A02D78 /* XCDebugLogDelegate-Protocol.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE35AD2E1E3B77D600A02D78 /* XCEventGenerator.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACBD1E3B77D600A02D78 /* XCEventGenerator.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE35AD2F1E3B77D600A02D78 /* XCKeyboardInputSolver.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACBE1E3B77D600A02D78 /* XCKeyboardInputSolver.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE35AD301E3B77D600A02D78 /* XCKeyboardKeyMap.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACBF1E3B77D600A02D78 /* XCKeyboardKeyMap.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE35AD311E3B77D600A02D78 /* XCKeyboardLayout.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACC01E3B77D600A02D78 /* XCKeyboardLayout.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE35AD321E3B77D600A02D78 /* XCKeyMappingPath.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACC11E3B77D600A02D78 /* XCKeyMappingPath.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE35AD331E3B77D600A02D78 /* XCPointerEvent.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACC21E3B77D600A02D78 /* XCPointerEvent.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE35AD341E3B77D600A02D78 /* XCPointerEventPath.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACC31E3B77D600A02D78 /* XCPointerEventPath.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE35AD351E3B77D600A02D78 /* XCSourceCodeRecording.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACC41E3B77D600A02D78 /* XCSourceCodeRecording.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE35AD361E3B77D600A02D78 /* XCSourceCodeTreeNode.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACC51E3B77D600A02D78 /* XCSourceCodeTreeNode.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE35AD371E3B77D600A02D78 /* XCSourceCodeTreeNodeEnumerator.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACC61E3B77D600A02D78 /* XCSourceCodeTreeNodeEnumerator.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE35AD381E3B77D600A02D78 /* XCSymbolicationRecord.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACC71E3B77D600A02D78 /* XCSymbolicationRecord.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE35AD391E3B77D600A02D78 /* XCSymbolicatorHolder.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACC81E3B77D600A02D78 /* XCSymbolicatorHolder.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE35AD3A1E3B77D600A02D78 /* XCSynthesizedEventRecord.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACC91E3B77D600A02D78 /* XCSynthesizedEventRecord.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE35AD3B1E3B77D600A02D78 /* XCTAsyncActivity-Protocol.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACCA1E3B77D600A02D78 /* XCTAsyncActivity-Protocol.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE35AD3C1E3B77D600A02D78 /* XCTAsyncActivity.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACCB1E3B77D600A02D78 /* XCTAsyncActivity.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE35AD3D1E3B77D600A02D78 /* XCTAutomationTarget-Protocol.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACCC1E3B77D600A02D78 /* XCTAutomationTarget-Protocol.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE35AD3E1E3B77D600A02D78 /* XCTAXClient-Protocol.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACCD1E3B77D600A02D78 /* XCTAXClient-Protocol.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE35AD3F1E3B77D600A02D78 /* XCTDarwinNotificationExpectation.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACCE1E3B77D600A02D78 /* XCTDarwinNotificationExpectation.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE35AD401E3B77D600A02D78 /* XCTest.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACCF1E3B77D600A02D78 /* XCTest.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE35AD411E3B77D600A02D78 /* XCTestCase.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACD01E3B77D600A02D78 /* XCTestCase.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE35AD421E3B77D600A02D78 /* XCTestCaseRun.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACD11E3B77D600A02D78 /* XCTestCaseRun.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE35AD431E3B77D600A02D78 /* XCTestCaseSuite.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACD21E3B77D600A02D78 /* XCTestCaseSuite.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE35AD441E3B77D600A02D78 /* XCTestConfiguration.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACD31E3B77D600A02D78 /* XCTestConfiguration.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE35AD451E3B77D600A02D78 /* XCTestContext.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACD41E3B77D600A02D78 /* XCTestContext.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE35AD461E3B77D600A02D78 /* XCTestContextScope.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACD51E3B77D600A02D78 /* XCTestContextScope.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE35AD471E3B77D600A02D78 /* XCTestDriver.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACD61E3B77D600A02D78 /* XCTestDriver.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE35AD481E3B77D600A02D78 /* XCTestDriverInterface-Protocol.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACD71E3B77D600A02D78 /* XCTestDriverInterface-Protocol.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE35AD491E3B77D600A02D78 /* XCTestExpectation.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACD81E3B77D600A02D78 /* XCTestExpectation.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE35AD4A1E3B77D600A02D78 /* XCTestExpectationDelegate-Protocol.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACD91E3B77D600A02D78 /* XCTestExpectationDelegate-Protocol.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE35AD4B1E3B77D600A02D78 /* XCTestExpectationWaiter.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACDA1E3B77D600A02D78 /* XCTestExpectationWaiter.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE35AD4C1E3B77D600A02D78 /* XCTestLog.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACDB1E3B77D600A02D78 /* XCTestLog.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE35AD4D1E3B77D600A02D78 /* XCTestManager_IDEInterface-Protocol.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACDC1E3B77D600A02D78 /* XCTestManager_IDEInterface-Protocol.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE35AD4E1E3B77D600A02D78 /* XCTestManager_ManagerInterface-Protocol.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACDD1E3B77D600A02D78 /* XCTestManager_ManagerInterface-Protocol.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE35AD4F1E3B77D600A02D78 /* XCTestManager_TestsInterface-Protocol.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACDE1E3B77D600A02D78 /* XCTestManager_TestsInterface-Protocol.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE35AD501E3B77D600A02D78 /* XCTestMisuseObserver.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACDF1E3B77D600A02D78 /* XCTestMisuseObserver.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE35AD511E3B77D600A02D78 /* XCTestObservation-Protocol.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACE01E3B77D600A02D78 /* XCTestObservation-Protocol.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE35AD521E3B77D600A02D78 /* XCTestObservationCenter.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACE11E3B77D600A02D78 /* XCTestObservationCenter.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE35AD531E3B77D600A02D78 /* XCTestObserver.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACE21E3B77D600A02D78 /* XCTestObserver.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE35AD541E3B77D600A02D78 /* XCTestProbe.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACE31E3B77D600A02D78 /* XCTestProbe.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE35AD551E3B77D600A02D78 /* XCTestRun.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACE41E3B77D600A02D78 /* XCTestRun.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE35AD561E3B77D600A02D78 /* XCTestSuite.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACE51E3B77D600A02D78 /* XCTestSuite.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE35AD571E3B77D600A02D78 /* XCTestSuiteRun.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACE61E3B77D600A02D78 /* XCTestSuiteRun.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE35AD581E3B77D600A02D78 /* XCTestWaiter.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACE71E3B77D600A02D78 /* XCTestWaiter.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE35AD591E3B77D600A02D78 /* XCTKVOExpectation.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACE81E3B77D600A02D78 /* XCTKVOExpectation.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE35AD5A1E3B77D600A02D78 /* XCTMetric.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACE91E3B77D600A02D78 /* XCTMetric.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE35AD5B1E3B77D600A02D78 /* XCTNSNotificationExpectation.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACEA1E3B77D600A02D78 /* XCTNSNotificationExpectation.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE35AD5C1E3B77D600A02D78 /* XCTNSPredicateExpectation.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACEB1E3B77D600A02D78 /* XCTNSPredicateExpectation.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE35AD5D1E3B77D600A02D78 /* XCTNSPredicateExpectationObject-Protocol.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACEC1E3B77D600A02D78 /* XCTNSPredicateExpectationObject-Protocol.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE35AD5F1E3B77D600A02D78 /* XCTRunnerAutomationSession.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACEE1E3B77D600A02D78 /* XCTRunnerAutomationSession.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE35AD601E3B77D600A02D78 /* XCTRunnerDaemonSession.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACEF1E3B77D600A02D78 /* XCTRunnerDaemonSession.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE35AD611E3B77D600A02D78 /* XCTRunnerIDESession.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACF01E3B77D600A02D78 /* XCTRunnerIDESession.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE35AD621E3B77D600A02D78 /* XCTTestRunSession.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACF11E3B77D600A02D78 /* XCTTestRunSession.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE35AD631E3B77D600A02D78 /* XCTTestRunSessionDelegate-Protocol.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACF21E3B77D600A02D78 /* XCTTestRunSessionDelegate-Protocol.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE35AD641E3B77D600A02D78 /* XCTUIApplicationMonitor-Protocol.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACF31E3B77D600A02D78 /* XCTUIApplicationMonitor-Protocol.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE35AD651E3B77D600A02D78 /* XCTWaiter.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACF41E3B77D600A02D78 /* XCTWaiter.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE35AD661E3B77D600A02D78 /* XCTWaiterDelegate-Protocol.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACF51E3B77D600A02D78 /* XCTWaiterDelegate-Protocol.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE35AD671E3B77D600A02D78 /* XCTWaiterDelegatePrivate-Protocol.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACF61E3B77D600A02D78 /* XCTWaiterDelegatePrivate-Protocol.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE35AD681E3B77D600A02D78 /* XCTWaiterManagement-Protocol.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACF71E3B77D600A02D78 /* XCTWaiterManagement-Protocol.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE35AD691E3B77D600A02D78 /* XCTWaiterManager.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACF81E3B77D600A02D78 /* XCTWaiterManager.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE35AD6A1E3B77D600A02D78 /* XCUIApplication.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACF91E3B77D600A02D78 /* XCUIApplication.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE35AD6B1E3B77D600A02D78 /* XCUIApplicationImpl.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACFA1E3B77D600A02D78 /* XCUIApplicationImpl.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE35AD6C1E3B77D600A02D78 /* XCUIApplicationProcess.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACFB1E3B77D600A02D78 /* XCUIApplicationProcess.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE35AD6D1E3B77D600A02D78 /* XCUICoordinate.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACFC1E3B77D600A02D78 /* XCUICoordinate.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE35AD6E1E3B77D600A02D78 /* XCUIDevice.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACFD1E3B77D600A02D78 /* XCUIDevice.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE35AD6F1E3B77D600A02D78 /* XCUIElement.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACFE1E3B77D600A02D78 /* XCUIElement.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE35AD701E3B77D600A02D78 /* XCUIElementAsynchronousHandlerWrapper.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35ACFF1E3B77D600A02D78 /* XCUIElementAsynchronousHandlerWrapper.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE35AD721E3B77D600A02D78 /* XCUIElementHitPointCoordinate.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35AD011E3B77D600A02D78 /* XCUIElementHitPointCoordinate.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE35AD731E3B77D600A02D78 /* XCUIElementQuery.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35AD021E3B77D600A02D78 /* XCUIElementQuery.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE35AD751E3B77D600A02D78 /* XCUIRecorderNodeFinder.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35AD041E3B77D600A02D78 /* XCUIRecorderNodeFinder.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE35AD761E3B77D600A02D78 /* XCUIRecorderNodeFinderMatch.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35AD051E3B77D600A02D78 /* XCUIRecorderNodeFinderMatch.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE35AD771E3B77D600A02D78 /* XCUIRecorderTimingMessage.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35AD061E3B77D600A02D78 /* XCUIRecorderTimingMessage.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE35AD781E3B77D600A02D78 /* XCUIRecorderUtilities.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35AD071E3B77D600A02D78 /* XCUIRecorderUtilities.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE35AD7B1E3B80C000A02D78 /* FBXCTestDaemonsProxy.h in Headers */ = {isa = PBXBuildFile; fileRef = EE35AD791E3B80C000A02D78 /* FBXCTestDaemonsProxy.h */; };
		EE35AD7C1E3B80C000A02D78 /* FBXCTestDaemonsProxy.m in Sources */ = {isa = PBXBuildFile; fileRef = EE35AD7A1E3B80C000A02D78 /* FBXCTestDaemonsProxy.m */; };
		EE3A18621CDE618F00DE4205 /* FBErrorBuilder.h in Headers */ = {isa = PBXBuildFile; fileRef = EE3A18601CDE618F00DE4205 /* FBErrorBuilder.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE3A18631CDE618F00DE4205 /* FBErrorBuilder.m in Sources */ = {isa = PBXBuildFile; fileRef = EE3A18611CDE618F00DE4205 /* FBErrorBuilder.m */; };
		EE3A18661CDE734B00DE4205 /* FBKeyboard.h in Headers */ = {isa = PBXBuildFile; fileRef = EE3A18641CDE734B00DE4205 /* FBKeyboard.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE3A18671CDE734B00DE4205 /* FBKeyboard.m in Sources */ = {isa = PBXBuildFile; fileRef = EE3A18651CDE734B00DE4205 /* FBKeyboard.m */; };
		EE3F8CFE1D08AA17006F02CE /* FBRunLoopSpinnerTests.m in Sources */ = {isa = PBXBuildFile; fileRef = EE3F8CFD1D08AA17006F02CE /* FBRunLoopSpinnerTests.m */; };
		EE3F8D001D08B05F006F02CE /* FBElementTypeTransformerTests.m in Sources */ = {isa = PBXBuildFile; fileRef = EE3F8CFF1D08B05F006F02CE /* FBElementTypeTransformerTests.m */; };
		EE5095E51EBCC9090028E2FE /* FBTypingTest.m in Sources */ = {isa = PBXBuildFile; fileRef = AD76723F1D6B826F00610457 /* FBTypingTest.m */; };
		EE5095EB1EBCC9090028E2FE /* XCElementSnapshotHitPointTests.m in Sources */ = {isa = PBXBuildFile; fileRef = EE006EB21EBA1C7B006900A4 /* XCElementSnapshotHitPointTests.m */; };
		EE5095EC1EBCC9090028E2FE /* XCUIApplicationHelperTests.m in Sources */ = {isa = PBXBuildFile; fileRef = EE1E06E31D18213F007CF043 /* XCUIApplicationHelperTests.m */; };
		EE5095ED1EBCC9090028E2FE /* XCElementSnapshotHelperTests.m in Sources */ = {isa = PBXBuildFile; fileRef = EEBBDB9A1D1032F0000738CD /* XCElementSnapshotHelperTests.m */; };
		EE5095EE1EBCC9090028E2FE /* FBXPathIntegrationTests.m in Sources */ = {isa = PBXBuildFile; fileRef = 714CA3C61DC23186000F12C9 /* FBXPathIntegrationTests.m */; };
		EE5095EF1EBCC9090028E2FE /* XCUIElementHelperIntegrationTests.m in Sources */ = {isa = PBXBuildFile; fileRef = EE1E06E11D181CC9007CF043 /* XCUIElementHelperIntegrationTests.m */; };
		EE5095F01EBCC9090028E2FE /* XCUIDeviceHelperTests.m in Sources */ = {isa = PBXBuildFile; fileRef = EE1E06DF1D181BB4007CF043 /* XCUIDeviceHelperTests.m */; };
		EE5095F11EBCC9090028E2FE /* XCUIElementFBFindTests.m in Sources */ = {isa = PBXBuildFile; fileRef = EEBBD48D1D4785FC00656A81 /* XCUIElementFBFindTests.m */; };
		EE5095F21EBCC9090028E2FE /* XCUIDeviceRotationTests.m in Sources */ = {isa = PBXBuildFile; fileRef = 44757A831D42CE8300ECF35E /* XCUIDeviceRotationTests.m */; };
		EE5095F41EBCC9090028E2FE /* XCUIDeviceHealthCheckTests.m in Sources */ = {isa = PBXBuildFile; fileRef = EEDFE1231D9C08C700E6FFE5 /* XCUIDeviceHealthCheckTests.m */; };
		EE5095F51EBCC9090028E2FE /* XCUIElementAttributesTests.m in Sources */ = {isa = PBXBuildFile; fileRef = 71E504941DF59BAD0020C32A /* XCUIElementAttributesTests.m */; };
		EE5095F91EBCC9090028E2FE /* WebDriverAgentLib.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = EE158A991CBD452B00A3E3F0 /* WebDriverAgentLib.framework */; };
		EE5096021EBCD0250028E2FE /* FBIntegrationTestCase.m in Sources */ = {isa = PBXBuildFile; fileRef = EE1E06D91D1808C2007CF043 /* FBIntegrationTestCase.m */; };
		EE55B3251D1D5388003AAAEC /* FBTableDataSource.m in Sources */ = {isa = PBXBuildFile; fileRef = EE55B3231D1D5388003AAAEC /* FBTableDataSource.m */; };
		EE55B3271D1D54CF003AAAEC /* FBScrollingTests.m in Sources */ = {isa = PBXBuildFile; fileRef = EE55B3261D1D54CF003AAAEC /* FBScrollingTests.m */; };
		EE5A24421F136D360078B1D9 /* FBXCodeCompatibility.m in Sources */ = {isa = PBXBuildFile; fileRef = EE5A24411F136C8D0078B1D9 /* FBXCodeCompatibility.m */; };
		EE6A89261D0B19E60083E92B /* FBSessionTests.m in Sources */ = {isa = PBXBuildFile; fileRef = EE6A89251D0B19E60083E92B /* FBSessionTests.m */; };
		EE6A892B1D0B25820083E92B /* XCUIApplicationDouble.m in Sources */ = {isa = PBXBuildFile; fileRef = EE6A89281D0B257B0083E92B /* XCUIApplicationDouble.m */; };
		EE6A892D1D0B2AF40083E92B /* FBErrorBuilderTests.m in Sources */ = {isa = PBXBuildFile; fileRef = EE6A892C1D0B2AF40083E92B /* FBErrorBuilderTests.m */; };
		EE6A89371D0B35920083E92B /* FBFailureProofTestCaseTests.m in Sources */ = {isa = PBXBuildFile; fileRef = EE6A89361D0B35920083E92B /* FBFailureProofTestCaseTests.m */; };
		EE6A893A1D0B38640083E92B /* FBFailureProofTestCase.h in Headers */ = {isa = PBXBuildFile; fileRef = EE6A89381D0B38640083E92B /* FBFailureProofTestCase.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE6A893B1D0B38640083E92B /* FBFailureProofTestCase.m in Sources */ = {isa = PBXBuildFile; fileRef = EE6A89391D0B38640083E92B /* FBFailureProofTestCase.m */; };
		EE6B64FD1D0F86EF00E85F5D /* XCTestPrivateSymbols.h in Headers */ = {isa = PBXBuildFile; fileRef = EE6B64FB1D0F86EF00E85F5D /* XCTestPrivateSymbols.h */; };
		EE6B64FE1D0F86EF00E85F5D /* XCTestPrivateSymbols.m in Sources */ = {isa = PBXBuildFile; fileRef = EE6B64FC1D0F86EF00E85F5D /* XCTestPrivateSymbols.m */; };
		EE7E271C1D06C69F001BEC7B /* FBDebugLogDelegateDecorator.h in Headers */ = {isa = PBXBuildFile; fileRef = EE7E27181D06C69F001BEC7B /* FBDebugLogDelegateDecorator.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE7E271D1D06C69F001BEC7B /* FBDebugLogDelegateDecorator.m in Sources */ = {isa = PBXBuildFile; fileRef = EE7E27191D06C69F001BEC7B /* FBDebugLogDelegateDecorator.m */; };
		EE8BA97A1DCCED9A00A9DEF8 /* FBNavigationController.m in Sources */ = {isa = PBXBuildFile; fileRef = EE8BA9791DCCED9A00A9DEF8 /* FBNavigationController.m */; };
		EE8DDD7E20C5733C004D4925 /* XCUIElement+FBForceTouch.m in Sources */ = {isa = PBXBuildFile; fileRef = EE8DDD7C20C5733B004D4925 /* XCUIElement+FBForceTouch.m */; };
		EE8DDD7F20C5733C004D4925 /* XCUIElement+FBForceTouch.h in Headers */ = {isa = PBXBuildFile; fileRef = EE8DDD7D20C5733C004D4925 /* XCUIElement+FBForceTouch.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE9AB8011CAEE048008C271F /* UITestingUITests.m in Sources */ = {isa = PBXBuildFile; fileRef = EE9AB7FD1CAEE048008C271F /* UITestingUITests.m */; };
		EE9B76591CF7987800275851 /* FBRouteTests.m in Sources */ = {isa = PBXBuildFile; fileRef = EE9B76571CF7987300275851 /* FBRouteTests.m */; };
		EE9B768E1CF7997600275851 /* AppDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = EE9B76831CF7997600275851 /* AppDelegate.m */; };
		EE9B768F1CF7997600275851 /* ViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = EE9B76851CF7997600275851 /* ViewController.m */; };
		EE9B76911CF7997600275851 /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = EE9B76871CF7997600275851 /* main.m */; };
		EE9B76941CF7997600275851 /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = EE9B768C1CF7997600275851 /* Main.storyboard */; };
		EE9B769A1CF799F400275851 /* FBAlertTests.m in Sources */ = {isa = PBXBuildFile; fileRef = EE9B76991CF799F400275851 /* FBAlertTests.m */; };
		EE9B76A01CF79C0F00275851 /* WebDriverAgentLib.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = EE158A991CBD452B00A3E3F0 /* WebDriverAgentLib.framework */; };
		EE9B76A61CF7A43900275851 /* FBConfiguration.h in Headers */ = {isa = PBXBuildFile; fileRef = EE9B76A11CF7A43900275851 /* FBConfiguration.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE9B76A71CF7A43900275851 /* FBConfiguration.m in Sources */ = {isa = PBXBuildFile; fileRef = EE9B76A21CF7A43900275851 /* FBConfiguration.m */; };
		EE9B76A81CF7A43900275851 /* FBLogger.h in Headers */ = {isa = PBXBuildFile; fileRef = EE9B76A31CF7A43900275851 /* FBLogger.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EE9B76A91CF7A43900275851 /* FBLogger.m in Sources */ = {isa = PBXBuildFile; fileRef = EE9B76A41CF7A43900275851 /* FBLogger.m */; };
		EE9B76AA1CF7A43900275851 /* FBMacros.h in Headers */ = {isa = PBXBuildFile; fileRef = EE9B76A51CF7A43900275851 /* FBMacros.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EEBBD48B1D47746D00656A81 /* XCUIElement+FBFind.h in Headers */ = {isa = PBXBuildFile; fileRef = EEBBD4891D47746D00656A81 /* XCUIElement+FBFind.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EEBBD48C1D47746D00656A81 /* XCUIElement+FBFind.m in Sources */ = {isa = PBXBuildFile; fileRef = EEBBD48A1D47746D00656A81 /* XCUIElement+FBFind.m */; };
		EEDFE1211D9C06F800E6FFE5 /* XCUIDevice+FBHealthCheck.h in Headers */ = {isa = PBXBuildFile; fileRef = EEDFE11F1D9C06F800E6FFE5 /* XCUIDevice+FBHealthCheck.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EEDFE1221D9C06F800E6FFE5 /* XCUIDevice+FBHealthCheck.m in Sources */ = {isa = PBXBuildFile; fileRef = EEDFE1201D9C06F800E6FFE5 /* XCUIDevice+FBHealthCheck.m */; };
		EEE16E971D33A25500172525 /* FBConfigurationTests.m in Sources */ = {isa = PBXBuildFile; fileRef = EEE16E961D33A25500172525 /* FBConfigurationTests.m */; };
		EEE376431D59F81400ED88DD /* XCUIDevice+FBRotation.h in Headers */ = {isa = PBXBuildFile; fileRef = EEE3763D1D59F81400ED88DD /* XCUIDevice+FBRotation.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EEE376441D59F81400ED88DD /* XCUIDevice+FBRotation.m in Sources */ = {isa = PBXBuildFile; fileRef = EEE3763E1D59F81400ED88DD /* XCUIDevice+FBRotation.m */; };
		EEE376451D59F81400ED88DD /* XCUIElement+FBUtilities.h in Headers */ = {isa = PBXBuildFile; fileRef = EEE3763F1D59F81400ED88DD /* XCUIElement+FBUtilities.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EEE376461D59F81400ED88DD /* XCUIElement+FBUtilities.m in Sources */ = {isa = PBXBuildFile; fileRef = EEE376401D59F81400ED88DD /* XCUIElement+FBUtilities.m */; };
		EEE376491D59FAE900ED88DD /* XCUIElement+FBWebDriverAttributes.h in Headers */ = {isa = PBXBuildFile; fileRef = EEE376471D59FAE900ED88DD /* XCUIElement+FBWebDriverAttributes.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EEE3764A1D59FAE900ED88DD /* XCUIElement+FBWebDriverAttributes.m in Sources */ = {isa = PBXBuildFile; fileRef = EEE376481D59FAE900ED88DD /* XCUIElement+FBWebDriverAttributes.m */; };
		EEE9B4721CD02B88009D2030 /* FBRunLoopSpinner.h in Headers */ = {isa = PBXBuildFile; fileRef = EEE9B4701CD02B88009D2030 /* FBRunLoopSpinner.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EEE9B4731CD02B88009D2030 /* FBRunLoopSpinner.m in Sources */ = {isa = PBXBuildFile; fileRef = EEE9B4711CD02B88009D2030 /* FBRunLoopSpinner.m */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		641EE6FA2240C5F400173FCB /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 91F9DAE11B99DBC2001349B2 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 641EE5D52240C5CA00173FCB;
			remoteInfo = WebDriverAgentLib_tvOS;
		};
		64B264FF228C50E0002A5025 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 91F9DAE11B99DBC2001349B2 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 641EE5D52240C5CA00173FCB;
			remoteInfo = WebDriverAgentLib_tvOS;
		};
		AD8D96F01D3C12960061268E /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 91F9DAE11B99DBC2001349B2 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = EE158A981CBD452B00A3E3F0;
			remoteInfo = WebDriverAgentLib;
		};
		EE158B5B1CBD462500A3E3F0 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 91F9DAE11B99DBC2001349B2 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = EE158A981CBD452B00A3E3F0;
			remoteInfo = WebDriverAgentLib;
		};
		EE2202051ECC612200A29571 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 91F9DAE11B99DBC2001349B2 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = EE158A981CBD452B00A3E3F0;
			remoteInfo = WebDriverAgentLib;
		};
		EE2202071ECC612200A29571 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 91F9DAE11B99DBC2001349B2 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = EE9B75D31CF7956C00275851;
			remoteInfo = Eval;
		};
		EE5095DF1EBCC9090028E2FE /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 91F9DAE11B99DBC2001349B2 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = EE158A981CBD452B00A3E3F0;
			remoteInfo = WebDriverAgentLib;
		};
		EE5095E11EBCC9090028E2FE /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 91F9DAE11B99DBC2001349B2 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = EE9B75D31CF7956C00275851;
			remoteInfo = Eval;
		};
		EE9B75ED1CF7956C00275851 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 91F9DAE11B99DBC2001349B2 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = EE9B75D31CF7956C00275851;
			remoteInfo = Eval;
		};
		EE9B769E1CF79C0A00275851 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 91F9DAE11B99DBC2001349B2 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = EE158A981CBD452B00A3E3F0;
			remoteInfo = WebDriverAgentLib;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		641EE3472240C1EF00173FCB /* Copy frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = **********;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				641EE6FD2240C61D00173FCB /* WebDriverAgentLib_tvOS.framework in Copy frameworks */,
			);
			name = "Copy frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
		EE93CFF41CCA501300708122 /* Copy frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = **********;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				AD35D06C1CF1C35500870A75 /* WebDriverAgentLib.framework in Copy frameworks */,
			);
			name = "Copy frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		0E0413372DF1E15100AF007C /* XCUIElement+FBMinMax.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "XCUIElement+FBMinMax.m"; sourceTree = "<group>"; };
		0E04133A2DF1E15900AF007C /* XCUIElement+FBMinMax.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "XCUIElement+FBMinMax.h"; sourceTree = "<group>"; };
		1357E295233D05240054BDB8 /* XCUIHitPointResult.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XCUIHitPointResult.h; sourceTree = "<group>"; };
		13815F6D2328D20400CDAB61 /* FBActiveAppDetectionPoint.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FBActiveAppDetectionPoint.h; sourceTree = "<group>"; };
		13815F6E2328D20400CDAB61 /* FBActiveAppDetectionPoint.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FBActiveAppDetectionPoint.m; sourceTree = "<group>"; };
		13DE7A41287C2A8D003243C6 /* FBXCAccessibilityElement.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FBXCAccessibilityElement.h; sourceTree = "<group>"; };
		13DE7A42287C2A8D003243C6 /* FBXCAccessibilityElement.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FBXCAccessibilityElement.m; sourceTree = "<group>"; };
		13DE7A47287C4005003243C6 /* FBXCDeviceEvent.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FBXCDeviceEvent.h; sourceTree = "<group>"; };
		13DE7A48287C4005003243C6 /* FBXCDeviceEvent.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FBXCDeviceEvent.m; sourceTree = "<group>"; };
		13DE7A4D287C46BB003243C6 /* FBXCElementSnapshot.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FBXCElementSnapshot.h; sourceTree = "<group>"; };
		13DE7A4E287C46BB003243C6 /* FBXCElementSnapshot.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FBXCElementSnapshot.m; sourceTree = "<group>"; };
		13DE7A53287CA1EC003243C6 /* FBXCElementSnapshotWrapper.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FBXCElementSnapshotWrapper.h; sourceTree = "<group>"; };
		13DE7A54287CA1EC003243C6 /* FBXCElementSnapshotWrapper.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FBXCElementSnapshotWrapper.m; sourceTree = "<group>"; };
		13DE7A59287CA444003243C6 /* FBXCElementSnapshotWrapper+Helpers.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "FBXCElementSnapshotWrapper+Helpers.h"; sourceTree = "<group>"; };
		13DE7A5A287CA444003243C6 /* FBXCElementSnapshotWrapper+Helpers.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "FBXCElementSnapshotWrapper+Helpers.m"; sourceTree = "<group>"; };
		13FFF2F0287DBEE600E561E4 /* XCElementSnapshotDouble.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XCElementSnapshotDouble.h; sourceTree = "<group>"; };
		13FFF2F1287DBEE600E561E4 /* XCElementSnapshotDouble.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XCElementSnapshotDouble.m; sourceTree = "<group>"; };
		1BA7DD8C206D694B007C7C26 /* XCTElementSetTransformer-Protocol.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "XCTElementSetTransformer-Protocol.h"; sourceTree = "<group>"; };
		315A14FF2518CB8700A3A064 /* TouchableView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TouchableView.h; sourceTree = "<group>"; };
		315A15002518CB8700A3A064 /* TouchableView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TouchableView.m; sourceTree = "<group>"; };
		315A15052518CC2800A3A064 /* TouchSpotView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TouchSpotView.h; sourceTree = "<group>"; };
		315A15062518CC2800A3A064 /* TouchSpotView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TouchSpotView.m; sourceTree = "<group>"; };
		315A15082518D6F400A3A064 /* TouchViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TouchViewController.h; sourceTree = "<group>"; };
		315A15092518D6F400A3A064 /* TouchViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TouchViewController.m; sourceTree = "<group>"; };
		44757A831D42CE8300ECF35E /* XCUIDeviceRotationTests.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = XCUIDeviceRotationTests.m; sourceTree = "<group>"; };
		631B523421F6174300625362 /* FBImageProcessorTests.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FBImageProcessorTests.m; sourceTree = "<group>"; };
		633E904A220DEE7F007CADF9 /* XCUIApplicationProcessDelay.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XCUIApplicationProcessDelay.h; sourceTree = "<group>"; };
		6385F4A5220A40760095BBDB /* XCUIApplicationProcessDelay.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XCUIApplicationProcessDelay.m; sourceTree = "<group>"; };
		63CCF91021ECE4C700E94ABD /* FBImageProcessor.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FBImageProcessor.h; sourceTree = "<group>"; };
		63CCF91121ECE4C700E94ABD /* FBImageProcessor.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FBImageProcessor.m; sourceTree = "<group>"; };
		641EE2DA2240BBE300173FCB /* WebDriverAgentRunner_tvOS.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = WebDriverAgentRunner_tvOS.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		641EE6F82240C5CA00173FCB /* WebDriverAgentLib_tvOS.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = WebDriverAgentLib_tvOS.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		641EE7042240CDCF00173FCB /* XCUIElement+FBTVFocuse.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "XCUIElement+FBTVFocuse.h"; sourceTree = "<group>"; };
		641EE7072240CDEB00173FCB /* XCUIElement+FBTVFocuse.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "XCUIElement+FBTVFocuse.m"; sourceTree = "<group>"; };
		641EE70A2240CE2D00173FCB /* FBTVNavigationTracker.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FBTVNavigationTracker.h; sourceTree = "<group>"; };
		641EE70D2240CE4800173FCB /* FBTVNavigationTracker.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FBTVNavigationTracker.m; sourceTree = "<group>"; };
		644D9CCD230E1F1A00C90459 /* FBConfigurationTests.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FBConfigurationTests.m; sourceTree = "<group>"; };
		648C10AA22AAAD9C00B81B9A /* UIKeyboardImpl.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UIKeyboardImpl.h; sourceTree = "<group>"; };
		648C10AE22AAAE4000B81B9A /* TIPreferencesController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TIPreferencesController.h; sourceTree = "<group>"; };
		6496A5D8230D6EB30087F8CB /* AXSettings.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AXSettings.h; sourceTree = "<group>"; };
		64B264EB228C4D54002A5025 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		64B264F3228C5098002A5025 /* FBTVNavigationTrackerTests.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FBTVNavigationTrackerTests.m; sourceTree = "<group>"; };
		64B264F9228C50E0002A5025 /* UnitTests_tvOS.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = UnitTests_tvOS.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		64B26506228C54F2002A5025 /* XCUIElementDouble.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XCUIElementDouble.h; sourceTree = "<group>"; };
		64B26507228C5514002A5025 /* XCUIElementDouble.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XCUIElementDouble.m; sourceTree = "<group>"; };
		64B26509228CE4FF002A5025 /* FBTVNavigationTracker-Private.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "FBTVNavigationTracker-Private.h"; sourceTree = "<group>"; };
		711084421DA3AA7500F913D6 /* FBXPath.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = FBXPath.h; sourceTree = "<group>"; };
		711084431DA3AA7500F913D6 /* FBXPath.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FBXPath.m; sourceTree = "<group>"; };
		7119097B2152580600BA3C7E /* XCUIScreen.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = XCUIScreen.h; sourceTree = "<group>"; };
		7119E1EB1E891F8600D0B125 /* FBPickerWheelSelectTests.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FBPickerWheelSelectTests.m; sourceTree = "<group>"; };
		711CD03325ED1106001C01D2 /* XCUIScreenDataSource-Protocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "XCUIScreenDataSource-Protocol.h"; sourceTree = "<group>"; };
		71241D791FAE3D2500B9559F /* FBTouchActionCommands.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FBTouchActionCommands.h; sourceTree = "<group>"; };
		71241D7A1FAE3D2500B9559F /* FBTouchActionCommands.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FBTouchActionCommands.m; sourceTree = "<group>"; };
		71241D7D1FAF084E00B9559F /* FBW3CTouchActionsIntegrationTests.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FBW3CTouchActionsIntegrationTests.m; sourceTree = "<group>"; };
		71241D7F1FAF087500B9559F /* FBW3CMultiTouchActionsIntegrationTests.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FBW3CMultiTouchActionsIntegrationTests.m; sourceTree = "<group>"; };
		712A0C841DA3E459007D02E5 /* FBXPathTests.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FBXPathTests.m; sourceTree = "<group>"; };
		712A0C861DA3E55D007D02E5 /* FBXPath-Private.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "FBXPath-Private.h"; sourceTree = "<group>"; };
		713352FC26CEF31D00523CBC /* FBLRUCacheTests.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FBLRUCacheTests.m; sourceTree = "<group>"; };
		7136A4771E8918E60024FC3D /* XCUIElement+FBPickerWheel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "XCUIElement+FBPickerWheel.h"; sourceTree = "<group>"; };
		7136A4781E8918E60024FC3D /* XCUIElement+FBPickerWheel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "XCUIElement+FBPickerWheel.m"; sourceTree = "<group>"; };
		7136C0F8243A182400921C76 /* FBW3CTypeActionsTests.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FBW3CTypeActionsTests.m; sourceTree = "<group>"; };
		713914591DF01989005896C2 /* XCUIElementHelpersTests.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = XCUIElementHelpersTests.m; sourceTree = "<group>"; };
		7139145B1DF01A12005896C2 /* NSExpressionFBFormatTests.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = NSExpressionFBFormatTests.m; sourceTree = "<group>"; };
		713AE573243A53BE0000D657 /* FBW3CActionsHelpers.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FBW3CActionsHelpers.h; sourceTree = "<group>"; };
		713AE574243A53BE0000D657 /* FBW3CActionsHelpers.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FBW3CActionsHelpers.m; sourceTree = "<group>"; };
		713C6DCD1DDC772A00285B92 /* FBElementUtils.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = FBElementUtils.h; sourceTree = "<group>"; };
		713C6DCE1DDC772A00285B92 /* FBElementUtils.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FBElementUtils.m; sourceTree = "<group>"; };
		714097411FAE1B0B008FB2C5 /* FBBaseActionsSynthesizer.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FBBaseActionsSynthesizer.h; sourceTree = "<group>"; };
		714097491FAE1B51008FB2C5 /* FBW3CActionsSynthesizer.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FBW3CActionsSynthesizer.h; sourceTree = "<group>"; };
		7140974A1FAE1B51008FB2C5 /* FBW3CActionsSynthesizer.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FBW3CActionsSynthesizer.m; sourceTree = "<group>"; };
		7140974D1FAE20EE008FB2C5 /* FBBaseActionsSynthesizer.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FBBaseActionsSynthesizer.m; sourceTree = "<group>"; };
		71414ED02670A1ED003A8C5D /* LRUCache.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = LRUCache.h; sourceTree = "<group>"; };
		71414ED12670A1ED003A8C5D /* LRUCacheNode.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = LRUCacheNode.h; sourceTree = "<group>"; };
		71414ED22670A1ED003A8C5D /* LRUCache.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = LRUCache.m; sourceTree = "<group>"; };
		71414ED32670A1ED003A8C5D /* LRUCacheNode.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = LRUCacheNode.m; sourceTree = "<group>"; };
		714801D01FA9D9FA00DC5997 /* FBSDKVersionTests.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FBSDKVersionTests.m; sourceTree = "<group>"; };
		714CA3C61DC23186000F12C9 /* FBXPathIntegrationTests.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FBXPathIntegrationTests.m; sourceTree = "<group>"; };
		714D88CA2733FB970074A925 /* FBXMLGenerationOptions.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FBXMLGenerationOptions.h; sourceTree = "<group>"; };
		714D88CB2733FB970074A925 /* FBXMLGenerationOptions.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FBXMLGenerationOptions.m; sourceTree = "<group>"; };
		714E14B629805CAE00375DD7 /* XCAXClient_iOS+FBSnapshotReqParams.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "XCAXClient_iOS+FBSnapshotReqParams.h"; sourceTree = "<group>"; };
		714E14B729805CAE00375DD7 /* XCAXClient_iOS+FBSnapshotReqParams.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "XCAXClient_iOS+FBSnapshotReqParams.m"; sourceTree = "<group>"; };
		714EAA0B2673FDFE005C5B47 /* FBCapabilities.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FBCapabilities.h; sourceTree = "<group>"; };
		714EAA0C2673FDFE005C5B47 /* FBCapabilities.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FBCapabilities.m; sourceTree = "<group>"; };
		7150348521A6DAD600A0F4BA /* FBImageUtils.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FBImageUtils.h; sourceTree = "<group>"; };
		7150348621A6DAD600A0F4BA /* FBImageUtils.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FBImageUtils.m; sourceTree = "<group>"; };
		7152EB2F1F41F9960047EEFF /* FBSessionIntegrationTests.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FBSessionIntegrationTests.m; sourceTree = "<group>"; };
		715557D1211DBCE700613B26 /* FBTCPSocket.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FBTCPSocket.h; sourceTree = "<group>"; };
		715557D2211DBCE700613B26 /* FBTCPSocket.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FBTCPSocket.m; sourceTree = "<group>"; };
		71555A3B1DEC460A007D4A8B /* NSExpression+FBFormat.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSExpression+FBFormat.h"; sourceTree = "<group>"; };
		71555A3C1DEC460A007D4A8B /* NSExpression+FBFormat.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "NSExpression+FBFormat.m"; sourceTree = "<group>"; };
		7155B419224D5B460042A993 /* libxml2.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libxml2.tbd; path = Platforms/AppleTVOS.platform/Developer/SDKs/AppleTVOS12.2.sdk/usr/lib/libxml2.tbd; sourceTree = DEVELOPER_DIR; };
		7155B41A224D5B480042A993 /* libAccessibility.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libAccessibility.tbd; path = Platforms/AppleTVOS.platform/Developer/SDKs/AppleTVOS12.2.sdk/usr/lib/libAccessibility.tbd; sourceTree = DEVELOPER_DIR; };
		7155B423224D5B980042A993 /* XCTest.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = XCTest.framework; path = Platforms/iPhoneOS.platform/Developer/Library/Frameworks/XCTest.framework; sourceTree = DEVELOPER_DIR; };
		7155B425224D5C130042A993 /* XCTAutomationSupport.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = XCTAutomationSupport.framework; path = Platforms/AppleTVOS.platform/Developer/Library/PrivateFrameworks/XCTAutomationSupport.framework; sourceTree = DEVELOPER_DIR; };
		7155D701211DCEF400166C20 /* FBMjpegServer.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FBMjpegServer.h; sourceTree = "<group>"; };
		7155D702211DCEF400166C20 /* FBMjpegServer.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FBMjpegServer.m; sourceTree = "<group>"; };
		7157B28F221DADD2001C348C /* FBXCAXClientProxy.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FBXCAXClientProxy.h; sourceTree = "<group>"; };
		7157B290221DADD2001C348C /* FBXCAXClientProxy.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FBXCAXClientProxy.m; sourceTree = "<group>"; };
		715A84CD2DD92AD3007134CC /* FBElementHelpers.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FBElementHelpers.h; sourceTree = "<group>"; };
		715A84CE2DD92AD3007134CC /* FBElementHelpers.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FBElementHelpers.m; sourceTree = "<group>"; };
		715AFABF1FFA29180053896D /* FBScreen.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FBScreen.h; sourceTree = "<group>"; };
		715AFAC01FFA29180053896D /* FBScreen.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FBScreen.m; sourceTree = "<group>"; };
		715AFAC31FFA2AAF0053896D /* FBScreenTests.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FBScreenTests.m; sourceTree = "<group>"; };
		715D554A2229891B00524509 /* FBExceptionHandlerTests.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FBExceptionHandlerTests.m; sourceTree = "<group>"; };
		71649EC82518C19C0087F212 /* IOSTestSettings.xcconfig */ = {isa = PBXFileReference; lastKnownFileType = text.xcconfig; path = IOSTestSettings.xcconfig; sourceTree = "<group>"; };
		716C9342224D53A1004B8542 /* XCTest.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = XCTest.framework; path = Platforms/AppleTVOS.platform/Developer/Library/Frameworks/XCTest.framework; sourceTree = DEVELOPER_DIR; };
		716C9343224D53DF004B8542 /* libAccessibility.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libAccessibility.tbd; path = usr/lib/libAccessibility.tbd; sourceTree = SDKROOT; };
		716C9344224D53FC004B8542 /* XCTAutomationSupport.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = XCTAutomationSupport.framework; path = Platforms/iPhoneOS.platform/Developer/Library/PrivateFrameworks/XCTAutomationSupport.framework; sourceTree = DEVELOPER_DIR; };
		716C9345224D540C004B8542 /* libxml2.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libxml2.tbd; path = usr/lib/libxml2.tbd; sourceTree = SDKROOT; };
		716C9DF827315D21005AD475 /* FBReflectionUtils.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FBReflectionUtils.h; sourceTree = "<group>"; };
		716C9DF927315D21005AD475 /* FBReflectionUtils.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FBReflectionUtils.m; sourceTree = "<group>"; };
		716C9DFE27315EFF005AD475 /* XCUIApplication+FBUIInterruptions.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "XCUIApplication+FBUIInterruptions.h"; sourceTree = "<group>"; };
		716C9DFF27315EFF005AD475 /* XCUIApplication+FBUIInterruptions.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "XCUIApplication+FBUIInterruptions.m"; sourceTree = "<group>"; };
		716E0BCC1E917E810087A825 /* NSString+FBXMLSafeString.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSString+FBXMLSafeString.h"; sourceTree = "<group>"; };
		716E0BCD1E917E810087A825 /* NSString+FBXMLSafeString.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "NSString+FBXMLSafeString.m"; sourceTree = "<group>"; };
		716E0BD01E917F260087A825 /* FBXMLSafeStringTests.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FBXMLSafeStringTests.m; sourceTree = "<group>"; };
		716F0D9F2A16CA1000CDD977 /* NSDictionary+FBUtf8SafeDictionary.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "NSDictionary+FBUtf8SafeDictionary.h"; sourceTree = "<group>"; };
		716F0DA02A16CA1000CDD977 /* NSDictionary+FBUtf8SafeDictionary.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "NSDictionary+FBUtf8SafeDictionary.m"; sourceTree = "<group>"; };
		716F0DA52A17323300CDD977 /* NSDictionaryFBUtf8SafeTests.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = NSDictionaryFBUtf8SafeTests.m; sourceTree = "<group>"; };
		717C0D702518ED2800CAA6EC /* TVOSSettings.xcconfig */ = {isa = PBXFileReference; lastKnownFileType = text.xcconfig; path = TVOSSettings.xcconfig; sourceTree = "<group>"; };
		717C0D862518ED7000CAA6EC /* TVOSTestSettings.xcconfig */ = {isa = PBXFileReference; lastKnownFileType = text.xcconfig; path = TVOSTestSettings.xcconfig; sourceTree = "<group>"; };
		718226C62587443600661B83 /* GCDAsyncUdpSocket.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = GCDAsyncUdpSocket.h; path = WebDriverAgentLib/Vendor/CocoaAsyncSocket/GCDAsyncUdpSocket.h; sourceTree = SOURCE_ROOT; };
		718226C72587443600661B83 /* GCDAsyncSocket.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = GCDAsyncSocket.h; path = WebDriverAgentLib/Vendor/CocoaAsyncSocket/GCDAsyncSocket.h; sourceTree = SOURCE_ROOT; };
		718226C82587443600661B83 /* GCDAsyncSocket.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = GCDAsyncSocket.m; path = WebDriverAgentLib/Vendor/CocoaAsyncSocket/GCDAsyncSocket.m; sourceTree = SOURCE_ROOT; };
		718226C92587443600661B83 /* GCDAsyncUdpSocket.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = GCDAsyncUdpSocket.m; path = WebDriverAgentLib/Vendor/CocoaAsyncSocket/GCDAsyncUdpSocket.m; sourceTree = SOURCE_ROOT; };
		718F49C7230844330045FE8B /* FBProtocolHelpersTests.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FBProtocolHelpersTests.m; sourceTree = "<group>"; };
		71930C4020662E1F00D3AFEC /* FBPasteboard.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FBPasteboard.h; sourceTree = "<group>"; };
		71930C4120662E1F00D3AFEC /* FBPasteboard.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FBPasteboard.m; sourceTree = "<group>"; };
		71930C462066434000D3AFEC /* FBPasteboardTests.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FBPasteboardTests.m; sourceTree = "<group>"; };
		719CD8F62126C78F00C7D0C2 /* FBAlertsMonitor.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FBAlertsMonitor.h; sourceTree = "<group>"; };
		719CD8F72126C78F00C7D0C2 /* FBAlertsMonitor.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FBAlertsMonitor.m; sourceTree = "<group>"; };
		719CD8FA2126C88B00C7D0C2 /* XCUIApplication+FBAlert.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "XCUIApplication+FBAlert.h"; sourceTree = "<group>"; };
		719CD8FB2126C88B00C7D0C2 /* XCUIApplication+FBAlert.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "XCUIApplication+FBAlert.m"; sourceTree = "<group>"; };
		719CD8FE2126C90200C7D0C2 /* FBAutoAlertsHandlerTests.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FBAutoAlertsHandlerTests.m; sourceTree = "<group>"; };
		719DCF132601EAFB000E765F /* FBNotificationsHelper.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FBNotificationsHelper.h; sourceTree = "<group>"; };
		719DCF142601EAFB000E765F /* FBNotificationsHelper.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FBNotificationsHelper.m; sourceTree = "<group>"; };
		719FF5B81DAD21F5008E0099 /* FBElementUtilitiesTests.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FBElementUtilitiesTests.m; sourceTree = "<group>"; };
		71A224E31DE2F56600844D55 /* NSPredicate+FBFormat.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = "NSPredicate+FBFormat.h"; path = "../Utilities/NSPredicate+FBFormat.h"; sourceTree = "<group>"; };
		71A224E41DE2F56600844D55 /* NSPredicate+FBFormat.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = "NSPredicate+FBFormat.m"; path = "../Utilities/NSPredicate+FBFormat.m"; sourceTree = "<group>"; };
		71A224E71DE326C500844D55 /* NSPredicateFBFormatTests.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = NSPredicateFBFormatTests.m; sourceTree = "<group>"; };
		71A5C67129A4F39600421C37 /* XCTIssue+FBPatcher.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "XCTIssue+FBPatcher.h"; sourceTree = "<group>"; };
		71A5C67229A4F39600421C37 /* XCTIssue+FBPatcher.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "XCTIssue+FBPatcher.m"; sourceTree = "<group>"; };
		71A7EAF31E20516B001DA4F2 /* XCUIElement+FBClassChain.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "XCUIElement+FBClassChain.h"; sourceTree = "<group>"; };
		71A7EAF41E20516B001DA4F2 /* XCUIElement+FBClassChain.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "XCUIElement+FBClassChain.m"; sourceTree = "<group>"; };
		71A7EAF71E224648001DA4F2 /* FBClassChainQueryParser.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = FBClassChainQueryParser.h; sourceTree = "<group>"; };
		71A7EAF81E224648001DA4F2 /* FBClassChainQueryParser.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FBClassChainQueryParser.m; sourceTree = "<group>"; };
		71A7EAFB1E229302001DA4F2 /* FBClassChainTests.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FBClassChainTests.m; sourceTree = "<group>"; };
		71ACF5B7242F2FDC00F0AAD4 /* FBSafariAlertTests.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FBSafariAlertTests.m; sourceTree = "<group>"; };
		71AE3CF52D38EE8E0039FC36 /* XCUIElement+FBVisibleFrame.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "XCUIElement+FBVisibleFrame.h"; sourceTree = "<group>"; };
		71AE3CF62D38EE8E0039FC36 /* XCUIElement+FBVisibleFrame.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "XCUIElement+FBVisibleFrame.m"; sourceTree = "<group>"; };
		71B155D923070ECF00646AFB /* FBHTTPStatusCodes.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FBHTTPStatusCodes.h; sourceTree = "<group>"; };
		71B155DB230711E900646AFB /* FBCommandStatus.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FBCommandStatus.m; sourceTree = "<group>"; };
		71B155DD23080CA600646AFB /* FBProtocolHelpers.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FBProtocolHelpers.h; sourceTree = "<group>"; };
		71B155DE23080CA600646AFB /* FBProtocolHelpers.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FBProtocolHelpers.m; sourceTree = "<group>"; };
		71B49EC51ED1A58100D51AD6 /* XCUIElement+FBUID.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "XCUIElement+FBUID.h"; sourceTree = "<group>"; };
		71B49EC61ED1A58100D51AD6 /* XCUIElement+FBUID.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "XCUIElement+FBUID.m"; sourceTree = "<group>"; };
		71BB58DD2B9631B700CB9BFE /* FBVideoRecordingTests.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FBVideoRecordingTests.m; sourceTree = "<group>"; };
		71BB58DF2B9631F100CB9BFE /* FBScreenRecordingPromise.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FBScreenRecordingPromise.h; sourceTree = "<group>"; };
		71BB58E02B9631F100CB9BFE /* FBScreenRecordingPromise.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FBScreenRecordingPromise.m; sourceTree = "<group>"; };
		71BB58E62B96328700CB9BFE /* FBScreenRecordingRequest.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FBScreenRecordingRequest.h; sourceTree = "<group>"; };
		71BB58E72B96328700CB9BFE /* FBScreenRecordingRequest.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FBScreenRecordingRequest.m; sourceTree = "<group>"; };
		71BB58ED2B96511800CB9BFE /* FBVideoCommands.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FBVideoCommands.h; sourceTree = "<group>"; };
		71BB58EE2B96511800CB9BFE /* FBVideoCommands.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FBVideoCommands.m; sourceTree = "<group>"; };
		71BB58F42B96531900CB9BFE /* FBScreenRecordingContainer.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FBScreenRecordingContainer.h; sourceTree = "<group>"; };
		71BB58F52B96531900CB9BFE /* FBScreenRecordingContainer.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FBScreenRecordingContainer.m; sourceTree = "<group>"; };
		71BD20711F86116100B36EC2 /* XCUIApplication+FBTouchAction.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "XCUIApplication+FBTouchAction.h"; sourceTree = "<group>"; };
		71BD20721F86116100B36EC2 /* XCUIApplication+FBTouchAction.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "XCUIApplication+FBTouchAction.m"; sourceTree = "<group>"; };
		71C8E54F25399A6B008572C1 /* XCUIApplication+FBQuiescence.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "XCUIApplication+FBQuiescence.h"; sourceTree = "<group>"; };
		71C8E55025399A6B008572C1 /* XCUIApplication+FBQuiescence.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "XCUIApplication+FBQuiescence.m"; sourceTree = "<group>"; };
		71C9EAAA25E8415A00470CD8 /* FBScreenshot.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FBScreenshot.h; sourceTree = "<group>"; };
		71C9EAAB25E8415A00470CD8 /* FBScreenshot.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FBScreenshot.m; sourceTree = "<group>"; };
		71D04DC625356C43008A052C /* XCUIElement+FBCaching.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "XCUIElement+FBCaching.h"; sourceTree = "<group>"; };
		71D04DC725356C43008A052C /* XCUIElement+FBCaching.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "XCUIElement+FBCaching.m"; sourceTree = "<group>"; };
		71D3B3D3267FC7260076473D /* XCUIElement+FBResolve.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "XCUIElement+FBResolve.h"; sourceTree = "<group>"; };
		71D3B3D4267FC7260076473D /* XCUIElement+FBResolve.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "XCUIElement+FBResolve.m"; sourceTree = "<group>"; };
		71D475C02538F5A8008D9401 /* XCUIApplicationProcess+FBQuiescence.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "XCUIApplicationProcess+FBQuiescence.h"; sourceTree = "<group>"; };
		71D475C12538F5A8008D9401 /* XCUIApplicationProcess+FBQuiescence.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "XCUIApplicationProcess+FBQuiescence.m"; sourceTree = "<group>"; };
		71E504941DF59BAD0020C32A /* XCUIElementAttributesTests.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = XCUIElementAttributesTests.m; sourceTree = "<group>"; };
		71E75E6B254824230099FC87 /* XCUIElementQuery+FBHelpers.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "XCUIElementQuery+FBHelpers.h"; sourceTree = "<group>"; };
		71E75E6C254824230099FC87 /* XCUIElementQuery+FBHelpers.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "XCUIElementQuery+FBHelpers.m"; sourceTree = "<group>"; };
		71F3E7D225417FF400E0C22B /* FBSettings.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FBSettings.h; sourceTree = "<group>"; };
		71F3E7D325417FF400E0C22B /* FBSettings.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FBSettings.m; sourceTree = "<group>"; };
		71F5BE21252E576C00EE9EBA /* XCUIElement+FBSwiping.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "XCUIElement+FBSwiping.h"; sourceTree = "<group>"; };
		71F5BE22252E576C00EE9EBA /* XCUIElement+FBSwiping.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "XCUIElement+FBSwiping.m"; sourceTree = "<group>"; };
		71F5BE33252E5B2200EE9EBA /* FBElementSwipingTests.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FBElementSwipingTests.m; sourceTree = "<group>"; };
		71F5BE4D252F14EB00EE9EBA /* FBExceptions.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FBExceptions.h; sourceTree = "<group>"; };
		71F5BE4E252F14EB00EE9EBA /* FBExceptions.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FBExceptions.m; sourceTree = "<group>"; };
		AD42DD2A1CF121E600806E5D /* module.modulemap */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.module-map"; path = module.modulemap; sourceTree = "<group>"; };
		AD6C26921CF2379700F8B5FF /* FBAlert.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = FBAlert.h; path = WebDriverAgentLib/FBAlert.h; sourceTree = SOURCE_ROOT; };
		AD6C26931CF2379700F8B5FF /* FBAlert.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; lineEnding = 0; name = FBAlert.m; path = WebDriverAgentLib/FBAlert.m; sourceTree = SOURCE_ROOT; xcLanguageSpecificationIdentifier = xcode.lang.objc; };
		AD6C26961CF2481700F8B5FF /* XCUIDevice+FBHelpers.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "XCUIDevice+FBHelpers.h"; sourceTree = "<group>"; };
		AD6C26971CF2481700F8B5FF /* XCUIDevice+FBHelpers.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "XCUIDevice+FBHelpers.m"; sourceTree = "<group>"; };
		AD6C269A1CF2494200F8B5FF /* XCUIApplication+FBHelpers.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "XCUIApplication+FBHelpers.h"; sourceTree = "<group>"; };
		AD6C269B1CF2494200F8B5FF /* XCUIApplication+FBHelpers.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "XCUIApplication+FBHelpers.m"; sourceTree = "<group>"; };
		AD76723B1D6B7CC000610457 /* XCUIElement+FBTyping.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "XCUIElement+FBTyping.h"; sourceTree = "<group>"; };
		AD76723C1D6B7CC000610457 /* XCUIElement+FBTyping.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "XCUIElement+FBTyping.m"; sourceTree = "<group>"; };
		AD76723F1D6B826F00610457 /* FBTypingTest.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FBTypingTest.m; sourceTree = "<group>"; };
		ADBC39931D0782CD00327304 /* FBElementCacheTests.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FBElementCacheTests.m; sourceTree = "<group>"; };
		ADBC39961D07842800327304 /* XCUIElementDouble.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = XCUIElementDouble.h; sourceTree = "<group>"; };
		ADBC39971D07842800327304 /* XCUIElementDouble.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = XCUIElementDouble.m; sourceTree = "<group>"; };
		ADDA07221D6BB2BF001700AC /* FBScrollViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = FBScrollViewController.h; sourceTree = "<group>"; };
		ADDA07231D6BB2BF001700AC /* FBScrollViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FBScrollViewController.m; sourceTree = "<group>"; };
		ADEF63AE1D09DEBE0070A7E3 /* FBRuntimeUtilsTests.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FBRuntimeUtilsTests.m; sourceTree = "<group>"; };
		B316351B2DDF0CF5007D9317 /* FBAccessibilityTraits.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FBAccessibilityTraits.m; sourceTree = "<group>"; };
		B316351E2DDF0D0B007D9317 /* FBAccessibilityTraits.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FBAccessibilityTraits.h; sourceTree = "<group>"; };
		C8FB547322D3949C00B69954 /* LSApplicationWorkspace.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = LSApplicationWorkspace.h; sourceTree = "<group>"; };
		C8FB547722D4C1FC00B69954 /* FBUnattachedAppLauncher.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FBUnattachedAppLauncher.h; sourceTree = "<group>"; };
		C8FB547822D4C1FC00B69954 /* FBUnattachedAppLauncher.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FBUnattachedAppLauncher.m; sourceTree = "<group>"; };
		E444DC59249131880060D7EB /* HTTPErrorResponse.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = HTTPErrorResponse.h; path = WebDriverAgentLib/Vendor/CocoaHTTPServer/Responses/HTTPErrorResponse.h; sourceTree = SOURCE_ROOT; };
		E444DC5B249131880060D7EB /* HTTPDataResponse.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = HTTPDataResponse.m; path = WebDriverAgentLib/Vendor/CocoaHTTPServer/Responses/HTTPDataResponse.m; sourceTree = SOURCE_ROOT; };
		E444DC60249131890060D7EB /* HTTPDataResponse.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = HTTPDataResponse.h; path = WebDriverAgentLib/Vendor/CocoaHTTPServer/Responses/HTTPDataResponse.h; sourceTree = SOURCE_ROOT; };
		E444DC61249131890060D7EB /* HTTPErrorResponse.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = HTTPErrorResponse.m; path = WebDriverAgentLib/Vendor/CocoaHTTPServer/Responses/HTTPErrorResponse.m; sourceTree = SOURCE_ROOT; };
		E444DC7B249131B00060D7EB /* DDRange.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = DDRange.h; path = WebDriverAgentLib/Vendor/CocoaHTTPServer/Categories/DDRange.h; sourceTree = SOURCE_ROOT; };
		E444DC7D249131B00060D7EB /* DDNumber.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = DDNumber.h; path = WebDriverAgentLib/Vendor/CocoaHTTPServer/Categories/DDNumber.h; sourceTree = SOURCE_ROOT; };
		E444DC7E249131B00060D7EB /* DDRange.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = DDRange.m; path = WebDriverAgentLib/Vendor/CocoaHTTPServer/Categories/DDRange.m; sourceTree = SOURCE_ROOT; };
		E444DC7F249131B00060D7EB /* DDNumber.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = DDNumber.m; path = WebDriverAgentLib/Vendor/CocoaHTTPServer/Categories/DDNumber.m; sourceTree = SOURCE_ROOT; };
		E444DC87249131D30060D7EB /* HTTPMessage.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = HTTPMessage.h; path = WebDriverAgentLib/Vendor/CocoaHTTPServer/HTTPMessage.h; sourceTree = SOURCE_ROOT; };
		E444DC89249131D30060D7EB /* HTTPConnection.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = HTTPConnection.h; path = WebDriverAgentLib/Vendor/CocoaHTTPServer/HTTPConnection.h; sourceTree = SOURCE_ROOT; };
		E444DC8B249131D30060D7EB /* HTTPServer.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = HTTPServer.h; path = WebDriverAgentLib/Vendor/CocoaHTTPServer/HTTPServer.h; sourceTree = SOURCE_ROOT; };
		E444DC8C249131D30060D7EB /* HTTPConnection.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = HTTPConnection.m; path = WebDriverAgentLib/Vendor/CocoaHTTPServer/HTTPConnection.m; sourceTree = SOURCE_ROOT; };
		E444DC8D249131D30060D7EB /* HTTPLogging.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = HTTPLogging.h; path = WebDriverAgentLib/Vendor/CocoaHTTPServer/HTTPLogging.h; sourceTree = SOURCE_ROOT; };
		E444DC8F249131D40060D7EB /* HTTPResponse.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = HTTPResponse.h; path = WebDriverAgentLib/Vendor/CocoaHTTPServer/HTTPResponse.h; sourceTree = SOURCE_ROOT; };
		E444DC90249131D40060D7EB /* HTTPServer.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = HTTPServer.m; path = WebDriverAgentLib/Vendor/CocoaHTTPServer/HTTPServer.m; sourceTree = SOURCE_ROOT; };
		E444DC91249131D40060D7EB /* HTTPMessage.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = HTTPMessage.m; path = WebDriverAgentLib/Vendor/CocoaHTTPServer/HTTPMessage.m; sourceTree = SOURCE_ROOT; };
		E444DC9F24913C210060D7EB /* HTTPResponseProxy.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = HTTPResponseProxy.m; path = WebDriverAgentLib/Vendor/RoutingHTTPServer/HTTPResponseProxy.m; sourceTree = SOURCE_ROOT; };
		E444DCA024913C210060D7EB /* Route.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = Route.m; path = WebDriverAgentLib/Vendor/RoutingHTTPServer/Route.m; sourceTree = SOURCE_ROOT; };
		E444DCA124913C210060D7EB /* RouteResponse.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = RouteResponse.h; path = WebDriverAgentLib/Vendor/RoutingHTTPServer/RouteResponse.h; sourceTree = SOURCE_ROOT; };
		E444DCA224913C210060D7EB /* HTTPResponseProxy.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = HTTPResponseProxy.h; path = WebDriverAgentLib/Vendor/RoutingHTTPServer/HTTPResponseProxy.h; sourceTree = SOURCE_ROOT; };
		E444DCA324913C210060D7EB /* Route.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = Route.h; path = WebDriverAgentLib/Vendor/RoutingHTTPServer/Route.h; sourceTree = SOURCE_ROOT; };
		E444DCA424913C210060D7EB /* RouteResponse.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = RouteResponse.m; path = WebDriverAgentLib/Vendor/RoutingHTTPServer/RouteResponse.m; sourceTree = SOURCE_ROOT; };
		E444DCA524913C210060D7EB /* RoutingConnection.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = RoutingConnection.h; path = WebDriverAgentLib/Vendor/RoutingHTTPServer/RoutingConnection.h; sourceTree = SOURCE_ROOT; };
		E444DCA624913C210060D7EB /* RoutingConnection.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = RoutingConnection.m; path = WebDriverAgentLib/Vendor/RoutingHTTPServer/RoutingConnection.m; sourceTree = SOURCE_ROOT; };
		E444DCA724913C210060D7EB /* RoutingHTTPServer.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = RoutingHTTPServer.h; path = WebDriverAgentLib/Vendor/RoutingHTTPServer/RoutingHTTPServer.h; sourceTree = SOURCE_ROOT; };
		E444DCA824913C220060D7EB /* RoutingHTTPServer.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = RoutingHTTPServer.m; path = WebDriverAgentLib/Vendor/RoutingHTTPServer/RoutingHTTPServer.m; sourceTree = SOURCE_ROOT; };
		E444DCA924913C220060D7EB /* RouteRequest.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = RouteRequest.m; path = WebDriverAgentLib/Vendor/RoutingHTTPServer/RouteRequest.m; sourceTree = SOURCE_ROOT; };
		E444DCAA24913C220060D7EB /* RouteRequest.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = RouteRequest.h; path = WebDriverAgentLib/Vendor/RoutingHTTPServer/RouteRequest.h; sourceTree = SOURCE_ROOT; };
		EE006EAC1EB99B15006900A4 /* FBElementVisibilityTests.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FBElementVisibilityTests.m; sourceTree = "<group>"; };
		EE006EB21EBA1C7B006900A4 /* XCElementSnapshotHitPointTests.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = XCElementSnapshotHitPointTests.m; sourceTree = "<group>"; };
		EE05BAF91D13003C00A3EB00 /* FBKeyboardTests.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FBKeyboardTests.m; sourceTree = "<group>"; };
		EE0D1F5F1EBCDCF7006A3123 /* NSString+FBVisualLength.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSString+FBVisualLength.h"; sourceTree = "<group>"; };
		EE0D1F601EBCDCF7006A3123 /* NSString+FBVisualLength.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "NSString+FBVisualLength.m"; sourceTree = "<group>"; };
		EE158A991CBD452B00A3E3F0 /* WebDriverAgentLib.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = WebDriverAgentLib.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		EE158B5D1CBD479000A3E3F0 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; name = Info.plist; path = WebDriverAgentLib/Info.plist; sourceTree = SOURCE_ROOT; };
		EE158B5E1CBD47A000A3E3F0 /* WebDriverAgentLib.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = WebDriverAgentLib.h; path = WebDriverAgentLib/WebDriverAgentLib.h; sourceTree = SOURCE_ROOT; };
		EE1888381DA661C400307AA8 /* FBMathUtils.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = FBMathUtils.h; sourceTree = "<group>"; };
		EE1888391DA661C400307AA8 /* FBMathUtils.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FBMathUtils.m; sourceTree = "<group>"; };
		EE18883C1DA663EB00307AA8 /* FBMathUtilsTests.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FBMathUtilsTests.m; sourceTree = "<group>"; };
		EE1E06D91D1808C2007CF043 /* FBIntegrationTestCase.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FBIntegrationTestCase.m; sourceTree = "<group>"; };
		EE1E06DB1D18090F007CF043 /* FBIntegrationTestCase.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = FBIntegrationTestCase.h; sourceTree = "<group>"; };
		EE1E06DC1D1811C4007CF043 /* FBTestMacros.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = FBTestMacros.h; sourceTree = "<group>"; };
		EE1E06DF1D181BB4007CF043 /* XCUIDeviceHelperTests.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = XCUIDeviceHelperTests.m; sourceTree = "<group>"; };
		EE1E06E11D181CC9007CF043 /* XCUIElementHelperIntegrationTests.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = XCUIElementHelperIntegrationTests.m; sourceTree = "<group>"; };
		EE1E06E31D18213F007CF043 /* XCUIApplicationHelperTests.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = XCUIApplicationHelperTests.m; sourceTree = "<group>"; };
		EE1E06E51D182E95007CF043 /* FBAlertViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = FBAlertViewController.h; sourceTree = "<group>"; };
		EE1E06E61D182E95007CF043 /* FBAlertViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FBAlertViewController.m; sourceTree = "<group>"; };
		EE22021C1ECC612200A29571 /* IntegrationTests_3.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = IntegrationTests_3.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		EE26409A1D0EB5E8009BE6B0 /* FBTapTest.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FBTapTest.m; sourceTree = "<group>"; };
		EE26409C1D0EBA25009BE6B0 /* FBElementAttributeTests.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FBElementAttributeTests.m; sourceTree = "<group>"; };
		EE35AC981E3B77D600A02D78 /* _XCInternalTestRun.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = _XCInternalTestRun.h; sourceTree = "<group>"; };
		EE35AC991E3B77D600A02D78 /* _XCKVOExpectationImplementation.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = _XCKVOExpectationImplementation.h; sourceTree = "<group>"; };
		EE35AC9A1E3B77D600A02D78 /* _XCTDarwinNotificationExpectationImplementation.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = _XCTDarwinNotificationExpectationImplementation.h; sourceTree = "<group>"; };
		EE35AC9B1E3B77D600A02D78 /* _XCTestCaseImplementation.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = _XCTestCaseImplementation.h; sourceTree = "<group>"; };
		EE35AC9C1E3B77D600A02D78 /* _XCTestCaseInterruptionException.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = _XCTestCaseInterruptionException.h; sourceTree = "<group>"; };
		EE35AC9D1E3B77D600A02D78 /* _XCTestExpectationImplementation.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = _XCTestExpectationImplementation.h; sourceTree = "<group>"; };
		EE35AC9E1E3B77D600A02D78 /* _XCTestImplementation.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = _XCTestImplementation.h; sourceTree = "<group>"; };
		EE35AC9F1E3B77D600A02D78 /* _XCTestObservationCenterImplementation.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = _XCTestObservationCenterImplementation.h; sourceTree = "<group>"; };
		EE35ACA01E3B77D600A02D78 /* _XCTestSuiteImplementation.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = _XCTestSuiteImplementation.h; sourceTree = "<group>"; };
		EE35ACA11E3B77D600A02D78 /* _XCTNSNotificationExpectationImplementation.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = _XCTNSNotificationExpectationImplementation.h; sourceTree = "<group>"; };
		EE35ACA21E3B77D600A02D78 /* _XCTNSPredicateExpectationImplementation.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = _XCTNSPredicateExpectationImplementation.h; sourceTree = "<group>"; };
		EE35ACA31E3B77D600A02D78 /* _XCTWaiterImpl.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = _XCTWaiterImpl.h; sourceTree = "<group>"; };
		EE35ACA41E3B77D600A02D78 /* CDStructures.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CDStructures.h; sourceTree = "<group>"; };
		EE35ACAB1E3B77D600A02D78 /* NSString-XCTAdditions.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSString-XCTAdditions.h"; sourceTree = "<group>"; };
		EE35ACAC1E3B77D600A02D78 /* NSValue-XCTestAdditions.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSValue-XCTestAdditions.h"; sourceTree = "<group>"; };
		EE35ACAD1E3B77D600A02D78 /* UIGestureRecognizer-RecordingAdditions.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIGestureRecognizer-RecordingAdditions.h"; sourceTree = "<group>"; };
		EE35ACAE1E3B77D600A02D78 /* UILongPressGestureRecognizer-RecordingAdditions.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UILongPressGestureRecognizer-RecordingAdditions.h"; sourceTree = "<group>"; };
		EE35ACAF1E3B77D600A02D78 /* UIPanGestureRecognizer-RecordingAdditions.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIPanGestureRecognizer-RecordingAdditions.h"; sourceTree = "<group>"; };
		EE35ACB01E3B77D600A02D78 /* UIPinchGestureRecognizer-RecordingAdditions.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIPinchGestureRecognizer-RecordingAdditions.h"; sourceTree = "<group>"; };
		EE35ACB11E3B77D600A02D78 /* UISwipeGestureRecognizer-RecordingAdditions.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UISwipeGestureRecognizer-RecordingAdditions.h"; sourceTree = "<group>"; };
		EE35ACB21E3B77D600A02D78 /* UITapGestureRecognizer-RecordingAdditions.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UITapGestureRecognizer-RecordingAdditions.h"; sourceTree = "<group>"; };
		EE35ACB41E3B77D600A02D78 /* XCActivityRecord.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = XCActivityRecord.h; sourceTree = "<group>"; };
		EE35ACB51E3B77D600A02D78 /* XCApplicationMonitor_iOS.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = XCApplicationMonitor_iOS.h; sourceTree = "<group>"; };
		EE35ACB61E3B77D600A02D78 /* XCApplicationMonitor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = XCApplicationMonitor.h; sourceTree = "<group>"; };
		EE35ACB71E3B77D600A02D78 /* XCApplicationQuery.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = XCApplicationQuery.h; sourceTree = "<group>"; };
		EE35ACB81E3B77D600A02D78 /* XCAXClient_iOS.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = XCAXClient_iOS.h; sourceTree = "<group>"; };
		EE35ACB91E3B77D600A02D78 /* XCDebugLogDelegate-Protocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "XCDebugLogDelegate-Protocol.h"; sourceTree = "<group>"; };
		EE35ACBD1E3B77D600A02D78 /* XCEventGenerator.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = XCEventGenerator.h; sourceTree = "<group>"; };
		EE35ACBE1E3B77D600A02D78 /* XCKeyboardInputSolver.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = XCKeyboardInputSolver.h; sourceTree = "<group>"; };
		EE35ACBF1E3B77D600A02D78 /* XCKeyboardKeyMap.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = XCKeyboardKeyMap.h; sourceTree = "<group>"; };
		EE35ACC01E3B77D600A02D78 /* XCKeyboardLayout.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = XCKeyboardLayout.h; sourceTree = "<group>"; };
		EE35ACC11E3B77D600A02D78 /* XCKeyMappingPath.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = XCKeyMappingPath.h; sourceTree = "<group>"; };
		EE35ACC21E3B77D600A02D78 /* XCPointerEvent.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = XCPointerEvent.h; sourceTree = "<group>"; };
		EE35ACC31E3B77D600A02D78 /* XCPointerEventPath.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = XCPointerEventPath.h; sourceTree = "<group>"; };
		EE35ACC41E3B77D600A02D78 /* XCSourceCodeRecording.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = XCSourceCodeRecording.h; sourceTree = "<group>"; };
		EE35ACC51E3B77D600A02D78 /* XCSourceCodeTreeNode.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = XCSourceCodeTreeNode.h; sourceTree = "<group>"; };
		EE35ACC61E3B77D600A02D78 /* XCSourceCodeTreeNodeEnumerator.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = XCSourceCodeTreeNodeEnumerator.h; sourceTree = "<group>"; };
		EE35ACC71E3B77D600A02D78 /* XCSymbolicationRecord.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = XCSymbolicationRecord.h; sourceTree = "<group>"; };
		EE35ACC81E3B77D600A02D78 /* XCSymbolicatorHolder.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = XCSymbolicatorHolder.h; sourceTree = "<group>"; };
		EE35ACC91E3B77D600A02D78 /* XCSynthesizedEventRecord.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = XCSynthesizedEventRecord.h; sourceTree = "<group>"; };
		EE35ACCA1E3B77D600A02D78 /* XCTAsyncActivity-Protocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "XCTAsyncActivity-Protocol.h"; sourceTree = "<group>"; };
		EE35ACCB1E3B77D600A02D78 /* XCTAsyncActivity.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = XCTAsyncActivity.h; sourceTree = "<group>"; };
		EE35ACCC1E3B77D600A02D78 /* XCTAutomationTarget-Protocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "XCTAutomationTarget-Protocol.h"; sourceTree = "<group>"; };
		EE35ACCD1E3B77D600A02D78 /* XCTAXClient-Protocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "XCTAXClient-Protocol.h"; sourceTree = "<group>"; };
		EE35ACCE1E3B77D600A02D78 /* XCTDarwinNotificationExpectation.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = XCTDarwinNotificationExpectation.h; sourceTree = "<group>"; };
		EE35ACCF1E3B77D600A02D78 /* XCTest.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = XCTest.h; sourceTree = "<group>"; };
		EE35ACD01E3B77D600A02D78 /* XCTestCase.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = XCTestCase.h; sourceTree = "<group>"; };
		EE35ACD11E3B77D600A02D78 /* XCTestCaseRun.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = XCTestCaseRun.h; sourceTree = "<group>"; };
		EE35ACD21E3B77D600A02D78 /* XCTestCaseSuite.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = XCTestCaseSuite.h; sourceTree = "<group>"; };
		EE35ACD31E3B77D600A02D78 /* XCTestConfiguration.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = XCTestConfiguration.h; sourceTree = "<group>"; };
		EE35ACD41E3B77D600A02D78 /* XCTestContext.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = XCTestContext.h; sourceTree = "<group>"; };
		EE35ACD51E3B77D600A02D78 /* XCTestContextScope.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = XCTestContextScope.h; sourceTree = "<group>"; };
		EE35ACD61E3B77D600A02D78 /* XCTestDriver.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = XCTestDriver.h; sourceTree = "<group>"; };
		EE35ACD71E3B77D600A02D78 /* XCTestDriverInterface-Protocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "XCTestDriverInterface-Protocol.h"; sourceTree = "<group>"; };
		EE35ACD81E3B77D600A02D78 /* XCTestExpectation.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = XCTestExpectation.h; sourceTree = "<group>"; };
		EE35ACD91E3B77D600A02D78 /* XCTestExpectationDelegate-Protocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "XCTestExpectationDelegate-Protocol.h"; sourceTree = "<group>"; };
		EE35ACDA1E3B77D600A02D78 /* XCTestExpectationWaiter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = XCTestExpectationWaiter.h; sourceTree = "<group>"; };
		EE35ACDB1E3B77D600A02D78 /* XCTestLog.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = XCTestLog.h; sourceTree = "<group>"; };
		EE35ACDC1E3B77D600A02D78 /* XCTestManager_IDEInterface-Protocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "XCTestManager_IDEInterface-Protocol.h"; sourceTree = "<group>"; };
		EE35ACDD1E3B77D600A02D78 /* XCTestManager_ManagerInterface-Protocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "XCTestManager_ManagerInterface-Protocol.h"; sourceTree = "<group>"; };
		EE35ACDE1E3B77D600A02D78 /* XCTestManager_TestsInterface-Protocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "XCTestManager_TestsInterface-Protocol.h"; sourceTree = "<group>"; };
		EE35ACDF1E3B77D600A02D78 /* XCTestMisuseObserver.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = XCTestMisuseObserver.h; sourceTree = "<group>"; };
		EE35ACE01E3B77D600A02D78 /* XCTestObservation-Protocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "XCTestObservation-Protocol.h"; sourceTree = "<group>"; };
		EE35ACE11E3B77D600A02D78 /* XCTestObservationCenter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = XCTestObservationCenter.h; sourceTree = "<group>"; };
		EE35ACE21E3B77D600A02D78 /* XCTestObserver.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = XCTestObserver.h; sourceTree = "<group>"; };
		EE35ACE31E3B77D600A02D78 /* XCTestProbe.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = XCTestProbe.h; sourceTree = "<group>"; };
		EE35ACE41E3B77D600A02D78 /* XCTestRun.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = XCTestRun.h; sourceTree = "<group>"; };
		EE35ACE51E3B77D600A02D78 /* XCTestSuite.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = XCTestSuite.h; sourceTree = "<group>"; };
		EE35ACE61E3B77D600A02D78 /* XCTestSuiteRun.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = XCTestSuiteRun.h; sourceTree = "<group>"; };
		EE35ACE71E3B77D600A02D78 /* XCTestWaiter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = XCTestWaiter.h; sourceTree = "<group>"; };
		EE35ACE81E3B77D600A02D78 /* XCTKVOExpectation.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = XCTKVOExpectation.h; sourceTree = "<group>"; };
		EE35ACE91E3B77D600A02D78 /* XCTMetric.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = XCTMetric.h; sourceTree = "<group>"; };
		EE35ACEA1E3B77D600A02D78 /* XCTNSNotificationExpectation.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = XCTNSNotificationExpectation.h; sourceTree = "<group>"; };
		EE35ACEB1E3B77D600A02D78 /* XCTNSPredicateExpectation.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = XCTNSPredicateExpectation.h; sourceTree = "<group>"; };
		EE35ACEC1E3B77D600A02D78 /* XCTNSPredicateExpectationObject-Protocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "XCTNSPredicateExpectationObject-Protocol.h"; sourceTree = "<group>"; };
		EE35ACEE1E3B77D600A02D78 /* XCTRunnerAutomationSession.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = XCTRunnerAutomationSession.h; sourceTree = "<group>"; };
		EE35ACEF1E3B77D600A02D78 /* XCTRunnerDaemonSession.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = XCTRunnerDaemonSession.h; sourceTree = "<group>"; };
		EE35ACF01E3B77D600A02D78 /* XCTRunnerIDESession.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = XCTRunnerIDESession.h; sourceTree = "<group>"; };
		EE35ACF11E3B77D600A02D78 /* XCTTestRunSession.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = XCTTestRunSession.h; sourceTree = "<group>"; };
		EE35ACF21E3B77D600A02D78 /* XCTTestRunSessionDelegate-Protocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "XCTTestRunSessionDelegate-Protocol.h"; sourceTree = "<group>"; };
		EE35ACF31E3B77D600A02D78 /* XCTUIApplicationMonitor-Protocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "XCTUIApplicationMonitor-Protocol.h"; sourceTree = "<group>"; };
		EE35ACF41E3B77D600A02D78 /* XCTWaiter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = XCTWaiter.h; sourceTree = "<group>"; };
		EE35ACF51E3B77D600A02D78 /* XCTWaiterDelegate-Protocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "XCTWaiterDelegate-Protocol.h"; sourceTree = "<group>"; };
		EE35ACF61E3B77D600A02D78 /* XCTWaiterDelegatePrivate-Protocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "XCTWaiterDelegatePrivate-Protocol.h"; sourceTree = "<group>"; };
		EE35ACF71E3B77D600A02D78 /* XCTWaiterManagement-Protocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "XCTWaiterManagement-Protocol.h"; sourceTree = "<group>"; };
		EE35ACF81E3B77D600A02D78 /* XCTWaiterManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = XCTWaiterManager.h; sourceTree = "<group>"; };
		EE35ACF91E3B77D600A02D78 /* XCUIApplication.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = XCUIApplication.h; sourceTree = "<group>"; };
		EE35ACFA1E3B77D600A02D78 /* XCUIApplicationImpl.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = XCUIApplicationImpl.h; sourceTree = "<group>"; };
		EE35ACFB1E3B77D600A02D78 /* XCUIApplicationProcess.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = XCUIApplicationProcess.h; sourceTree = "<group>"; };
		EE35ACFC1E3B77D600A02D78 /* XCUICoordinate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = XCUICoordinate.h; sourceTree = "<group>"; };
		EE35ACFD1E3B77D600A02D78 /* XCUIDevice.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = XCUIDevice.h; sourceTree = "<group>"; };
		EE35ACFE1E3B77D600A02D78 /* XCUIElement.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = XCUIElement.h; sourceTree = "<group>"; };
		EE35ACFF1E3B77D600A02D78 /* XCUIElementAsynchronousHandlerWrapper.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = XCUIElementAsynchronousHandlerWrapper.h; sourceTree = "<group>"; };
		EE35AD011E3B77D600A02D78 /* XCUIElementHitPointCoordinate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = XCUIElementHitPointCoordinate.h; sourceTree = "<group>"; };
		EE35AD021E3B77D600A02D78 /* XCUIElementQuery.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = XCUIElementQuery.h; sourceTree = "<group>"; };
		EE35AD041E3B77D600A02D78 /* XCUIRecorderNodeFinder.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = XCUIRecorderNodeFinder.h; sourceTree = "<group>"; };
		EE35AD051E3B77D600A02D78 /* XCUIRecorderNodeFinderMatch.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = XCUIRecorderNodeFinderMatch.h; sourceTree = "<group>"; };
		EE35AD061E3B77D600A02D78 /* XCUIRecorderTimingMessage.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = XCUIRecorderTimingMessage.h; sourceTree = "<group>"; };
		EE35AD071E3B77D600A02D78 /* XCUIRecorderUtilities.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = XCUIRecorderUtilities.h; sourceTree = "<group>"; };
		EE35AD791E3B80C000A02D78 /* FBXCTestDaemonsProxy.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = FBXCTestDaemonsProxy.h; sourceTree = "<group>"; };
		EE35AD7A1E3B80C000A02D78 /* FBXCTestDaemonsProxy.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FBXCTestDaemonsProxy.m; sourceTree = "<group>"; };
		EE3A18601CDE618F00DE4205 /* FBErrorBuilder.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = FBErrorBuilder.h; sourceTree = "<group>"; };
		EE3A18611CDE618F00DE4205 /* FBErrorBuilder.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FBErrorBuilder.m; sourceTree = "<group>"; };
		EE3A18641CDE734B00DE4205 /* FBKeyboard.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = FBKeyboard.h; path = WebDriverAgentLib/Utilities/FBKeyboard.h; sourceTree = SOURCE_ROOT; };
		EE3A18651CDE734B00DE4205 /* FBKeyboard.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = FBKeyboard.m; path = WebDriverAgentLib/Utilities/FBKeyboard.m; sourceTree = SOURCE_ROOT; };
		EE3F8CFD1D08AA17006F02CE /* FBRunLoopSpinnerTests.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FBRunLoopSpinnerTests.m; sourceTree = "<group>"; };
		EE3F8CFF1D08B05F006F02CE /* FBElementTypeTransformerTests.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FBElementTypeTransformerTests.m; sourceTree = "<group>"; };
		EE5095FE1EBCC9090028E2FE /* IntegrationTests_2.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = IntegrationTests_2.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		EE55B3221D1D5388003AAAEC /* FBTableDataSource.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = FBTableDataSource.h; sourceTree = "<group>"; };
		EE55B3231D1D5388003AAAEC /* FBTableDataSource.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FBTableDataSource.m; sourceTree = "<group>"; };
		EE55B3261D1D54CF003AAAEC /* FBScrollingTests.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FBScrollingTests.m; sourceTree = "<group>"; };
		EE5A24401F136C8D0078B1D9 /* FBXCodeCompatibility.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FBXCodeCompatibility.h; sourceTree = "<group>"; };
		EE5A24411F136C8D0078B1D9 /* FBXCodeCompatibility.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FBXCodeCompatibility.m; sourceTree = "<group>"; };
		EE6A89251D0B19E60083E92B /* FBSessionTests.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FBSessionTests.m; sourceTree = "<group>"; };
		EE6A89271D0B257B0083E92B /* XCUIApplicationDouble.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = XCUIApplicationDouble.h; sourceTree = "<group>"; };
		EE6A89281D0B257B0083E92B /* XCUIApplicationDouble.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = XCUIApplicationDouble.m; sourceTree = "<group>"; };
		EE6A892C1D0B2AF40083E92B /* FBErrorBuilderTests.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FBErrorBuilderTests.m; sourceTree = "<group>"; };
		EE6A89361D0B35920083E92B /* FBFailureProofTestCaseTests.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FBFailureProofTestCaseTests.m; sourceTree = "<group>"; };
		EE6A89381D0B38640083E92B /* FBFailureProofTestCase.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = FBFailureProofTestCase.h; sourceTree = "<group>"; };
		EE6A89391D0B38640083E92B /* FBFailureProofTestCase.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FBFailureProofTestCase.m; sourceTree = "<group>"; };
		EE6B64FB1D0F86EF00E85F5D /* XCTestPrivateSymbols.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = XCTestPrivateSymbols.h; sourceTree = "<group>"; };
		EE6B64FC1D0F86EF00E85F5D /* XCTestPrivateSymbols.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = XCTestPrivateSymbols.m; sourceTree = "<group>"; };
		EE7E27181D06C69F001BEC7B /* FBDebugLogDelegateDecorator.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = FBDebugLogDelegateDecorator.h; sourceTree = "<group>"; };
		EE7E27191D06C69F001BEC7B /* FBDebugLogDelegateDecorator.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FBDebugLogDelegateDecorator.m; sourceTree = "<group>"; };
		EE836C021C0F118600D87246 /* UnitTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = UnitTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		EE8BA9781DCCED9A00A9DEF8 /* FBNavigationController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = FBNavigationController.h; sourceTree = "<group>"; };
		EE8BA9791DCCED9A00A9DEF8 /* FBNavigationController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FBNavigationController.m; sourceTree = "<group>"; };
		EE8DDD7A20C57320004D4925 /* FBForceTouchTests.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FBForceTouchTests.m; sourceTree = "<group>"; };
		EE8DDD7C20C5733B004D4925 /* XCUIElement+FBForceTouch.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "XCUIElement+FBForceTouch.m"; sourceTree = "<group>"; };
		EE8DDD7D20C5733C004D4925 /* XCUIElement+FBForceTouch.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "XCUIElement+FBForceTouch.h"; sourceTree = "<group>"; };
		EE9AB7451CAEDF0C008C271F /* XCUIElement+FBAccessibility.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "XCUIElement+FBAccessibility.h"; sourceTree = "<group>"; };
		EE9AB7461CAEDF0C008C271F /* XCUIElement+FBAccessibility.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "XCUIElement+FBAccessibility.m"; sourceTree = "<group>"; };
		EE9AB7471CAEDF0C008C271F /* XCUIElement+FBIsVisible.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "XCUIElement+FBIsVisible.h"; sourceTree = "<group>"; };
		EE9AB7481CAEDF0C008C271F /* XCUIElement+FBIsVisible.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "XCUIElement+FBIsVisible.m"; sourceTree = "<group>"; };
		EE9AB7491CAEDF0C008C271F /* XCUIElement+FBScrolling.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "XCUIElement+FBScrolling.h"; sourceTree = "<group>"; };
		EE9AB74A1CAEDF0C008C271F /* XCUIElement+FBScrolling.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "XCUIElement+FBScrolling.m"; sourceTree = "<group>"; };
		EE9AB7501CAEDF0C008C271F /* FBAlertViewCommands.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = FBAlertViewCommands.h; sourceTree = "<group>"; };
		EE9AB7511CAEDF0C008C271F /* FBAlertViewCommands.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FBAlertViewCommands.m; sourceTree = "<group>"; };
		EE9AB7521CAEDF0C008C271F /* FBCustomCommands.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = FBCustomCommands.h; sourceTree = "<group>"; };
		EE9AB7531CAEDF0C008C271F /* FBCustomCommands.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FBCustomCommands.m; sourceTree = "<group>"; };
		EE9AB7541CAEDF0C008C271F /* FBDebugCommands.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = FBDebugCommands.h; sourceTree = "<group>"; };
		EE9AB7551CAEDF0C008C271F /* FBDebugCommands.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FBDebugCommands.m; sourceTree = "<group>"; };
		EE9AB7561CAEDF0C008C271F /* FBElementCommands.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = FBElementCommands.h; sourceTree = "<group>"; };
		EE9AB7571CAEDF0C008C271F /* FBElementCommands.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FBElementCommands.m; sourceTree = "<group>"; };
		EE9AB7581CAEDF0C008C271F /* FBFindElementCommands.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = FBFindElementCommands.h; sourceTree = "<group>"; };
		EE9AB7591CAEDF0C008C271F /* FBFindElementCommands.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FBFindElementCommands.m; sourceTree = "<group>"; };
		EE9AB75C1CAEDF0C008C271F /* FBOrientationCommands.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = FBOrientationCommands.h; sourceTree = "<group>"; };
		EE9AB75D1CAEDF0C008C271F /* FBOrientationCommands.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FBOrientationCommands.m; sourceTree = "<group>"; };
		EE9AB75E1CAEDF0C008C271F /* FBScreenshotCommands.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = FBScreenshotCommands.h; sourceTree = "<group>"; };
		EE9AB75F1CAEDF0C008C271F /* FBScreenshotCommands.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FBScreenshotCommands.m; sourceTree = "<group>"; };
		EE9AB7601CAEDF0C008C271F /* FBSessionCommands.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = FBSessionCommands.h; sourceTree = "<group>"; };
		EE9AB7611CAEDF0C008C271F /* FBSessionCommands.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FBSessionCommands.m; sourceTree = "<group>"; };
		EE9AB7621CAEDF0C008C271F /* FBTouchIDCommands.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = FBTouchIDCommands.h; sourceTree = "<group>"; };
		EE9AB7631CAEDF0C008C271F /* FBTouchIDCommands.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FBTouchIDCommands.m; sourceTree = "<group>"; };
		EE9AB7641CAEDF0C008C271F /* FBUnknownCommands.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = FBUnknownCommands.h; sourceTree = "<group>"; };
		EE9AB7651CAEDF0C008C271F /* FBUnknownCommands.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FBUnknownCommands.m; sourceTree = "<group>"; };
		EE9AB7731CAEDF0C008C271F /* WebDriverAgent.bundle */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.plug-in"; path = WebDriverAgent.bundle; sourceTree = "<group>"; };
		EE9AB7751CAEDF0C008C271F /* FBCommandHandler.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = FBCommandHandler.h; sourceTree = "<group>"; };
		EE9AB7761CAEDF0C008C271F /* FBCommandStatus.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = FBCommandStatus.h; sourceTree = "<group>"; };
		EE9AB7791CAEDF0C008C271F /* FBElement.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = FBElement.h; sourceTree = "<group>"; };
		EE9AB77B1CAEDF0C008C271F /* FBElementCache.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = FBElementCache.h; sourceTree = "<group>"; };
		EE9AB7801CAEDF0C008C271F /* FBResponseJSONPayload.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = FBResponseJSONPayload.h; sourceTree = "<group>"; };
		EE9AB7811CAEDF0C008C271F /* FBResponseJSONPayload.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FBResponseJSONPayload.m; sourceTree = "<group>"; };
		EE9AB7821CAEDF0C008C271F /* FBResponsePayload.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = FBResponsePayload.h; sourceTree = "<group>"; };
		EE9AB7831CAEDF0C008C271F /* FBResponsePayload.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FBResponsePayload.m; sourceTree = "<group>"; };
		EE9AB7841CAEDF0C008C271F /* FBRoute.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = FBRoute.h; sourceTree = "<group>"; };
		EE9AB7851CAEDF0C008C271F /* FBRoute.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FBRoute.m; sourceTree = "<group>"; };
		EE9AB7861CAEDF0C008C271F /* FBRouteRequest-Private.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "FBRouteRequest-Private.h"; sourceTree = "<group>"; };
		EE9AB7871CAEDF0C008C271F /* FBRouteRequest.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = FBRouteRequest.h; sourceTree = "<group>"; };
		EE9AB7881CAEDF0C008C271F /* FBRouteRequest.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FBRouteRequest.m; sourceTree = "<group>"; };
		EE9AB7891CAEDF0C008C271F /* FBSession-Private.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "FBSession-Private.h"; sourceTree = "<group>"; };
		EE9AB78A1CAEDF0C008C271F /* FBSession.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = FBSession.h; sourceTree = "<group>"; };
		EE9AB78B1CAEDF0C008C271F /* FBSession.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FBSession.m; sourceTree = "<group>"; };
		EE9AB78C1CAEDF0C008C271F /* FBWebServer.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = FBWebServer.h; sourceTree = "<group>"; };
		EE9AB78D1CAEDF0C008C271F /* FBWebServer.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FBWebServer.m; sourceTree = "<group>"; };
		EE9AB78F1CAEDF0C008C271F /* FBElementTypeTransformer.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = FBElementTypeTransformer.h; sourceTree = "<group>"; };
		EE9AB7901CAEDF0C008C271F /* FBElementTypeTransformer.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FBElementTypeTransformer.m; sourceTree = "<group>"; };
		EE9AB7911CAEDF0C008C271F /* FBRuntimeUtils.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = FBRuntimeUtils.h; sourceTree = "<group>"; };
		EE9AB7921CAEDF0C008C271F /* FBRuntimeUtils.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FBRuntimeUtils.m; sourceTree = "<group>"; };
		EE9AB7FC1CAEE048008C271F /* Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; name = Info.plist; path = WebDriverAgentRunner/Info.plist; sourceTree = SOURCE_ROOT; };
		EE9AB7FD1CAEE048008C271F /* UITestingUITests.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = UITestingUITests.m; path = WebDriverAgentRunner/UITestingUITests.m; sourceTree = SOURCE_ROOT; };
		EE9AB8031CAEE182008C271F /* build.sh */ = {isa = PBXFileReference; lastKnownFileType = text.script.sh; path = build.sh; sourceTree = "<group>"; };
		EE9B75D41CF7956C00275851 /* IntegrationApp.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = IntegrationApp.app; sourceTree = BUILT_PRODUCTS_DIR; };
		EE9B75EC1CF7956C00275851 /* IntegrationTests_1.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = IntegrationTests_1.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		EE9B76571CF7987300275851 /* FBRouteTests.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FBRouteTests.m; sourceTree = "<group>"; };
		EE9B76581CF7987300275851 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		EE9B76821CF7997600275851 /* AppDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = AppDelegate.h; sourceTree = "<group>"; };
		EE9B76831CF7997600275851 /* AppDelegate.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = AppDelegate.m; sourceTree = "<group>"; };
		EE9B76841CF7997600275851 /* ViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ViewController.h; sourceTree = "<group>"; };
		EE9B76851CF7997600275851 /* ViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = ViewController.m; sourceTree = "<group>"; };
		EE9B76861CF7997600275851 /* Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		EE9B76871CF7997600275851 /* main.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = main.m; sourceTree = "<group>"; };
		EE9B768D1CF7997600275851 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/Main.storyboard; sourceTree = "<group>"; };
		EE9B76991CF799F400275851 /* FBAlertTests.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FBAlertTests.m; sourceTree = "<group>"; };
		EE9B76A11CF7A43900275851 /* FBConfiguration.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; lineEnding = 0; path = FBConfiguration.h; sourceTree = "<group>"; xcLanguageSpecificationIdentifier = xcode.lang.objcpp; };
		EE9B76A21CF7A43900275851 /* FBConfiguration.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FBConfiguration.m; sourceTree = "<group>"; };
		EE9B76A31CF7A43900275851 /* FBLogger.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = FBLogger.h; sourceTree = "<group>"; };
		EE9B76A41CF7A43900275851 /* FBLogger.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FBLogger.m; sourceTree = "<group>"; };
		EE9B76A51CF7A43900275851 /* FBMacros.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = FBMacros.h; sourceTree = "<group>"; };
		EEBBD4891D47746D00656A81 /* XCUIElement+FBFind.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "XCUIElement+FBFind.h"; sourceTree = "<group>"; };
		EEBBD48A1D47746D00656A81 /* XCUIElement+FBFind.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "XCUIElement+FBFind.m"; sourceTree = "<group>"; };
		EEBBD48D1D4785FC00656A81 /* XCUIElementFBFindTests.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = XCUIElementFBFindTests.m; sourceTree = "<group>"; };
		EEBBDB9A1D1032F0000738CD /* XCElementSnapshotHelperTests.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = XCElementSnapshotHelperTests.m; sourceTree = "<group>"; };
		EEC088E41CB56AC000B65968 /* FBElementCache.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FBElementCache.m; sourceTree = "<group>"; };
		EEC088E61CB56DA400B65968 /* FBExceptionHandler.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = FBExceptionHandler.h; sourceTree = "<group>"; };
		EEC088E71CB56DA400B65968 /* FBExceptionHandler.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FBExceptionHandler.m; sourceTree = "<group>"; };
		EEDBEBBA1CB2681900A790A2 /* WebDriverAgent.bundle */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.plug-in"; path = WebDriverAgent.bundle; sourceTree = "<group>"; };
		EEDFE11F1D9C06F800E6FFE5 /* XCUIDevice+FBHealthCheck.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "XCUIDevice+FBHealthCheck.h"; sourceTree = "<group>"; };
		EEDFE1201D9C06F800E6FFE5 /* XCUIDevice+FBHealthCheck.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "XCUIDevice+FBHealthCheck.m"; sourceTree = "<group>"; };
		EEDFE1231D9C08C700E6FFE5 /* XCUIDeviceHealthCheckTests.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = XCUIDeviceHealthCheckTests.m; sourceTree = "<group>"; };
		EEE16E961D33A25500172525 /* FBConfigurationTests.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FBConfigurationTests.m; sourceTree = "<group>"; };
		EEE3763D1D59F81400ED88DD /* XCUIDevice+FBRotation.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "XCUIDevice+FBRotation.h"; sourceTree = "<group>"; };
		EEE3763E1D59F81400ED88DD /* XCUIDevice+FBRotation.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "XCUIDevice+FBRotation.m"; sourceTree = "<group>"; };
		EEE3763F1D59F81400ED88DD /* XCUIElement+FBUtilities.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "XCUIElement+FBUtilities.h"; sourceTree = "<group>"; };
		EEE376401D59F81400ED88DD /* XCUIElement+FBUtilities.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "XCUIElement+FBUtilities.m"; sourceTree = "<group>"; };
		EEE376471D59FAE900ED88DD /* XCUIElement+FBWebDriverAttributes.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "XCUIElement+FBWebDriverAttributes.h"; sourceTree = "<group>"; };
		EEE376481D59FAE900ED88DD /* XCUIElement+FBWebDriverAttributes.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "XCUIElement+FBWebDriverAttributes.m"; sourceTree = "<group>"; };
		EEE5CABF1C80361500CBBDD9 /* IOSSettings.xcconfig */ = {isa = PBXFileReference; lastKnownFileType = text.xcconfig; path = IOSSettings.xcconfig; sourceTree = "<group>"; };
		EEE9B4701CD02B88009D2030 /* FBRunLoopSpinner.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = FBRunLoopSpinner.h; sourceTree = "<group>"; };
		EEE9B4711CD02B88009D2030 /* FBRunLoopSpinner.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FBRunLoopSpinner.m; sourceTree = "<group>"; };
		EEF9882A1C486603005CA669 /* WebDriverAgentRunner.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = WebDriverAgentRunner.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		641EE2D72240BBE300173FCB /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				641EE6FC2240C5FD00173FCB /* WebDriverAgentLib_tvOS.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		641EE6282240C5CA00173FCB /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				7155B414224D5B170042A993 /* XCTest.framework in Frameworks */,
				7155B41C224D5B5D0042A993 /* libxml2.tbd in Frameworks */,
				7155B41B224D5B5A0042A993 /* libAccessibility.tbd in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		64B264F6228C50E0002A5025 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				64B264FE228C50E0002A5025 /* WebDriverAgentLib_tvOS.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		EE158A951CBD452B00A3E3F0 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				7155B40E224D5A850042A993 /* libAccessibility.tbd in Frameworks */,
				7155B424224D5BA10042A993 /* XCTest.framework in Frameworks */,
				716C9347224D540C004B8542 /* libxml2.tbd in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		EE2202151ECC612200A29571 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				715D5776224DE06500DA2D99 /* libxml2.tbd in Frameworks */,
				EE2202171ECC612200A29571 /* WebDriverAgentLib.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		EE5095F71EBCC9090028E2FE /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				715D5775224DE05C00DA2D99 /* libxml2.tbd in Frameworks */,
				EE5095F91EBCC9090028E2FE /* WebDriverAgentLib.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		EE836BFF1C0F118600D87246 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				715D5773224DE02E00DA2D99 /* libxml2.tbd in Frameworks */,
				AD8D96F21D3C12990061268E /* WebDriverAgentLib.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		EE9B75D11CF7956C00275851 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		EE9B75E91CF7956C00275851 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				715D5774224DE05400DA2D99 /* libxml2.tbd in Frameworks */,
				EE9B76A01CF79C0F00275851 /* WebDriverAgentLib.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		EEF988271C486603005CA669 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				EE158B5A1CBD462100A3E3F0 /* WebDriverAgentLib.framework in Frameworks */,
				716C9346224D540C004B8542 /* libxml2.tbd in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		498495C81BB2E6FA009CC848 /* Resources */ = {
			isa = PBXGroup;
			children = (
				EEDBEBBA1CB2681900A790A2 /* WebDriverAgent.bundle */,
			);
			path = Resources;
			sourceTree = "<group>";
		};
		648C10A922AAAD7600B81B9A /* UIKitCore */ = {
			isa = PBXGroup;
			children = (
				648C10AA22AAAD9C00B81B9A /* UIKeyboardImpl.h */,
			);
			path = UIKitCore;
			sourceTree = "<group>";
		};
		648C10AD22AAAE2400B81B9A /* TextInput */ = {
			isa = PBXGroup;
			children = (
				648C10AE22AAAE4000B81B9A /* TIPreferencesController.h */,
			);
			path = TextInput;
			sourceTree = "<group>";
		};
		6496A5D7230D6E9D0087F8CB /* AccessibilityUtilities */ = {
			isa = PBXGroup;
			children = (
				6496A5D8230D6EB30087F8CB /* AXSettings.h */,
			);
			path = AccessibilityUtilities;
			sourceTree = "<group>";
		};
		64B264E8228C4D54002A5025 /* UnitTests_tvOS */ = {
			isa = PBXGroup;
			children = (
				64B26505228C54C9002A5025 /* Doubles */,
				64B264EB228C4D54002A5025 /* Info.plist */,
				64B264F3228C5098002A5025 /* FBTVNavigationTrackerTests.m */,
			);
			path = UnitTests_tvOS;
			sourceTree = "<group>";
		};
		64B26505228C54C9002A5025 /* Doubles */ = {
			isa = PBXGroup;
			children = (
				64B26506228C54F2002A5025 /* XCUIElementDouble.h */,
				64B26507228C5514002A5025 /* XCUIElementDouble.m */,
			);
			path = Doubles;
			sourceTree = "<group>";
		};
		71414ECF2670A1ED003A8C5D /* LRUCache */ = {
			isa = PBXGroup;
			children = (
				71414ED02670A1ED003A8C5D /* LRUCache.h */,
				71414ED12670A1ED003A8C5D /* LRUCacheNode.h */,
				71414ED22670A1ED003A8C5D /* LRUCache.m */,
				71414ED32670A1ED003A8C5D /* LRUCacheNode.m */,
			);
			path = LRUCache;
			sourceTree = "<group>";
		};
		716C9340224D5358004B8542 /* tvOS */ = {
			isa = PBXGroup;
			children = (
				7155B425224D5C130042A993 /* XCTAutomationSupport.framework */,
				7155B41A224D5B480042A993 /* libAccessibility.tbd */,
				7155B419224D5B460042A993 /* libxml2.tbd */,
				716C9342224D53A1004B8542 /* XCTest.framework */,
			);
			name = tvOS;
			sourceTree = "<group>";
		};
		716C9341224D5369004B8542 /* iOS */ = {
			isa = PBXGroup;
			children = (
				7155B423224D5B980042A993 /* XCTest.framework */,
				716C9344224D53FC004B8542 /* XCTAutomationSupport.framework */,
				716C9343224D53DF004B8542 /* libAccessibility.tbd */,
				716C9345224D540C004B8542 /* libxml2.tbd */,
			);
			name = iOS;
			sourceTree = "<group>";
		};
		7182268F2587432E00661B83 /* CocoaAsyncSocket */ = {
			isa = PBXGroup;
			children = (
				718226C72587443600661B83 /* GCDAsyncSocket.h */,
				718226C82587443600661B83 /* GCDAsyncSocket.m */,
				718226C62587443600661B83 /* GCDAsyncUdpSocket.h */,
				718226C92587443600661B83 /* GCDAsyncUdpSocket.m */,
			);
			name = CocoaAsyncSocket;
			sourceTree = "<group>";
		};
		91F9DAE01B99DBC2001349B2 = {
			isa = PBXGroup;
			children = (
				EEE5CABE1C80361500CBBDD9 /* Configurations */,
				91F9DB731B99DDD8001349B2 /* PrivateHeaders */,
				498495C81BB2E6FA009CC848 /* Resources */,
				EEC288F81BF0ED2500B4DC79 /* WebDriverAgentLib */,
				EE9B75F91CF7964100275851 /* WebDriverAgentTests */,
				EEF988341C486655005CA669 /* WebDriverAgentRunner */,
				EE9AB8021CAEE182008C271F /* Scripts */,
				91F9DAEA1B99DBC2001349B2 /* Products */,
				B6E83A410C45944B036B6B0F /* Frameworks */,
				AD42DD291CF121E600806E5D /* Modules */,
			);
			indentWidth = 2;
			sourceTree = "<group>";
			tabWidth = 2;
			usesTabs = 0;
		};
		91F9DAEA1B99DBC2001349B2 /* Products */ = {
			isa = PBXGroup;
			children = (
				EE836C021C0F118600D87246 /* UnitTests.xctest */,
				EEF9882A1C486603005CA669 /* WebDriverAgentRunner.xctest */,
				EE158A991CBD452B00A3E3F0 /* WebDriverAgentLib.framework */,
				EE9B75D41CF7956C00275851 /* IntegrationApp.app */,
				EE9B75EC1CF7956C00275851 /* IntegrationTests_1.xctest */,
				EE5095FE1EBCC9090028E2FE /* IntegrationTests_2.xctest */,
				EE22021C1ECC612200A29571 /* IntegrationTests_3.xctest */,
				641EE2DA2240BBE300173FCB /* WebDriverAgentRunner_tvOS.xctest */,
				641EE6F82240C5CA00173FCB /* WebDriverAgentLib_tvOS.framework */,
				64B264F9228C50E0002A5025 /* UnitTests_tvOS.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		91F9DB731B99DDD8001349B2 /* PrivateHeaders */ = {
			isa = PBXGroup;
			children = (
				6496A5D7230D6E9D0087F8CB /* AccessibilityUtilities */,
				C8FB547222D3948300B69954 /* MobileCoreServices */,
				648C10AD22AAAE2400B81B9A /* TextInput */,
				648C10A922AAAD7600B81B9A /* UIKitCore */,
				EED030DB1BFA3461007EDC1D /* XCTest */,
			);
			path = PrivateHeaders;
			sourceTree = "<group>";
		};
		AD42DD291CF121E600806E5D /* Modules */ = {
			isa = PBXGroup;
			children = (
				AD42DD2A1CF121E600806E5D /* module.modulemap */,
			);
			path = Modules;
			sourceTree = "<group>";
		};
		ADBC39951D07840300327304 /* Doubles */ = {
			isa = PBXGroup;
			children = (
				13FFF2F0287DBEE600E561E4 /* XCElementSnapshotDouble.h */,
				13FFF2F1287DBEE600E561E4 /* XCElementSnapshotDouble.m */,
				ADBC39961D07842800327304 /* XCUIElementDouble.h */,
				ADBC39971D07842800327304 /* XCUIElementDouble.m */,
				EE6A89271D0B257B0083E92B /* XCUIApplicationDouble.h */,
				EE6A89281D0B257B0083E92B /* XCUIApplicationDouble.m */,
			);
			path = Doubles;
			sourceTree = "<group>";
		};
		B6E83A410C45944B036B6B0F /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				716C9341224D5369004B8542 /* iOS */,
				716C9340224D5358004B8542 /* tvOS */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		C8FB547222D3948300B69954 /* MobileCoreServices */ = {
			isa = PBXGroup;
			children = (
				C8FB547322D3949C00B69954 /* LSApplicationWorkspace.h */,
			);
			path = MobileCoreServices;
			sourceTree = "<group>";
		};
		E444DC4A24912EC40060D7EB /* Vendor */ = {
			isa = PBXGroup;
			children = (
				7182268F2587432E00661B83 /* CocoaAsyncSocket */,
				E444DC9E24913C080060D7EB /* RoutingHTTPServer */,
				E444DC52249131050060D7EB /* CocoaHTTPServer */,
			);
			name = Vendor;
			sourceTree = "<group>";
		};
		E444DC52249131050060D7EB /* CocoaHTTPServer */ = {
			isa = PBXGroup;
			children = (
				E444DC89249131D30060D7EB /* HTTPConnection.h */,
				E444DC8C249131D30060D7EB /* HTTPConnection.m */,
				E444DC8D249131D30060D7EB /* HTTPLogging.h */,
				E444DC87249131D30060D7EB /* HTTPMessage.h */,
				E444DC91249131D40060D7EB /* HTTPMessage.m */,
				E444DC8F249131D40060D7EB /* HTTPResponse.h */,
				E444DC8B249131D30060D7EB /* HTTPServer.h */,
				E444DC90249131D40060D7EB /* HTTPServer.m */,
				E444DC55249131740060D7EB /* Responses */,
				E444DC53249131640060D7EB /* Categories */,
			);
			name = CocoaHTTPServer;
			sourceTree = "<group>";
		};
		E444DC53249131640060D7EB /* Categories */ = {
			isa = PBXGroup;
			children = (
				E444DC7D249131B00060D7EB /* DDNumber.h */,
				E444DC7F249131B00060D7EB /* DDNumber.m */,
				E444DC7B249131B00060D7EB /* DDRange.h */,
				E444DC7E249131B00060D7EB /* DDRange.m */,
			);
			name = Categories;
			sourceTree = "<group>";
		};
		E444DC55249131740060D7EB /* Responses */ = {
			isa = PBXGroup;
			children = (
				E444DC60249131890060D7EB /* HTTPDataResponse.h */,
				E444DC5B249131880060D7EB /* HTTPDataResponse.m */,
				E444DC59249131880060D7EB /* HTTPErrorResponse.h */,
				E444DC61249131890060D7EB /* HTTPErrorResponse.m */,
			);
			name = Responses;
			sourceTree = "<group>";
		};
		E444DC9E24913C080060D7EB /* RoutingHTTPServer */ = {
			isa = PBXGroup;
			children = (
				E444DCA224913C210060D7EB /* HTTPResponseProxy.h */,
				E444DC9F24913C210060D7EB /* HTTPResponseProxy.m */,
				E444DCA324913C210060D7EB /* Route.h */,
				E444DCA024913C210060D7EB /* Route.m */,
				E444DCAA24913C220060D7EB /* RouteRequest.h */,
				E444DCA924913C220060D7EB /* RouteRequest.m */,
				E444DCA124913C210060D7EB /* RouteResponse.h */,
				E444DCA424913C210060D7EB /* RouteResponse.m */,
				E444DCA524913C210060D7EB /* RoutingConnection.h */,
				E444DCA624913C210060D7EB /* RoutingConnection.m */,
				E444DCA724913C210060D7EB /* RoutingHTTPServer.h */,
				E444DCA824913C220060D7EB /* RoutingHTTPServer.m */,
			);
			name = RoutingHTTPServer;
			sourceTree = "<group>";
		};
		EE9AB73E1CAEDF0C008C271F /* Categories */ = {
			isa = PBXGroup;
			children = (
				716F0D9F2A16CA1000CDD977 /* NSDictionary+FBUtf8SafeDictionary.h */,
				716F0DA02A16CA1000CDD977 /* NSDictionary+FBUtf8SafeDictionary.m */,
				71555A3B1DEC460A007D4A8B /* NSExpression+FBFormat.h */,
				71555A3C1DEC460A007D4A8B /* NSExpression+FBFormat.m */,
				71A224E31DE2F56600844D55 /* NSPredicate+FBFormat.h */,
				71A224E41DE2F56600844D55 /* NSPredicate+FBFormat.m */,
				EE0D1F5F1EBCDCF7006A3123 /* NSString+FBVisualLength.h */,
				EE0D1F601EBCDCF7006A3123 /* NSString+FBVisualLength.m */,
				716E0BCC1E917E810087A825 /* NSString+FBXMLSafeString.h */,
				716E0BCD1E917E810087A825 /* NSString+FBXMLSafeString.m */,
				714E14B629805CAE00375DD7 /* XCAXClient_iOS+FBSnapshotReqParams.h */,
				714E14B729805CAE00375DD7 /* XCAXClient_iOS+FBSnapshotReqParams.m */,
				71A5C67129A4F39600421C37 /* XCTIssue+FBPatcher.h */,
				71A5C67229A4F39600421C37 /* XCTIssue+FBPatcher.m */,
				AD6C269A1CF2494200F8B5FF /* XCUIApplication+FBHelpers.h */,
				AD6C269B1CF2494200F8B5FF /* XCUIApplication+FBHelpers.m */,
				71C8E54F25399A6B008572C1 /* XCUIApplication+FBQuiescence.h */,
				71C8E55025399A6B008572C1 /* XCUIApplication+FBQuiescence.m */,
				716C9DFE27315EFF005AD475 /* XCUIApplication+FBUIInterruptions.h */,
				716C9DFF27315EFF005AD475 /* XCUIApplication+FBUIInterruptions.m */,
				71D475C02538F5A8008D9401 /* XCUIApplicationProcess+FBQuiescence.h */,
				71D475C12538F5A8008D9401 /* XCUIApplicationProcess+FBQuiescence.m */,
				719CD8FA2126C88B00C7D0C2 /* XCUIApplication+FBAlert.h */,
				719CD8FB2126C88B00C7D0C2 /* XCUIApplication+FBAlert.m */,
				71BD20711F86116100B36EC2 /* XCUIApplication+FBTouchAction.h */,
				71BD20721F86116100B36EC2 /* XCUIApplication+FBTouchAction.m */,
				EEDFE11F1D9C06F800E6FFE5 /* XCUIDevice+FBHealthCheck.h */,
				EEDFE1201D9C06F800E6FFE5 /* XCUIDevice+FBHealthCheck.m */,
				AD6C26961CF2481700F8B5FF /* XCUIDevice+FBHelpers.h */,
				AD6C26971CF2481700F8B5FF /* XCUIDevice+FBHelpers.m */,
				EEE3763D1D59F81400ED88DD /* XCUIDevice+FBRotation.h */,
				EEE3763E1D59F81400ED88DD /* XCUIDevice+FBRotation.m */,
				EE9AB7451CAEDF0C008C271F /* XCUIElement+FBAccessibility.h */,
				EE9AB7461CAEDF0C008C271F /* XCUIElement+FBAccessibility.m */,
				71D04DC625356C43008A052C /* XCUIElement+FBCaching.h */,
				71D04DC725356C43008A052C /* XCUIElement+FBCaching.m */,
				71A7EAF31E20516B001DA4F2 /* XCUIElement+FBClassChain.h */,
				71A7EAF41E20516B001DA4F2 /* XCUIElement+FBClassChain.m */,
				EEBBD4891D47746D00656A81 /* XCUIElement+FBFind.h */,
				EEBBD48A1D47746D00656A81 /* XCUIElement+FBFind.m */,
				EE8DDD7D20C5733C004D4925 /* XCUIElement+FBForceTouch.h */,
				EE8DDD7C20C5733B004D4925 /* XCUIElement+FBForceTouch.m */,
				EE9AB7471CAEDF0C008C271F /* XCUIElement+FBIsVisible.h */,
				EE9AB7481CAEDF0C008C271F /* XCUIElement+FBIsVisible.m */,
				0E04133A2DF1E15900AF007C /* XCUIElement+FBMinMax.h */,
				0E0413372DF1E15100AF007C /* XCUIElement+FBMinMax.m */,
				7136A4771E8918E60024FC3D /* XCUIElement+FBPickerWheel.h */,
				7136A4781E8918E60024FC3D /* XCUIElement+FBPickerWheel.m */,
				71D3B3D3267FC7260076473D /* XCUIElement+FBResolve.h */,
				71D3B3D4267FC7260076473D /* XCUIElement+FBResolve.m */,
				EE9AB7491CAEDF0C008C271F /* XCUIElement+FBScrolling.h */,
				EE9AB74A1CAEDF0C008C271F /* XCUIElement+FBScrolling.m */,
				71F5BE21252E576C00EE9EBA /* XCUIElement+FBSwiping.h */,
				71F5BE22252E576C00EE9EBA /* XCUIElement+FBSwiping.m */,
				AD76723B1D6B7CC000610457 /* XCUIElement+FBTyping.h */,
				AD76723C1D6B7CC000610457 /* XCUIElement+FBTyping.m */,
				71B49EC51ED1A58100D51AD6 /* XCUIElement+FBUID.h */,
				71B49EC61ED1A58100D51AD6 /* XCUIElement+FBUID.m */,
				EEE3763F1D59F81400ED88DD /* XCUIElement+FBUtilities.h */,
				EEE376401D59F81400ED88DD /* XCUIElement+FBUtilities.m */,
				71AE3CF52D38EE8E0039FC36 /* XCUIElement+FBVisibleFrame.h */,
				71AE3CF62D38EE8E0039FC36 /* XCUIElement+FBVisibleFrame.m */,
				EEE376471D59FAE900ED88DD /* XCUIElement+FBWebDriverAttributes.h */,
				EEE376481D59FAE900ED88DD /* XCUIElement+FBWebDriverAttributes.m */,
				641EE7042240CDCF00173FCB /* XCUIElement+FBTVFocuse.h */,
				641EE7072240CDEB00173FCB /* XCUIElement+FBTVFocuse.m */,
				71E75E6B254824230099FC87 /* XCUIElementQuery+FBHelpers.h */,
				71E75E6C254824230099FC87 /* XCUIElementQuery+FBHelpers.m */,
				13DE7A59287CA444003243C6 /* FBXCElementSnapshotWrapper+Helpers.h */,
				13DE7A5A287CA444003243C6 /* FBXCElementSnapshotWrapper+Helpers.m */,
			);
			name = Categories;
			path = WebDriverAgentLib/Categories;
			sourceTree = SOURCE_ROOT;
		};
		EE9AB74F1CAEDF0C008C271F /* Commands */ = {
			isa = PBXGroup;
			children = (
				EE9AB7501CAEDF0C008C271F /* FBAlertViewCommands.h */,
				EE9AB7511CAEDF0C008C271F /* FBAlertViewCommands.m */,
				EE9AB7521CAEDF0C008C271F /* FBCustomCommands.h */,
				EE9AB7531CAEDF0C008C271F /* FBCustomCommands.m */,
				EE9AB7541CAEDF0C008C271F /* FBDebugCommands.h */,
				EE9AB7551CAEDF0C008C271F /* FBDebugCommands.m */,
				EE9AB7561CAEDF0C008C271F /* FBElementCommands.h */,
				EE9AB7571CAEDF0C008C271F /* FBElementCommands.m */,
				EE9AB7581CAEDF0C008C271F /* FBFindElementCommands.h */,
				EE9AB7591CAEDF0C008C271F /* FBFindElementCommands.m */,
				EE9AB75C1CAEDF0C008C271F /* FBOrientationCommands.h */,
				EE9AB75D1CAEDF0C008C271F /* FBOrientationCommands.m */,
				EE9AB75E1CAEDF0C008C271F /* FBScreenshotCommands.h */,
				EE9AB75F1CAEDF0C008C271F /* FBScreenshotCommands.m */,
				EE9AB7601CAEDF0C008C271F /* FBSessionCommands.h */,
				EE9AB7611CAEDF0C008C271F /* FBSessionCommands.m */,
				71241D791FAE3D2500B9559F /* FBTouchActionCommands.h */,
				71241D7A1FAE3D2500B9559F /* FBTouchActionCommands.m */,
				EE9AB7621CAEDF0C008C271F /* FBTouchIDCommands.h */,
				EE9AB7631CAEDF0C008C271F /* FBTouchIDCommands.m */,
				EE9AB7641CAEDF0C008C271F /* FBUnknownCommands.h */,
				EE9AB7651CAEDF0C008C271F /* FBUnknownCommands.m */,
				71BB58ED2B96511800CB9BFE /* FBVideoCommands.h */,
				71BB58EE2B96511800CB9BFE /* FBVideoCommands.m */,
			);
			name = Commands;
			path = WebDriverAgentLib/Commands;
			sourceTree = SOURCE_ROOT;
		};
		EE9AB7721CAEDF0C008C271F /* Resources */ = {
			isa = PBXGroup;
			children = (
				EE9AB7731CAEDF0C008C271F /* WebDriverAgent.bundle */,
			);
			name = Resources;
			path = WebDriverAgentLib/Resources;
			sourceTree = SOURCE_ROOT;
		};
		EE9AB7741CAEDF0C008C271F /* Routing */ = {
			isa = PBXGroup;
			children = (
				EE9AB7751CAEDF0C008C271F /* FBCommandHandler.h */,
				EE9AB7761CAEDF0C008C271F /* FBCommandStatus.h */,
				71B155DB230711E900646AFB /* FBCommandStatus.m */,
				EE9AB7791CAEDF0C008C271F /* FBElement.h */,
				EE9AB77B1CAEDF0C008C271F /* FBElementCache.h */,
				EEC088E41CB56AC000B65968 /* FBElementCache.m */,
				713C6DCD1DDC772A00285B92 /* FBElementUtils.h */,
				713C6DCE1DDC772A00285B92 /* FBElementUtils.m */,
				71F5BE4D252F14EB00EE9EBA /* FBExceptions.h */,
				71F5BE4E252F14EB00EE9EBA /* FBExceptions.m */,
				EEC088E61CB56DA400B65968 /* FBExceptionHandler.h */,
				EEC088E71CB56DA400B65968 /* FBExceptionHandler.m */,
				71B155D923070ECF00646AFB /* FBHTTPStatusCodes.h */,
				EE9AB7801CAEDF0C008C271F /* FBResponseJSONPayload.h */,
				EE9AB7811CAEDF0C008C271F /* FBResponseJSONPayload.m */,
				EE9AB7821CAEDF0C008C271F /* FBResponsePayload.h */,
				EE9AB7831CAEDF0C008C271F /* FBResponsePayload.m */,
				EE9AB7841CAEDF0C008C271F /* FBRoute.h */,
				EE9AB7851CAEDF0C008C271F /* FBRoute.m */,
				EE9AB7861CAEDF0C008C271F /* FBRouteRequest-Private.h */,
				EE9AB7871CAEDF0C008C271F /* FBRouteRequest.h */,
				EE9AB7881CAEDF0C008C271F /* FBRouteRequest.m */,
				71BB58F42B96531900CB9BFE /* FBScreenRecordingContainer.h */,
				71BB58F52B96531900CB9BFE /* FBScreenRecordingContainer.m */,
				71BB58DF2B9631F100CB9BFE /* FBScreenRecordingPromise.h */,
				71BB58E02B9631F100CB9BFE /* FBScreenRecordingPromise.m */,
				71BB58E62B96328700CB9BFE /* FBScreenRecordingRequest.h */,
				71BB58E72B96328700CB9BFE /* FBScreenRecordingRequest.m */,
				EE9AB7891CAEDF0C008C271F /* FBSession-Private.h */,
				EE9AB78A1CAEDF0C008C271F /* FBSession.h */,
				EE9AB78B1CAEDF0C008C271F /* FBSession.m */,
				715557D1211DBCE700613B26 /* FBTCPSocket.h */,
				715557D2211DBCE700613B26 /* FBTCPSocket.m */,
				EE9AB78C1CAEDF0C008C271F /* FBWebServer.h */,
				EE9AB78D1CAEDF0C008C271F /* FBWebServer.m */,
				13DE7A41287C2A8D003243C6 /* FBXCAccessibilityElement.h */,
				13DE7A42287C2A8D003243C6 /* FBXCAccessibilityElement.m */,
				13DE7A47287C4005003243C6 /* FBXCDeviceEvent.h */,
				13DE7A48287C4005003243C6 /* FBXCDeviceEvent.m */,
				13DE7A4D287C46BB003243C6 /* FBXCElementSnapshot.h */,
				13DE7A4E287C46BB003243C6 /* FBXCElementSnapshot.m */,
				13DE7A53287CA1EC003243C6 /* FBXCElementSnapshotWrapper.h */,
				13DE7A54287CA1EC003243C6 /* FBXCElementSnapshotWrapper.m */,
			);
			name = Routing;
			path = WebDriverAgentLib/Routing;
			sourceTree = SOURCE_ROOT;
		};
		EE9AB78E1CAEDF0C008C271F /* Utilities */ = {
			isa = PBXGroup;
			children = (
				71414ECF2670A1ED003A8C5D /* LRUCache */,
				13815F6D2328D20400CDAB61 /* FBActiveAppDetectionPoint.h */,
				13815F6E2328D20400CDAB61 /* FBActiveAppDetectionPoint.m */,
				719CD8F62126C78F00C7D0C2 /* FBAlertsMonitor.h */,
				719CD8F72126C78F00C7D0C2 /* FBAlertsMonitor.m */,
				714097411FAE1B0B008FB2C5 /* FBBaseActionsSynthesizer.h */,
				7140974D1FAE20EE008FB2C5 /* FBBaseActionsSynthesizer.m */,
				714EAA0B2673FDFE005C5B47 /* FBCapabilities.h */,
				714EAA0C2673FDFE005C5B47 /* FBCapabilities.m */,
				71A7EAF71E224648001DA4F2 /* FBClassChainQueryParser.h */,
				71A7EAF81E224648001DA4F2 /* FBClassChainQueryParser.m */,
				EE9B76A11CF7A43900275851 /* FBConfiguration.h */,
				EE9B76A21CF7A43900275851 /* FBConfiguration.m */,
				EE7E27181D06C69F001BEC7B /* FBDebugLogDelegateDecorator.h */,
				EE7E27191D06C69F001BEC7B /* FBDebugLogDelegateDecorator.m */,
				715A84CD2DD92AD3007134CC /* FBElementHelpers.h */,
				715A84CE2DD92AD3007134CC /* FBElementHelpers.m */,
				EE9AB78F1CAEDF0C008C271F /* FBElementTypeTransformer.h */,
				EE9AB7901CAEDF0C008C271F /* FBElementTypeTransformer.m */,
				EE3A18601CDE618F00DE4205 /* FBErrorBuilder.h */,
				EE3A18611CDE618F00DE4205 /* FBErrorBuilder.m */,
				EE6A89381D0B38640083E92B /* FBFailureProofTestCase.h */,
				EE6A89391D0B38640083E92B /* FBFailureProofTestCase.m */,
				63CCF91021ECE4C700E94ABD /* FBImageProcessor.h */,
				63CCF91121ECE4C700E94ABD /* FBImageProcessor.m */,
				7150348521A6DAD600A0F4BA /* FBImageUtils.h */,
				7150348621A6DAD600A0F4BA /* FBImageUtils.m */,
				EE9B76A31CF7A43900275851 /* FBLogger.h */,
				EE9B76A41CF7A43900275851 /* FBLogger.m */,
				EE9B76A51CF7A43900275851 /* FBMacros.h */,
				EE1888381DA661C400307AA8 /* FBMathUtils.h */,
				EE1888391DA661C400307AA8 /* FBMathUtils.m */,
				7155D701211DCEF400166C20 /* FBMjpegServer.h */,
				7155D702211DCEF400166C20 /* FBMjpegServer.m */,
				719DCF132601EAFB000E765F /* FBNotificationsHelper.h */,
				719DCF142601EAFB000E765F /* FBNotificationsHelper.m */,
				71930C4020662E1F00D3AFEC /* FBPasteboard.h */,
				71930C4120662E1F00D3AFEC /* FBPasteboard.m */,
				71B155DD23080CA600646AFB /* FBProtocolHelpers.h */,
				71B155DE23080CA600646AFB /* FBProtocolHelpers.m */,
				716C9DF827315D21005AD475 /* FBReflectionUtils.h */,
				716C9DF927315D21005AD475 /* FBReflectionUtils.m */,
				EEE9B4701CD02B88009D2030 /* FBRunLoopSpinner.h */,
				EEE9B4711CD02B88009D2030 /* FBRunLoopSpinner.m */,
				EE9AB7911CAEDF0C008C271F /* FBRuntimeUtils.h */,
				EE9AB7921CAEDF0C008C271F /* FBRuntimeUtils.m */,
				71F3E7D225417FF400E0C22B /* FBSettings.h */,
				71F3E7D325417FF400E0C22B /* FBSettings.m */,
				715AFABF1FFA29180053896D /* FBScreen.h */,
				715AFAC01FFA29180053896D /* FBScreen.m */,
				71C9EAAA25E8415A00470CD8 /* FBScreenshot.h */,
				71C9EAAB25E8415A00470CD8 /* FBScreenshot.m */,
				641EE70A2240CE2D00173FCB /* FBTVNavigationTracker.h */,
				64B26509228CE4FF002A5025 /* FBTVNavigationTracker-Private.h */,
				641EE70D2240CE4800173FCB /* FBTVNavigationTracker.m */,
				C8FB547722D4C1FC00B69954 /* FBUnattachedAppLauncher.h */,
				C8FB547822D4C1FC00B69954 /* FBUnattachedAppLauncher.m */,
				714097491FAE1B51008FB2C5 /* FBW3CActionsSynthesizer.h */,
				7140974A1FAE1B51008FB2C5 /* FBW3CActionsSynthesizer.m */,
				713AE573243A53BE0000D657 /* FBW3CActionsHelpers.h */,
				713AE574243A53BE0000D657 /* FBW3CActionsHelpers.m */,
				7157B28F221DADD2001C348C /* FBXCAXClientProxy.h */,
				7157B290221DADD2001C348C /* FBXCAXClientProxy.m */,
				EE5A24401F136C8D0078B1D9 /* FBXCodeCompatibility.h */,
				EE5A24411F136C8D0078B1D9 /* FBXCodeCompatibility.m */,
				EE35AD791E3B80C000A02D78 /* FBXCTestDaemonsProxy.h */,
				EE35AD7A1E3B80C000A02D78 /* FBXCTestDaemonsProxy.m */,
				714D88CA2733FB970074A925 /* FBXMLGenerationOptions.h */,
				714D88CB2733FB970074A925 /* FBXMLGenerationOptions.m */,
				712A0C861DA3E55D007D02E5 /* FBXPath-Private.h */,
				711084421DA3AA7500F913D6 /* FBXPath.h */,
				711084431DA3AA7500F913D6 /* FBXPath.m */,
				EE6B64FB1D0F86EF00E85F5D /* XCTestPrivateSymbols.h */,
				EE6B64FC1D0F86EF00E85F5D /* XCTestPrivateSymbols.m */,
				633E904A220DEE7F007CADF9 /* XCUIApplicationProcessDelay.h */,
				6385F4A5220A40760095BBDB /* XCUIApplicationProcessDelay.m */,
				B316351B2DDF0CF5007D9317 /* FBAccessibilityTraits.m */,
				B316351E2DDF0D0B007D9317 /* FBAccessibilityTraits.h */,
			);
			name = Utilities;
			path = WebDriverAgentLib/Utilities;
			sourceTree = SOURCE_ROOT;
		};
		EE9AB8021CAEE182008C271F /* Scripts */ = {
			isa = PBXGroup;
			children = (
				EE9AB8031CAEE182008C271F /* build.sh */,
			);
			path = Scripts;
			sourceTree = "<group>";
		};
		EE9B75F91CF7964100275851 /* WebDriverAgentTests */ = {
			isa = PBXGroup;
			children = (
				64B264E8228C4D54002A5025 /* UnitTests_tvOS */,
				EE9B76801CF7997600275851 /* IntegrationApp */,
				EE9B76541CF7987300275851 /* IntegrationTests */,
				EE9B76561CF7987300275851 /* UnitTests */,
			);
			path = WebDriverAgentTests;
			sourceTree = "<group>";
		};
		EE9B76541CF7987300275851 /* IntegrationTests */ = {
			isa = PBXGroup;
			children = (
				EE9B76991CF799F400275851 /* FBAlertTests.m */,
				719CD8FE2126C90200C7D0C2 /* FBAutoAlertsHandlerTests.m */,
				EE26409C1D0EBA25009BE6B0 /* FBElementAttributeTests.m */,
				71F5BE33252E5B2200EE9EBA /* FBElementSwipingTests.m */,
				EE006EAC1EB99B15006900A4 /* FBElementVisibilityTests.m */,
				EE6A89361D0B35920083E92B /* FBFailureProofTestCaseTests.m */,
				EE8DDD7A20C57320004D4925 /* FBForceTouchTests.m */,
				EE1E06DB1D18090F007CF043 /* FBIntegrationTestCase.h */,
				EE1E06D91D1808C2007CF043 /* FBIntegrationTestCase.m */,
				EE05BAF91D13003C00A3EB00 /* FBKeyboardTests.m */,
				71930C462066434000D3AFEC /* FBPasteboardTests.m */,
				7119E1EB1E891F8600D0B125 /* FBPickerWheelSelectTests.m */,
				71ACF5B7242F2FDC00F0AAD4 /* FBSafariAlertTests.m */,
				715AFAC31FFA2AAF0053896D /* FBScreenTests.m */,
				EE55B3261D1D54CF003AAAEC /* FBScrollingTests.m */,
				7152EB2F1F41F9960047EEFF /* FBSessionIntegrationTests.m */,
				EE26409A1D0EB5E8009BE6B0 /* FBTapTest.m */,
				EE1E06DC1D1811C4007CF043 /* FBTestMacros.h */,
				AD76723F1D6B826F00610457 /* FBTypingTest.m */,
				714CA3C61DC23186000F12C9 /* FBXPathIntegrationTests.m */,
				71BB58DD2B9631B700CB9BFE /* FBVideoRecordingTests.m */,
				71241D7D1FAF084E00B9559F /* FBW3CTouchActionsIntegrationTests.m */,
				71241D7F1FAF087500B9559F /* FBW3CMultiTouchActionsIntegrationTests.m */,
				7136C0F8243A182400921C76 /* FBW3CTypeActionsTests.m */,
				EEBBDB9A1D1032F0000738CD /* XCElementSnapshotHelperTests.m */,
				EE006EB21EBA1C7B006900A4 /* XCElementSnapshotHitPointTests.m */,
				EE1E06E31D18213F007CF043 /* XCUIApplicationHelperTests.m */,
				EEDFE1231D9C08C700E6FFE5 /* XCUIDeviceHealthCheckTests.m */,
				EE1E06DF1D181BB4007CF043 /* XCUIDeviceHelperTests.m */,
				44757A831D42CE8300ECF35E /* XCUIDeviceRotationTests.m */,
				71E504941DF59BAD0020C32A /* XCUIElementAttributesTests.m */,
				EEBBD48D1D4785FC00656A81 /* XCUIElementFBFindTests.m */,
				EE1E06E11D181CC9007CF043 /* XCUIElementHelperIntegrationTests.m */,
				631B523421F6174300625362 /* FBImageProcessorTests.m */,
				644D9CCD230E1F1A00C90459 /* FBConfigurationTests.m */,
			);
			path = IntegrationTests;
			sourceTree = "<group>";
		};
		EE9B76561CF7987300275851 /* UnitTests */ = {
			isa = PBXGroup;
			children = (
				ADBC39951D07840300327304 /* Doubles */,
				71A7EAFB1E229302001DA4F2 /* FBClassChainTests.m */,
				EEE16E961D33A25500172525 /* FBConfigurationTests.m */,
				ADBC39931D0782CD00327304 /* FBElementCacheTests.m */,
				EE3F8CFF1D08B05F006F02CE /* FBElementTypeTransformerTests.m */,
				719FF5B81DAD21F5008E0099 /* FBElementUtilitiesTests.m */,
				EE6A892C1D0B2AF40083E92B /* FBErrorBuilderTests.m */,
				715D554A2229891B00524509 /* FBExceptionHandlerTests.m */,
				713352FC26CEF31D00523CBC /* FBLRUCacheTests.m */,
				EE18883C1DA663EB00307AA8 /* FBMathUtilsTests.m */,
				718F49C7230844330045FE8B /* FBProtocolHelpersTests.m */,
				EE9B76571CF7987300275851 /* FBRouteTests.m */,
				EE3F8CFD1D08AA17006F02CE /* FBRunLoopSpinnerTests.m */,
				ADEF63AE1D09DEBE0070A7E3 /* FBRuntimeUtilsTests.m */,
				714801D01FA9D9FA00DC5997 /* FBSDKVersionTests.m */,
				EE6A89251D0B19E60083E92B /* FBSessionTests.m */,
				716E0BD01E917F260087A825 /* FBXMLSafeStringTests.m */,
				712A0C841DA3E459007D02E5 /* FBXPathTests.m */,
				EE9B76581CF7987300275851 /* Info.plist */,
				716F0DA52A17323300CDD977 /* NSDictionaryFBUtf8SafeTests.m */,
				7139145B1DF01A12005896C2 /* NSExpressionFBFormatTests.m */,
				71A224E71DE326C500844D55 /* NSPredicateFBFormatTests.m */,
				713914591DF01989005896C2 /* XCUIElementHelpersTests.m */,
			);
			path = UnitTests;
			sourceTree = "<group>";
		};
		EE9B76801CF7997600275851 /* IntegrationApp */ = {
			isa = PBXGroup;
			children = (
				EE9B76811CF7997600275851 /* Classes */,
				EE9B76881CF7997600275851 /* Resources */,
				EE9B76861CF7997600275851 /* Info.plist */,
				EE9B76871CF7997600275851 /* main.m */,
			);
			path = IntegrationApp;
			sourceTree = "<group>";
		};
		EE9B76811CF7997600275851 /* Classes */ = {
			isa = PBXGroup;
			children = (
				EE9B76821CF7997600275851 /* AppDelegate.h */,
				EE9B76831CF7997600275851 /* AppDelegate.m */,
				EE1E06E51D182E95007CF043 /* FBAlertViewController.h */,
				EE1E06E61D182E95007CF043 /* FBAlertViewController.m */,
				EE8BA9781DCCED9A00A9DEF8 /* FBNavigationController.h */,
				EE8BA9791DCCED9A00A9DEF8 /* FBNavigationController.m */,
				ADDA07221D6BB2BF001700AC /* FBScrollViewController.h */,
				ADDA07231D6BB2BF001700AC /* FBScrollViewController.m */,
				EE55B3221D1D5388003AAAEC /* FBTableDataSource.h */,
				EE55B3231D1D5388003AAAEC /* FBTableDataSource.m */,
				EE9B76841CF7997600275851 /* ViewController.h */,
				EE9B76851CF7997600275851 /* ViewController.m */,
				315A14FF2518CB8700A3A064 /* TouchableView.h */,
				315A15002518CB8700A3A064 /* TouchableView.m */,
				315A15052518CC2800A3A064 /* TouchSpotView.h */,
				315A15062518CC2800A3A064 /* TouchSpotView.m */,
				315A15082518D6F400A3A064 /* TouchViewController.h */,
				315A15092518D6F400A3A064 /* TouchViewController.m */,
			);
			path = Classes;
			sourceTree = "<group>";
		};
		EE9B76881CF7997600275851 /* Resources */ = {
			isa = PBXGroup;
			children = (
				EE9B768C1CF7997600275851 /* Main.storyboard */,
			);
			path = Resources;
			sourceTree = "<group>";
		};
		EEC288F81BF0ED2500B4DC79 /* WebDriverAgentLib */ = {
			isa = PBXGroup;
			children = (
				E444DC4A24912EC40060D7EB /* Vendor */,
				EE9AB73E1CAEDF0C008C271F /* Categories */,
				EE9AB74F1CAEDF0C008C271F /* Commands */,
				EE9AB7721CAEDF0C008C271F /* Resources */,
				EE9AB7741CAEDF0C008C271F /* Routing */,
				EE9AB78E1CAEDF0C008C271F /* Utilities */,
				EE158B5E1CBD47A000A3E3F0 /* WebDriverAgentLib.h */,
				AD6C26921CF2379700F8B5FF /* FBAlert.h */,
				AD6C26931CF2379700F8B5FF /* FBAlert.m */,
				EE3A18641CDE734B00DE4205 /* FBKeyboard.h */,
				EE3A18651CDE734B00DE4205 /* FBKeyboard.m */,
				EE158B5D1CBD479000A3E3F0 /* Info.plist */,
			);
			name = WebDriverAgentLib;
			path = XCTWebDriverAgentLib;
			sourceTree = "<group>";
		};
		EED030DB1BFA3461007EDC1D /* XCTest */ = {
			isa = PBXGroup;
			children = (
				EE35AC981E3B77D600A02D78 /* _XCInternalTestRun.h */,
				EE35AC991E3B77D600A02D78 /* _XCKVOExpectationImplementation.h */,
				EE35AC9A1E3B77D600A02D78 /* _XCTDarwinNotificationExpectationImplementation.h */,
				EE35AC9B1E3B77D600A02D78 /* _XCTestCaseImplementation.h */,
				EE35AC9C1E3B77D600A02D78 /* _XCTestCaseInterruptionException.h */,
				EE35AC9D1E3B77D600A02D78 /* _XCTestExpectationImplementation.h */,
				EE35AC9E1E3B77D600A02D78 /* _XCTestImplementation.h */,
				EE35AC9F1E3B77D600A02D78 /* _XCTestObservationCenterImplementation.h */,
				EE35ACA01E3B77D600A02D78 /* _XCTestSuiteImplementation.h */,
				EE35ACA11E3B77D600A02D78 /* _XCTNSNotificationExpectationImplementation.h */,
				EE35ACA21E3B77D600A02D78 /* _XCTNSPredicateExpectationImplementation.h */,
				EE35ACA31E3B77D600A02D78 /* _XCTWaiterImpl.h */,
				EE35ACA41E3B77D600A02D78 /* CDStructures.h */,
				EE35ACAB1E3B77D600A02D78 /* NSString-XCTAdditions.h */,
				EE35ACAC1E3B77D600A02D78 /* NSValue-XCTestAdditions.h */,
				EE35ACAD1E3B77D600A02D78 /* UIGestureRecognizer-RecordingAdditions.h */,
				EE35ACAE1E3B77D600A02D78 /* UILongPressGestureRecognizer-RecordingAdditions.h */,
				EE35ACAF1E3B77D600A02D78 /* UIPanGestureRecognizer-RecordingAdditions.h */,
				EE35ACB01E3B77D600A02D78 /* UIPinchGestureRecognizer-RecordingAdditions.h */,
				EE35ACB11E3B77D600A02D78 /* UISwipeGestureRecognizer-RecordingAdditions.h */,
				EE35ACB21E3B77D600A02D78 /* UITapGestureRecognizer-RecordingAdditions.h */,
				EE35ACB41E3B77D600A02D78 /* XCActivityRecord.h */,
				EE35ACB51E3B77D600A02D78 /* XCApplicationMonitor_iOS.h */,
				EE35ACB61E3B77D600A02D78 /* XCApplicationMonitor.h */,
				EE35ACB71E3B77D600A02D78 /* XCApplicationQuery.h */,
				EE35ACB81E3B77D600A02D78 /* XCAXClient_iOS.h */,
				EE35ACB91E3B77D600A02D78 /* XCDebugLogDelegate-Protocol.h */,
				EE35ACBD1E3B77D600A02D78 /* XCEventGenerator.h */,
				EE35ACBE1E3B77D600A02D78 /* XCKeyboardInputSolver.h */,
				EE35ACBF1E3B77D600A02D78 /* XCKeyboardKeyMap.h */,
				EE35ACC01E3B77D600A02D78 /* XCKeyboardLayout.h */,
				EE35ACC11E3B77D600A02D78 /* XCKeyMappingPath.h */,
				EE35ACC21E3B77D600A02D78 /* XCPointerEvent.h */,
				EE35ACC31E3B77D600A02D78 /* XCPointerEventPath.h */,
				EE35ACC41E3B77D600A02D78 /* XCSourceCodeRecording.h */,
				EE35ACC51E3B77D600A02D78 /* XCSourceCodeTreeNode.h */,
				EE35ACC61E3B77D600A02D78 /* XCSourceCodeTreeNodeEnumerator.h */,
				EE35ACC71E3B77D600A02D78 /* XCSymbolicationRecord.h */,
				EE35ACC81E3B77D600A02D78 /* XCSymbolicatorHolder.h */,
				EE35ACC91E3B77D600A02D78 /* XCSynthesizedEventRecord.h */,
				EE35ACCA1E3B77D600A02D78 /* XCTAsyncActivity-Protocol.h */,
				EE35ACCB1E3B77D600A02D78 /* XCTAsyncActivity.h */,
				EE35ACCC1E3B77D600A02D78 /* XCTAutomationTarget-Protocol.h */,
				EE35ACCD1E3B77D600A02D78 /* XCTAXClient-Protocol.h */,
				EE35ACCE1E3B77D600A02D78 /* XCTDarwinNotificationExpectation.h */,
				EE35ACCF1E3B77D600A02D78 /* XCTest.h */,
				EE35ACD01E3B77D600A02D78 /* XCTestCase.h */,
				EE35ACD11E3B77D600A02D78 /* XCTestCaseRun.h */,
				EE35ACD21E3B77D600A02D78 /* XCTestCaseSuite.h */,
				EE35ACD31E3B77D600A02D78 /* XCTestConfiguration.h */,
				EE35ACD41E3B77D600A02D78 /* XCTestContext.h */,
				EE35ACD51E3B77D600A02D78 /* XCTestContextScope.h */,
				EE35ACD61E3B77D600A02D78 /* XCTestDriver.h */,
				EE35ACD71E3B77D600A02D78 /* XCTestDriverInterface-Protocol.h */,
				EE35ACD81E3B77D600A02D78 /* XCTestExpectation.h */,
				EE35ACD91E3B77D600A02D78 /* XCTestExpectationDelegate-Protocol.h */,
				EE35ACDA1E3B77D600A02D78 /* XCTestExpectationWaiter.h */,
				EE35ACDB1E3B77D600A02D78 /* XCTestLog.h */,
				EE35ACDC1E3B77D600A02D78 /* XCTestManager_IDEInterface-Protocol.h */,
				EE35ACDD1E3B77D600A02D78 /* XCTestManager_ManagerInterface-Protocol.h */,
				EE35ACDE1E3B77D600A02D78 /* XCTestManager_TestsInterface-Protocol.h */,
				EE35ACDF1E3B77D600A02D78 /* XCTestMisuseObserver.h */,
				EE35ACE01E3B77D600A02D78 /* XCTestObservation-Protocol.h */,
				EE35ACE11E3B77D600A02D78 /* XCTestObservationCenter.h */,
				EE35ACE21E3B77D600A02D78 /* XCTestObserver.h */,
				EE35ACE31E3B77D600A02D78 /* XCTestProbe.h */,
				EE35ACE41E3B77D600A02D78 /* XCTestRun.h */,
				EE35ACE51E3B77D600A02D78 /* XCTestSuite.h */,
				EE35ACE61E3B77D600A02D78 /* XCTestSuiteRun.h */,
				EE35ACE71E3B77D600A02D78 /* XCTestWaiter.h */,
				EE35ACE81E3B77D600A02D78 /* XCTKVOExpectation.h */,
				EE35ACE91E3B77D600A02D78 /* XCTMetric.h */,
				EE35ACEA1E3B77D600A02D78 /* XCTNSNotificationExpectation.h */,
				EE35ACEB1E3B77D600A02D78 /* XCTNSPredicateExpectation.h */,
				EE35ACEC1E3B77D600A02D78 /* XCTNSPredicateExpectationObject-Protocol.h */,
				EE35ACEE1E3B77D600A02D78 /* XCTRunnerAutomationSession.h */,
				EE35ACEF1E3B77D600A02D78 /* XCTRunnerDaemonSession.h */,
				EE35ACF01E3B77D600A02D78 /* XCTRunnerIDESession.h */,
				EE35ACF11E3B77D600A02D78 /* XCTTestRunSession.h */,
				EE35ACF21E3B77D600A02D78 /* XCTTestRunSessionDelegate-Protocol.h */,
				EE35ACF31E3B77D600A02D78 /* XCTUIApplicationMonitor-Protocol.h */,
				EE35ACF41E3B77D600A02D78 /* XCTWaiter.h */,
				EE35ACF51E3B77D600A02D78 /* XCTWaiterDelegate-Protocol.h */,
				EE35ACF61E3B77D600A02D78 /* XCTWaiterDelegatePrivate-Protocol.h */,
				EE35ACF71E3B77D600A02D78 /* XCTWaiterManagement-Protocol.h */,
				EE35ACF81E3B77D600A02D78 /* XCTWaiterManager.h */,
				EE35ACF91E3B77D600A02D78 /* XCUIApplication.h */,
				EE35ACFA1E3B77D600A02D78 /* XCUIApplicationImpl.h */,
				EE35ACFB1E3B77D600A02D78 /* XCUIApplicationProcess.h */,
				EE35ACFC1E3B77D600A02D78 /* XCUICoordinate.h */,
				EE35ACFD1E3B77D600A02D78 /* XCUIDevice.h */,
				EE35ACFE1E3B77D600A02D78 /* XCUIElement.h */,
				EE35ACFF1E3B77D600A02D78 /* XCUIElementAsynchronousHandlerWrapper.h */,
				EE35AD011E3B77D600A02D78 /* XCUIElementHitPointCoordinate.h */,
				EE35AD021E3B77D600A02D78 /* XCUIElementQuery.h */,
				1357E295233D05240054BDB8 /* XCUIHitPointResult.h */,
				EE35AD041E3B77D600A02D78 /* XCUIRecorderNodeFinder.h */,
				EE35AD051E3B77D600A02D78 /* XCUIRecorderNodeFinderMatch.h */,
				EE35AD061E3B77D600A02D78 /* XCUIRecorderTimingMessage.h */,
				EE35AD071E3B77D600A02D78 /* XCUIRecorderUtilities.h */,
				1BA7DD8C206D694B007C7C26 /* XCTElementSetTransformer-Protocol.h */,
				7119097B2152580600BA3C7E /* XCUIScreen.h */,
				711CD03325ED1106001C01D2 /* XCUIScreenDataSource-Protocol.h */,
			);
			path = XCTest;
			sourceTree = "<group>";
		};
		EEE5CABE1C80361500CBBDD9 /* Configurations */ = {
			isa = PBXGroup;
			children = (
				717C0D862518ED7000CAA6EC /* TVOSTestSettings.xcconfig */,
				717C0D702518ED2800CAA6EC /* TVOSSettings.xcconfig */,
				71649EC82518C19C0087F212 /* IOSTestSettings.xcconfig */,
				EEE5CABF1C80361500CBBDD9 /* IOSSettings.xcconfig */,
			);
			path = Configurations;
			sourceTree = "<group>";
		};
		EEF988341C486655005CA669 /* WebDriverAgentRunner */ = {
			isa = PBXGroup;
			children = (
				EE9AB7FC1CAEE048008C271F /* Info.plist */,
				EE9AB7FD1CAEE048008C271F /* UITestingUITests.m */,
			);
			name = WebDriverAgentRunner;
			path = XCTUITestRunner;
			sourceTree = SOURCE_ROOT;
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		641EE6302240C5CA00173FCB /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = **********;
			files = (
				641EE6312240C5CA00173FCB /* XCUIElement+FBWebDriverAttributes.h in Headers */,
				7182274A258744BE00661B83 /* HTTPMessage.h in Headers */,
				641EE6322240C5CA00173FCB /* FBScreen.h in Headers */,
				641EE6332240C5CA00173FCB /* XCTestPrivateSymbols.h in Headers */,
				641EE6342240C5CA00173FCB /* XCUIElement+FBTyping.h in Headers */,
				7182270B258744A700661B83 /* Route.h in Headers */,
				641EE6352240C5CA00173FCB /* XCUIElement+FBUtilities.h in Headers */,
				641EE6362240C5CA00173FCB /* XCUIElement+FBScrolling.h in Headers */,
				1357E297233D05240054BDB8 /* XCUIHitPointResult.h in Headers */,
				716C9DFB27315D21005AD475 /* FBReflectionUtils.h in Headers */,
				641EE6372240C5CA00173FCB /* XCSourceCodeTreeNode.h in Headers */,
				641EE6382240C5CA00173FCB /* XCPointerEventPath.h in Headers */,
				641EE6392240C5CA00173FCB /* FBRouteRequest.h in Headers */,
				648C10AC22AAAD9C00B81B9A /* UIKeyboardImpl.h in Headers */,
				718226CD2587443700661B83 /* GCDAsyncSocket.h in Headers */,
				13DE7A50287C46BB003243C6 /* FBXCElementSnapshot.h in Headers */,
				13DE7A56287CA1EC003243C6 /* FBXCElementSnapshotWrapper.h in Headers */,
				71F3E7D525417FF400E0C22B /* FBSettings.h in Headers */,
				641EE63A2240C5CA00173FCB /* XCTest.h in Headers */,
				641EE63B2240C5CA00173FCB /* FBAlertsMonitor.h in Headers */,
				641EE63D2240C5CA00173FCB /* FBSession.h in Headers */,
				641EE63E2240C5CA00173FCB /* _XCTestImplementation.h in Headers */,
				641EE63F2240C5CA00173FCB /* FBTouchActionCommands.h in Headers */,
				641EE6402240C5CA00173FCB /* FBTouchIDCommands.h in Headers */,
				714D88CD2733FB970074A925 /* FBXMLGenerationOptions.h in Headers */,
				641EE6412240C5CA00173FCB /* XCUIApplication.h in Headers */,
				641EE6422240C5CA00173FCB /* FBCustomCommands.h in Headers */,
				641EE6432240C5CA00173FCB /* _XCTestCaseInterruptionException.h in Headers */,
				641EE6442240C5CA00173FCB /* FBOrientationCommands.h in Headers */,
				641EE6452240C5CA00173FCB /* XCUIScreen.h in Headers */,
				641EE6462240C5CA00173FCB /* XCTRunnerIDESession.h in Headers */,
				641EE6472240C5CA00173FCB /* FBRouteRequest-Private.h in Headers */,
				71D475C32538F5A8008D9401 /* XCUIApplicationProcess+FBQuiescence.h in Headers */,
				641EE6482240C5CA00173FCB /* XCTTestRunSession.h in Headers */,
				641EE6492240C5CA00173FCB /* XCTestProbe.h in Headers */,
				641EE64A2240C5CA00173FCB /* XCApplicationQuery.h in Headers */,
				641EE64B2240C5CA00173FCB /* XCTAsyncActivity.h in Headers */,
				641EE64C2240C5CA00173FCB /* XCTestMisuseObserver.h in Headers */,
				641EE64D2240C5CA00173FCB /* XCTRunnerDaemonSession.h in Headers */,
				714E14B929805CAE00375DD7 /* XCAXClient_iOS+FBSnapshotReqParams.h in Headers */,
				64B2650B228CE4FF002A5025 /* FBTVNavigationTracker-Private.h in Headers */,
				641EE64F2240C5CA00173FCB /* XCTestExpectationWaiter.h in Headers */,
				13DE7A5C287CA444003243C6 /* FBXCElementSnapshotWrapper+Helpers.h in Headers */,
				641EE6502240C5CA00173FCB /* UIGestureRecognizer-RecordingAdditions.h in Headers */,
				71BB58E92B96328700CB9BFE /* FBScreenRecordingRequest.h in Headers */,
				641EE6512240C5CA00173FCB /* XCKeyboardKeyMap.h in Headers */,
				641EE6522240C5CA00173FCB /* XCTNSPredicateExpectationObject-Protocol.h in Headers */,
				641EE6532240C5CA00173FCB /* WebDriverAgentLib.h in Headers */,
				641EE6542240C5CA00173FCB /* FBFindElementCommands.h in Headers */,
				641EE6552240C5CA00173FCB /* XCTestRun.h in Headers */,
				641EE6562240C5CA00173FCB /* FBWebServer.h in Headers */,
				641EE6572240C5CA00173FCB /* FBScreenshotCommands.h in Headers */,
				641EE6582240C5CA00173FCB /* _XCKVOExpectationImplementation.h in Headers */,
				641EE6592240C5CA00173FCB /* NSString+FBVisualLength.h in Headers */,
				641EE65A2240C5CA00173FCB /* FBXCTestDaemonsProxy.h in Headers */,
				641EE65B2240C5CA00173FCB /* XCUIElementHitPointCoordinate.h in Headers */,
				641EE65C2240C5CA00173FCB /* XCTDarwinNotificationExpectation.h in Headers */,
				641EE65D2240C5CA00173FCB /* XCTRunnerAutomationSession.h in Headers */,
				641EE65F2240C5CA00173FCB /* XCSourceCodeTreeNodeEnumerator.h in Headers */,
				641EE6602240C5CA00173FCB /* XCUIElement+FBIsVisible.h in Headers */,
				641EE6622240C5CA00173FCB /* FBResponsePayload.h in Headers */,
				71BB58E22B9631F100CB9BFE /* FBScreenRecordingPromise.h in Headers */,
				641EE6632240C5CA00173FCB /* FBUnknownCommands.h in Headers */,
				641EE7062240CDCF00173FCB /* XCUIElement+FBTVFocuse.h in Headers */,
				71822738258744B800661B83 /* HTTPConnection.h in Headers */,
				641EE6642240C5CA00173FCB /* NSPredicate+FBFormat.h in Headers */,
				641EE6652240C5CA00173FCB /* UILongPressGestureRecognizer-RecordingAdditions.h in Headers */,
				641EE6662240C5CA00173FCB /* XCTestCase.h in Headers */,
				641EE6672240C5CA00173FCB /* XCSymbolicatorHolder.h in Headers */,
				641EE6682240C5CA00173FCB /* XCUIApplicationImpl.h in Headers */,
				71414ED72670A1EE003A8C5D /* LRUCacheNode.h in Headers */,
				641EE6692240C5CA00173FCB /* UIPanGestureRecognizer-RecordingAdditions.h in Headers */,
				13815F702328D20400CDAB61 /* FBActiveAppDetectionPoint.h in Headers */,
				641EE66A2240C5CA00173FCB /* NSExpression+FBFormat.h in Headers */,
				641EE66B2240C5CA00173FCB /* _XCTestCaseImplementation.h in Headers */,
				641EE66C2240C5CA00173FCB /* UIPinchGestureRecognizer-RecordingAdditions.h in Headers */,
				641EE66D2240C5CA00173FCB /* XCTestManager_TestsInterface-Protocol.h in Headers */,
				641EE66E2240C5CA00173FCB /* XCUIApplication+FBAlert.h in Headers */,
				716C9E0127315EFF005AD475 /* XCUIApplication+FBUIInterruptions.h in Headers */,
				7182275C258744C300661B83 /* HTTPServer.h in Headers */,
				641EE6702240C5CA00173FCB /* FBMathUtils.h in Headers */,
				641EE6712240C5CA00173FCB /* UISwipeGestureRecognizer-RecordingAdditions.h in Headers */,
				641EE6722240C5CA00173FCB /* FBElementUtils.h in Headers */,
				641EE6732240C5CA00173FCB /* FBDebugCommands.h in Headers */,
				641EE6742240C5CA00173FCB /* XCTestSuite.h in Headers */,
				641EE6752240C5CA00173FCB /* XCUICoordinate.h in Headers */,
				715A84D22DD92AD3007134CC /* FBElementHelpers.h in Headers */,
				641EE6762240C5CA00173FCB /* XCTNSPredicateExpectation.h in Headers */,
				641EE6772240C5CA00173FCB /* XCTestObservationCenter.h in Headers */,
				641EE6782240C5CA00173FCB /* XCTNSNotificationExpectation.h in Headers */,
				641EE6792240C5CA00173FCB /* XCUIRecorderNodeFinder.h in Headers */,
				641EE67A2240C5CA00173FCB /* XCUIElement+FBAccessibility.h in Headers */,
				0E04133C2DF1E15900AF007C /* XCUIElement+FBMinMax.h in Headers */,
				641EE67B2240C5CA00173FCB /* XCUIRecorderUtilities.h in Headers */,
				6496A5DA230D6EB30087F8CB /* AXSettings.h in Headers */,
				641EE67C2240C5CA00173FCB /* XCTestCaseRun.h in Headers */,
				71A5C67429A4F39600421C37 /* XCTIssue+FBPatcher.h in Headers */,
				641EE67D2240C5CA00173FCB /* XCTestConfiguration.h in Headers */,
				641EE67E2240C5CA00173FCB /* _XCTDarwinNotificationExpectationImplementation.h in Headers */,
				641EE67F2240C5CA00173FCB /* XCTestExpectation.h in Headers */,
				641EE6802240C5CA00173FCB /* FBElementTypeTransformer.h in Headers */,
				641EE6812240C5CA00173FCB /* FBXCAXClientProxy.h in Headers */,
				641EE6822240C5CA00173FCB /* FBElementCache.h in Headers */,
				641EE6832240C5CA00173FCB /* XCTMetric.h in Headers */,
				641EE6842240C5CA00173FCB /* XCTestContextScope.h in Headers */,
				7182271D258744AB00661B83 /* RouteResponse.h in Headers */,
				641EE6852240C5CA00173FCB /* XCUIElement+FBClassChain.h in Headers */,
				13DE7A44287C2A8D003243C6 /* FBXCAccessibilityElement.h in Headers */,
				641EE6862240C5CA00173FCB /* FBResponseJSONPayload.h in Headers */,
				71822714258744A900661B83 /* RouteRequest.h in Headers */,
				641EE6872240C5CA00173FCB /* XCTAutomationTarget-Protocol.h in Headers */,
				641EE6882240C5CA00173FCB /* FBElement.h in Headers */,
				641EE6892240C5CA00173FCB /* XCTAXClient-Protocol.h in Headers */,
				641EE68B2240C5CA00173FCB /* FBExceptionHandler.h in Headers */,
				71822726258744AE00661B83 /* RoutingConnection.h in Headers */,
				641EE68C2240C5CA00173FCB /* FBRoute.h in Headers */,
				641EE68D2240C5CA00173FCB /* XCTestDriver.h in Headers */,
				641EE68E2240C5CA00173FCB /* _XCTNSNotificationExpectationImplementation.h in Headers */,
				641EE68F2240C5CA00173FCB /* XCSynthesizedEventRecord.h in Headers */,
				641EE6922240C5CA00173FCB /* XCTWaiterDelegatePrivate-Protocol.h in Headers */,
				641EE6932240C5CA00173FCB /* XCTestManager_IDEInterface-Protocol.h in Headers */,
				71822753258744C100661B83 /* HTTPResponse.h in Headers */,
				641EE6942240C5CA00173FCB /* FBXPath.h in Headers */,
				641EE6952240C5CA00173FCB /* XCUIRecorderTimingMessage.h in Headers */,
				641EE6962240C5CA00173FCB /* XCApplicationMonitor.h in Headers */,
				641EE6972240C5CA00173FCB /* XCUIElement+FBForceTouch.h in Headers */,
				641EE6982240C5CA00173FCB /* FBRuntimeUtils.h in Headers */,
				71F5BE50252F14EB00EE9EBA /* FBExceptions.h in Headers */,
				641EE6992240C5CA00173FCB /* XCUIElement+FBPickerWheel.h in Headers */,
				641EE69A2240C5CA00173FCB /* XCTestObservation-Protocol.h in Headers */,
				641EE69B2240C5CA00173FCB /* _XCTNSPredicateExpectationImplementation.h in Headers */,
				641EE69C2240C5CA00173FCB /* FBElementCommands.h in Headers */,
				641EE69F2240C5CA00173FCB /* FBTCPSocket.h in Headers */,
				641EE6A02240C5CA00173FCB /* XCUIElement+FBUID.h in Headers */,
				641EE6A12240C5CA00173FCB /* XCSymbolicationRecord.h in Headers */,
				641EE6A22240C5CA00173FCB /* XCUIDevice.h in Headers */,
				7182272F258744B000661B83 /* RoutingHTTPServer.h in Headers */,
				641EE6A32240C5CA00173FCB /* XCUIApplication+FBTouchAction.h in Headers */,
				641EE6A42240C5CA00173FCB /* FBCommandHandler.h in Headers */,
				641EE6A52240C5CA00173FCB /* FBSessionCommands.h in Headers */,
				641EE70C2240CE2D00173FCB /* FBTVNavigationTracker.h in Headers */,
				71AE3CF72D38EE8E0039FC36 /* XCUIElement+FBVisibleFrame.h in Headers */,
				641EE6A62240C5CA00173FCB /* FBImageProcessor.h in Headers */,
				641EE6A72240C5CA00173FCB /* FBSession-Private.h in Headers */,
				641EE6A82240C5CA00173FCB /* NSString+FBXMLSafeString.h in Headers */,
				B316351F2DDF0D0B007D9317 /* FBAccessibilityTraits.h in Headers */,
				64E3502F2AC0B6FE005F3ACB /* NSDictionary+FBUtf8SafeDictionary.h in Headers */,
				641EE6A92240C5CA00173FCB /* FBCommandStatus.h in Headers */,
				71822702258744A400661B83 /* HTTPResponseProxy.h in Headers */,
				71822741258744BB00661B83 /* HTTPLogging.h in Headers */,
				641EE6AB2240C5CA00173FCB /* FBAlertViewCommands.h in Headers */,
				641EE6AC2240C5CA00173FCB /* XCTWaiter.h in Headers */,
				641EE6AD2240C5CA00173FCB /* XCTWaiterManagement-Protocol.h in Headers */,
				641EE6AF2240C5CA00173FCB /* XCTestContext.h in Headers */,
				71C9EAAD25E8415A00470CD8 /* FBScreenshot.h in Headers */,
				641EE6B12240C5CA00173FCB /* XCTWaiterDelegate-Protocol.h in Headers */,
				641EE6B22240C5CA00173FCB /* _XCTestExpectationImplementation.h in Headers */,
				641EE6B32240C5CA00173FCB /* XCAXClient_iOS.h in Headers */,
				641EE6B42240C5CA00173FCB /* XCTWaiterManager.h in Headers */,
				641EE6B52240C5CA00173FCB /* XCTestDriverInterface-Protocol.h in Headers */,
				648C10B022AAAE4000B81B9A /* TIPreferencesController.h in Headers */,
				71F5BE24252E576C00EE9EBA /* XCUIElement+FBSwiping.h in Headers */,
				641EE6B62240C5CA00173FCB /* _XCTestSuiteImplementation.h in Headers */,
				641EE6B72240C5CA00173FCB /* FBBaseActionsSynthesizer.h in Headers */,
				7182276E258744C900661B83 /* HTTPErrorResponse.h in Headers */,
				641EE6B82240C5CA00173FCB /* FBAlert.h in Headers */,
				641EE6B92240C5CA00173FCB /* XCUIElementQuery.h in Headers */,
				71BB58F02B96511800CB9BFE /* FBVideoCommands.h in Headers */,
				641EE6BA2240C5CA00173FCB /* XCPointerEvent.h in Headers */,
				718F49C923087ACF0045FE8B /* FBProtocolHelpers.h in Headers */,
				641EE6BB2240C5CA00173FCB /* XCSourceCodeRecording.h in Headers */,
				641EE6BC2240C5CA00173FCB /* FBRunLoopSpinner.h in Headers */,
				641EE6BD2240C5CA00173FCB /* FBErrorBuilder.h in Headers */,
				641EE6BE2240C5CA00173FCB /* XCApplicationMonitor_iOS.h in Headers */,
				13DE7A4A287C4005003243C6 /* FBXCDeviceEvent.h in Headers */,
				641EE6BF2240C5CA00173FCB /* FBKeyboard.h in Headers */,
				71E75E6E254824230099FC87 /* XCUIElementQuery+FBHelpers.h in Headers */,
				641EE6C02240C5CA00173FCB /* XCUIApplication+FBHelpers.h in Headers */,
				641EE6C12240C5CA00173FCB /* _XCTestObservationCenterImplementation.h in Headers */,
				714EAA0E2673FDFE005C5B47 /* FBCapabilities.h in Headers */,
				641EE6C22240C5CA00173FCB /* XCUIDevice+FBHelpers.h in Headers */,
				71D3B3D6267FC7260076473D /* XCUIElement+FBResolve.h in Headers */,
				641EE6C32240C5CA00173FCB /* FBClassChainQueryParser.h in Headers */,
				641EE6C42240C5CA00173FCB /* FBMacros.h in Headers */,
				641EE6C52240C5CA00173FCB /* XCTestExpectationDelegate-Protocol.h in Headers */,
				641EE6C62240C5CA00173FCB /* XCTUIApplicationMonitor-Protocol.h in Headers */,
				71822777258744CE00661B83 /* DDNumber.h in Headers */,
				641EE6C82240C5CA00173FCB /* XCTKVOExpectation.h in Headers */,
				641EE6C92240C5CA00173FCB /* XCUIDevice+FBRotation.h in Headers */,
				641EE6CA2240C5CA00173FCB /* XCEventGenerator.h in Headers */,
				719DCF162601EAFB000E765F /* FBNotificationsHelper.h in Headers */,
				71414ED52670A1EE003A8C5D /* LRUCache.h in Headers */,
				641EE6CB2240C5CA00173FCB /* FBConfiguration.h in Headers */,
				641EE6CC2240C5CA00173FCB /* XCTestSuiteRun.h in Headers */,
				641EE6CD2240C5CA00173FCB /* XCUIElementAsynchronousHandlerWrapper.h in Headers */,
				641EE6CE2240C5CA00173FCB /* XCTestLog.h in Headers */,
				C845206222D5E79400EA68CB /* FBUnattachedAppLauncher.h in Headers */,
				641EE6CF2240C5CA00173FCB /* UITapGestureRecognizer-RecordingAdditions.h in Headers */,
				641EE6D02240C5CA00173FCB /* XCDebugLogDelegate-Protocol.h in Headers */,
				641EE6D12240C5CA00173FCB /* NSString-XCTAdditions.h in Headers */,
				641EE6D22240C5CA00173FCB /* XCTestWaiter.h in Headers */,
				641EE6D32240C5CA00173FCB /* FBImageUtils.h in Headers */,
				641EE6D42240C5CA00173FCB /* NSValue-XCTestAdditions.h in Headers */,
				641EE6D52240C5CA00173FCB /* _XCTWaiterImpl.h in Headers */,
				641EE6D62240C5CA00173FCB /* FBLogger.h in Headers */,
				71BB58F72B96531900CB9BFE /* FBScreenRecordingContainer.h in Headers */,
				641EE6D72240C5CA00173FCB /* XCTestObserver.h in Headers */,
				641EE6D82240C5CA00173FCB /* XCUIElement.h in Headers */,
				641EE6D92240C5CA00173FCB /* XCKeyboardInputSolver.h in Headers */,
				718226CB2587443700661B83 /* GCDAsyncUdpSocket.h in Headers */,
				641EE6DB2240C5CA00173FCB /* FBPasteboard.h in Headers */,
				711CD03525ED1106001C01D2 /* XCUIScreenDataSource-Protocol.h in Headers */,
				641EE6DD2240C5CA00173FCB /* FBDebugLogDelegateDecorator.h in Headers */,
				641EE6DE2240C5CA00173FCB /* XCUIDevice+FBHealthCheck.h in Headers */,
				641EE6DF2240C5CA00173FCB /* FBMjpegServer.h in Headers */,
				641EE6E02240C5CA00173FCB /* XCUIRecorderNodeFinderMatch.h in Headers */,
				641EE6E12240C5CA00173FCB /* XCUIApplicationProcess.h in Headers */,
				641EE6E22240C5CA00173FCB /* FBW3CActionsSynthesizer.h in Headers */,
				641EE6E32240C5CA00173FCB /* CDStructures.h in Headers */,
				71822780258744D000661B83 /* DDRange.h in Headers */,
				641EE6E42240C5CA00173FCB /* XCKeyboardLayout.h in Headers */,
				641EE6E52240C5CA00173FCB /* XCTAsyncActivity-Protocol.h in Headers */,
				641EE6E62240C5CA00173FCB /* XCActivityRecord.h in Headers */,
				71822765258744C700661B83 /* HTTPDataResponse.h in Headers */,
				641EE6E72240C5CA00173FCB /* XCUIElement+FBFind.h in Headers */,
				641EE6E82240C5CA00173FCB /* XCTestManager_ManagerInterface-Protocol.h in Headers */,
				641EE6E92240C5CA00173FCB /* FBFailureProofTestCase.h in Headers */,
				641EE6EA2240C5CA00173FCB /* XCTTestRunSessionDelegate-Protocol.h in Headers */,
				641EE6EB2240C5CA00173FCB /* XCTestCaseSuite.h in Headers */,
				641EE6EC2240C5CA00173FCB /* _XCInternalTestRun.h in Headers */,
				641EE6ED2240C5CA00173FCB /* FBXPath-Private.h in Headers */,
				71D04DC925356C43008A052C /* XCUIElement+FBCaching.h in Headers */,
				641EE6EE2240C5CA00173FCB /* XCKeyMappingPath.h in Headers */,
				71C8E55225399A6B008572C1 /* XCUIApplication+FBQuiescence.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		EE158A961CBD452B00A3E3F0 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = **********;
			files = (
				EEE376491D59FAE900ED88DD /* XCUIElement+FBWebDriverAttributes.h in Headers */,
				715AFAC11FFA29180053896D /* FBScreen.h in Headers */,
				EE6B64FD1D0F86EF00E85F5D /* XCTestPrivateSymbols.h in Headers */,
				AD76723D1D6B7CC000610457 /* XCUIElement+FBTyping.h in Headers */,
				EEE376451D59F81400ED88DD /* XCUIElement+FBUtilities.h in Headers */,
				EE158AB21CBD456F00A3E3F0 /* XCUIElement+FBScrolling.h in Headers */,
				EE35AD361E3B77D600A02D78 /* XCSourceCodeTreeNode.h in Headers */,
				EE35AD341E3B77D600A02D78 /* XCPointerEventPath.h in Headers */,
				713AE575243A53BE0000D657 /* FBW3CActionsHelpers.h in Headers */,
				EE158AE11CBD456F00A3E3F0 /* FBRouteRequest.h in Headers */,
				71F5BE4F252F14EB00EE9EBA /* FBExceptions.h in Headers */,
				648C10AB22AAAD9C00B81B9A /* UIKeyboardImpl.h in Headers */,
				EE35AD401E3B77D600A02D78 /* XCTest.h in Headers */,
				716C9E0027315EFF005AD475 /* XCUIApplication+FBUIInterruptions.h in Headers */,
				719CD8F82126C78F00C7D0C2 /* FBAlertsMonitor.h in Headers */,
				EE158AE41CBD456F00A3E3F0 /* FBSession.h in Headers */,
				13DE7A55287CA1EC003243C6 /* FBXCElementSnapshotWrapper.h in Headers */,
				EE35AD0F1E3B77D600A02D78 /* _XCTestImplementation.h in Headers */,
				71241D7B1FAE3D2500B9559F /* FBTouchActionCommands.h in Headers */,
				EE158ACA1CBD456F00A3E3F0 /* FBTouchIDCommands.h in Headers */,
				EE35AD6A1E3B77D600A02D78 /* XCUIApplication.h in Headers */,
				EE158ABA1CBD456F00A3E3F0 /* FBCustomCommands.h in Headers */,
				EE35AD0D1E3B77D600A02D78 /* _XCTestCaseInterruptionException.h in Headers */,
				EE158AC41CBD456F00A3E3F0 /* FBOrientationCommands.h in Headers */,
				71BB58EF2B96511800CB9BFE /* FBVideoCommands.h in Headers */,
				7119097C2152580600BA3C7E /* XCUIScreen.h in Headers */,
				EE35AD611E3B77D600A02D78 /* XCTRunnerIDESession.h in Headers */,
				EE158AE01CBD456F00A3E3F0 /* FBRouteRequest-Private.h in Headers */,
				EE35AD621E3B77D600A02D78 /* XCTTestRunSession.h in Headers */,
				EE35AD541E3B77D600A02D78 /* XCTestProbe.h in Headers */,
				EE35AD281E3B77D600A02D78 /* XCApplicationQuery.h in Headers */,
				E444DCB124913C220060D7EB /* RoutingConnection.h in Headers */,
				EE35AD3C1E3B77D600A02D78 /* XCTAsyncActivity.h in Headers */,
				EE35AD501E3B77D600A02D78 /* XCTestMisuseObserver.h in Headers */,
				EE35AD601E3B77D600A02D78 /* XCTRunnerDaemonSession.h in Headers */,
				71414ED62670A1EE003A8C5D /* LRUCacheNode.h in Headers */,
				64B2650A228CE4FF002A5025 /* FBTVNavigationTracker-Private.h in Headers */,
				71B155DF23080CA600646AFB /* FBProtocolHelpers.h in Headers */,
				EE35AD4B1E3B77D600A02D78 /* XCTestExpectationWaiter.h in Headers */,
				EE35AD1E1E3B77D600A02D78 /* UIGestureRecognizer-RecordingAdditions.h in Headers */,
				6496A5D9230D6EB30087F8CB /* AXSettings.h in Headers */,
				EE35AD301E3B77D600A02D78 /* XCKeyboardKeyMap.h in Headers */,
				EE35AD5D1E3B77D600A02D78 /* XCTNSPredicateExpectationObject-Protocol.h in Headers */,
				714E14B829805CAE00375DD7 /* XCAXClient_iOS+FBSnapshotReqParams.h in Headers */,
				EE158B5F1CBD47A000A3E3F0 /* WebDriverAgentLib.h in Headers */,
				EE158AC01CBD456F00A3E3F0 /* FBFindElementCommands.h in Headers */,
				71D475C22538F5A8008D9401 /* XCUIApplicationProcess+FBQuiescence.h in Headers */,
				EE35AD551E3B77D600A02D78 /* XCTestRun.h in Headers */,
				EE158AE61CBD456F00A3E3F0 /* FBWebServer.h in Headers */,
				EE158AC61CBD456F00A3E3F0 /* FBScreenshotCommands.h in Headers */,
				EE35AD0A1E3B77D600A02D78 /* _XCKVOExpectationImplementation.h in Headers */,
				EE0D1F611EBCDCF7006A3123 /* NSString+FBVisualLength.h in Headers */,
				EE35AD7B1E3B80C000A02D78 /* FBXCTestDaemonsProxy.h in Headers */,
				EE35AD721E3B77D600A02D78 /* XCUIElementHitPointCoordinate.h in Headers */,
				EE35AD3F1E3B77D600A02D78 /* XCTDarwinNotificationExpectation.h in Headers */,
				EE35AD5F1E3B77D600A02D78 /* XCTRunnerAutomationSession.h in Headers */,
				13DE7A4F287C46BB003243C6 /* FBXCElementSnapshot.h in Headers */,
				71C9EAAC25E8415A00470CD8 /* FBScreenshot.h in Headers */,
				EE35AD371E3B77D600A02D78 /* XCSourceCodeTreeNodeEnumerator.h in Headers */,
				EE158AB01CBD456F00A3E3F0 /* XCUIElement+FBIsVisible.h in Headers */,
				71414ED42670A1EE003A8C5D /* LRUCache.h in Headers */,
				EE158ADC1CBD456F00A3E3F0 /* FBResponsePayload.h in Headers */,
				13815F6F2328D20400CDAB61 /* FBActiveAppDetectionPoint.h in Headers */,
				EE158ACC1CBD456F00A3E3F0 /* FBUnknownCommands.h in Headers */,
				641EE7052240CDCF00173FCB /* XCUIElement+FBTVFocuse.h in Headers */,
				71A224E51DE2F56600844D55 /* NSPredicate+FBFormat.h in Headers */,
				EE35AD1F1E3B77D600A02D78 /* UILongPressGestureRecognizer-RecordingAdditions.h in Headers */,
				EE35AD411E3B77D600A02D78 /* XCTestCase.h in Headers */,
				EE35AD391E3B77D600A02D78 /* XCSymbolicatorHolder.h in Headers */,
				EE35AD6B1E3B77D600A02D78 /* XCUIApplicationImpl.h in Headers */,
				EE35AD201E3B77D600A02D78 /* UIPanGestureRecognizer-RecordingAdditions.h in Headers */,
				71555A3D1DEC460A007D4A8B /* NSExpression+FBFormat.h in Headers */,
				71D3B3D5267FC7260076473D /* XCUIElement+FBResolve.h in Headers */,
				EE35AD0C1E3B77D600A02D78 /* _XCTestCaseImplementation.h in Headers */,
				EE35AD211E3B77D600A02D78 /* UIPinchGestureRecognizer-RecordingAdditions.h in Headers */,
				EE35AD4F1E3B77D600A02D78 /* XCTestManager_TestsInterface-Protocol.h in Headers */,
				719CD8FC2126C88B00C7D0C2 /* XCUIApplication+FBAlert.h in Headers */,
				EE18883A1DA661C400307AA8 /* FBMathUtils.h in Headers */,
				13DE7A49287C4005003243C6 /* FBXCDeviceEvent.h in Headers */,
				71B155DA23070ECF00646AFB /* FBHTTPStatusCodes.h in Headers */,
				EE35AD221E3B77D600A02D78 /* UISwipeGestureRecognizer-RecordingAdditions.h in Headers */,
				713C6DCF1DDC772A00285B92 /* FBElementUtils.h in Headers */,
				EE158ABC1CBD456F00A3E3F0 /* FBDebugCommands.h in Headers */,
				EE35AD561E3B77D600A02D78 /* XCTestSuite.h in Headers */,
				EE35AD6D1E3B77D600A02D78 /* XCUICoordinate.h in Headers */,
				714EAA0D2673FDFE005C5B47 /* FBCapabilities.h in Headers */,
				EE35AD5C1E3B77D600A02D78 /* XCTNSPredicateExpectation.h in Headers */,
				EE35AD521E3B77D600A02D78 /* XCTestObservationCenter.h in Headers */,
				71AE3CF92D38EE8E0039FC36 /* XCUIElement+FBVisibleFrame.h in Headers */,
				EE35AD5B1E3B77D600A02D78 /* XCTNSNotificationExpectation.h in Headers */,
				E444DC97249131D40060D7EB /* HTTPServer.h in Headers */,
				E444DCAE24913C220060D7EB /* HTTPResponseProxy.h in Headers */,
				EE35AD751E3B77D600A02D78 /* XCUIRecorderNodeFinder.h in Headers */,
				1357E296233D05240054BDB8 /* XCUIHitPointResult.h in Headers */,
				711CD03425ED1106001C01D2 /* XCUIScreenDataSource-Protocol.h in Headers */,
				EE158AAE1CBD456F00A3E3F0 /* XCUIElement+FBAccessibility.h in Headers */,
				EE35AD781E3B77D600A02D78 /* XCUIRecorderUtilities.h in Headers */,
				EE35AD421E3B77D600A02D78 /* XCTestCaseRun.h in Headers */,
				EE35AD441E3B77D600A02D78 /* XCTestConfiguration.h in Headers */,
				715A84D02DD92AD3007134CC /* FBElementHelpers.h in Headers */,
				EE35AD0B1E3B77D600A02D78 /* _XCTDarwinNotificationExpectationImplementation.h in Headers */,
				718226CA2587443700661B83 /* GCDAsyncUdpSocket.h in Headers */,
				EE35AD491E3B77D600A02D78 /* XCTestExpectation.h in Headers */,
				EE158AE81CBD456F00A3E3F0 /* FBElementTypeTransformer.h in Headers */,
				7157B291221DADD2001C348C /* FBXCAXClientProxy.h in Headers */,
				EE158AD21CBD456F00A3E3F0 /* FBElementCache.h in Headers */,
				EE35AD5A1E3B77D600A02D78 /* XCTMetric.h in Headers */,
				EE35AD461E3B77D600A02D78 /* XCTestContextScope.h in Headers */,
				71BB58F62B96531900CB9BFE /* FBScreenRecordingContainer.h in Headers */,
				71A7EAF51E20516B001DA4F2 /* XCUIElement+FBClassChain.h in Headers */,
				EE158ADA1CBD456F00A3E3F0 /* FBResponseJSONPayload.h in Headers */,
				EE35AD3D1E3B77D600A02D78 /* XCTAutomationTarget-Protocol.h in Headers */,
				EE158AD01CBD456F00A3E3F0 /* FBElement.h in Headers */,
				EE35AD3E1E3B77D600A02D78 /* XCTAXClient-Protocol.h in Headers */,
				EE158AD41CBD456F00A3E3F0 /* FBExceptionHandler.h in Headers */,
				EE158ADE1CBD456F00A3E3F0 /* FBRoute.h in Headers */,
				E444DC81249131B10060D7EB /* DDRange.h in Headers */,
				EE35AD471E3B77D600A02D78 /* XCTestDriver.h in Headers */,
				EE35AD121E3B77D600A02D78 /* _XCTNSNotificationExpectationImplementation.h in Headers */,
				E444DC93249131D40060D7EB /* HTTPMessage.h in Headers */,
				EE35AD3A1E3B77D600A02D78 /* XCSynthesizedEventRecord.h in Headers */,
				E444DCAD24913C220060D7EB /* RouteResponse.h in Headers */,
				EE35AD671E3B77D600A02D78 /* XCTWaiterDelegatePrivate-Protocol.h in Headers */,
				EE35AD4D1E3B77D600A02D78 /* XCTestManager_IDEInterface-Protocol.h in Headers */,
				13DE7A5B287CA444003243C6 /* FBXCElementSnapshotWrapper+Helpers.h in Headers */,
				711084441DA3AA7500F913D6 /* FBXPath.h in Headers */,
				EE35AD771E3B77D600A02D78 /* XCUIRecorderTimingMessage.h in Headers */,
				E444DC83249131B10060D7EB /* DDNumber.h in Headers */,
				EE35AD271E3B77D600A02D78 /* XCApplicationMonitor.h in Headers */,
				EE8DDD7F20C5733C004D4925 /* XCUIElement+FBForceTouch.h in Headers */,
				71A5C67329A4F39600421C37 /* XCTIssue+FBPatcher.h in Headers */,
				716F0DA12A16CA1000CDD977 /* NSDictionary+FBUtf8SafeDictionary.h in Headers */,
				EE158AEA1CBD456F00A3E3F0 /* FBRuntimeUtils.h in Headers */,
				7136A4791E8918E60024FC3D /* XCUIElement+FBPickerWheel.h in Headers */,
				E444DCB324913C220060D7EB /* RoutingHTTPServer.h in Headers */,
				EE35AD511E3B77D600A02D78 /* XCTestObservation-Protocol.h in Headers */,
				EE35AD131E3B77D600A02D78 /* _XCTNSPredicateExpectationImplementation.h in Headers */,
				EE158ABE1CBD456F00A3E3F0 /* FBElementCommands.h in Headers */,
				715557D3211DBCE700613B26 /* FBTCPSocket.h in Headers */,
				71B49EC71ED1A58100D51AD6 /* XCUIElement+FBUID.h in Headers */,
				EE35AD381E3B77D600A02D78 /* XCSymbolicationRecord.h in Headers */,
				EE35AD6E1E3B77D600A02D78 /* XCUIDevice.h in Headers */,
				71BD20731F86116100B36EC2 /* XCUIApplication+FBTouchAction.h in Headers */,
				EE158ACE1CBD456F00A3E3F0 /* FBCommandHandler.h in Headers */,
				EE158AC81CBD456F00A3E3F0 /* FBSessionCommands.h in Headers */,
				71C8E55125399A6B008572C1 /* XCUIApplication+FBQuiescence.h in Headers */,
				641EE70B2240CE2D00173FCB /* FBTVNavigationTracker.h in Headers */,
				63CCF91221ECE4C700E94ABD /* FBImageProcessor.h in Headers */,
				EE158AE31CBD456F00A3E3F0 /* FBSession-Private.h in Headers */,
				716E0BCE1E917E810087A825 /* NSString+FBXMLSafeString.h in Headers */,
				EE158ACF1CBD456F00A3E3F0 /* FBCommandStatus.h in Headers */,
				EE158AB81CBD456F00A3E3F0 /* FBAlertViewCommands.h in Headers */,
				EE35AD651E3B77D600A02D78 /* XCTWaiter.h in Headers */,
				EE35AD681E3B77D600A02D78 /* XCTWaiterManagement-Protocol.h in Headers */,
				EE35AD451E3B77D600A02D78 /* XCTestContext.h in Headers */,
				EE35AD661E3B77D600A02D78 /* XCTWaiterDelegate-Protocol.h in Headers */,
				EE35AD0E1E3B77D600A02D78 /* _XCTestExpectationImplementation.h in Headers */,
				EE35AD291E3B77D600A02D78 /* XCAXClient_iOS.h in Headers */,
				EE35AD691E3B77D600A02D78 /* XCTWaiterManager.h in Headers */,
				EE35AD481E3B77D600A02D78 /* XCTestDriverInterface-Protocol.h in Headers */,
				648C10AF22AAAE4000B81B9A /* TIPreferencesController.h in Headers */,
				E444DC6C249131890060D7EB /* HTTPDataResponse.h in Headers */,
				E444DC65249131890060D7EB /* HTTPErrorResponse.h in Headers */,
				EE35AD111E3B77D600A02D78 /* _XCTestSuiteImplementation.h in Headers */,
				714097431FAE1B0B008FB2C5 /* FBBaseActionsSynthesizer.h in Headers */,
				AD6C26941CF2379700F8B5FF /* FBAlert.h in Headers */,
				EE35AD731E3B77D600A02D78 /* XCUIElementQuery.h in Headers */,
				EE35AD331E3B77D600A02D78 /* XCPointerEvent.h in Headers */,
				EE35AD351E3B77D600A02D78 /* XCSourceCodeRecording.h in Headers */,
				71D04DC825356C43008A052C /* XCUIElement+FBCaching.h in Headers */,
				71BB58E12B9631F100CB9BFE /* FBScreenRecordingPromise.h in Headers */,
				E444DC99249131D40060D7EB /* HTTPLogging.h in Headers */,
				E444DC9B249131D40060D7EB /* HTTPResponse.h in Headers */,
				EEE9B4721CD02B88009D2030 /* FBRunLoopSpinner.h in Headers */,
				EE3A18621CDE618F00DE4205 /* FBErrorBuilder.h in Headers */,
				EE35AD261E3B77D600A02D78 /* XCApplicationMonitor_iOS.h in Headers */,
				0E04133B2DF1E15900AF007C /* XCUIElement+FBMinMax.h in Headers */,
				EE3A18661CDE734B00DE4205 /* FBKeyboard.h in Headers */,
				AD6C269C1CF2494200F8B5FF /* XCUIApplication+FBHelpers.h in Headers */,
				714D88CC2733FB970074A925 /* FBXMLGenerationOptions.h in Headers */,
				EE35AD101E3B77D600A02D78 /* _XCTestObservationCenterImplementation.h in Headers */,
				AD6C26981CF2481700F8B5FF /* XCUIDevice+FBHelpers.h in Headers */,
				71A7EAF91E224648001DA4F2 /* FBClassChainQueryParser.h in Headers */,
				EE9B76AA1CF7A43900275851 /* FBMacros.h in Headers */,
				C8FB547922D4C1FC00B69954 /* FBUnattachedAppLauncher.h in Headers */,
				719DCF152601EAFB000E765F /* FBNotificationsHelper.h in Headers */,
				EE35AD4A1E3B77D600A02D78 /* XCTestExpectationDelegate-Protocol.h in Headers */,
				71F3E7D425417FF400E0C22B /* FBSettings.h in Headers */,
				EE35AD641E3B77D600A02D78 /* XCTUIApplicationMonitor-Protocol.h in Headers */,
				EE35AD591E3B77D600A02D78 /* XCTKVOExpectation.h in Headers */,
				13DE7A43287C2A8D003243C6 /* FBXCAccessibilityElement.h in Headers */,
				EEE376431D59F81400ED88DD /* XCUIDevice+FBRotation.h in Headers */,
				EE35AD2E1E3B77D600A02D78 /* XCEventGenerator.h in Headers */,
				EE9B76A61CF7A43900275851 /* FBConfiguration.h in Headers */,
				EE35AD571E3B77D600A02D78 /* XCTestSuiteRun.h in Headers */,
				EE35AD701E3B77D600A02D78 /* XCUIElementAsynchronousHandlerWrapper.h in Headers */,
				EE35AD4C1E3B77D600A02D78 /* XCTestLog.h in Headers */,
				B31635202DDF0D0B007D9317 /* FBAccessibilityTraits.h in Headers */,
				71BB58E82B96328700CB9BFE /* FBScreenRecordingRequest.h in Headers */,
				EE35AD231E3B77D600A02D78 /* UITapGestureRecognizer-RecordingAdditions.h in Headers */,
				EE35AD2A1E3B77D600A02D78 /* XCDebugLogDelegate-Protocol.h in Headers */,
				EE35AD1C1E3B77D600A02D78 /* NSString-XCTAdditions.h in Headers */,
				EE35AD581E3B77D600A02D78 /* XCTestWaiter.h in Headers */,
				7150348721A6DAD600A0F4BA /* FBImageUtils.h in Headers */,
				C8FB547422D3949C00B69954 /* LSApplicationWorkspace.h in Headers */,
				E444DCAF24913C220060D7EB /* Route.h in Headers */,
				EE35AD1D1E3B77D600A02D78 /* NSValue-XCTestAdditions.h in Headers */,
				EE35AD141E3B77D600A02D78 /* _XCTWaiterImpl.h in Headers */,
				EE9B76A81CF7A43900275851 /* FBLogger.h in Headers */,
				EE35AD531E3B77D600A02D78 /* XCTestObserver.h in Headers */,
				EE35AD6F1E3B77D600A02D78 /* XCUIElement.h in Headers */,
				EE35AD2F1E3B77D600A02D78 /* XCKeyboardInputSolver.h in Headers */,
				71930C4220662E1F00D3AFEC /* FBPasteboard.h in Headers */,
				EE7E271C1D06C69F001BEC7B /* FBDebugLogDelegateDecorator.h in Headers */,
				EEDFE1211D9C06F800E6FFE5 /* XCUIDevice+FBHealthCheck.h in Headers */,
				7155D703211DCEF400166C20 /* FBMjpegServer.h in Headers */,
				EE35AD761E3B77D600A02D78 /* XCUIRecorderNodeFinderMatch.h in Headers */,
				EE35AD6C1E3B77D600A02D78 /* XCUIApplicationProcess.h in Headers */,
				7140974B1FAE1B51008FB2C5 /* FBW3CActionsSynthesizer.h in Headers */,
				EE35AD151E3B77D600A02D78 /* CDStructures.h in Headers */,
				71E75E6D254824230099FC87 /* XCUIElementQuery+FBHelpers.h in Headers */,
				EE35AD311E3B77D600A02D78 /* XCKeyboardLayout.h in Headers */,
				716C9DFA27315D21005AD475 /* FBReflectionUtils.h in Headers */,
				E444DCB624913C220060D7EB /* RouteRequest.h in Headers */,
				71F5BE23252E576C00EE9EBA /* XCUIElement+FBSwiping.h in Headers */,
				718226CC2587443700661B83 /* GCDAsyncSocket.h in Headers */,
				EE35AD3B1E3B77D600A02D78 /* XCTAsyncActivity-Protocol.h in Headers */,
				EE35AD251E3B77D600A02D78 /* XCActivityRecord.h in Headers */,
				EEBBD48B1D47746D00656A81 /* XCUIElement+FBFind.h in Headers */,
				EE35AD4E1E3B77D600A02D78 /* XCTestManager_ManagerInterface-Protocol.h in Headers */,
				EE6A893A1D0B38640083E92B /* FBFailureProofTestCase.h in Headers */,
				E444DC95249131D40060D7EB /* HTTPConnection.h in Headers */,
				EE35AD631E3B77D600A02D78 /* XCTTestRunSessionDelegate-Protocol.h in Headers */,
				EE35AD431E3B77D600A02D78 /* XCTestCaseSuite.h in Headers */,
				EE35AD091E3B77D600A02D78 /* _XCInternalTestRun.h in Headers */,
				712A0C871DA3E55D007D02E5 /* FBXPath-Private.h in Headers */,
				EE35AD321E3B77D600A02D78 /* XCKeyMappingPath.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		641EE2D92240BBE300173FCB /* WebDriverAgentRunner_tvOS */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 641EE2DF2240BBE300173FCB /* Build configuration list for PBXNativeTarget "WebDriverAgentRunner_tvOS" */;
			buildPhases = (
				641EE2D62240BBE300173FCB /* Sources */,
				641EE2D72240BBE300173FCB /* Frameworks */,
				641EE2D82240BBE300173FCB /* Resources */,
				641EE3472240C1EF00173FCB /* Copy frameworks */,
			);
			buildRules = (
			);
			dependencies = (
				641EE6FB2240C5F400173FCB /* PBXTargetDependency */,
			);
			name = WebDriverAgentRunner_tvOS;
			productName = WebDriverAgent;
			productReference = 641EE2DA2240BBE300173FCB /* WebDriverAgentRunner_tvOS.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
		641EE5D52240C5CA00173FCB /* WebDriverAgentLib_tvOS */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 641EE6F52240C5CA00173FCB /* Build configuration list for PBXNativeTarget "WebDriverAgentLib_tvOS" */;
			buildPhases = (
				641EE5D62240C5CA00173FCB /* Sources */,
				641EE6282240C5CA00173FCB /* Frameworks */,
				641EE6302240C5CA00173FCB /* Headers */,
				641EE6EF2240C5CA00173FCB /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = WebDriverAgentLib_tvOS;
			productName = WebDriverAgentLib_;
			productReference = 641EE6F82240C5CA00173FCB /* WebDriverAgentLib_tvOS.framework */;
			productType = "com.apple.product-type.framework";
		};
		64B264F8228C50E0002A5025 /* UnitTests_tvOS */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 64B26501228C50E0002A5025 /* Build configuration list for PBXNativeTarget "UnitTests_tvOS" */;
			buildPhases = (
				64B264F5228C50E0002A5025 /* Sources */,
				64B264F6228C50E0002A5025 /* Frameworks */,
				64B264F7228C50E0002A5025 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				64B26500228C50E0002A5025 /* PBXTargetDependency */,
			);
			name = UnitTests_tvOS;
			productName = WebDriverAgentLib_tvOSTests;
			productReference = 64B264F9228C50E0002A5025 /* UnitTests_tvOS.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		EE158A981CBD452B00A3E3F0 /* WebDriverAgentLib */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = EE158AA01CBD452B00A3E3F0 /* Build configuration list for PBXNativeTarget "WebDriverAgentLib" */;
			buildPhases = (
				EE158A941CBD452B00A3E3F0 /* Sources */,
				EE158A951CBD452B00A3E3F0 /* Frameworks */,
				EE158A961CBD452B00A3E3F0 /* Headers */,
				EE158A971CBD452B00A3E3F0 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = WebDriverAgentLib;
			productName = WebDriverAgentLib_;
			productReference = EE158A991CBD452B00A3E3F0 /* WebDriverAgentLib.framework */;
			productType = "com.apple.product-type.framework";
		};
		EE2202031ECC612200A29571 /* IntegrationTests_3 */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = EE2202191ECC612200A29571 /* Build configuration list for PBXNativeTarget "IntegrationTests_3" */;
			buildPhases = (
				EE2202081ECC612200A29571 /* Sources */,
				EE2202151ECC612200A29571 /* Frameworks */,
				EE2202181ECC612200A29571 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				EE2202041ECC612200A29571 /* PBXTargetDependency */,
				EE2202061ECC612200A29571 /* PBXTargetDependency */,
			);
			name = IntegrationTests_3;
			productName = EvalUITests;
			productReference = EE22021C1ECC612200A29571 /* IntegrationTests_3.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
		EE5095DD1EBCC9090028E2FE /* IntegrationTests_2 */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = EE5095FB1EBCC9090028E2FE /* Build configuration list for PBXNativeTarget "IntegrationTests_2" */;
			buildPhases = (
				EE5095E21EBCC9090028E2FE /* Sources */,
				EE5095F71EBCC9090028E2FE /* Frameworks */,
				EE5095FA1EBCC9090028E2FE /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				EE5095DE1EBCC9090028E2FE /* PBXTargetDependency */,
				EE5095E01EBCC9090028E2FE /* PBXTargetDependency */,
			);
			name = IntegrationTests_2;
			productName = EvalUITests;
			productReference = EE5095FE1EBCC9090028E2FE /* IntegrationTests_2.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
		EE836C011C0F118600D87246 /* UnitTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = EE836C0A1C0F118600D87246 /* Build configuration list for PBXNativeTarget "UnitTests" */;
			buildPhases = (
				EE836BFE1C0F118600D87246 /* Sources */,
				EE836BFF1C0F118600D87246 /* Frameworks */,
				EE836C001C0F118600D87246 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				AD8D96F11D3C12960061268E /* PBXTargetDependency */,
			);
			name = UnitTests;
			productName = WebDriverAgentCoreTests;
			productReference = EE836C021C0F118600D87246 /* UnitTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		EE9B75D31CF7956C00275851 /* IntegrationApp */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = EE9B75F71CF7956C00275851 /* Build configuration list for PBXNativeTarget "IntegrationApp" */;
			buildPhases = (
				EE9B75D01CF7956C00275851 /* Sources */,
				EE9B75D11CF7956C00275851 /* Frameworks */,
				EE9B75D21CF7956C00275851 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = IntegrationApp;
			productName = Eval;
			productReference = EE9B75D41CF7956C00275851 /* IntegrationApp.app */;
			productType = "com.apple.product-type.application";
		};
		EE9B75EB1CF7956C00275851 /* IntegrationTests_1 */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = EE9B75F81CF7956C00275851 /* Build configuration list for PBXNativeTarget "IntegrationTests_1" */;
			buildPhases = (
				EE9B75E81CF7956C00275851 /* Sources */,
				EE9B75E91CF7956C00275851 /* Frameworks */,
				EE9B75EA1CF7956C00275851 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				EE9B769F1CF79C0A00275851 /* PBXTargetDependency */,
				EE9B75EE1CF7956C00275851 /* PBXTargetDependency */,
			);
			name = IntegrationTests_1;
			productName = EvalUITests;
			productReference = EE9B75EC1CF7956C00275851 /* IntegrationTests_1.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
		EEF988291C486603005CA669 /* WebDriverAgentRunner */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = EEF988311C486604005CA669 /* Build configuration list for PBXNativeTarget "WebDriverAgentRunner" */;
			buildPhases = (
				EEF988261C486603005CA669 /* Sources */,
				EEF988271C486603005CA669 /* Frameworks */,
				EEF988281C486603005CA669 /* Resources */,
				EE93CFF41CCA501300708122 /* Copy frameworks */,
			);
			buildRules = (
			);
			dependencies = (
				EE158B5C1CBD462500A3E3F0 /* PBXTargetDependency */,
			);
			name = WebDriverAgentRunner;
			productName = XCTUITestRunner;
			productReference = EEF9882A1C486603005CA669 /* WebDriverAgentRunner.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		91F9DAE11B99DBC2001349B2 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastSwiftUpdateCheck = 1020;
				LastUpgradeCheck = 1310;
				ORGANIZATIONNAME = Facebook;
				TargetAttributes = {
					641EE2D92240BBE300173FCB = {
						CreatedOnToolsVersion = 10.1;
					};
					641EE5D52240C5CA00173FCB = {
						ProvisioningStyle = Manual;
					};
					64B264F8228C50E0002A5025 = {
						CreatedOnToolsVersion = 10.2.1;
					};
					EE158A981CBD452B00A3E3F0 = {
						CreatedOnToolsVersion = 7.3;
					};
					EE836C011C0F118600D87246 = {
						CreatedOnToolsVersion = 7.1.1;
					};
					EE9B75D31CF7956C00275851 = {
						CreatedOnToolsVersion = 7.3.1;
					};
					EE9B75EB1CF7956C00275851 = {
						CreatedOnToolsVersion = 7.3.1;
						TestTargetID = EE9B75D31CF7956C00275851;
					};
					EEF988291C486603005CA669 = {
						CreatedOnToolsVersion = 7.2;
					};
				};
			};
			buildConfigurationList = 91F9DAE41B99DBC2001349B2 /* Build configuration list for PBXProject "WebDriverAgent" */;
			compatibilityVersion = "Xcode 12.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 91F9DAE01B99DBC2001349B2;
			productRefGroup = 91F9DAEA1B99DBC2001349B2 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				EE158A981CBD452B00A3E3F0 /* WebDriverAgentLib */,
				641EE5D52240C5CA00173FCB /* WebDriverAgentLib_tvOS */,
				EEF988291C486603005CA669 /* WebDriverAgentRunner */,
				641EE2D92240BBE300173FCB /* WebDriverAgentRunner_tvOS */,
				EE836C011C0F118600D87246 /* UnitTests */,
				64B264F8228C50E0002A5025 /* UnitTests_tvOS */,
				EE9B75EB1CF7956C00275851 /* IntegrationTests_1 */,
				EE5095DD1EBCC9090028E2FE /* IntegrationTests_2 */,
				EE2202031ECC612200A29571 /* IntegrationTests_3 */,
				EE9B75D31CF7956C00275851 /* IntegrationApp */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		641EE2D82240BBE300173FCB /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		641EE6EF2240C5CA00173FCB /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		64B264F7228C50E0002A5025 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		EE158A971CBD452B00A3E3F0 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		EE2202181ECC612200A29571 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		EE5095FA1EBCC9090028E2FE /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		EE836C001C0F118600D87246 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		EE9B75D21CF7956C00275851 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				EE9B76941CF7997600275851 /* Main.storyboard in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		EE9B75EA1CF7956C00275851 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		EEF988281C486603005CA669 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		641EE2D62240BBE300173FCB /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				641EE3452240C1C800173FCB /* UITestingUITests.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		641EE5D62240C5CA00173FCB /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				64E3502E2AC0B6EB005F3ACB /* NSDictionary+FBUtf8SafeDictionary.m in Sources */,
				718226CF2587443700661B83 /* GCDAsyncSocket.m in Sources */,
				E444DCBC24917A5E0060D7EB /* HTTPResponseProxy.m in Sources */,
				71D3B3D8267FC7260076473D /* XCUIElement+FBResolve.m in Sources */,
				E444DCBE24917A5E0060D7EB /* Route.m in Sources */,
				E444DCC024917A5E0060D7EB /* RouteRequest.m in Sources */,
				13DE7A52287C46BB003243C6 /* FBXCElementSnapshot.m in Sources */,
				E444DCC224917A5E0060D7EB /* RouteResponse.m in Sources */,
				E444DCC424917A5E0060D7EB /* RoutingConnection.m in Sources */,
				E444DCC624917A5E0060D7EB /* RoutingHTTPServer.m in Sources */,
				E444DCC824917A5E0060D7EB /* HTTPConnection.m in Sources */,
				E444DCCB24917A5E0060D7EB /* HTTPMessage.m in Sources */,
				E444DCCE24917A5E0060D7EB /* HTTPServer.m in Sources */,
				E444DCD024917A5E0060D7EB /* HTTPDataResponse.m in Sources */,
				E444DCD224917A5E0060D7EB /* HTTPErrorResponse.m in Sources */,
				71414ED92670A1EE003A8C5D /* LRUCache.m in Sources */,
				E444DCD424917A5E0060D7EB /* DDNumber.m in Sources */,
				E444DCD624917A5E0060D7EB /* DDRange.m in Sources */,
				641EE5D72240C5CA00173FCB /* FBScreenshotCommands.m in Sources */,
				71F3E7D725417FF400E0C22B /* FBSettings.m in Sources */,
				641EE5D92240C5CA00173FCB /* XCUIElement+FBPickerWheel.m in Sources */,
				641EE5DA2240C5CA00173FCB /* XCUIApplicationProcessDelay.m in Sources */,
				641EE5DB2240C5CA00173FCB /* FBXPath.m in Sources */,
				71C8E55425399A6B008572C1 /* XCUIApplication+FBQuiescence.m in Sources */,
				641EE5DC2240C5CA00173FCB /* XCUIApplication+FBAlert.m in Sources */,
				641EE70F2240CE4800173FCB /* FBTVNavigationTracker.m in Sources */,
				71BB58EB2B96328700CB9BFE /* FBScreenRecordingRequest.m in Sources */,
				714D88CF2733FB970074A925 /* FBXMLGenerationOptions.m in Sources */,
				641EE5DE2240C5CA00173FCB /* XCUIApplication+FBTouchAction.m in Sources */,
				714E14BB29805CAE00375DD7 /* XCAXClient_iOS+FBSnapshotReqParams.m in Sources */,
				641EE5DF2240C5CA00173FCB /* FBWebServer.m in Sources */,
				641EE5E02240C5CA00173FCB /* FBTCPSocket.m in Sources */,
				641EE5E12240C5CA00173FCB /* FBErrorBuilder.m in Sources */,
				71C9EAAF25E8415A00470CD8 /* FBScreenshot.m in Sources */,
				641EE5E22240C5CA00173FCB /* XCUIElement+FBClassChain.m in Sources */,
				13DE7A4C287C4005003243C6 /* FBXCDeviceEvent.m in Sources */,
				641EE5E32240C5CA00173FCB /* NSExpression+FBFormat.m in Sources */,
				641EE5E42240C5CA00173FCB /* XCUIApplication+FBHelpers.m in Sources */,
				641EE5E52240C5CA00173FCB /* FBKeyboard.m in Sources */,
				641EE5E62240C5CA00173FCB /* FBElementUtils.m in Sources */,
				641EE5E72240C5CA00173FCB /* FBW3CActionsSynthesizer.m in Sources */,
				641EE5E92240C5CA00173FCB /* FBFailureProofTestCase.m in Sources */,
				641EE5EA2240C5CA00173FCB /* XCUIElement+FBIsVisible.m in Sources */,
				71F5BE52252F14EB00EE9EBA /* FBExceptions.m in Sources */,
				641EE5EB2240C5CA00173FCB /* XCUIElement+FBFind.m in Sources */,
				641EE5EC2240C5CA00173FCB /* FBResponsePayload.m in Sources */,
				C845206322D5E79700EA68CB /* FBUnattachedAppLauncher.m in Sources */,
				641EE5ED2240C5CA00173FCB /* FBRoute.m in Sources */,
				641EE5EE2240C5CA00173FCB /* NSString+FBVisualLength.m in Sources */,
				641EE5EF2240C5CA00173FCB /* FBRunLoopSpinner.m in Sources */,
				641EE5F02240C5CA00173FCB /* FBAlertsMonitor.m in Sources */,
				641EE5F12240C5CA00173FCB /* FBClassChainQueryParser.m in Sources */,
				641EE5F22240C5CA00173FCB /* NSPredicate+FBFormat.m in Sources */,
				718F49CA23087AD30045FE8B /* FBProtocolHelpers.m in Sources */,
				641EE5F42240C5CA00173FCB /* XCUIDevice+FBRotation.m in Sources */,
				13815F722328D20400CDAB61 /* FBActiveAppDetectionPoint.m in Sources */,
				71D475C52538F5A8008D9401 /* XCUIApplicationProcess+FBQuiescence.m in Sources */,
				641EE5F52240C5CA00173FCB /* XCUIElement+FBUID.m in Sources */,
				641EE5F62240C5CA00173FCB /* FBRouteRequest.m in Sources */,
				641EE5F72240C5CA00173FCB /* FBResponseJSONPayload.m in Sources */,
				718226D12587443700661B83 /* GCDAsyncUdpSocket.m in Sources */,
				641EE5F92240C5CA00173FCB /* FBMjpegServer.m in Sources */,
				641EE5FA2240C5CA00173FCB /* XCUIDevice+FBHealthCheck.m in Sources */,
				641EE5FD2240C5CA00173FCB /* FBBaseActionsSynthesizer.m in Sources */,
				13DE7A46287C2A8D003243C6 /* FBXCAccessibilityElement.m in Sources */,
				641EE5FE2240C5CA00173FCB /* XCUIElement+FBWebDriverAttributes.m in Sources */,
				641EE5FF2240C5CA00173FCB /* XCUIElement+FBForceTouch.m in Sources */,
				716C9E0327315EFF005AD475 /* XCUIApplication+FBUIInterruptions.m in Sources */,
				641EE6002240C5CA00173FCB /* FBTouchActionCommands.m in Sources */,
				719DCF182601EAFB000E765F /* FBNotificationsHelper.m in Sources */,
				714EAA102673FDFE005C5B47 /* FBCapabilities.m in Sources */,
				641EE6012240C5CA00173FCB /* FBImageProcessor.m in Sources */,
				641EE6022240C5CA00173FCB /* FBTouchIDCommands.m in Sources */,
				641EE6032240C5CA00173FCB /* FBDebugCommands.m in Sources */,
				641EE6042240C5CA00173FCB /* NSString+FBXMLSafeString.m in Sources */,
				641EE6052240C5CA00173FCB /* FBUnknownCommands.m in Sources */,
				641EE6062240C5CA00173FCB /* FBOrientationCommands.m in Sources */,
				641EE7092240CDEB00173FCB /* XCUIElement+FBTVFocuse.m in Sources */,
				641EE6082240C5CA00173FCB /* FBRuntimeUtils.m in Sources */,
				641EE6092240C5CA00173FCB /* XCUIElement+FBUtilities.m in Sources */,
				641EE60A2240C5CA00173FCB /* FBLogger.m in Sources */,
				B316351D2DDF0CF5007D9317 /* FBAccessibilityTraits.m in Sources */,
				641EE60B2240C5CA00173FCB /* FBCustomCommands.m in Sources */,
				71BB58E42B9631F100CB9BFE /* FBScreenRecordingPromise.m in Sources */,
				641EE60C2240C5CA00173FCB /* XCUIDevice+FBHelpers.m in Sources */,
				641EE60D2240C5CA00173FCB /* XCTestPrivateSymbols.m in Sources */,
				641EE60E2240C5CA00173FCB /* XCUIElement+FBTyping.m in Sources */,
				641EE60F2240C5CA00173FCB /* XCUIElement+FBAccessibility.m in Sources */,
				641EE6102240C5CA00173FCB /* FBImageUtils.m in Sources */,
				71AE3CF82D38EE8E0039FC36 /* XCUIElement+FBVisibleFrame.m in Sources */,
				715A84D12DD92AD3007134CC /* FBElementHelpers.m in Sources */,
				641EE6112240C5CA00173FCB /* FBSession.m in Sources */,
				641EE6122240C5CA00173FCB /* FBFindElementCommands.m in Sources */,
				71A5C67629A4F39600421C37 /* XCTIssue+FBPatcher.m in Sources */,
				641EE6132240C5CA00173FCB /* FBDebugLogDelegateDecorator.m in Sources */,
				641EE6142240C5CA00173FCB /* FBAlertViewCommands.m in Sources */,
				71414EDB2670A1EE003A8C5D /* LRUCacheNode.m in Sources */,
				71BB58F92B96531900CB9BFE /* FBScreenRecordingContainer.m in Sources */,
				641EE6152240C5CA00173FCB /* XCUIElement+FBScrolling.m in Sources */,
				641EE6162240C5CA00173FCB /* FBSessionCommands.m in Sources */,
				0E0413392DF1E15100AF007C /* XCUIElement+FBMinMax.m in Sources */,
				641EE6192240C5CA00173FCB /* FBConfiguration.m in Sources */,
				641EE61A2240C5CA00173FCB /* FBElementCache.m in Sources */,
				71F5BE26252E576C00EE9EBA /* XCUIElement+FBSwiping.m in Sources */,
				641EE61B2240C5CA00173FCB /* FBPasteboard.m in Sources */,
				641EE61C2240C5CA00173FCB /* FBAlert.m in Sources */,
				718F49CB23087B040045FE8B /* FBCommandStatus.m in Sources */,
				716C9DFD27315D21005AD475 /* FBReflectionUtils.m in Sources */,
				641EE61D2240C5CA00173FCB /* FBElementCommands.m in Sources */,
				641EE61E2240C5CA00173FCB /* FBExceptionHandler.m in Sources */,
				71BB58F22B96511800CB9BFE /* FBVideoCommands.m in Sources */,
				641EE61F2240C5CA00173FCB /* FBXCodeCompatibility.m in Sources */,
				71E75E70254824230099FC87 /* XCUIElementQuery+FBHelpers.m in Sources */,
				641EE6212240C5CA00173FCB /* FBElementTypeTransformer.m in Sources */,
				13DE7A5E287CA444003243C6 /* FBXCElementSnapshotWrapper+Helpers.m in Sources */,
				641EE6232240C5CA00173FCB /* FBScreen.m in Sources */,
				71D04DCB25356C43008A052C /* XCUIElement+FBCaching.m in Sources */,
				641EE6242240C5CA00173FCB /* FBXCTestDaemonsProxy.m in Sources */,
				13DE7A58287CA1EC003243C6 /* FBXCElementSnapshotWrapper.m in Sources */,
				641EE6262240C5CA00173FCB /* FBMathUtils.m in Sources */,
				641EE6272240C5CA00173FCB /* FBXCAXClientProxy.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		64B264F5228C50E0002A5025 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				64B26508228C5514002A5025 /* XCUIElementDouble.m in Sources */,
				64B26504228C5299002A5025 /* FBTVNavigationTrackerTests.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		EE158A941CBD452B00A3E3F0 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				EE158AC71CBD456F00A3E3F0 /* FBScreenshotCommands.m in Sources */,
				E444DC98249131D40060D7EB /* HTTPConnection.m in Sources */,
				7136A47A1E8918E60024FC3D /* XCUIElement+FBPickerWheel.m in Sources */,
				E444DC84249131B10060D7EB /* DDRange.m in Sources */,
				6385F4A7220A40760095BBDB /* XCUIApplicationProcessDelay.m in Sources */,
				71A5C67529A4F39600421C37 /* XCTIssue+FBPatcher.m in Sources */,
				711084451DA3AA7500F913D6 /* FBXPath.m in Sources */,
				719CD8FD2126C88B00C7D0C2 /* XCUIApplication+FBAlert.m in Sources */,
				13DE7A45287C2A8D003243C6 /* FBXCAccessibilityElement.m in Sources */,
				641EE70E2240CE4800173FCB /* FBTVNavigationTracker.m in Sources */,
				71BD20741F86116100B36EC2 /* XCUIApplication+FBTouchAction.m in Sources */,
				0E0413382DF1E15100AF007C /* XCUIElement+FBMinMax.m in Sources */,
				EE158AE71CBD456F00A3E3F0 /* FBWebServer.m in Sources */,
				715557D4211DBCE700613B26 /* FBTCPSocket.m in Sources */,
				EE3A18631CDE618F00DE4205 /* FBErrorBuilder.m in Sources */,
				71A7EAF61E20516B001DA4F2 /* XCUIElement+FBClassChain.m in Sources */,
				71555A3E1DEC460A007D4A8B /* NSExpression+FBFormat.m in Sources */,
				AD6C269D1CF2494200F8B5FF /* XCUIApplication+FBHelpers.m in Sources */,
				EE3A18671CDE734B00DE4205 /* FBKeyboard.m in Sources */,
				719DCF172601EAFB000E765F /* FBNotificationsHelper.m in Sources */,
				E444DCAC24913C220060D7EB /* Route.m in Sources */,
				713C6DD01DDC772A00285B92 /* FBElementUtils.m in Sources */,
				71BB58E32B9631F100CB9BFE /* FBScreenRecordingPromise.m in Sources */,
				7140974C1FAE1B51008FB2C5 /* FBW3CActionsSynthesizer.m in Sources */,
				EE6A893B1D0B38640083E92B /* FBFailureProofTestCase.m in Sources */,
				713AE576243A53BE0000D657 /* FBW3CActionsHelpers.m in Sources */,
				71B155E123080CA600646AFB /* FBProtocolHelpers.m in Sources */,
				EE158AB11CBD456F00A3E3F0 /* XCUIElement+FBIsVisible.m in Sources */,
				71AE3CFA2D38EE8E0039FC36 /* XCUIElement+FBVisibleFrame.m in Sources */,
				EEBBD48C1D47746D00656A81 /* XCUIElement+FBFind.m in Sources */,
				EE158ADD1CBD456F00A3E3F0 /* FBResponsePayload.m in Sources */,
				B316351C2DDF0CF5007D9317 /* FBAccessibilityTraits.m in Sources */,
				E444DCB524913C220060D7EB /* RouteRequest.m in Sources */,
				C8FB547A22D4C1FC00B69954 /* FBUnattachedAppLauncher.m in Sources */,
				EE158ADF1CBD456F00A3E3F0 /* FBRoute.m in Sources */,
				EE0D1F621EBCDCF7006A3123 /* NSString+FBVisualLength.m in Sources */,
				EEE9B4731CD02B88009D2030 /* FBRunLoopSpinner.m in Sources */,
				719CD8F92126C78F00C7D0C2 /* FBAlertsMonitor.m in Sources */,
				71A7EAFA1E224648001DA4F2 /* FBClassChainQueryParser.m in Sources */,
				718226D02587443700661B83 /* GCDAsyncUdpSocket.m in Sources */,
				13DE7A51287C46BB003243C6 /* FBXCElementSnapshot.m in Sources */,
				71A224E61DE2F56600844D55 /* NSPredicate+FBFormat.m in Sources */,
				E444DC85249131B10060D7EB /* DDNumber.m in Sources */,
				EEE376441D59F81400ED88DD /* XCUIDevice+FBRotation.m in Sources */,
				13815F712328D20400CDAB61 /* FBActiveAppDetectionPoint.m in Sources */,
				71B49EC81ED1A58100D51AD6 /* XCUIElement+FBUID.m in Sources */,
				EE158AE21CBD456F00A3E3F0 /* FBRouteRequest.m in Sources */,
				EE158ADB1CBD456F00A3E3F0 /* FBResponseJSONPayload.m in Sources */,
				714EAA0F2673FDFE005C5B47 /* FBCapabilities.m in Sources */,
				7155D704211DCEF400166C20 /* FBMjpegServer.m in Sources */,
				EEDFE1221D9C06F800E6FFE5 /* XCUIDevice+FBHealthCheck.m in Sources */,
				714D88CE2733FB970074A925 /* FBXMLGenerationOptions.m in Sources */,
				E444DCB424913C220060D7EB /* RoutingHTTPServer.m in Sources */,
				7140974E1FAE20EE008FB2C5 /* FBBaseActionsSynthesizer.m in Sources */,
				EEE3764A1D59FAE900ED88DD /* XCUIElement+FBWebDriverAttributes.m in Sources */,
				EE8DDD7E20C5733C004D4925 /* XCUIElement+FBForceTouch.m in Sources */,
				71241D7C1FAE3D2500B9559F /* FBTouchActionCommands.m in Sources */,
				63CCF91321ECE4C700E94ABD /* FBImageProcessor.m in Sources */,
				EE158ACB1CBD456F00A3E3F0 /* FBTouchIDCommands.m in Sources */,
				71F5BE51252F14EB00EE9EBA /* FBExceptions.m in Sources */,
				EE158ABD1CBD456F00A3E3F0 /* FBDebugCommands.m in Sources */,
				716E0BCF1E917E810087A825 /* NSString+FBXMLSafeString.m in Sources */,
				71BB58EA2B96328700CB9BFE /* FBScreenRecordingRequest.m in Sources */,
				EE158ACD1CBD456F00A3E3F0 /* FBUnknownCommands.m in Sources */,
				EE158AC51CBD456F00A3E3F0 /* FBOrientationCommands.m in Sources */,
				716F0DA32A16CA1000CDD977 /* NSDictionary+FBUtf8SafeDictionary.m in Sources */,
				71D475C42538F5A8008D9401 /* XCUIApplicationProcess+FBQuiescence.m in Sources */,
				641EE7082240CDEB00173FCB /* XCUIElement+FBTVFocuse.m in Sources */,
				71E75E6F254824230099FC87 /* XCUIElementQuery+FBHelpers.m in Sources */,
				71D04DCA25356C43008A052C /* XCUIElement+FBCaching.m in Sources */,
				EE158AEB1CBD456F00A3E3F0 /* FBRuntimeUtils.m in Sources */,
				EEE376461D59F81400ED88DD /* XCUIElement+FBUtilities.m in Sources */,
				EE9B76A91CF7A43900275851 /* FBLogger.m in Sources */,
				EE158ABB1CBD456F00A3E3F0 /* FBCustomCommands.m in Sources */,
				AD6C26991CF2481700F8B5FF /* XCUIDevice+FBHelpers.m in Sources */,
				716C9E0227315EFF005AD475 /* XCUIApplication+FBUIInterruptions.m in Sources */,
				EE6B64FE1D0F86EF00E85F5D /* XCTestPrivateSymbols.m in Sources */,
				AD76723E1D6B7CC000610457 /* XCUIElement+FBTyping.m in Sources */,
				EE158AAF1CBD456F00A3E3F0 /* XCUIElement+FBAccessibility.m in Sources */,
				714E14BA29805CAE00375DD7 /* XCAXClient_iOS+FBSnapshotReqParams.m in Sources */,
				7150348821A6DAD600A0F4BA /* FBImageUtils.m in Sources */,
				E444DCAB24913C220060D7EB /* HTTPResponseProxy.m in Sources */,
				E444DC6D249131890060D7EB /* HTTPErrorResponse.m in Sources */,
				71F5BE25252E576C00EE9EBA /* XCUIElement+FBSwiping.m in Sources */,
				EE158AE51CBD456F00A3E3F0 /* FBSession.m in Sources */,
				71C9EAAE25E8415A00470CD8 /* FBScreenshot.m in Sources */,
				E444DCB224913C220060D7EB /* RoutingConnection.m in Sources */,
				EE158AC11CBD456F00A3E3F0 /* FBFindElementCommands.m in Sources */,
				EE7E271D1D06C69F001BEC7B /* FBDebugLogDelegateDecorator.m in Sources */,
				716C9DFC27315D21005AD475 /* FBReflectionUtils.m in Sources */,
				71C8E55325399A6B008572C1 /* XCUIApplication+FBQuiescence.m in Sources */,
				71414EDA2670A1EE003A8C5D /* LRUCacheNode.m in Sources */,
				EE158AB91CBD456F00A3E3F0 /* FBAlertViewCommands.m in Sources */,
				71BB58F12B96511800CB9BFE /* FBVideoCommands.m in Sources */,
				71F3E7D625417FF400E0C22B /* FBSettings.m in Sources */,
				13DE7A57287CA1EC003243C6 /* FBXCElementSnapshotWrapper.m in Sources */,
				71BB58F82B96531900CB9BFE /* FBScreenRecordingContainer.m in Sources */,
				EE158AB31CBD456F00A3E3F0 /* XCUIElement+FBScrolling.m in Sources */,
				718226CE2587443700661B83 /* GCDAsyncSocket.m in Sources */,
				EE158AC91CBD456F00A3E3F0 /* FBSessionCommands.m in Sources */,
				715A84CF2DD92AD3007134CC /* FBElementHelpers.m in Sources */,
				EE9B76A71CF7A43900275851 /* FBConfiguration.m in Sources */,
				E444DC9C249131D40060D7EB /* HTTPServer.m in Sources */,
				71414ED82670A1EE003A8C5D /* LRUCache.m in Sources */,
				E444DC67249131890060D7EB /* HTTPDataResponse.m in Sources */,
				EE158AD31CBD456F00A3E3F0 /* FBElementCache.m in Sources */,
				71930C4320662E1F00D3AFEC /* FBPasteboard.m in Sources */,
				AD6C26951CF2379700F8B5FF /* FBAlert.m in Sources */,
				EE158ABF1CBD456F00A3E3F0 /* FBElementCommands.m in Sources */,
				13DE7A5D287CA444003243C6 /* FBXCElementSnapshotWrapper+Helpers.m in Sources */,
				13DE7A4B287C4005003243C6 /* FBXCDeviceEvent.m in Sources */,
				EE158AD51CBD456F00A3E3F0 /* FBExceptionHandler.m in Sources */,
				EE5A24421F136D360078B1D9 /* FBXCodeCompatibility.m in Sources */,
				EE158AE91CBD456F00A3E3F0 /* FBElementTypeTransformer.m in Sources */,
				E444DC9D249131D40060D7EB /* HTTPMessage.m in Sources */,
				E444DCB024913C220060D7EB /* RouteResponse.m in Sources */,
				71D3B3D7267FC7260076473D /* XCUIElement+FBResolve.m in Sources */,
				715AFAC21FFA29180053896D /* FBScreen.m in Sources */,
				71B155DC230711E900646AFB /* FBCommandStatus.m in Sources */,
				EE35AD7C1E3B80C000A02D78 /* FBXCTestDaemonsProxy.m in Sources */,
				EE18883B1DA661C400307AA8 /* FBMathUtils.m in Sources */,
				7157B292221DADD2001C348C /* FBXCAXClientProxy.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		EE2202081ECC612200A29571 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				71241D801FAF087500B9559F /* FBW3CMultiTouchActionsIntegrationTests.m in Sources */,
				71BB58DE2B9631B700CB9BFE /* FBVideoRecordingTests.m in Sources */,
				71241D7E1FAF084E00B9559F /* FBW3CTouchActionsIntegrationTests.m in Sources */,
				63FD950221F9D06100A3E356 /* FBImageProcessorTests.m in Sources */,
				719CD8FF2126C90200C7D0C2 /* FBAutoAlertsHandlerTests.m in Sources */,
				EE2202131ECC612200A29571 /* FBIntegrationTestCase.m in Sources */,
				715AFAC41FFA2AAF0053896D /* FBScreenTests.m in Sources */,
				71BB58EC2B96328700CB9BFE /* FBScreenRecordingRequest.m in Sources */,
				EE22021E1ECC618900A29571 /* FBTapTest.m in Sources */,
				71930C472066434000D3AFEC /* FBPasteboardTests.m in Sources */,
				71BB58E52B9631F100CB9BFE /* FBScreenRecordingPromise.m in Sources */,
				71BB58FA2B96531900CB9BFE /* FBScreenRecordingContainer.m in Sources */,
				7150FFF722476B3A00B2EE28 /* FBForceTouchTests.m in Sources */,
				71BB58F32B96511800CB9BFE /* FBVideoCommands.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		EE5095E21EBCC9090028E2FE /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				EE5095E51EBCC9090028E2FE /* FBTypingTest.m in Sources */,
				63FD950321F9D06100A3E356 /* FBImageProcessorTests.m in Sources */,
				EE5095EB1EBCC9090028E2FE /* XCElementSnapshotHitPointTests.m in Sources */,
				EE5095EC1EBCC9090028E2FE /* XCUIApplicationHelperTests.m in Sources */,
				7136C0F9243A182400921C76 /* FBW3CTypeActionsTests.m in Sources */,
				EE5095ED1EBCC9090028E2FE /* XCElementSnapshotHelperTests.m in Sources */,
				EE5095EE1EBCC9090028E2FE /* FBXPathIntegrationTests.m in Sources */,
				EE5095EF1EBCC9090028E2FE /* XCUIElementHelperIntegrationTests.m in Sources */,
				EE5095F01EBCC9090028E2FE /* XCUIDeviceHelperTests.m in Sources */,
				644D9CCE230E1F1A00C90459 /* FBConfigurationTests.m in Sources */,
				EE5095F11EBCC9090028E2FE /* XCUIElementFBFindTests.m in Sources */,
				EE5095F21EBCC9090028E2FE /* XCUIDeviceRotationTests.m in Sources */,
				EE5095F41EBCC9090028E2FE /* XCUIDeviceHealthCheckTests.m in Sources */,
				EE5096021EBCD0250028E2FE /* FBIntegrationTestCase.m in Sources */,
				EE5095F51EBCC9090028E2FE /* XCUIElementAttributesTests.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		EE836BFE1C0F118600D87246 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				713352FD26CEF31D00523CBC /* FBLRUCacheTests.m in Sources */,
				EE3F8CFE1D08AA17006F02CE /* FBRunLoopSpinnerTests.m in Sources */,
				714801D11FA9D9FA00DC5997 /* FBSDKVersionTests.m in Sources */,
				EE3F8D001D08B05F006F02CE /* FBElementTypeTransformerTests.m in Sources */,
				13FFF2F2287DBEE600E561E4 /* XCElementSnapshotDouble.m in Sources */,
				EEE16E971D33A25500172525 /* FBConfigurationTests.m in Sources */,
				ADBC39941D0782CD00327304 /* FBElementCacheTests.m in Sources */,
				715D554B2229891B00524509 /* FBExceptionHandlerTests.m in Sources */,
				718F49C8230844330045FE8B /* FBProtocolHelpersTests.m in Sources */,
				719FF5B91DAD21F5008E0099 /* FBElementUtilitiesTests.m in Sources */,
				716E0BD11E917F260087A825 /* FBXMLSafeStringTests.m in Sources */,
				ADEF63AF1D09DEBE0070A7E3 /* FBRuntimeUtilsTests.m in Sources */,
				EE9B76591CF7987800275851 /* FBRouteTests.m in Sources */,
				7139145C1DF01A12005896C2 /* NSExpressionFBFormatTests.m in Sources */,
				71A224E81DE326C500844D55 /* NSPredicateFBFormatTests.m in Sources */,
				EE6A892B1D0B25820083E92B /* XCUIApplicationDouble.m in Sources */,
				716F0DA62A17323300CDD977 /* NSDictionaryFBUtf8SafeTests.m in Sources */,
				EE6A892D1D0B2AF40083E92B /* FBErrorBuilderTests.m in Sources */,
				712A0C851DA3E459007D02E5 /* FBXPathTests.m in Sources */,
				ADBC39981D07842800327304 /* XCUIElementDouble.m in Sources */,
				7139145A1DF01989005896C2 /* XCUIElementHelpersTests.m in Sources */,
				EE6A89261D0B19E60083E92B /* FBSessionTests.m in Sources */,
				71A7EAFC1E229302001DA4F2 /* FBClassChainTests.m in Sources */,
				EE18883D1DA663EB00307AA8 /* FBMathUtilsTests.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		EE9B75D01CF7956C00275851 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				EE9B768E1CF7997600275851 /* AppDelegate.m in Sources */,
				EE1E06E71D182E95007CF043 /* FBAlertViewController.m in Sources */,
				315A15072518CC2800A3A064 /* TouchSpotView.m in Sources */,
				EE9B76911CF7997600275851 /* main.m in Sources */,
				EE9B768F1CF7997600275851 /* ViewController.m in Sources */,
				315A15012518CB8700A3A064 /* TouchableView.m in Sources */,
				315A150A2518D6F400A3A064 /* TouchViewController.m in Sources */,
				ADDA07241D6BB2BF001700AC /* FBScrollViewController.m in Sources */,
				EE8BA97A1DCCED9A00A9DEF8 /* FBNavigationController.m in Sources */,
				EE55B3251D1D5388003AAAEC /* FBTableDataSource.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		EE9B75E81CF7956C00275851 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				EE26409D1D0EBA25009BE6B0 /* FBElementAttributeTests.m in Sources */,
				7119E1EC1E891F8600D0B125 /* FBPickerWheelSelectTests.m in Sources */,
				71ACF5B8242F2FDC00F0AAD4 /* FBSafariAlertTests.m in Sources */,
				EE1E06DA1D1808C2007CF043 /* FBIntegrationTestCase.m in Sources */,
				63FD950421F9D06200A3E356 /* FBImageProcessorTests.m in Sources */,
				EE05BAFA1D13003C00A3EB00 /* FBKeyboardTests.m in Sources */,
				EE55B3271D1D54CF003AAAEC /* FBScrollingTests.m in Sources */,
				EE6A89371D0B35920083E92B /* FBFailureProofTestCaseTests.m in Sources */,
				EE006EAD1EB99B15006900A4 /* FBElementVisibilityTests.m in Sources */,
				71F5BE34252E5B2200EE9EBA /* FBElementSwipingTests.m in Sources */,
				7152EB301F41F9960047EEFF /* FBSessionIntegrationTests.m in Sources */,
				EE9B769A1CF799F400275851 /* FBAlertTests.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		EEF988261C486603005CA669 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				EE9AB8011CAEE048008C271F /* UITestingUITests.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		641EE6FB2240C5F400173FCB /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 641EE5D52240C5CA00173FCB /* WebDriverAgentLib_tvOS */;
			targetProxy = 641EE6FA2240C5F400173FCB /* PBXContainerItemProxy */;
		};
		64B26500228C50E0002A5025 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 641EE5D52240C5CA00173FCB /* WebDriverAgentLib_tvOS */;
			targetProxy = 64B264FF228C50E0002A5025 /* PBXContainerItemProxy */;
		};
		AD8D96F11D3C12960061268E /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = EE158A981CBD452B00A3E3F0 /* WebDriverAgentLib */;
			targetProxy = AD8D96F01D3C12960061268E /* PBXContainerItemProxy */;
		};
		EE158B5C1CBD462500A3E3F0 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = EE158A981CBD452B00A3E3F0 /* WebDriverAgentLib */;
			targetProxy = EE158B5B1CBD462500A3E3F0 /* PBXContainerItemProxy */;
		};
		EE2202041ECC612200A29571 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = EE158A981CBD452B00A3E3F0 /* WebDriverAgentLib */;
			targetProxy = EE2202051ECC612200A29571 /* PBXContainerItemProxy */;
		};
		EE2202061ECC612200A29571 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = EE9B75D31CF7956C00275851 /* IntegrationApp */;
			targetProxy = EE2202071ECC612200A29571 /* PBXContainerItemProxy */;
		};
		EE5095DE1EBCC9090028E2FE /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = EE158A981CBD452B00A3E3F0 /* WebDriverAgentLib */;
			targetProxy = EE5095DF1EBCC9090028E2FE /* PBXContainerItemProxy */;
		};
		EE5095E01EBCC9090028E2FE /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = EE9B75D31CF7956C00275851 /* IntegrationApp */;
			targetProxy = EE5095E11EBCC9090028E2FE /* PBXContainerItemProxy */;
		};
		EE9B75EE1CF7956C00275851 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = EE9B75D31CF7956C00275851 /* IntegrationApp */;
			targetProxy = EE9B75ED1CF7956C00275851 /* PBXContainerItemProxy */;
		};
		EE9B769F1CF79C0A00275851 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = EE158A981CBD452B00A3E3F0 /* WebDriverAgentLib */;
			targetProxy = EE9B769E1CF79C0A00275851 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin PBXVariantGroup section */
		EE9B768C1CF7997600275851 /* Main.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				EE9B768D1CF7997600275851 /* Base */,
			);
			name = Main.storyboard;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		641EE2E02240BBE300173FCB /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 717C0D702518ED2800CAA6EC /* TVOSSettings.xcconfig */;
			buildSettings = {
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "iPhone Developer";
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_TESTING_SEARCH_PATHS = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				INFOPLIST_FILE = WebDriverAgentRunner/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
					/System/Developer/Library/Frameworks,
					/System/Developer/Library/PrivateFrameworks,
					/Developer/Library/PrivateFrameworks,
					/Developer/Library/Frameworks,
				);
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = com.facebook.WebDriverAgentRunner;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = appletvos;
				TARGETED_DEVICE_FAMILY = 3;
				WARNING_CFLAGS = (
					"$(inherited)",
					"-Weverything",
					"-Wno-objc-missing-property-synthesis",
					"-Wno-unused-macros",
					"-Wno-disabled-macro-expansion",
					"-Wno-gnu-statement-expression",
					"-Wno-language-extension-token",
					"-Wno-overriding-method-mismatch",
					"-Wno-missing-variable-declarations",
					"-Rno-module-build",
					"-Wno-auto-import",
					"-Wno-objc-interface-ivars",
					"-Wno-documentation-unknown-command",
					"-Wno-reserved-id-macro",
					"-Wno-unused-parameter",
					"-Wno-gnu-conditional-omitted-operand",
					"-Wno-explicit-ownership-type",
					"-Wno-date-time",
					"-Wno-cast-align",
					"-Wno-cstring-format-directive",
					"-Wno-double-promotion",
					"-Wno-partial-availability",
					"-Wno-cast-qual",
				);
			};
			name = Debug;
		};
		641EE2E12240BBE300173FCB /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 717C0D702518ED2800CAA6EC /* TVOSSettings.xcconfig */;
			buildSettings = {
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "iPhone Developer";
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_TESTING_SEARCH_PATHS = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				INFOPLIST_FILE = WebDriverAgentRunner/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
					/System/Developer/Library/Frameworks,
					/System/Developer/Library/PrivateFrameworks,
					/Developer/Library/PrivateFrameworks,
					/Developer/Library/Frameworks,
				);
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = com.facebook.WebDriverAgentRunner;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = appletvos;
				TARGETED_DEVICE_FAMILY = 3;
				WARNING_CFLAGS = (
					"$(inherited)",
					"-Weverything",
					"-Wno-objc-missing-property-synthesis",
					"-Wno-unused-macros",
					"-Wno-disabled-macro-expansion",
					"-Wno-gnu-statement-expression",
					"-Wno-language-extension-token",
					"-Wno-overriding-method-mismatch",
					"-Wno-missing-variable-declarations",
					"-Rno-module-build",
					"-Wno-auto-import",
					"-Wno-objc-interface-ivars",
					"-Wno-documentation-unknown-command",
					"-Wno-reserved-id-macro",
					"-Wno-unused-parameter",
					"-Wno-gnu-conditional-omitted-operand",
					"-Wno-explicit-ownership-type",
					"-Wno-date-time",
					"-Wno-cast-align",
					"-Wno-cstring-format-directive",
					"-Wno-double-promotion",
					"-Wno-partial-availability",
					"-Wno-cast-qual",
				);
			};
			name = Release;
		};
		641EE6F62240C5CA00173FCB /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 717C0D702518ED2800CAA6EC /* TVOSSettings.xcconfig */;
			buildSettings = {
				CLANG_STATIC_ANALYZER_MODE = deep;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEFINES_MODULE = NO;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_TESTING_SEARCH_PATHS = YES;
				FRAMEWORK_SEARCH_PATHS = (
					"$(PLATFORM_DIR)/Developer/Library/Frameworks",
					"$(PLATFORM_DIR)/Developer/Library/PrivateFrameworks",
				);
				GCC_TREAT_WARNINGS_AS_ERRORS = NO;
				INFOPLIST_FILE = WebDriverAgentLib/Info.plist;
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
					/System/Developer/Library/Frameworks,
					/System/Developer/Library/PrivateFrameworks,
					/Developer/Library/PrivateFrameworks,
					/Developer/Library/Frameworks,
				);
				OTHER_LDFLAGS = "";
				PRODUCT_BUNDLE_IDENTIFIER = com.facebook.WebDriverAgentLib;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = appletvos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = 3;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
				WARNING_CFLAGS = (
					"$(inherited)",
					"-Weverything",
					"-Wno-objc-missing-property-synthesis",
					"-Wno-unused-macros",
					"-Wno-disabled-macro-expansion",
					"-Wno-gnu-statement-expression",
					"-Wno-language-extension-token",
					"-Wno-overriding-method-mismatch",
					"-Wno-missing-variable-declarations",
					"-Rno-module-build",
					"-Wno-auto-import",
					"-Wno-objc-interface-ivars",
					"-Wno-documentation-unknown-command",
					"-Wno-reserved-id-macro",
					"-Wno-unused-parameter",
					"-Wno-gnu-conditional-omitted-operand",
					"-Wno-explicit-ownership-type",
					"-Wno-date-time",
					"-Wno-cast-align",
					"-Wno-cstring-format-directive",
					"-Wno-double-promotion",
					"-Wno-partial-availability",
					"-Wno-objc-messaging-id",
					"-Wno-direct-ivar-access",
					"-Wno-cast-qual",
					"-Wno-declaration-after-statement",
				);
			};
			name = Debug;
		};
		641EE6F72240C5CA00173FCB /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 717C0D702518ED2800CAA6EC /* TVOSSettings.xcconfig */;
			buildSettings = {
				CLANG_STATIC_ANALYZER_MODE = deep;
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = NO;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_TESTING_SEARCH_PATHS = YES;
				FRAMEWORK_SEARCH_PATHS = (
					"$(PLATFORM_DIR)/Developer/Library/Frameworks",
					"$(PLATFORM_DIR)/Developer/Library/PrivateFrameworks",
				);
				GCC_TREAT_WARNINGS_AS_ERRORS = NO;
				INFOPLIST_FILE = WebDriverAgentLib/Info.plist;
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
					/System/Developer/Library/Frameworks,
					/System/Developer/Library/PrivateFrameworks,
					/Developer/Library/PrivateFrameworks,
					/Developer/Library/Frameworks,
				);
				ONLY_ACTIVE_ARCH = YES;
				OTHER_LDFLAGS = "";
				PRODUCT_BUNDLE_IDENTIFIER = com.facebook.WebDriverAgentLib;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SDKROOT = appletvos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = 3;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
				WARNING_CFLAGS = (
					"$(inherited)",
					"-Weverything",
					"-Wno-objc-missing-property-synthesis",
					"-Wno-unused-macros",
					"-Wno-disabled-macro-expansion",
					"-Wno-gnu-statement-expression",
					"-Wno-language-extension-token",
					"-Wno-overriding-method-mismatch",
					"-Wno-missing-variable-declarations",
					"-Rno-module-build",
					"-Wno-auto-import",
					"-Wno-objc-interface-ivars",
					"-Wno-documentation-unknown-command",
					"-Wno-reserved-id-macro",
					"-Wno-unused-parameter",
					"-Wno-gnu-conditional-omitted-operand",
					"-Wno-explicit-ownership-type",
					"-Wno-date-time",
					"-Wno-cast-align",
					"-Wno-cstring-format-directive",
					"-Wno-double-promotion",
					"-Wno-partial-availability",
					"-Wno-objc-messaging-id",
					"-Wno-direct-ivar-access",
					"-Wno-cast-qual",
					"-Wno-declaration-after-statement",
				);
			};
			name = Release;
		};
		64B26502228C50E0002A5025 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 717C0D862518ED7000CAA6EC /* TVOSTestSettings.xcconfig */;
			buildSettings = {
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = "";
				FRAMEWORK_SEARCH_PATHS = "$(inherited)";
				GCC_C_LANGUAGE_STANDARD = gnu11;
				INFOPLIST_FILE = WebDriverAgentTests/UnitTests_tvOS/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = com.facebook.WebDriverAgentTvOSCoreTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SDKROOT = appletvos;
				TARGETED_DEVICE_FAMILY = 3;
			};
			name = Debug;
		};
		64B26503228C50E0002A5025 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 717C0D862518ED7000CAA6EC /* TVOSTestSettings.xcconfig */;
			buildSettings = {
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				DEVELOPMENT_TEAM = "";
				FRAMEWORK_SEARCH_PATHS = "$(inherited)";
				GCC_C_LANGUAGE_STANDARD = gnu11;
				INFOPLIST_FILE = WebDriverAgentTests/UnitTests_tvOS/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = com.facebook.WebDriverAgentTvOSCoreTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SDKROOT = appletvos;
				TARGETED_DEVICE_FAMILY = 3;
			};
			name = Release;
		};
		91F9DB0A1B99DBC2001349B2 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES = YES;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_BITCODE = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_TREAT_WARNINGS_AS_ERRORS = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				HEADER_SEARCH_PATHS = (
					"$(SDKROOT)/usr/include/libxml2",
					"$(SRCROOT)/Modules",
				);
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				TVOS_DEPLOYMENT_TARGET = 12.0;
				VALIDATE_WORKSPACE = NO;
			};
			name = Debug;
		};
		91F9DB0B1B99DBC2001349B2 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES = YES;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_BITCODE = NO;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_PREPROCESSOR_DEFINITIONS = "DEBUG=0";
				GCC_TREAT_WARNINGS_AS_ERRORS = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				HEADER_SEARCH_PATHS = (
					"$(SDKROOT)/usr/include/libxml2",
					"$(SRCROOT)/Modules",
				);
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				TVOS_DEPLOYMENT_TARGET = 12.0;
				VALIDATE_PRODUCT = YES;
				VALIDATE_WORKSPACE = NO;
			};
			name = Release;
		};
		EE158A9E1CBD452B00A3E3F0 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = EEE5CABF1C80361500CBBDD9 /* IOSSettings.xcconfig */;
			buildSettings = {
				CLANG_STATIC_ANALYZER_MODE = deep;
				CODE_SIGN_IDENTITY = "Apple Development";
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEFINES_MODULE = NO;
				DEVELOPMENT_TEAM = YTZC3SB5GK;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_TESTING_SEARCH_PATHS = YES;
				FRAMEWORK_SEARCH_PATHS = (
					"$(PLATFORM_DIR)/Developer/Library/Frameworks",
					"$(PLATFORM_DIR)/Developer/Library/PrivateFrameworks",
				);
				GCC_TREAT_WARNINGS_AS_ERRORS = NO;
				INFOPLIST_FILE = WebDriverAgentLib/Info.plist;
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
					/Developer/Library/Frameworks,
					/Developer/Library/PrivateFrameworks,
					/System/Developer/Library/PrivateFrameworks,
					/System/Developer/Library/Frameworks,
				);
				OTHER_LDFLAGS = "";
				PRODUCT_BUNDLE_IDENTIFIER = com.facebook.WebDriverAgentLib;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
				WARNING_CFLAGS = (
					"$(inherited)",
					"-Weverything",
					"-Wno-objc-missing-property-synthesis",
					"-Wno-unused-macros",
					"-Wno-disabled-macro-expansion",
					"-Wno-gnu-statement-expression",
					"-Wno-language-extension-token",
					"-Wno-overriding-method-mismatch",
					"-Wno-missing-variable-declarations",
					"-Rno-module-build",
					"-Wno-auto-import",
					"-Wno-objc-interface-ivars",
					"-Wno-documentation-unknown-command",
					"-Wno-reserved-id-macro",
					"-Wno-unused-parameter",
					"-Wno-gnu-conditional-omitted-operand",
					"-Wno-explicit-ownership-type",
					"-Wno-date-time",
					"-Wno-cast-align",
					"-Wno-cstring-format-directive",
					"-Wno-double-promotion",
					"-Wno-partial-availability",
					"-Wno-objc-messaging-id",
					"-Wno-direct-ivar-access",
					"-Wno-cast-qual",
					"-Wno-declaration-after-statement",
				);
			};
			name = Debug;
		};
		EE158A9F1CBD452B00A3E3F0 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = EEE5CABF1C80361500CBBDD9 /* IOSSettings.xcconfig */;
			buildSettings = {
				CLANG_STATIC_ANALYZER_MODE = deep;
				CODE_SIGN_IDENTITY = "Apple Development";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = NO;
				DEVELOPMENT_TEAM = YTZC3SB5GK;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_TESTING_SEARCH_PATHS = YES;
				FRAMEWORK_SEARCH_PATHS = (
					"$(PLATFORM_DIR)/Developer/Library/Frameworks",
					"$(PLATFORM_DIR)/Developer/Library/PrivateFrameworks",
				);
				GCC_TREAT_WARNINGS_AS_ERRORS = NO;
				INFOPLIST_FILE = WebDriverAgentLib/Info.plist;
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
					/Developer/Library/Frameworks,
					/Developer/Library/PrivateFrameworks,
					/System/Developer/Library/PrivateFrameworks,
					/System/Developer/Library/Frameworks,
				);
				ONLY_ACTIVE_ARCH = YES;
				OTHER_LDFLAGS = "";
				PRODUCT_BUNDLE_IDENTIFIER = com.facebook.WebDriverAgentLib;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
				WARNING_CFLAGS = (
					"$(inherited)",
					"-Weverything",
					"-Wno-objc-missing-property-synthesis",
					"-Wno-unused-macros",
					"-Wno-disabled-macro-expansion",
					"-Wno-gnu-statement-expression",
					"-Wno-language-extension-token",
					"-Wno-overriding-method-mismatch",
					"-Wno-missing-variable-declarations",
					"-Rno-module-build",
					"-Wno-auto-import",
					"-Wno-objc-interface-ivars",
					"-Wno-documentation-unknown-command",
					"-Wno-reserved-id-macro",
					"-Wno-unused-parameter",
					"-Wno-gnu-conditional-omitted-operand",
					"-Wno-explicit-ownership-type",
					"-Wno-date-time",
					"-Wno-cast-align",
					"-Wno-cstring-format-directive",
					"-Wno-double-promotion",
					"-Wno-partial-availability",
					"-Wno-objc-messaging-id",
					"-Wno-direct-ivar-access",
					"-Wno-cast-qual",
					"-Wno-declaration-after-statement",
				);
			};
			name = Release;
		};
		EE22021A1ECC612200A29571 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 71649EC82518C19C0087F212 /* IOSTestSettings.xcconfig */;
			buildSettings = {
				CLANG_ANALYZER_NONNULL = YES;
				DEBUG_INFORMATION_FORMAT = dwarf;
				FRAMEWORK_SEARCH_PATHS = "$(inherited)";
				INFOPLIST_FILE = WebDriverAgentTests/IntegrationTests/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.facebook.IntegrationTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				TEST_TARGET_NAME = IntegrationApp;
			};
			name = Debug;
		};
		EE22021B1ECC612200A29571 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 71649EC82518C19C0087F212 /* IOSTestSettings.xcconfig */;
			buildSettings = {
				CLANG_ANALYZER_NONNULL = YES;
				FRAMEWORK_SEARCH_PATHS = "$(inherited)";
				INFOPLIST_FILE = WebDriverAgentTests/IntegrationTests/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				ONLY_ACTIVE_ARCH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = com.facebook.IntegrationTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				TEST_TARGET_NAME = IntegrationApp;
			};
			name = Release;
		};
		EE5095FC1EBCC9090028E2FE /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 71649EC82518C19C0087F212 /* IOSTestSettings.xcconfig */;
			buildSettings = {
				CLANG_ANALYZER_NONNULL = YES;
				DEBUG_INFORMATION_FORMAT = dwarf;
				FRAMEWORK_SEARCH_PATHS = "$(inherited)";
				INFOPLIST_FILE = WebDriverAgentTests/IntegrationTests/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.facebook.IntegrationTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				TEST_TARGET_NAME = IntegrationApp;
			};
			name = Debug;
		};
		EE5095FD1EBCC9090028E2FE /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 71649EC82518C19C0087F212 /* IOSTestSettings.xcconfig */;
			buildSettings = {
				CLANG_ANALYZER_NONNULL = YES;
				FRAMEWORK_SEARCH_PATHS = "$(inherited)";
				INFOPLIST_FILE = WebDriverAgentTests/IntegrationTests/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				ONLY_ACTIVE_ARCH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = com.facebook.IntegrationTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				TEST_TARGET_NAME = IntegrationApp;
			};
			name = Release;
		};
		EE836C0B1C0F118600D87246 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 71649EC82518C19C0087F212 /* IOSTestSettings.xcconfig */;
			buildSettings = {
				DEBUG_INFORMATION_FORMAT = dwarf;
				FRAMEWORK_SEARCH_PATHS = "$(inherited)";
				INFOPLIST_FILE = WebDriverAgentTests/UnitTests/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.facebook.WebDriverAgentCoreTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
			};
			name = Debug;
		};
		EE836C0C1C0F118600D87246 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 71649EC82518C19C0087F212 /* IOSTestSettings.xcconfig */;
			buildSettings = {
				FRAMEWORK_SEARCH_PATHS = "$(inherited)";
				INFOPLIST_FILE = WebDriverAgentTests/UnitTests/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				ONLY_ACTIVE_ARCH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = com.facebook.WebDriverAgentCoreTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
			};
			name = Release;
		};
		EE9B75F31CF7956C00275851 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 71649EC82518C19C0087F212 /* IOSTestSettings.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ANALYZER_NONNULL = YES;
				DEBUG_INFORMATION_FORMAT = dwarf;
				INFOPLIST_FILE = WebDriverAgentTests/IntegrationApp/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.facebook.IntegrationApp;
				PRODUCT_NAME = "$(TARGET_NAME)";
			};
			name = Debug;
		};
		EE9B75F41CF7956C00275851 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 71649EC82518C19C0087F212 /* IOSTestSettings.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ANALYZER_NONNULL = YES;
				INFOPLIST_FILE = WebDriverAgentTests/IntegrationApp/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				ONLY_ACTIVE_ARCH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = com.facebook.IntegrationApp;
				PRODUCT_NAME = "$(TARGET_NAME)";
			};
			name = Release;
		};
		EE9B75F51CF7956C00275851 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 71649EC82518C19C0087F212 /* IOSTestSettings.xcconfig */;
			buildSettings = {
				CLANG_ANALYZER_NONNULL = YES;
				DEBUG_INFORMATION_FORMAT = dwarf;
				FRAMEWORK_SEARCH_PATHS = "$(inherited)";
				INFOPLIST_FILE = WebDriverAgentTests/IntegrationTests/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.facebook.IntegrationTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				TEST_TARGET_NAME = IntegrationApp;
			};
			name = Debug;
		};
		EE9B75F61CF7956C00275851 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 71649EC82518C19C0087F212 /* IOSTestSettings.xcconfig */;
			buildSettings = {
				CLANG_ANALYZER_NONNULL = YES;
				FRAMEWORK_SEARCH_PATHS = "$(inherited)";
				INFOPLIST_FILE = WebDriverAgentTests/IntegrationTests/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				ONLY_ACTIVE_ARCH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = com.facebook.IntegrationTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				TEST_TARGET_NAME = IntegrationApp;
			};
			name = Release;
		};
		EEF988321C486604005CA669 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = EEE5CABF1C80361500CBBDD9 /* IOSSettings.xcconfig */;
			buildSettings = {
				CLANG_STATIC_ANALYZER_MODE = deep;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = YTZC3SB5GK;
				ENABLE_TESTING_SEARCH_PATHS = YES;
				FRAMEWORK_SEARCH_PATHS = "$(inherited)";
				INFOPLIST_FILE = WebDriverAgentRunner/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
					/Developer/Library/Frameworks,
					/Developer/Library/PrivateFrameworks,
					/System/Developer/Library/PrivateFrameworks,
					/System/Developer/Library/Frameworks,
				);
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-all_load",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.facebook.WebDriverAgentRunner;
				PRODUCT_NAME = "$(TARGET_NAME)";
				USES_XCTRUNNER = YES;
				WARNING_CFLAGS = (
					"$(inherited)",
					"-Weverything",
					"-Wno-objc-missing-property-synthesis",
					"-Wno-unused-macros",
					"-Wno-disabled-macro-expansion",
					"-Wno-gnu-statement-expression",
					"-Wno-language-extension-token",
					"-Wno-overriding-method-mismatch",
					"-Wno-missing-variable-declarations",
					"-Rno-module-build",
					"-Wno-auto-import",
					"-Wno-objc-interface-ivars",
					"-Wno-documentation-unknown-command",
					"-Wno-reserved-id-macro",
					"-Wno-unused-parameter",
					"-Wno-gnu-conditional-omitted-operand",
					"-Wno-explicit-ownership-type",
					"-Wno-date-time",
					"-Wno-cast-align",
					"-Wno-cstring-format-directive",
					"-Wno-double-promotion",
					"-Wno-partial-availability",
					"-Wno-cast-qual",
				);
			};
			name = Debug;
		};
		EEF988331C486604005CA669 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = EEE5CABF1C80361500CBBDD9 /* IOSSettings.xcconfig */;
			buildSettings = {
				CLANG_STATIC_ANALYZER_MODE = deep;
				DEVELOPMENT_TEAM = YTZC3SB5GK;
				ENABLE_TESTING_SEARCH_PATHS = YES;
				FRAMEWORK_SEARCH_PATHS = "$(inherited)";
				INFOPLIST_FILE = WebDriverAgentRunner/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
					/Developer/Library/Frameworks,
					/Developer/Library/PrivateFrameworks,
					/System/Developer/Library/PrivateFrameworks,
					/System/Developer/Library/Frameworks,
				);
				ONLY_ACTIVE_ARCH = YES;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-all_load",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.facebook.WebDriverAgentRunner;
				PRODUCT_NAME = "$(TARGET_NAME)";
				USES_XCTRUNNER = YES;
				WARNING_CFLAGS = (
					"$(inherited)",
					"-Weverything",
					"-Wno-objc-missing-property-synthesis",
					"-Wno-unused-macros",
					"-Wno-disabled-macro-expansion",
					"-Wno-gnu-statement-expression",
					"-Wno-language-extension-token",
					"-Wno-overriding-method-mismatch",
					"-Wno-missing-variable-declarations",
					"-Rno-module-build",
					"-Wno-auto-import",
					"-Wno-objc-interface-ivars",
					"-Wno-documentation-unknown-command",
					"-Wno-reserved-id-macro",
					"-Wno-unused-parameter",
					"-Wno-gnu-conditional-omitted-operand",
					"-Wno-explicit-ownership-type",
					"-Wno-date-time",
					"-Wno-cast-align",
					"-Wno-cstring-format-directive",
					"-Wno-double-promotion",
					"-Wno-partial-availability",
					"-Wno-cast-qual",
				);
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		641EE2DF2240BBE300173FCB /* Build configuration list for PBXNativeTarget "WebDriverAgentRunner_tvOS" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				641EE2E02240BBE300173FCB /* Debug */,
				641EE2E12240BBE300173FCB /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		641EE6F52240C5CA00173FCB /* Build configuration list for PBXNativeTarget "WebDriverAgentLib_tvOS" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				641EE6F62240C5CA00173FCB /* Debug */,
				641EE6F72240C5CA00173FCB /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		64B26501228C50E0002A5025 /* Build configuration list for PBXNativeTarget "UnitTests_tvOS" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				64B26502228C50E0002A5025 /* Debug */,
				64B26503228C50E0002A5025 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		91F9DAE41B99DBC2001349B2 /* Build configuration list for PBXProject "WebDriverAgent" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				91F9DB0A1B99DBC2001349B2 /* Debug */,
				91F9DB0B1B99DBC2001349B2 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		EE158AA01CBD452B00A3E3F0 /* Build configuration list for PBXNativeTarget "WebDriverAgentLib" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				EE158A9E1CBD452B00A3E3F0 /* Debug */,
				EE158A9F1CBD452B00A3E3F0 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		EE2202191ECC612200A29571 /* Build configuration list for PBXNativeTarget "IntegrationTests_3" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				EE22021A1ECC612200A29571 /* Debug */,
				EE22021B1ECC612200A29571 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		EE5095FB1EBCC9090028E2FE /* Build configuration list for PBXNativeTarget "IntegrationTests_2" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				EE5095FC1EBCC9090028E2FE /* Debug */,
				EE5095FD1EBCC9090028E2FE /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		EE836C0A1C0F118600D87246 /* Build configuration list for PBXNativeTarget "UnitTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				EE836C0B1C0F118600D87246 /* Debug */,
				EE836C0C1C0F118600D87246 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		EE9B75F71CF7956C00275851 /* Build configuration list for PBXNativeTarget "IntegrationApp" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				EE9B75F31CF7956C00275851 /* Debug */,
				EE9B75F41CF7956C00275851 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		EE9B75F81CF7956C00275851 /* Build configuration list for PBXNativeTarget "IntegrationTests_1" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				EE9B75F51CF7956C00275851 /* Debug */,
				EE9B75F61CF7956C00275851 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		EEF988311C486604005CA669 /* Build configuration list for PBXNativeTarget "WebDriverAgentRunner" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				EEF988321C486604005CA669 /* Debug */,
				EEF988331C486604005CA669 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 91F9DAE11B99DBC2001349B2 /* Project object */;
}
