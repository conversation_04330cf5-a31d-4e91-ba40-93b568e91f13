<?xml version="1.0" encoding="UTF-8"?>
<Scheme
   LastUpgradeVersion = "1310"
   version = "1.3">
   <BuildAction
      parallelizeBuildables = "YES"
      buildImplicitDependencies = "YES">
      <BuildActionEntries>
         <BuildActionEntry
            buildForTesting = "YES"
            buildForRunning = "YES"
            buildForProfiling = "YES"
            buildForArchiving = "YES"
            buildForAnalyzing = "YES">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "641EE2D92240BBE300173FCB"
               BuildableName = "WebDriverAgentRunner_tvOS.xctest"
               BlueprintName = "WebDriverAgentRunner_tvOS"
               ReferencedContainer = "container:WebDriverAgent.xcodeproj">
            </BuildableReference>
         </BuildActionEntry>
      </BuildActionEntries>
   </BuildAction>
   <TestAction
      buildConfiguration = "Debug"
      selectedDebuggerIdentifier = ""
      selectedLauncherIdentifier = "Xcode.IDEFoundation.Launcher.PosixSpawn"
      shouldUseLaunchSchemeArgsEnv = "YES"
      systemAttachmentLifetime = "keepNever">
      <MacroExpansion>
         <BuildableReference
            BuildableIdentifier = "primary"
            BlueprintIdentifier = "641EE2D92240BBE300173FCB"
            BuildableName = "WebDriverAgentRunner_tvOS.xctest"
            BlueprintName = "WebDriverAgentRunner_tvOS"
            ReferencedContainer = "container:WebDriverAgent.xcodeproj">
         </BuildableReference>
      </MacroExpansion>
      <Testables>
         <TestableReference
            skipped = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "641EE2D92240BBE300173FCB"
               BuildableName = "WebDriverAgentRunner_tvOS.xctest"
               BlueprintName = "WebDriverAgentRunner_tvOS"
               ReferencedContainer = "container:WebDriverAgent.xcodeproj">
            </BuildableReference>
         </TestableReference>
      </Testables>
   </TestAction>
   <LaunchAction
      buildConfiguration = "Debug"
      selectedDebuggerIdentifier = "Xcode.DebuggerFoundation.Debugger.LLDB"
      selectedLauncherIdentifier = "Xcode.DebuggerFoundation.Launcher.LLDB"
      launchStyle = "0"
      useCustomWorkingDirectory = "NO"
      ignoresPersistentStateOnLaunch = "NO"
      debugDocumentVersioning = "YES"
      debugServiceExtension = "internal"
      allowLocationSimulation = "YES">
      <MacroExpansion>
         <BuildableReference
            BuildableIdentifier = "primary"
            BlueprintIdentifier = "641EE2D92240BBE300173FCB"
            BuildableName = "WebDriverAgentRunner_tvOS.xctest"
            BlueprintName = "WebDriverAgentRunner_tvOS"
            ReferencedContainer = "container:WebDriverAgent.xcodeproj">
         </BuildableReference>
      </MacroExpansion>
      <EnvironmentVariables>
         <EnvironmentVariable
            key = "USE_PORT"
            value = "$(USE_PORT)"
            isEnabled = "YES">
         </EnvironmentVariable>
         <EnvironmentVariable
            key = "UPGRADE_TIMESTAMP"
            value = "$(UPGRADE_TIMESTAMP)"
            isEnabled = "YES">
         </EnvironmentVariable>
         <EnvironmentVariable
            key = "MJPEG_SERVER_PORT"
            value = "$(MJPEG_SERVER_PORT)"
            isEnabled = "YES">
         </EnvironmentVariable>
         <EnvironmentVariable
            key = "WDA_PRODUCT_BUNDLE_IDENTIFIER"
            value = "$(WDA_PRODUCT_BUNDLE_IDENTIFIER)"
            isEnabled = "YES">
         </EnvironmentVariable>
      </EnvironmentVariables>
   </LaunchAction>
   <ProfileAction
      buildConfiguration = "Release"
      shouldUseLaunchSchemeArgsEnv = "YES"
      savedToolIdentifier = ""
      useCustomWorkingDirectory = "NO"
      debugDocumentVersioning = "YES">
      <MacroExpansion>
         <BuildableReference
            BuildableIdentifier = "primary"
            BlueprintIdentifier = "641EE2D92240BBE300173FCB"
            BuildableName = "WebDriverAgentRunner_tvOS.xctest"
            BlueprintName = "WebDriverAgentRunner_tvOS"
            ReferencedContainer = "container:WebDriverAgent.xcodeproj">
         </BuildableReference>
      </MacroExpansion>
   </ProfileAction>
   <AnalyzeAction
      buildConfiguration = "Debug">
   </AnalyzeAction>
   <ArchiveAction
      buildConfiguration = "Release"
      revealArchiveInOrganizer = "YES">
   </ArchiveAction>
</Scheme>
