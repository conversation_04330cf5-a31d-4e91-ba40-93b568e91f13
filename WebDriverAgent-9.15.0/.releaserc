{"plugins": [["@semantic-release/commit-analyzer", {"preset": "angular", "releaseRules": [{"type": "chore", "release": "patch"}]}], ["@semantic-release/release-notes-generator", {"preset": "conventionalcommits", "presetConfig": {"types": [{"type": "feat", "section": "Features"}, {"type": "fix", "section": "Bug Fixes"}, {"type": "perf", "section": "Performance Improvements"}, {"type": "revert", "section": "Reverts"}, {"type": "chore", "section": "Miscellaneous Chores"}, {"type": "refactor", "section": "Code Refactoring"}, {"type": "docs", "section": "Documentation", "hidden": true}, {"type": "style", "section": "Styles", "hidden": true}, {"type": "test", "section": "Tests", "hidden": true}, {"type": "build", "section": "Build System", "hidden": true}, {"type": "ci", "section": "Continuous Integration", "hidden": true}]}}], ["@semantic-release/changelog", {"changelogFile": "CHANGELOG.md"}], "@semantic-release/npm", ["@semantic-release/git", {"assets": ["docs", "package.json", "CHANGELOG.md"], "message": "chore(release): ${nextRelease.version} [skip ci]\n\n${nextRelease.notes}"}], ["@semantic-release/github", {"assets": ["WebDriverAgentRunner-Runner.zip", "WebDriverAgentRunner_tvOS-Runner.zip", "WebDriverAgentRunner-Build-Sim-arm64.zip", "WebDriverAgentRunner-Build-Sim-x86_64.zip", "WebDriverAgentRunner_tvOS-Build-Sim-arm64.zip", "WebDriverAgentRunner_tvOS-Build-Sim-x86_64.zip"]}]]}