//
//     Generated by class-dump 3.5 (64 bit).
//
//     class-dump is Copyright (C) 1997-1998, 2000-2001, 2004-2013 by <PERSON>.
//

#import <Foundation/Foundation.h>

@class NSString, XCUIApplicationProcess;

@interface XCUIApplicationImpl : NSObject
{
    NSString *_path;
    NSString *_bundleID;
    XCUIApplicationProcess *_currentProcess;
}

@property(retain, nonatomic) XCUIApplicationProcess *currentProcess; // @synthesize currentProcess=_currentProcess;
@property(readonly, copy) NSString *bundleID; // @synthesize bundleID=_bundleID;
@property(readonly, copy) NSString *path; // @synthesize path=_path;
@property(nonatomic) unsigned long long state;
@property(nonatomic) int processID;
@property(readonly) id/*XCAccessibilityElement*/ accessibilityElement;

- (instancetype)initWithPath:(id)arg1 bundleID:(id)arg2;

- (void)launchWithArguments:(id)arg1 environment:(id)arg2 usingXcode:(BOOL)arg3;
- (void)handleCrashUnderSymbol:(id)arg1;
- (void)terminate;

- (void)waitForViewControllerViewDidDisappearWithTimeout:(double)arg1;

- (void)_waitForRunningActive;
- (void)_launchUsingPlatformWithArguments:(id)arg1 environment:(id)arg2;
- (void)_launchUsingXcodeWithArguments:(id)arg1 environment:(id)arg2;
- (void)_waitForLaunchProgress;

@end
