//
//     Generated by class-dump 3.5 (64 bit).
//
//     class-dump is Copyright (C) 1997-1998, 2000-2001, 2004-2013 by <PERSON>.
//

#import <XCTest/XCUIDevice.h>

@interface XCUIDevice ()

// Since Xcode 10.2
@property (readonly) id accessibilityInterface; // implements XCUIAccessibilityInterface
@property (readonly) id eventSynthesizer; // implements XCUIEventSynthesizing
@property (readonly) id screenDataSource; // @synthesize screenDataSource=_screenDataSource;

- (_Bool)performDeviceEvent:(id)arg1 error:(id *)arg2;

// Since Xcode 13
// 1 - Light
// 2 - Dark
- (void)setAppearanceMode:(long long)arg1;
- (long long)appearanceMode;

- (void)pressLockButton;
- (void)holdHomeButtonForDuration:(double)arg1;
- (void)_silentPressButton:(long long)arg1;
// Since Xcode 11
- (_Bool)supportsPressureInteraction;

@end
