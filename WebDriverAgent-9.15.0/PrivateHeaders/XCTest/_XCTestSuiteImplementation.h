//
//     Generated by class-dump 3.5 (64 bit).
//
//     class-dump is Copyright (C) 1997-1998, 2000-2001, 2004-2013 by <PERSON>.
//

#import <XCTest/XCTest.h>

@class NSMutableArray, NSString, XCTestConfiguration;

@interface _XCTestSuiteImplementation : XCTest
{
    NSString *_name;
    NSMutableArray *_tests;
    XCTestConfiguration *_testConfiguration;
}
@property(retain) XCTestConfiguration *testConfiguration; // @synthesize testConfiguration=_testConfiguration;
@property(retain) NSMutableArray *tests; // @synthesize tests=_tests;
@property(copy) NSString *name; // @synthesize name=_name;

- (id)initWithName:(id)arg1;

@end
