//
//     Generated by class-dump 3.5 (64 bit).
//
//     class-dump is Copyright (C) 1997-1998, 2000-2001, 2004-2013 by <PERSON>.
//

#import <XCTest/XCUICoordinate.h>
#import <TargetConditionals.h>

#if !TARGET_OS_IPHONE

@interface XCUIElementHitPointCoordinate : XCUICoordinate
{
}

- (id)description;
- (struct CGPoint)screenPoint;
- (id)initWithCoordinate:(id)arg1 pointsOffset:(struct CGVector)arg2;
- (id)initWithElement:(id)arg1 normalizedOffset:(struct CGVector)arg2;
- (id)initWithElement:(id)arg1;

@end

#endif
