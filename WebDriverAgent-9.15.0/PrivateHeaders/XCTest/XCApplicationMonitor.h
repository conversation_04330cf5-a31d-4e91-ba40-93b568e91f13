//
//     Generated by class-dump 3.5 (64 bit).
//
//     class-dump is Copyright (C) 1997-1998, 2000-2001, 2004-2013 by <PERSON>.
//

#import "XCTestObservation.h"

@class NSArray, NSMutableDictionary, NSObject<OS_dispatch_queue>, NSString;

@interface XCApplicationMonitor : NSObject <XCTUIApplicationMonitor>
{
    NSMutableDictionary *_applicationImplementations;
    NSMutableDictionary *_applicationProcessesForPID;
    NSMutableDictionary *_applicationProcessesForToken;
    NSMutableSet *_launchedApplications;
    NSObject<OS_dispatch_queue> *_queue;
}
@property NSObject<OS_dispatch_queue> *queue; // @synthesize queue=_queue;
@property(readonly, copy) NSArray *monitoredApplications;

+ (instancetype)sharedMonitor;
- (void)requestAutomationSessionForTestTargetWithPID:(int)arg1 reply:(CDUnknownBlockType)arg2;
- (void)processWithToken:(id)arg1 exitedWithStatus:(int)arg2;
- (void)stopTrackingProcessWithToken:(id)arg1;
- (void)crashInProcessWithBundleID:(id)arg1 path:(id)arg2 pid:(int)arg3 symbol:(id)arg4;
- (void)waitForUnrequestedTerminationOfLaunchedApplicationsWithTimeout:(double)arg1;
- (void)_waitForCrashReportOrCleanExitStatusOfApp:(id)arg1;
- (id)_appFromSet:(id)arg1 thatTransitionedToNotRunningDuringTimeInterval:(double)arg2;
- (void)terminationTrackedForApplicationProcess:(id)arg1;
- (void)launchRequestedForApplicationProcess:(id)arg1;
- (void)_terminateApplicationProcess:(id)arg1;
- (void)terminateApplicationProcess:(id)arg1 withToken:(id)arg2;
- (id)monitoredApplicationWithProcessIdentifier:(int)arg1;
- (void)applicationWithBundleID:(id)arg1 didUpdatePID:(int)arg2 state:(unsigned long long)arg3;
- (void)_beginMonitoringApplication:(id)arg1;
- (void)setApplicationProcess:(id)arg1 forToken:(id)arg2;
- (id)applicationProcessWithToken:(id)arg1;
- (void)setApplicationProcess:(id)arg1 forPID:(int)arg2;
- (id)applicationProcessWithPID:(int)arg1;
- (id)applicationImplementationForApplicationAtPath:(id)arg1 bundleID:(id)arg2;
- (id)init;

@end
