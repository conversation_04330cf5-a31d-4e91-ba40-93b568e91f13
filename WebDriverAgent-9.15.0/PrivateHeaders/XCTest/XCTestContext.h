//
//     Generated by class-dump 3.5 (64 bit).
//
//     class-dump is Copyright (C) 1997-1998, 2000-2001, 2004-2013 by <PERSON>.
//

@class NSArray, XCTestContextScope;

@interface XCTestContext : NSObject
{
    BOOL _didHandleUIInterruption;
    XCTestContextScope *_currentScope;
}
@property BOOL didHandleUIInterruption; // @synthesize didHandleUIInterruption=_didHandleUIInterruption;
@property(retain, nonatomic) XCTestContextScope *currentScope; // @synthesize currentScope=_currentScope;
@property(readonly, copy) NSArray *handlers;

+ (CDUnknownBlockType)defaultAsynchronousUIElementHandler;

- (BOOL)handleAsynchronousUIElement:(id)arg1;
- (void)removeUIInterruptionMonitor:(id)arg1;
- (id)addUIInterruptionMonitorWithDescription:(id)arg1 handler:(CDUnknownBlockType)arg2;
- (void)performInScope:(CDUnknownBlockType)arg1;
- (id)init;

@end
