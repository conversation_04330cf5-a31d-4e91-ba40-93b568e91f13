//
//     Generated by class-dump 3.5 (64 bit).
//
//     class-dump is Copyright (C) 1997-1998, 2000-2001, 2004-2013 by <PERSON>.
//

#import <XCTest/XCTestExpectation.h>

#import "XCTAsyncActivity.h"

@class NSError, NSString;

@interface XCTAsyncActivity : XCTestExpectation <XCTAsyncActivity>
{
    NSError *_error;
    BOOL _timedOut;
}
@property BOOL timedOut; // @synthesize timedOut=_timedOut;
@property(retain) NSError *error; // @synthesize error=_error;

- (void)finishWithError:(id)arg1;

@end
