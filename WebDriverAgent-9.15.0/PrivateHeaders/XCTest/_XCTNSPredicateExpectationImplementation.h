//
//     Generated by class-dump 3.5 (64 bit).
//
//     class-dump is Copyright (C) 1997-1998, 2000-2001, 2004-2013 by <PERSON>.
//

#import "NSObject.h"

@class NSObject<OS_dispatch_queue>, NSPredicate, NSString, NSTimer, XCTNSPredicateExpectation;

@interface _XCTNSPredicateExpectationImplementation : NSObject
{
    XCTNSPredicateExpectation *_expectation;
    id <XCTNSPredicateExpectationObject> _object;
    NSPredicate *_predicate;
    CDUnknownBlockType _handler;
    NSTimer *_timer;
    NSObject<OS_dispatch_queue> *_queue;
    BOOL _hasCleanedUp;
}
@property(readonly, copy) NSPredicate *predicate; // @synthesize predicate=_predicate;
@property(readonly) id <XCTNSPredicateExpectationObject> object; // @synthesize object=_object;
@property(copy) CDUnknownBlockType handler;

- (void)cleanup;
- (void)_considerFulfilling;
- (id)initWithPredicate:(id)arg1 object:(id)arg2 expectation:(id)arg3;

@end
