//
//     Generated by class-dump 3.5 (64 bit).
//
//     class-dump is Copyright (C) 1997-1998, 2000-2001, 2004-2013 by <PERSON>.
//

#import <XCTest/XCTestExpectation.h>

@class NSString, _XCKVOExpectationImplementation;

@interface XCTKVOExpectation : XCTestExpectation
{
    id _internal;
}
@property(retain) _XCKVOExpectationImplementation *internal; // @synthesize internal=_internal;
@property(copy) CDUnknownBlockType handler;
@property(readonly) unsigned long long options;
@property(readonly) id expectedValue;
@property(readonly) id observedObject;
@property(readonly, copy) NSString *keyPath;

- (void)cleanup;
- (void)fulfill;
- (id)initWithKeyPath:(id)arg1 object:(id)arg2;
- (id)initWithKeyPath:(id)arg1 object:(id)arg2 expectedValue:(id)arg3;
- (id)initWithKeyPath:(id)arg1 object:(id)arg2 expectedValue:(id)arg3 options:(unsigned long long)arg4;

@end
