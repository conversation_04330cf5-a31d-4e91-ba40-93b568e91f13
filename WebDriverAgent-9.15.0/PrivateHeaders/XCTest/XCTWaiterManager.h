//
//     Generated by class-dump 3.5 (64 bit).
//
//     class-dump is Copyright (C) 1997-1998, 2000-2001, 2004-2013 by <PERSON>.
//

#import "NSObject.h"

@class NSMutableArray, NSObject<OS_dispatch_queue>, NSThread;

@interface XCTWaiterManager : NSObject
{
    NSMutableArray *_waiterStack;
    NSThread *_thread;
    NSObject<OS_dispatch_queue> *_queue;
}
@property(readonly) NSObject<OS_dispatch_queue> *queue; // @synthesize queue=_queue;
@property NSThread *thread; // @synthesize thread=_thread;
@property(retain) NSMutableArray *waiterStack; // @synthesize waiterStack=_waiterStack;

+ (id)threadLocalManager;

- (void)waiterDidFinishWaiting:(id)arg1;
- (void)waiterTimedOutWhileWaiting:(id)arg1;
- (void)waiterWillBeginWaiting:(id)arg1;
- (id)init;

@end
