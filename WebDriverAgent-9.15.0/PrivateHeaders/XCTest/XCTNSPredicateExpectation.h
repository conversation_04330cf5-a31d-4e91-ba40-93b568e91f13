//
//     Generated by class-dump 3.5 (64 bit).
//
//     class-dump is Copyright (C) 1997-1998, 2000-2001, 2004-2013 by <PERSON>.
//

#import <XCTest/XCTestExpectation.h>

@class NSPredicate, _XCTNSPredicateExpectationImplementation;

@interface XCTNSPredicateExpectation : XCTestExpectation
{
    id _internal;
}
@property(retain) _XCTNSPredicateExpectationImplementation *internal; // @synthesize internal=_internal;
@property(copy) CDUnknownBlockType handler;
@property(readonly, copy) NSPredicate *predicate;
@property(readonly) id object;

- (void)cleanup;
- (void)fulfill;
- (id)initWithPredicate:(id)arg1 object:(id)arg2;

@end
