//
//     Generated by class-dump 3.5 (64 bit).
//
//     class-dump is Copyright (C) 1997-1998, 2000-2001, 2004-2013 by <PERSON>.
//

@class NSString, XCTestRun;

@interface XCTest : NSObject
{
    id _internal;
}
@property(readonly) NSString *nameForLegacyLogging;
@property(readonly) NSString *languageAgnosticTestMethodName;
@property(readonly) NSString *languageAgnosticTestClassName;
@property(readonly) XCTestRun *testRun;
@property(readonly) Class testRunClass;
@property(readonly) Class _requiredTestRunBaseClass;
@property(readonly, copy) NSString *name;
@property(readonly) unsigned long long testCaseCount;
@property(readonly) NSString *_methodNameForReporting;
@property(readonly) NSString *_classNameForReporting;

+ (id)languageAgnosticTestClassNameForTestClass:(Class)arg1;

- (void)tearDown;
- (void)setUp;
- (void)runTest;
- (id)run;
- (void)performTest:(id)arg1;
- (id)init;
- (void)removeTestsWithNames:(id)arg1;

@end
