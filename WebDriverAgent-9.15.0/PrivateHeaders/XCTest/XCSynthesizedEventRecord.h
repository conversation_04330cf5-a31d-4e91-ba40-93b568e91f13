//
//     Generated by class-dump 3.5 (64 bit).
//
//     class-dump is Copyright (C) 1997-1998, 2000-2001, 2004-2013 by <PERSON>.
//

@class NSArray, NSMutableArray, NSString, XCPointerEventPath;

@interface XCSynthesizedEventRecord : NSObject <NSSecureCoding>
{
    NSMutableArray *_eventPaths;
    NSString *_name;
#if !TARGET_OS_TV
    UIInterfaceOrientation _interfaceOrientation;
#endif
}
#if !TARGET_OS_TV
@property(readonly) UIInterfaceOrientation interfaceOrientation; // @synthesize interfaceOrientation=_interfaceOrientation;
#endif
@property(readonly, copy) NSString *name; // @synthesize name=_name;
@property(readonly) double maximumOffset;
@property(readonly) NSArray *eventPaths;

- (void)addPointerEventPath:(XCPointerEventPath *)arg1;
#if !TARGET_OS_TV
- (id)initWithName:(NSString *)arg1 interfaceOrientation:(UIInterfaceOrientation)arg2;
#endif
- (id)initWithName:(id)arg1;
- (id)init;
- (BOOL)synthesizeWithError:(NSError **)arg1;

@end
