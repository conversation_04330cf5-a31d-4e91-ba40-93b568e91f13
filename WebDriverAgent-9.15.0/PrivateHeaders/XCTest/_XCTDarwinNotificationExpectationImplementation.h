//
//     Generated by class-dump 3.5 (64 bit).
//
//     class-dump is Copyright (C) 1997-1998, 2000-2001, 2004-2013 by <PERSON>.
//

#import "NSObject.h"

@class NSObject<OS_dispatch_queue>, NSString, XCTDarwinNotificationExpectation;

@interface _XCTDarwinNotificationExpectationImplementation : NSObject
{
    XCTDarwinNotificationExpectation *_expectation;
    NSString *_notificationName;
    int _notifyToken;
    CDUnknownBlockType _handler;
    NSObject<OS_dispatch_queue> *_queue;
    BOOL _hasCleanedUp;
}
@property(readonly, copy) NSString *notificationName; // @synthesize notificationName=_notificationName;
@property(copy) CDUnknownBlockType handler;

- (void)cleanup;
- (void)_handleNotification;
- (id)initWithNotificationName:(id)arg1 expectation:(id)arg2;

@end
