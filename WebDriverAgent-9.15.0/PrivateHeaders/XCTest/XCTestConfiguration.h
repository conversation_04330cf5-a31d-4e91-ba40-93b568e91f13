//
//     Generated by class-dump 3.5 (64 bit).
//
//     class-dump is Copyright (C) 1997-1998, 2000-2001, 2004-2013 by <PERSON>.
//

@class NSSet, NSString, NSURL, NSUUID;

@interface XCTestConfiguration : NSObject <NSSecureCoding>
{
    NSURL *_testBundleURL;
    NSString *_testBundleRelativePath;
    NSString *_absolutePath;
    NSSet *_testsToSkip;
    NSSet *_testsToRun;
    BOOL _reportResultsToIDE;
    NSUUID *_sessionIdentifier;
    NSString *_pathToXcodeReportingSocket;
    BOOL _disablePerformanceMetrics;
    BOOL _treatMissingBaselinesAsFailures;
    NSURL *_baselineFileURL;
    NSString *_baselineFileRelativePath;
    NSString *_targetApplicationPath;
    NSString *_targetApplicationBundleID;
    NSString *_productModuleName;
    BOOL _reportActivities;
    BOOL _testsMustRunOnMainThread;
    BOOL _initializeForUITesting;
    NSArray *_targetApplicationArguments;
    NSDictionary *_targetApplicationEnvironment;
    NSDictionary *_aggregateStatisticsBeforeCrash;
    NSString *_automationFrameworkPath;
    BOOL _emitOSLogs;
}
@property BOOL emitOSLogs; // @synthesize emitOSLogs=_emitOSLogs;
@property(copy) NSString *automationFrameworkPath; // @synthesize automationFrameworkPath=_automationFrameworkPath;
@property(copy) NSDictionary *aggregateStatisticsBeforeCrash; // @synthesize aggregateStatisticsBeforeCrash=_aggregateStatisticsBeforeCrash;
@property(copy) NSArray *targetApplicationArguments; // @synthesize targetApplicationArguments=_targetApplicationArguments;
@property(copy) NSDictionary *targetApplicationEnvironment; // @synthesize targetApplicationEnvironment=_targetApplicationEnvironment;
@property BOOL initializeForUITesting; // @synthesize initializeForUITesting=_initializeForUITesting;
@property BOOL testsMustRunOnMainThread; // @synthesize testsMustRunOnMainThread=_testsMustRunOnMainThread;
@property BOOL reportActivities; // @synthesize reportActivities=_reportActivities;
@property(copy) NSString *productModuleName; // @synthesize productModuleName=_productModuleName;
@property(copy) NSString *targetApplicationBundleID; // @synthesize targetApplicationBundleID=_targetApplicationBundleID;
@property(copy) NSString *targetApplicationPath; // @synthesize targetApplicationPath=_targetApplicationPath;
@property BOOL treatMissingBaselinesAsFailures; // @synthesize treatMissingBaselinesAsFailures=_treatMissingBaselinesAsFailures;
@property BOOL disablePerformanceMetrics; // @synthesize disablePerformanceMetrics=_disablePerformanceMetrics;
@property BOOL reportResultsToIDE; // @synthesize reportResultsToIDE=_reportResultsToIDE;
@property(copy, nonatomic) NSURL *baselineFileURL; // @synthesize baselineFileURL=_baselineFileURL;
@property(copy) NSString *baselineFileRelativePath; // @synthesize baselineFileRelativePath=_baselineFileRelativePath;
@property(copy) NSString *pathToXcodeReportingSocket; // @synthesize pathToXcodeReportingSocket=_pathToXcodeReportingSocket;
@property(copy) NSUUID *sessionIdentifier; // @synthesize sessionIdentifier=_sessionIdentifier;
@property(copy) NSSet *testsToSkip; // @synthesize testsToSkip=_testsToSkip;
@property(copy) NSSet *testsToRun; // @synthesize testsToRun=_testsToRun;
@property(copy, nonatomic) NSURL *testBundleURL; // @synthesize testBundleURL=_testBundleURL;
@property(copy) NSString *testBundleRelativePath; // @synthesize testBundleRelativePath=_testBundleRelativePath;
@property(copy) NSString *absolutePath; // @synthesize absolutePath=_absolutePath;

+ (id)configurationWithContentsOfFile:(id)arg1;
+ (id)activeTestConfiguration;
+ (void)setActiveTestConfiguration:(id)arg1;

- (BOOL)writeToFile:(id)arg1;
- (id)init;

@end
