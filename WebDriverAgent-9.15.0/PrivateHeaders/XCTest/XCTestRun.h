//
//     Generated by class-dump 3.5 (64 bit).
//
//     class-dump is Copyright (C) 1997-1998, 2000-2001, 2004-2013 by <PERSON>.
//

#import <XCTest/XCTestRun.h>

@class NSDate, XCTest, _XCInternalTestRun;

@interface XCTestRun ()
{
    id _internalTestRun;
}
@property(readonly) _XCInternalTestRun *implementation; // @synthesize implementation=_internalTestRun;
@property(readonly) BOOL hasSucceeded;
@property unsigned long long unexpectedExceptionCountBeforeCrash;
@property unsigned long long failureCountBeforeCrash;
@property unsigned long long executionCountBeforeCrash;
@property(readonly) unsigned long long testCaseCount;
@property(readonly) unsigned long long unexpectedExceptionCount;
@property(readonly) unsigned long long failureCount;
@property(readonly) unsigned long long totalFailureCount;
@property(readonly) unsigned long long executionCount;
@property(readonly, copy) NSDate *stopDate;
@property(readonly, copy) NSDate *startDate;
@property(readonly) double testDuration;
@property(readonly) double totalDuration;
@property(readonly) XCTest *test;

+ (id)testRunWithTest:(id)arg1;

- (void)recordFailureWithDescription:(id)arg1 inFile:(id)arg2 atLine:(unsigned long long)arg3 expected:(BOOL)arg4;
- (void)stop;
- (void)start;
- (id)init;
- (id)initWithTest:(id)arg1;

@end
