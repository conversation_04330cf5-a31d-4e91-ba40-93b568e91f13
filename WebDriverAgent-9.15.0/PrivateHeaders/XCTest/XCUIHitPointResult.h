//
//     Generated by class-dump 3.5 (64 bit).
//
//     class-dump is Copyright (C) 1997-1998, 2000-2001, 2004-2013 by <PERSON>.
//

#import <Foundation/Foundation.h>
#import <CoreGraphics/CoreGraphics.h>

@interface XCUIHitPointResult : NSObject
{
    BOOL _hittable;
    CGPoint _hitPoint;
}

@property(readonly, getter=isHittable) BOOL hittable; // @synthesize hittable=_hittable;
@property(readonly) struct CGPoint hitPoint; // @synthesize hitPoint=_hitPoint;
- (id)initWithHitPoint:(struct CGPoint)arg1 hittable:(BOOL)arg2;

@end
