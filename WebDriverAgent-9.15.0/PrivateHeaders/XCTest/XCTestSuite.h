//
//     Generated by class-dump 3.5 (64 bit).
//
//     class-dump is Copyright (C) 1997-1998, 2000-2001, 2004-2013 by <PERSON>.
//

#import <XCTest/XCTest.h>

@class NSArray, NSMutableArray, NSString;

@interface XCTestSuite : XCTest
{
    id _internalImplementation;
}
@property(readonly, copy) NSArray *tests;
@property(copy) NSString *name;

+ (id)testSuiteForTestConfiguration:(id)arg1;
+ (id)defaultTestSuite;
+ (id)allTests;
+ (id)testSuiteForTestCaseClass:(Class)arg1;
+ (id)testSuiteForTestCaseWithName:(id)arg1;
+ (id)testSuiteForBundlePath:(id)arg1;
+ (id)suiteForBundleCache;
+ (void)invalidateCache;
+ (id)_suiteForBundleCache;
+ (id)emptyTestSuiteNamedFromPath:(id)arg1;
+ (id)testSuiteWithName:(id)arg1;
+ (id)testCaseNamesForScopeNames:(id)arg1;

- (id)_initWithTestConfiguration:(id)arg1;
- (void)_sortTestsUsingComparator:(CDUnknownBlockType)arg1;
- (void)performTest:(id)arg1;
- (void)_performProtectedSectionForTest:(id)arg1 testSection:(CDUnknownBlockType)arg2;
- (void)_recordUnexpectedFailureForTestRun:(id)arg1 description:(id)arg2 exception:(id)arg3;
- (void)recordFailureWithDescription:(id)arg1 inFile:(id)arg2 atLine:(unsigned long long)arg3 expected:(BOOL)arg4;
- (Class)testRunClass;
- (Class)_requiredTestRunBaseClass;
- (unsigned long long)testCaseCount;
- (void)setTests:(id)arg1;
- (void)addTest:(id)arg1;
- (id)_testSuiteWithIdentifier:(id)arg1;
- (id)description;
- (id)initWithName:(id)arg1;
- (id)init;
- (void)removeTestsWithNames:(id)arg1;

@end
