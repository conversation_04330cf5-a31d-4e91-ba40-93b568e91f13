//
//     Generated by class-dump 3.5 (64 bit).
//
//     class-dump is Copyright (C) 1997-1998, 2000-2001, 2004-2013 by <PERSON>.
//

#import "NSObject.h"

@class NSObject<OS_dispatch_queue>, NSString, XCTKVOExpectation;

@interface _XCKVOExpectationImplementation : NSObject
{
    XCTKVOExpectation *_expectation;
    id _observedObject;
    NSString *_keyPath;
    id _expectedValue;
    unsigned long long _options;
    CDUnknownBlockType _handler;
    NSObject<OS_dispatch_queue> *_queue;
    BOOL _hasCleanedUp;
}
@property(readonly) unsigned long long options; // @synthesize options=_options;
@property(readonly) id expectedValue; // @synthesize expectedValue=_expectedValue;
@property(readonly, copy) NSString *keyPath; // @synthesize keyPath=_keyPath;
@property(readonly) id observedObject; // @synthesize observedObject=_observedObject;
@property(copy) CDUnknownBlockType handler;

- (void)cleanup;
- (void)observeValueForKeyPath:(id)arg1 ofObject:(id)arg2 change:(id)arg3 context:(void *)arg4;
- (id)initWithKeyPath:(id)arg1 object:(id)arg2 expectedValue:(id)arg3 expectation:(id)arg4 options:(unsigned long long)arg5;

@end
