//
//     Generated by class-dump 3.5 (64 bit).
//
//     class-dump is Copyright (C) 1997-1998, 2000-2001, 2004-2013 by <PERSON>.
//

#import <XCTest/XCTestExpectation.h>

@class NSNotificationCenter, NSString, _XCTNSNotificationExpectationImplementation;

@interface XCTNSNotificationExpectation : XCTestExpectation
{
    id _internal;
}
@property(retain) _XCTNSNotificationExpectationImplementation *internal; // @synthesize internal=_internal;
@property(copy) CDUnknownBlockType handler;
@property(readonly) NSNotificationCenter *notificationCenter;
@property(readonly, copy) NSString *notificationName;
@property(readonly) id observedObject;

- (void)cleanup;
- (void)fulfill;
- (id)initWithName:(id)arg1;
- (id)initWithName:(id)arg1 object:(id)arg2;
- (id)initWithName:(id)arg1 object:(id)arg2 notificationCenter:(id)arg3;

@end
