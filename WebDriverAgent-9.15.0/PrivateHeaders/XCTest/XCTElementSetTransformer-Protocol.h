//
//     Generated by class-dump 3.5 (64 bit) (Debug version compiled Jun  9 2015 22:53:21).
//
//     class-dump is Copyright (C) 1997-1998, 2000-2001, 2004-2014 by <PERSON>.
//

@class NSOrderedSet, NSSet, NSString;
@protocol XCTMatchingElementIterator;

@protocol XCTElementSetTransformer <NSObject>
@property BOOL stopsOnFirstMatch;
@property(readonly) BOOL supportsAttributeKeyPathAnalysis;
@property(copy) NSString *transformationDescription;
@property(readonly) BOOL supportsRemoteEvaluation;
- (NSSet *)requiredKeyPathsOrError:(id *)arg1;
- (id <XCTMatchingElementIterator>)iteratorForInput:(id/*XCElementSnapshot*/)arg1;
- (NSOrderedSet *)transform:(NSOrderedSet *)arg1 relatedElements:(id *)arg2;
@end
