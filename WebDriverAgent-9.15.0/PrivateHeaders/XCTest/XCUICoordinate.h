//
//     Generated by class-dump 3.5 (64 bit).
//
//     class-dump is Copyright (C) 1997-1998, 2000-2001, 2004-2013 by <PERSON>.
//

#import <TargetConditionals.h>
#import <XCTest/XCUICoordinate.h>

@class XCUIElement;

#if !TARGET_OS_TV
@interface XCUICoordinate ()
{
    XCUIElement *_element;
    XCUICoordinate *_coordinate;
    CGVector _normalizedOffset;
    CGVector _pointsOffset;
}

@property(readonly) CGVector pointsOffset; // @synthesize pointsOffset=_pointsOffset;
@property(readonly) CGVector normalizedOffset; // @synthesize normalizedOffset=_normalizedOffset;
@property(readonly) XCUICoordinate *coordinate; // @synthesize coordinate=_coordinate;
@property(readonly) XCUIElement *element; // @synthesize element=_element;

- (id)initWithCoordinate:(id)arg1 pointsOffset:(CGVector)arg2;
- (id)initWithElement:(id)arg1 normalizedOffset:(CGVector)arg2;
- (id)init;

- (void)pressForDuration:(double)arg1 thenDragToCoordinate:(id)arg2;
- (void)pressForDuration:(double)arg1;
- (void)doubleTap;
- (void)tap;
- (void)pressWithPressure:(double)arg1 duration:(double)arg2;
- (void)forcePress;

// Since Xcode 12
- (void)pressForDuration:(double)duration
    thenDragToCoordinate:(XCUICoordinate *)otherCoordinate
            withVelocity:(CGFloat)velocity
     thenHoldForDuration:(double)holdDuration;

@end
#endif
