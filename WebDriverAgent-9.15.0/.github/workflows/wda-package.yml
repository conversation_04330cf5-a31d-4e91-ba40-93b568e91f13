name: Building WebDriverAgent

on:
  workflow_dispatch:
  workflow_run:
    workflows: ["Release"]
    types:
      - completed

env:
  HOST: macos-15
  XCODE_VERSION: 16.3
  DESTINATION_SIM: platform=iOS Simulator,name=iPhone 16 Plus
  DESTINATION_SIM_tvOS: platform=tvOS Simulator,name=Apple TV 4K (3rd generation)

jobs:
  host_machine:
    runs-on: ubuntu-latest
    outputs:
      host: ${{ steps.macos_host.outputs.host }}
    steps:
      - run: |
          echo "host=${{ env.HOST }}" >> $GITHUB_OUTPUT
        id: macos_host

  for_real_devices:
    needs: [host_machine]
    name: Build WDA for real iOS and tvOS devices
    runs-on: ${{ needs.host_machine.outputs.host }}

    env:
      PKG_NAME_IOS: "WebDriverAgentRunner-Runner"
      ZIP_PKG_NAME_IOS: "WebDriverAgentRunner-Runner.zip"
      PKG_NAME_TVOS: "WebDriverAgentRunner_tvOS-Runner"
      ZIP_PKG_NAME_TVOS: "WebDriverAgentRunner_tvOS-Runner.zip"

    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - uses: maxim-lobanov/setup-xcode@v1
        with:
          xcode-version: "${{ env.XCODE_VERSION }}"
      - name: Create a zip file of WebDriverAgentRunner-Runner.app for iOS
        run: sh $GITHUB_WORKSPACE/Scripts/ci/build-real.sh
        env:
          DERIVED_DATA_PATH: appium_wda_ios
          SCHEME: WebDriverAgentRunner
          DESTINATION: generic/platform=iOS
          WD: appium_wda_ios/Build/Products/Debug-iphoneos
          ZIP_PKG_NAME: "${{ env.ZIP_PKG_NAME_IOS }}"

      - name: Create a zip file of WebDriverAgentRunner-Runner.app for tvOS
        run: sh $GITHUB_WORKSPACE/Scripts/ci/build-real.sh
        env:
          DERIVED_DATA_PATH: appium_wda_tvos
          SCHEME: WebDriverAgentRunner_tvOS
          DESTINATION: generic/platform=tvOS
          WD: appium_wda_tvos/Build/Products/Debug-appletvos
          ZIP_PKG_NAME: "${{ env.ZIP_PKG_NAME_TVOS }}"

      - name: Upload the built generic app package for iOS
        uses: actions/upload-artifact@master
        with:
          name: "${{ env.PKG_NAME_IOS }}"
          path: "${{ env.ZIP_PKG_NAME_IOS }}"
      - name: Upload the built generic app package for tvOS
        uses: actions/upload-artifact@master
        with:
          name: "${{ env.PKG_NAME_TVOS }}"
          path: "${{ env.ZIP_PKG_NAME_TVOS }}"

  for_simulator_devices:
    needs: [host_machine]
    name: Build WDA for ${{ matrix.target }} simulators
    runs-on: ${{ needs.host_machine.outputs.host }}

    strategy:
      matrix:
        include:
          - target: ''
            arch: x86_64
            simulator_name: Debug-iphonesimulator
          - target: ''
            arch: arm64
            simulator_name: Debug-iphonesimulator
          - target: '_tvOS'
            arch: x86_64
            simulator_name: Debug-appletvsimulator
          - target: '_tvOS'
            arch: arm64
            simulator_name: Debug-appletvsimulator
    steps:
      - name: Checkout
        uses: actions/checkout@v3
      - uses: maxim-lobanov/setup-xcode@v1
        with:
          xcode-version: "${{ env.XCODE_VERSION }}"
      - name: Create a zip of WebDriverAgentRunner${{ matrix.target }} for simulator for ${{ matrix.arch }}
        run: |
          DESTINATION=$DESTINATION_SIM${{ matrix.target }} sh $GITHUB_WORKSPACE/Scripts/ci/build-sim.sh
        env:
          SCHEME: WebDriverAgentRunner${{ matrix.target }}
          ARCHS: ${{ matrix.arch }}
          ZIP_PKG_NAME: "WebDriverAgentRunner${{ matrix.target }}-Build-Sim-${{ matrix.arch }}.zip"
          DERIVED_DATA_PATH: wda_build
          WD: wda_build/Build/Products/${{ matrix.simulator_name }}
      - name: Upload the built generic app package for WebDriverAgentRunner${{ matrix.target }} with ${{ matrix.arch }}
        uses: actions/upload-artifact@master
        with:
          name: "WebDriverAgentRunner${{ matrix.target }}-Build-Sim-${{ matrix.arch }}"
          path: "WebDriverAgentRunner${{ matrix.target }}-Build-Sim-${{ matrix.arch }}.zip"
