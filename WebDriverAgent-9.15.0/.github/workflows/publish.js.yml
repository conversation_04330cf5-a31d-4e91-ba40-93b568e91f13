# This workflow will do a clean install of node dependencies, build the source code and run tests across different versions of node
# For more information see: https://help.github.com/actions/language-and-framework-guides/using-nodejs-with-github-actions

name: Release

on:
  workflow_dispatch:
  push:
    branches: [ master ]

permissions:
  contents: write
  pull-requests: write
  issues: write

jobs:
  build:
    runs-on: macos-15

    env:
      XCODE_VERSION: 16.3
      # Available destination for simulators depend on Xcode version.
      DESTINATION_SIM: platform=iOS Simulator,name=iPhone 16 Plus
      DESTINATION_SIM_TVOS: platform=tvOS Simulator,name=Apple TV 4K (3rd generation)

    steps:
    - uses: actions/checkout@v2
    - name: Use Node.js
      uses: actions/setup-node@v3
      with:
        node-version: lts/*
    - uses: maxim-lobanov/setup-xcode@v1
      with:
        xcode-version: "${{ env.XCODE_VERSION }}"
    - run: npm install --no-package-lock
      name: Install dev dependencies
    - run: npm run build
      name: Run build
    - run: npm run test
      name: Run test

    # building WDA packages
    - name: Building iOS
      run: sh $GITHUB_WORKSPACE/Scripts/ci/build-real.sh
      env:
        DERIVED_DATA_PATH: appium_wda_ios
        SCHEME: WebDriverAgentRunner
        DESTINATION: generic/platform=iOS
        WD: appium_wda_ios/Build/Products/Debug-iphoneos
        ZIP_PKG_NAME: WebDriverAgentRunner-Runner.zip
    - name: Building tvOS
      run: sh $GITHUB_WORKSPACE/Scripts/ci/build-real.sh
      env:
        DERIVED_DATA_PATH: appium_wda_tvos
        SCHEME: WebDriverAgentRunner_tvOS
        DESTINATION: generic/platform=tvOS
        WD: appium_wda_tvos/Build/Products/Debug-appletvos
        ZIP_PKG_NAME: WebDriverAgentRunner_tvOS-Runner.zip
    - name: Building iOS sim arm64
      run: sh $GITHUB_WORKSPACE/Scripts/ci/build-sim.sh
      env:
        DERIVED_DATA_PATH: appium_wda_ios_sim_arm64
        SCHEME: WebDriverAgentRunner
        DESTINATION: ${{ env.DESTINATION_SIM }}
        WD: appium_wda_ios_sim_arm64/Build/Products/Debug-iphonesimulator
        ZIP_PKG_NAME: WebDriverAgentRunner-Build-Sim-arm64.zip
        ARCHS: arm64
    - name: Building iOS sim x86_64
      run: sh $GITHUB_WORKSPACE/Scripts/ci/build-sim.sh
      env:
        DERIVED_DATA_PATH: appium_wda_ios_sim_x86_64
        SCHEME: WebDriverAgentRunner
        DESTINATION: ${{ env.DESTINATION_SIM }}
        WD: appium_wda_ios_sim_x86_64/Build/Products/Debug-iphonesimulator
        ZIP_PKG_NAME: WebDriverAgentRunner-Build-Sim-x86_64.zip
        ARCHS: x86_64
    - name: Building tvOS sim arm64
      run: sh $GITHUB_WORKSPACE/Scripts/ci/build-sim.sh
      env:
        DERIVED_DATA_PATH: appium_wda_tvos_sim_arm64
        SCHEME: WebDriverAgentRunner_tvOS
        DESTINATION: ${{ env.DESTINATION_SIM_TVOS }}
        WD: appium_wda_tvos_sim_arm64/Build/Products/Debug-appletvsimulator
        ZIP_PKG_NAME: WebDriverAgentRunner_tvOS-Build-Sim-arm64.zip
        ARCHS: arm64
    - name: Building tvOS sim x86_64
      run: sh $GITHUB_WORKSPACE/Scripts/ci/build-sim.sh
      env:
        DERIVED_DATA_PATH: appium_wda_tvos_sim_x86_64
        SCHEME: WebDriverAgentRunner_tvOS
        DESTINATION: ${{ env.DESTINATION_SIM_TVOS }}
        WD: appium_wda_tvos_sim_x86_64/Build/Products/Debug-appletvsimulator
        ZIP_PKG_NAME: WebDriverAgentRunner_tvOS-Build-Sim-x86_64.zip
        ARCHS: x86_64

    # release tasks
    - run: npx semantic-release
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        NPM_TOKEN: ${{ secrets.NPM_TOKEN }}
      name: Release

