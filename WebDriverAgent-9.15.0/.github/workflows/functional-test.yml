name: Functional Tests

on: [pull_request]

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

jobs:
  test:
    strategy:
      fail-fast: false
      matrix:
        test_targets:
          - HOST_OS: 'macos-15'
            XCODE_VERSION: '16.4'
            IOS_VERSION: '18.4'
            IOS_MODEL: iPhone 16 Plus
          - HOST_OS: 'macos-14'
            XCODE_VERSION: '15.4'
            IOS_VERSION: '17.5'
            IOS_MODEL: iPhone 15 Plus

    # https://github.com/actions/runner-images/blob/main/images/macos/macos-14-Readme.md
    runs-on: ${{matrix.test_targets.HOST_OS}}
    steps:
    - uses: actions/checkout@v3
    - uses: actions/setup-node@v3
      with:
        node-version: lts/*
    - uses: maxim-lobanov/setup-xcode@v1
      with:
        xcode-version: ${{matrix.test_targets.XCODE_VERSION}}
    - run: |
        npm install
        mkdir -p ./Resources/WebDriverAgent.bundle
      name: Install dev dependencies

    - name: Prepare iOS simulator
      env:
        DEVICE_NAME: ${{matrix.test_targets.IOS_MODEL}}
        PLATFORM_VERSION: ${{matrix.test_targets.IOS_VERSION}}
      run: |
        open -Fn "$(xcode-select -p)/Applications/Simulator.app"
        udid=$(xcrun simctl list devices available -j | \
          node -p "Object.entries(JSON.parse(fs.readFileSync(0)).devices).filter((x) => x[0].includes('$PLATFORM_VERSION'.replace('.', '-'))).reduce((acc, x) => [...acc, ...x[1]], []).find(({name}) => name === '$DEVICE_NAME').udid")
        xcrun simctl bootstatus $udid -b
        xcrun simctl shutdown $udid

    - run: npm run e2e-test
      name: Run functional tests
      env:
        CI: true
        _FORCE_LOGS: 1
        _LOG_TIMESTAMP: 1
        DEVICE_NAME: ${{matrix.test_targets.IOS_MODEL}}
        PLATFORM_VERSION: ${{matrix.test_targets.IOS_VERSION}}
