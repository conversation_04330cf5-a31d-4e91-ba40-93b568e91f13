name: Unit Tests

on: [pull_request, push]


jobs:
  prepare_matrix:
    runs-on: ubuntu-latest
    outputs:
      versions: ${{ steps.generate-matrix.outputs.lts }}
    steps:
    - name: Select all current LTS versions of Node.js
      id: generate-matrix
      uses: msimerson/node-lts-versions@v1

  test:
    needs:
    - prepare_matrix
    strategy:
      matrix:
        node-version: ${{ fromJSON(needs.prepare_matrix.outputs.versions) }}
    runs-on: macos-latest
    steps:
    - uses: actions/checkout@v3
    - uses: actions/setup-node@v3
      with:
        node-version: ${{ matrix.node-version }}
    - run: npm install --no-package-lock
      name: Install dev dependencies
    - run: npm run lint
      name: Run linter
    - run: npm run test
      name: Run unit tests
